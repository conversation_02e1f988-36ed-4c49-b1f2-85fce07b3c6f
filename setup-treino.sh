#!/bin/bash

set -e

echo "=== Setup Treino Project with Java 8 and Tomcat 8 ==="

if [ ! -d "$HOME/.sdkman" ]; then
    echo "Installing SDKMAN..."
    curl -s "https://get.sdkman.io" | bash
    source "$HOME/.sdkman/bin/sdkman-init.sh"
else
    echo "SDKMAN already installed"
    source "$HOME/.sdkman/bin/sdkman-init.sh"
fi

echo "Setting up Java 8..."
if ! sdk list java | grep -q "8.0.*-tem.*installed"; then
    sdk install java 8.0.402-tem
fi
sdk use java 8.0.402-tem

java -version

TOMCAT_DIR="$HOME/tomcat8"
if [ ! -d "$TOMCAT_DIR" ]; then
    echo "Downloading Tomcat 8..."
    cd "$HOME"
    wget https://archive.apache.org/dist/tomcat/tomcat-8/v8.5.100/bin/apache-tomcat-8.5.100.tar.gz
    tar -xzf apache-tomcat-8.5.100.tar.gz
    mv apache-tomcat-8.5.100 tomcat8
    rm apache-tomcat-8.5.100.tar.gz
    chmod +x "$TOMCAT_DIR/bin/"*.sh
else
    echo "Tomcat 8 already installed at $TOMCAT_DIR"
fi

echo "Stopping any running Tomcat instances..."
"$TOMCAT_DIR/bin/shutdown.sh" || true

echo "=== Setup complete. Starting application... ==="
./run.sh
