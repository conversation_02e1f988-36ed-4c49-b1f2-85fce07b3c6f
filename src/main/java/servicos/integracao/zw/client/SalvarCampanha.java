
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de salvarCampanha complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="salvarCampanha">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="campanha" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "salvarCampanha", propOrder = {
    "campanha"
})
public class SalvarCampanha {

    protected String campanha;

    /**
     * Obtém o valor da propriedade campanha.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCampanha() {
        return campanha;
    }

    /**
     * Define o valor da propriedade campanha.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCampanha(String value) {
        this.campanha = value;
    }

}
