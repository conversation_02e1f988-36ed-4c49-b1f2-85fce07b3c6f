
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de gerarConvite complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="gerarConvite">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="empresa" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoCliente" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoTipoConvite" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="nomeIndicado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="telefoneConvidado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="emailConvidado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "gerarConvite", propOrder = {
    "key",
    "empresa",
    "codigoCliente",
    "codigoTipoConvite",
    "nomeIndicado",
    "telefoneConvidado",
    "emailConvidado"
})
public class GerarConvite {

    protected String key;
    protected Integer empresa;
    protected Integer codigoCliente;
    protected Integer codigoTipoConvite;
    protected String nomeIndicado;
    protected String telefoneConvidado;
    protected String emailConvidado;

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Obtém o valor da propriedade empresa.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getEmpresa() {
        return empresa;
    }

    /**
     * Define o valor da propriedade empresa.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setEmpresa(Integer value) {
        this.empresa = value;
    }

    /**
     * Obtém o valor da propriedade codigoCliente.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    /**
     * Define o valor da propriedade codigoCliente.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoCliente(Integer value) {
        this.codigoCliente = value;
    }

    /**
     * Obtém o valor da propriedade codigoTipoConvite.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoTipoConvite() {
        return codigoTipoConvite;
    }

    /**
     * Define o valor da propriedade codigoTipoConvite.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoTipoConvite(Integer value) {
        this.codigoTipoConvite = value;
    }

    /**
     * Obtém o valor da propriedade nomeIndicado.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeIndicado() {
        return nomeIndicado;
    }

    /**
     * Define o valor da propriedade nomeIndicado.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeIndicado(String value) {
        this.nomeIndicado = value;
    }

    /**
     * Obtém o valor da propriedade telefoneConvidado.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTelefoneConvidado() {
        return telefoneConvidado;
    }

    /**
     * Define o valor da propriedade telefoneConvidado.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTelefoneConvidado(String value) {
        this.telefoneConvidado = value;
    }

    /**
     * Obtém o valor da propriedade emailConvidado.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmailConvidado() {
        return emailConvidado;
    }

    /**
     * Define o valor da propriedade emailConvidado.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmailConvidado(String value) {
        this.emailConvidado = value;
    }

}
