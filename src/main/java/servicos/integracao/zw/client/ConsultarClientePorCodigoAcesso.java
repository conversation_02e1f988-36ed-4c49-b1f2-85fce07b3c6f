
package servicos.integracao.zw.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de consultarClientePorCodigoAcesso complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="consultarClientePorCodigoAcesso">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoEmpresaZW" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codacesso" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "consultarClientePorCodigoAcesso", propOrder = {
    "key",
    "codigoEmpresaZW",
    "codacesso"
})
public class ConsultarClientePorCodigoAcesso {

    protected String key;
    protected Integer codigoEmpresaZW;
    protected String codacesso;

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Obtém o valor da propriedade codigoEmpresaZW.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoEmpresaZW() {
        return codigoEmpresaZW;
    }

    /**
     * Define o valor da propriedade codigoEmpresaZW.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoEmpresaZW(Integer value) {
        this.codigoEmpresaZW = value;
    }

    /**
     * Obtém o valor da propriedade codacesso.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodacesso() {
        return codacesso;
    }

    /**
     * Define o valor da propriedade codacesso.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodacesso(String value) {
        this.codacesso = value;
    }

}
