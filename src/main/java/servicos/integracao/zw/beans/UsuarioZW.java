/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.zw.beans;

import br.com.pacto.bean.professor.ProfessorSintetic<PERSON>;
import br.com.pacto.bean.usuario.StatusEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Dados do usuário para sincronização entre sistemas externos e interno")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioZW implements Serializable {

    @ApiModelProperty(value = "Código único do usuário no sistema interno", example = "1001")
    private Integer codigo;

    @ApiModelProperty(value = "Nome de usuário para acesso ao sistema", example = "joao.silva")
    private String userName;

    @ApiModelProperty(value = "Senha do usuário para autenticação", example = "senha123")
    private String senha;

    @ApiModelProperty(value = "Nome completo do usuário", example = "João Silva Santos")
    private String nome;

    @ApiModelProperty(value = "Chave de identificação da empresa", example = "empresa123")
    private String chave;

    @ApiModelProperty(value = "Tipo do usuário no sistema. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- ALUNO (0 - Aluno)\n" +
            "- PROFESSOR (1 - Professor)\n" +
            "- COORDENADOR (2 - Coordenador)\n" +
            "- ROOT (3 - Root)\n" +
            "- CONSULTOR (4 - Consultor)\n" +
            "- CONVIDADO (5 - Convidado)\n", example = "ALUNO")
    private TipoUsuarioEnum tipo;

    @ApiModelProperty(value = "Dados do cliente quando o usuário é do tipo ALUNO")
    private ClienteZW cliente;

    @ApiModelProperty(value = "Dados do professor quando o usuário é do tipo PROFESSOR, COORDENADOR ou similar")
    private ProfessorSintetico professor;

    @ApiModelProperty(value = "Código externo do usuário no sistema de origem", example = "EXT001")
    private String codigoExterno;

    @ApiModelProperty(value = "Código do usuário no sistema ZW", example = "2001")
    private Integer usuarioZW;

    @ApiModelProperty(value = "Código da empresa no sistema ZW", example = "100")
    private Integer empresaZW;

    @ApiModelProperty(value = "Status atual do usuário no sistema", example = "ATIVO")
    private StatusEnum status;

    @ApiModelProperty(value = "Endereço de e-mail do usuário", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "CPF do usuário", example = "12345678901")
    private String cpf;

    @ApiModelProperty(value = "Chave identificadora da foto do usuário para sincronização", example = "foto_123_abc")
    private String fotoKey;

    @ApiModelProperty(value = "Código do convite associado ao usuário", example = "500")
    private Integer convite;

    @ApiModelProperty(value = "Código do usuário que indicou este usuário", example = "300")
    private Integer indicado;

    @ApiModelProperty(value = "Indica se o e-mail do usuário foi verificado", example = "true")
    private Boolean emailVerificado;

    @ApiModelProperty(value = "Código do perfil TW do usuário", example = "10")
    private Integer perfilTw;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public TipoUsuarioEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoUsuarioEnum tipo) {
        this.tipo = tipo;
    }

    public ClienteZW getCliente() {
        return cliente;
    }

    public void setCliente(ClienteZW cliente) {
        this.cliente = cliente;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public String getCodigoExterno() {
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

    public Integer getUsuarioZW() {
        return usuarioZW;
    }

    public void setUsuarioZW(Integer usuarioZW) {
        this.usuarioZW = usuarioZW;
    }

    public Integer getEmpresaZW() {
        return empresaZW;
    }

    public void setEmpresaZW(Integer empresaZW) {
        this.empresaZW = empresaZW;
    }

    public StatusEnum getStatus() {
        return status;
    }

    public void setStatus(StatusEnum status) {
        this.status = status;
    }

    public static Usuario toUsuarioTreino(UsuarioZW zw) {
        Usuario u = new Usuario();
        if (zw.getTipo() == TipoUsuarioEnum.ALUNO) {
            u.setCliente(ClienteZW.toClienteSintetico(zw.getCliente()));
        }
        u.setCodigoExterno(zw.getCodigoExterno());
        u.setNome(zw.getNome());
        u.setProfessor(zw.getProfessor());
        u.setSenha(zw.getSenha());
        u.setStatus(zw.getStatus());
        u.setUserName(zw.getUserName());
        u.setTipo(zw.getTipo());
        u.setUsuarioZW(zw.getUsuarioZW());
        u.setEmpresaZW(zw.getEmpresaZW());
        u.setCpf(zw.getCpf());
        return u;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public Integer getConvite() {
        return convite;
    }

    public void setConvite(Integer convite) {
        this.convite = convite;
    }

    public Integer getIndicado() {
        return indicado;
    }

    public void setIndicado(Integer indicado) {
        this.indicado = indicado;
    }

    public Boolean getEmailVerificado() {
        return emailVerificado;
    }

    public void setEmailVerificado(Boolean emailVerificado) {
        this.emailVerificado = emailVerificado;
    }

    public Integer getPerfilTw() {
        return perfilTw;
    }

    public void setPerfilTw(Integer perfilTw) {
        this.perfilTw = perfilTw;
    }
}
