/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.zw.json;

import br.com.pacto.objeto.Uteis;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class VinculoJSON implements Serializable {

    private Integer codColaborador;
    private Integer codPessoaColaborador;
    private String nomeColaborador;
    private String tipoVinculo;
    private Integer codigoCliente;

    public VinculoJSON() {
    }

    public Integer getCodColaborador() {
        return codColaborador;
    }

    public void setCodColaborador(Integer codColaborador) {
        this.codColaborador = codColaborador;
    }

    public String getNomeColaborador() {
        return nomeColaborador;
    }

    public void setNomeColaborador(String nomeColaborador) {
        this.nomeColaborador = nomeColaborador;
    }

    public String getTipoVinculo() {
        return tipoVinculo;
    }

    public void setTipoVinculo(String tipoVinculo) {
        this.tipoVinculo = tipoVinculo;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getPrimeiroNome() {
        return Uteis.getPrimeiroNome(nomeColaborador);
    }

    public Integer getCodPessoaColaborador() {
        return codPessoaColaborador;
    }

    public void setCodPessoaColaborador(Integer codPessoaColaborador) {
        this.codPessoaColaborador = codPessoaColaborador;
    }
}
