package servicos.integracao.zw.json;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(description = "Informações do coletor de acesso NFC")
public class ColetorJSON implements Serializable {

    @JsonAlias(value = "numeroTerminal")
    @ApiModelProperty(value = "Número do terminal do coletor", example = "1001")
    private Integer numeroTerminal;

    @JsonAlias(value = "codigo")
    @ApiModelProperty(value = "Código único identificador do coletor", example = "15")
    private Integer codigoColetor;

    @JsonAlias(value = "localAcesso")
    @ApiModelProperty(value = "Código do local de acesso associado ao coletor", example = "3")
    private Integer codigoLocalAcesso;

    @JsonAlias(value = "codigoNFC")
    @ApiModelProperty(value = "Código NFC único do coletor para identificação", example = "NFC123456")
    private String codigoNFC;

    @JsonAlias(value = "descricao")
    @ApiModelProperty(value = "Descrição ou nome do coletor de acesso", example = "Catraca Principal - Entrada")
    private String descricaoColetor;

    @JsonAlias(value = "longitude")
    @ApiModelProperty(value = "Coordenada de longitude da localização do coletor", example = "-49.2648")
    private String longitude;

    @JsonAlias(value = "latitude")
    @ApiModelProperty(value = "Coordenada de latitude da localização do coletor", example = "-16.6869")
    private String latitude;

    public Integer getCodigoColetor() {
        return codigoColetor;
    }

    public void setCodigoColetor(Integer codigoColetor) {
        this.codigoColetor = codigoColetor;
    }

    public Integer getNumeroTerminal() {
        return numeroTerminal;
    }

    public void setNumeroTerminal(Integer numeroTerminal) {
        this.numeroTerminal = numeroTerminal;
    }

    public Integer getCodigoLocalAcesso() {
        return codigoLocalAcesso;
    }

    public void setCodigoLocalAcesso(Integer codigoLocalAcesso) {
        this.codigoLocalAcesso = codigoLocalAcesso;
    }

    public String getCodigoNFC() {
        return codigoNFC;
    }

    public void setCodigoNFC(String codigoNFC) {
        this.codigoNFC = codigoNFC;
    }

    public String getDescricaoColetor() {
        return descricaoColetor;
    }

    public void setDescricaoColetor(String descricaoColetor) {
        this.descricaoColetor = descricaoColetor;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }
}