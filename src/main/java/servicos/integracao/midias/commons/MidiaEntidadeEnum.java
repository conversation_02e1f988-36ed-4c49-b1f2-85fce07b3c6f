/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.midias.commons;

/**
 *
 * <AUTHOR>
 */
public enum MidiaEntidadeEnum {

    FOTO_EMPRESA(1, ".jpg", "foto"),
    FOTO_EMPRESA_RELATORIO(2, ".jpg", "fotoRelatorio"),
    FOTO_EMPRESA_EMAIL(3, ".jpg", "fotoEmail"),
    FOTO_EMPRESA_REDESOCIAL(4, ".jpg", "fotoRedeSocial"),
    FOTO_EMPRESA_HomeBackground640x551(5, ".jpg", "homeBackground640x551"),
    FOTO_EMPRESA_HomeBackground320x276(6, ".jpg", "homeBackground320x276"),
    FOTO_PACOTE_STUDIO(7, ".jpg", "imagem"),
    FOTO_PESSOA(8, ".jpg", "foto"),
    FOTO_MODELO_MENSAGEM(9, ".jpg", "imagemModelo"),
    FOTO_LOGIN_POST_PEQUENO(10, ".jpg", "imagem"),
    FOTO_LOGIN_SLIDER_JPG(11, ".jpg", "slider"),
    FOTO_LOGIN_SLIDER_GIF(12, ".gif", "slider_gif"),
    DOCUMENTO_WORD97(13, ".doc", "documento"),
    DOCUMENTO_WORD(14, ".docx", "documento"),
    DOCUMENTO_PDF(15, ".pdf", "documento"),
    FOTO_ANEXO_JPG(16, ".jpg", "documento"),
    FOTO_ANEXO_PNG(17, ".png", "documento"),
    DOCUMENTO_TEXTO(18, ".txt", "documento"),
    ANEXO_ENDERECO_CONTRATO(19, ".jpg", "anexoEndereco"),
    ANEXO_ATESTADO_APTIDAO(20, ".jpg", "atestadoAptidao"),
    ASSINATURA_EMPRESA(21, ".jpg", "assinaturaEmpresa"),
    ANEXO_ATESTADO_OPERACAO_CONTRATO(22,".jpg","atestadoOperacaoContrato"),
    ANEXO_CLIENTE(23,".jpg","anexoCliente"),
    FOTO_AVALIACAO_FISICA(24, ".jpg", "avaliacaoFisica"),
    FOTO_AVALIACAO_POSTURAL(25, ".jpg", "avaliacaoPostural"),
    ASSINATURA_PARQ(26, ".png", "assinaturaParq"),
    FOTO_USUARIO_APP(27, ".jpg", "fotoUsuarioApp"),
    ASSINATURA_USUARIO(28, ".jpg", "assinaturaUsuario"),
    FOTO_MODADLIDADE(29, ".jpg", "fotoModalidade"),
    FOTO_ATIVIDADE(30, ".jpg", "fotoAtividade"),
    FOTO_JPEG(31, ".jpeg", "fotoJpeg"),
    FOTO_WEB(32, ".webp", "fotoWeb"),
    FOTO_COLABORADOR(33, ".jpg", "foto"),
    GIF_ATIVIDADE(34, ".gif", "imagem"),
    FOTO_LOCACAO(35, ".jpg", "fotoLocacao"),
    FOTO_WOD(36, ".jpg", "fotoWod"),
    FOTO_AULA_COLETIVA(37, ".jpg", "fotoAulaColetiva")

    ;
    private Integer codigo;
    private String extensao;
    private String nomeCampo;

    private MidiaEntidadeEnum(Integer codigo, final String extensao, final String nomeCampo) {
        this.codigo = codigo;
        this.extensao = extensao;
        this.nomeCampo = nomeCampo;
    }
    public static MidiaEntidadeEnum obterPorExtensao(String fileName) {
        for (MidiaEntidadeEnum obj : values()) {
            if (fileName != null && fileName.equals(obj.getExtensao())) {
                return obj;
            }
        }
        return null;
    }
    public Integer getCodigo() {
        return codigo;
    }

    public String getExtensao() {
        return extensao;
    }

    public String getNomeCampo() {
        return nomeCampo;
    }
}
