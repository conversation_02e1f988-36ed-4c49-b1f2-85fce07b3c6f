
package servicos.integracao.admapp.client;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de tipoMidiaEnum.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * <p>
 * <pre>
 * &lt;simpleType name="tipoMidiaEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="IMAGEM"/>
 *     &lt;enumeration value="VIDEO"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "tipoMidiaEnum")
@XmlEnum
public enum TipoMidiaEnum {

    IMAGEM,
    VIDEO;

    public String value() {
        return name();
    }

    public static TipoMidiaEnum fromValue(String v) {
        return valueOf(v);
    }

}
