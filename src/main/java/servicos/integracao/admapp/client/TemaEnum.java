
package servicos.integracao.admapp.client;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de temaEnum.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * <p>
 * <pre>
 * &lt;simpleType name="temaEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="DEFAULT"/>
 *     &lt;enumeration value="CLUBWELL"/>
 *     &lt;enumeration value="IRONBOX"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "temaEnum")
@XmlEnum
public enum TemaEnum {

    DEFAULT,
    CLUBWELL,
    IRONBOX;

    public String value() {
        return name();
    }

    public static TemaEnum fromValue(String v) {
        return valueOf(v);
    }

}
