/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.integracao.adm;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.impl.JWTTokenService;
import br.com.pacto.service.discovery.ClientDiscoveryDataDTO;
import br.com.pacto.service.discovery.DiscoveryMsService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import servicos.integracao.adm.client.AdmWS;
import servicos.integracao.adm.client.AdmWS_Service;
import servicos.integracao.adm.client.EmpresaTO;
import servicos.integracao.adm.client.EmpresaWS;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;

import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 *
 * <AUTHOR>
 */
@Service
public class AdmWSConsumer {

    private static final int TIMEOUT = 5000;
    public static final String CONNECT_TIMEOUT = "com.sun.xml.ws.connect.timeout";
    public static final String REQUEST_TIMEOUT = "com.sun.xml.ws.request.timeout";
    private static final ConcurrentHashMap<String, Map<Integer, List<EmpresaWS>>> cacheUsuarioEmpresa = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, List<EmpresaWS>> cacheEmpresas = new ConcurrentHashMap<>();

    private AdmWS wrapper = null;

    public AdmWSConsumer() {
        try {
            getInstance();
        }catch (Exception e){
            Uteis.logar(e, AdmWSConsumer.class);
        }
    }

    public void init() {
        try {
            wrapper = null;
            getInstance();
        }catch (Exception e){
            Uteis.logar(e, IntegracaoCadastrosWSConsumer.class);
        }
    }

    public AdmWS getInstance() {
        try {
            if (wrapper == null) {
                URL u = new URL(Aplicacao.getProp(Aplicacao.urlZillyonWebIntegProp) + "/AdmWS?wsdl");
                QName qName = new QName("http://adm.integracao.servicos/", "AdmWS");
                wrapper = new AdmWS_Service(u, qName).getAdmWSPort();
                Map<String, Object> reqContext = ((BindingProvider) wrapper).getRequestContext();
                reqContext.put(CONNECT_TIMEOUT, TIMEOUT);
                reqContext.put(REQUEST_TIMEOUT, TIMEOUT);
            }
        } catch (Exception e){
            Uteis.logar(e, IntegracaoCadastrosWSConsumer.class);
        }
        return wrapper;
    }

    public String obterModulos(final String chave) {
        Uteis.logar(null, "Obtendo modulos para chave: " + chave);
        EmpresaTO e = getInstance().obterEmpresa(chave);
        final String modulos = e != null ? e.getModulos() : "";
        Uteis.logar(null, String.format("-> Modulos habilitados - Chave: %s Modulos: %s ", new Object[]{chave, modulos}));
        return modulos;
    }

    public List<EmpresaWS> obterEmpresasUsuarioServlet(final String chave, final Integer usuarioZW) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("op", "obter-empresas-usuario");
            params.put("key", chave);
            params.put("usuario", usuarioZW.toString());

            String url = Aplicacao.getProp(chave, Aplicacao.urlZillyonWebInteg);
            String urlParams = ExecuteRequestHttpService.obterUrlComParams(url + "/prest/pacto", params, "UTF-8");
            String msgRetorno = ExecuteRequestHttpService.executeRequestGET(urlParams);

            Uteis.logar(null, "obterEmpresasUsuarioServlet | RETORNO CHAMADA_ZW | " + msgRetorno);

            JSONObject jsonResp = new JSONObject(msgRetorno);
            if (jsonResp.has("content")) {
                List<EmpresaWS> lista = JSONMapper.getList(jsonResp.optJSONArray("content"), EmpresaWS.class, true);
                Uteis.logar(null, "obterEmpresasUsuarioServlet | CONVERT LISTA | " + lista);
                return lista;
            } else {
                throw new ServiceException(msgRetorno);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("obterEmpresasUsuarioServlet | ERRO | " + ex.getMessage());
            //se deu erro no servlet vou consultar AdmWS
            return getInstance().obterEmpresasUsuario(chave, usuarioZW);
        }
    }

    public List<EmpresaWS> obterEmpresasUsuarioZwBoot(final String chave, final Integer usuarioZW) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("key", chave);
            params.put("usuarioZW", usuarioZW.toString());

//            JWTTokenService jwtTokenService = UtilContext.getBean(JWTTokenService.class);
//            String token = jwtTokenService.gerarTokenSimples(chave, null, null);

//            Uteis.logar(null, "obterEmpresasUsuarioZwBoot | TOKEN | " + token);

            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + chave);
//            headers.put("mD0dL5oG5xI6pR8b", token);

            ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(chave);

            String urlZwBoot = clientDiscoveryDataDTO.getServiceUrls().getZwBack();
            String urlParams = ExecuteRequestHttpService.obterUrlComParams(urlZwBoot + "/adm/obter-empresas-usuario", params, "UTF-8");
            String msgRetorno = ExecuteRequestHttpService.executeRequestGet(urlParams, headers);

            Uteis.logar(null, "obterEmpresasUsuarioZwBoot | RETORNO CHAMADA_ZW_BOOT | " + msgRetorno);

            JSONObject jsonResp = new JSONObject(msgRetorno);
            if (jsonResp.has("content")) {
                List<EmpresaWS> lista = JSONMapper.getList(jsonResp.optJSONArray("content"), EmpresaWS.class, true);
                Uteis.logar(null, "obterEmpresasUsuarioZwBoot | CONVERT LISTA | " + lista);
                return lista;
            } else {
                throw new ServiceException(msgRetorno);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("obterEmpresasUsuarioZwBoot | ERRO | " + ex.getMessage());
            //se deu erro no servlet vou consultar AdmWS
            return getInstance().obterEmpresasUsuario(chave, usuarioZW);
        }
    }

    public List<EmpresaWS> obterEmpresasZW(final String chave, final Integer usuarioZW) {
        Uteis.logar(null, "obterEmpresasZW | Obtendo Empresas permitidas para chave: " + chave);

        List<EmpresaWS> lista = null;
        if (Boolean.parseBoolean(Aplicacao.getProp(Aplicacao.usarUsuarioCache)) &&
                cacheUsuarioEmpresa.containsKey(chave)) {
            lista = cacheUsuarioEmpresa.get(chave).get(usuarioZW);
            Uteis.logar(null, String.format("obterEmpresasZW | Peguei do cache Empresas permitidas - Chave: %s Empresas: %s ", chave, (lista != null ? lista : "empresas do usuario vazias")));
        }

        if (UteisValidacao.emptyList(lista)){
            if (Boolean.parseBoolean(Aplicacao.getProp(Aplicacao.usarUsuarioZwBoot))) {
                lista = obterEmpresasUsuarioZwBoot(chave, usuarioZW);
            } else if (Boolean.parseBoolean(Aplicacao.getProp(Aplicacao.usarUsuarioServlet))) {
                lista = obterEmpresasUsuarioServlet(chave, usuarioZW);
            } else {
                lista = getInstance().obterEmpresasUsuario(chave, usuarioZW);
            }

            Map<Integer, List<EmpresaWS>> mapa = cacheUsuarioEmpresa.get(chave);
            if (mapa == null) {
                mapa = new ConcurrentHashMap<>();
            }
            mapa.put(usuarioZW, lista);
            cacheUsuarioEmpresa.put(chave, mapa);
            Uteis.logar(null, String.format("obterEmpresasZW |  Adicionei no cache Empresas permitidas - Chave: %s Empresas: %s ", chave, lista));

            try {
                //alterar no banco somente se consultar novamente
                //se pegou do cache não teve alteração então não precisa alterar no banco do treino
                EmpresaService empresaService = UtilContext.getBean(EmpresaService.class);
                empresaService.persistirEmpresasZW(chave, lista);
            } catch (Exception e) {
                Uteis.logar(null, "obterEmpresasZW | ERRO: Ao persistir empresas do ZillyonWeb no Treino -> Empresa: " + chave);
                Uteis.logar(e, AdmWSConsumer.class);
            }
        }
        Uteis.logar(null, String.format("obterEmpresasZW |  Empresas permitidas - Chave: %s Empresas: %s ", chave, lista));
        return lista;
    }

    public List<EmpresaWS> obterEmpresas(final String chave) {
        Uteis.logar(null, "obterEmpresas | Obtendo Empresas permitidas para chave: " + chave);

        List<EmpresaWS> lista = null;
        if (Boolean.parseBoolean(Aplicacao.getProp(Aplicacao.usarUsuarioCache)) &&
                cacheEmpresas.containsKey(chave)) {
            lista = cacheEmpresas.get(chave);
            Uteis.logar(null, String.format("obterEmpresas | Peguei do cache Empresas - Chave: %s Empresas: %s ", chave, (lista != null ? lista : "empresas vazias")));
        }

        if (UteisValidacao.emptyList(lista)){
            if (Boolean.parseBoolean(Aplicacao.getProp(Aplicacao.usarUsuarioZwBoot))) {
                lista = obterEmpresasZwBoot(chave);
            } else if (Boolean.parseBoolean(Aplicacao.getProp(Aplicacao.usarUsuarioServlet))) {
                lista = obterEmpresasServlet(chave);
            } else {
                lista = getInstance().obterEmpresas(chave);
            }
            cacheEmpresas.put(chave, lista);
            Uteis.logar(null, String.format("obterEmpresas | Adicionei no cache Empresas permitidas - Chave: %s Empresas: %s ", chave, lista));
        }
        Uteis.logar(null, String.format("obterEmpresas | Empresas permitidas - Chave: %s Empresas: %s ", chave, lista));
        return lista;
    }

    public List<EmpresaWS> obterEmpresasZwBoot(final String chave) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("key", chave);

//            JWTTokenService jwtTokenService = UtilContext.getBean(JWTTokenService.class);
//            String token = jwtTokenService.gerarTokenSimples(chave, null, null);
//
////            Uteis.logar(null, "obterEmpresasZwBoot | TOKEN | " + token);

            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + chave);
//            headers.put("mD0dL5oG5xI6pR8b", token);

            ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(chave);

            String urlZwBoot = clientDiscoveryDataDTO.getServiceUrls().getZwBack();
            String urlParams = ExecuteRequestHttpService.obterUrlComParams(urlZwBoot + "/adm/obter-empresas-com-situacao", params, "UTF-8");
            String msgRetorno = ExecuteRequestHttpService.executeRequestGet(urlParams, headers);

            Uteis.logar(null, "obterEmpresasZwBoot | RETORNO CHAMADA_ZW_BOOT | " + msgRetorno);

            JSONObject jsonResp = new JSONObject(msgRetorno);
            if (jsonResp.has("content")) {
                List<EmpresaWS> lista = JSONMapper.getList(jsonResp.optJSONArray("content"), EmpresaWS.class, true);
                Uteis.logar(null, "obterEmpresasZwBoot | CONVERT LISTA | " + lista);
                return lista;
            } else {
                throw new ServiceException(msgRetorno);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("obterEmpresasZwBoot | ERRO | " + ex.getMessage());
            //se deu erro no servlet vou consultar AdmWS
            return getInstance().obterEmpresas(chave);
        }
    }

    public List<EmpresaWS> obterEmpresasServlet(final String chave) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("op", "obter-empresas-com-situacao");
            params.put("key", chave);

            String url = Aplicacao.getProp(chave, Aplicacao.urlZillyonWebInteg);
            String urlParams = ExecuteRequestHttpService.obterUrlComParams(url + "/prest/pacto", params, "UTF-8");
            String msgRetorno = ExecuteRequestHttpService.executeRequestGET(urlParams);

            Uteis.logar(null, "obterEmpresasServlet | RETORNO CHAMADA_ZW | " + msgRetorno);

            JSONObject jsonResp = new JSONObject(msgRetorno);
            if (jsonResp.has("content")) {
                List<EmpresaWS> lista = JSONMapper.getList(jsonResp.optJSONArray("content"), EmpresaWS.class, true);
                Uteis.logar(null, "obterEmpresasServlet | CONVERT LISTA | " + lista);
                return lista;
            } else {
                throw new ServiceException(msgRetorno);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("obterEmpresasServlet | ERRO | " + ex.getMessage());
            //se deu erro no servlet vou consultar AdmWS
            return getInstance().obterEmpresas(chave);
        }
    }

    public static void purgeCache(final String chave, final Integer usuario) {
        Uteis.logar(null, "AdmWSConsumer | purgeCache | clear | " + (chave != null ? chave : "null") + " | Usuario: " + (usuario != null ? usuario : "null"));
        if (!UteisValidacao.emptyString(chave)) {
            if (!UteisValidacao.emptyNumber(usuario)) {
                Map<Integer, List<EmpresaWS>> map = cacheUsuarioEmpresa.get(chave);
                if (map != null) {
                    map.remove(usuario);
                    cacheUsuarioEmpresa.put(chave, map);
                }
            } else {
                cacheUsuarioEmpresa.remove(chave);
            }
            cacheEmpresas.remove(chave);
        } else {
            cacheUsuarioEmpresa.clear();
            cacheEmpresas.clear();
        }
    }
}
