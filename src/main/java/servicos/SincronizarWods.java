package servicos;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.bean.wod.WodTO;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.wod.WodServiceImpl;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class SincronizarWods {

    private static String inicio = "20/02/2023";
    private static String fim = "09/03/2023";

    public static void main(String[] args) {
        try {
            String chaveFranqueadora = "d57083709f0fecd013ed05e81654cf9a";
            //obter chaves da rede
            Set<String> chavesRede = getOAMDChavesRede(chaveFranqueadora);
            JSONObject result = Uteis.getJSON("https://discovery.ms.pactosolucoes.com.br/find/"+chaveFranqueadora);
            String treinoUrl = result.getJSONObject("content").getJSONObject("serviceUrls").getString("treinoUrl");
            //obter wods dos proximos 15 dias da franqueadora
            List<Wod> wodsFranquia = wodsRecentes(treinoUrl, chaveFranqueadora);
            //iterar nos wods verificando se as chaves da rede ja tem o wod, se não tiverem
            //enviar
            int i = 0;
            for(String chaveFranqueado : chavesRede){
//                erro na d2d2373b1f254882c3a8d7625fd41bd
//                erro na 187ead6b01672c42fe23781af9b3ec84
//                erro na 60d227bbcff955e59340f32cfe2b5217
//                erro na e3d99f70e35014e6bcfa47d41dc6e2ca
//                erro na b0dfc1ccc3928d776f6c32e9ef062982
//                erro na fc6282f649763c84219ae97d8ac419da
//                erro na aaa76894065ed05b901c2a7f9f5b229b
//                erro na cf0055dfab9fef442b98990d59202e38
//                erro na 22ba7b3c187dcb7c6dbc3a910991127e
//                erro na 2823cb0964b80a0abfd85c0a2a5ef128
//                erro na f49ae423f484962e0fd7af9239db12c2
//                erro na 66a3fa82fbb166a1aa085cbd62a95436
//                erro na aa0c1d7bb9a46cb2cc1a149da59c5d2a
//                erro na 84af9b0f54f820a8777390940920c8c5
//                erro na 7876e805841216a23f8a340f75ca5341
//                erro na 8a375f985ce88322c5ea55879830f2da
//                erro na 30766968829fe6970ebbff028e074f36
//                erro na 60ac9edf68b6a57770df21e5fdfa58f6
//                erro na 315dd35f6b9ae2c692049190ef760f42
//                erro na 2eed0b45f07b44e447a33b9769a0d0f5
//                erro na 32b89e66ae1a662a80b60295485dc8b6
//                erro na c7879553b2b286c7db4b25add9c3e544
//                erro na 3765124efd330e3131e5a10449ae7f0e
//                erro na b9fca7784fd95b03b49b998836b88c7e
//                erro na 4b9c0bc521316af58ae0b39be5d13cf7
//                erro na c573aadaabcaf21aaf70d3befa714dcf
//                erro na d817e59d5246150755b0a6df72f032bf
//                erro na 44ef8454f9814f7d45ef705b0a08960f
//                erro na b56854576706d9fd824bf6d9cceed92
//                erro na 2f236851e2429ceec2cab6b68b21760c
//                erro na 924d41efd42d3df47bcb1498c8fb6ce6
//                erro na 8e53e5a7efb933fea3107206189734c9
//                erro na ce7c27e26af6bd25454b5569295eab07
//                erro na 1739d9147254f03678ac64f49ce6a338
//                erro na bbc9035b35f94ce16df9d12d9453cd34
//                erro na 5e1f6fb0c3613b3e06faffd8e69aa53e
//                erro na 6c01b477444b77c16220330bc474dd9c
//                erro na eeb775ce969ca166973e8de5b82d9337
//                erro na ff1596908ce5d40ace39b72e316a58dc
//                erro na 8e1e9b6c5313110246541011c3c8d570
//                erro na 5dfa4a18bc8065267845022728a3eda6
//                erro na 7c225350eda43d53e2c181ec02a0d1bd
//                erro na d50241ecb0c09d81571422a1531ad7f1
//                erro na 3f7be9a0fbf628303465037810477c81
//                erro na 6463939adf4a4734666b750001dbbc49
//                erro na c546fbe529c50f0cbbf68974b6e16c48
//                erro na b5ddd9b18e19e9b653da2c05b2692520
//                erro na 1a70d397266e5ffbf053cd6ac37d9443
//                erro na c6dd2a38f8b57e02cb0a8c3420772543
//                erro na cc9b8dccc01cadd8e93fbb0adf30ae47
//                erro na be08ffd4241c680ed4c7d6f134dee55f
//                erro na faecd8421cd8870c9da276265177af4
//                erro na 62f578247708f7fc1c62a35681231456
//                erro na 5e2a28f8961d35159aa7ba5cf0ddd5a2
//                erro na e4946b70a40f9a559231f4472317115f
//                erro na 484cfc830032d9db53be5eed69ff8d6e
//                erro na cd13641161fec2c89c987ccf0b3f36f2
//                erro na 6c751914212ed3fdd6f084526c32ac76
//                erro na 5bc98d500698b05e7dd2414d5c5f9132
//                erro na 8a0c72481412724c9ac0328507de0331
//                erro na cbbcf0ce5495d56b4d13e614c8c7a807
//                erro na bf04a3236c3431f7579c9065e636568e
//                erro na 5f1e0911f79f63921c0e1654aaa16180
                if(!chaveFranqueado.equals(chaveFranqueadora)){
                    result = Uteis.getJSON("https://discovery.ms.pactosolucoes.com.br/find/"+chaveFranqueado);
                    treinoUrl = result.getJSONObject("content").getJSONObject("serviceUrls").getString("treinoUrl");
                    try {
                        List<Wod> wodsFranqueados = wodsRecentes(treinoUrl, chaveFranqueado);

                        for(Wod w : wodsFranquia){
                            boolean compartilhar = true;

                            for(Wod wfranqueado : wodsFranqueados){
                                if(wfranqueado.getNome().equals(w.getNome())){
                                    compartilhar = false;
                                }
                            }

                            if(compartilhar){
                                compartilharWodRede(chaveFranqueado, w, treinoUrl);
                            }
                        }
                    }catch (Exception e){
                        System.out.println("erro na " + chaveFranqueado);
                    }

                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }


    private static List<Wod> wodsRecentes(String treinoUrl, String chave) throws Exception{
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("inicio", inicio));
        params.add(new BasicNameValuePair("fim", fim));
        params.add(new BasicNameValuePair("empresaCodigo", "1"));
        params.add(new BasicNameValuePair("usuario", "1"));

        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(treinoUrl + "/prest/crossfit/" + chave + "/wods");
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        JSONObject retornoWODS = new JSONObject(handler.handleResponse(response));
        JSONArray aReturn = retornoWODS.getJSONArray("return");
        JSONArray wods = new JSONArray();
        for(int i = 0; i < aReturn.length(); i++){
            JSONObject jsonObject = aReturn.getJSONObject(i);
            jsonObject.remove("diaApresentar");
            jsonObject.remove("urlImagemRetorno");
            jsonObject.remove("keyImagem");
            wods.put(jsonObject);
        }

        return JSONMapper.getList(wods, Wod.class);
    }

    private static Set<String> getOAMDChavesRede(final String chave) throws Exception {
        final String url = "https://app.pactosolucoes.com.br/oamd/prest/empresaFinanceiro/chavesRede?chaveZW=" + chave;
        String s = ExecuteRequestHttpService.executeRequestGET(url);
        JSONArray array = new JSONObject(s).getJSONArray("chaves");
        return new HashSet(){{
            for(int i = 0; i < array.length(); i++){
                try {
                    add(array.getString(i));
                } catch (Exception e){

                }

            }
        }};
    }


    private static void compartilharWodRede(final String chaveFranqueado, Wod wod, String treinoUrl){
        try {
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("idRede", wod.getIdRede());
            jsonBody.put("empresaId", wod.getEmpresa());
            jsonBody.put("lancou", wod.getUsuarioLancouRede());
            jsonBody.put("chaveLancou", wod.getChaveLancouRede());
            jsonBody.put("nome", wod.getNome());
            jsonBody.put("dia", wod.getDia().getTime());
            jsonBody.put("tipo", wod.getTipoWodTabela().getCodigo());
            jsonBody.put("aquecimento", wod.getAquecimento());
            jsonBody.put("alongamento", wod.getAlongamento());
            jsonBody.put("parteTecnicaskill", wod.getParteTecnicaSkill());
            jsonBody.put("complex", wod.getComplexEmom());
            jsonBody.put("wod", wod.getDescricaoExercicios());
            jsonBody.put("unidadeLancou", wod.getUnidadeLancou());

            try {
                StringEntity entity = new StringEntity(jsonBody.toString(), "UTF-8");
                HttpPost httpPost = new HttpPost(treinoUrl + "/prest/crossfit/" + chaveFranqueado + "/compartilhado");
                httpPost.setEntity(entity);
                httpPost.setHeader("Content-Type", "application/json");
                HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
                HttpResponse response = client.execute(httpPost);
                ResponseHandler<String> handler = new BasicResponseHandler();
                JSONObject retornoWODS = new JSONObject(handler.handleResponse(response));
            }catch (Exception e){
                Uteis.logar(e, WodServiceImpl.class);
            }
        }catch (Exception e){
            Uteis.logar(e, WodServiceImpl.class);
        }
    }



}
