package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações sobre assimetrias posturais observadas na avaliação postural.")
public class AnamnesePosturalAssimetriasDTO {

    @ApiModelProperty(
            value = "Indica se há assimetria nos ombros.\n\n" +
                    "<strong>Valores possíveis:</strong>\n" +
                    "- NENHUMA_ELEVACAO: Nenhuma elevação observada\n" +
                    "- ELEVECAO_DIREITO: Elevação do ombro direito\n" +
                    "- ELEVECAO_ESQUERDO: Elevação do ombro esquerdo",
            example = "ELEVECAO_DIREITO"
    )
    private AssimetriaEnum ombrosAssimetricos;

    @ApiModelProperty(
            value = "Indica se há assimetria no quadril.\n\n" +
                    "<strong>Valores possíveis:</strong>\n" +
                    "- NENHUMA_ELEVACAO: Nenhuma elevação observada\n" +
                    "- ELEVECAO_PELVE_DIREITA: Elevação da pelve direita\n" +
                    "- ELEVECAO_PELVE_ESQUERDA: Elevação da pelve esquerda",
            example = "ELEVECAO_PELVE_DIREITA"
    )
    private AssimetriaEnum assimetriaQuadril;


    public AnamnesePosturalAssimetriasDTO() {
    }

    public AnamnesePosturalAssimetriasDTO(AssimetriaEnum ombrosAssimetricos, AssimetriaEnum assimetriaQuadril) {
        this.ombrosAssimetricos = ombrosAssimetricos;
        this.assimetriaQuadril = assimetriaQuadril;
    }

    public AssimetriaEnum getOmbrosAssimetricos() {
        return ombrosAssimetricos;
    }

    public void setOmbrosAssimetricos(AssimetriaEnum ombrosAssimetricos) {
        this.ombrosAssimetricos = ombrosAssimetricos;
    }

    public AssimetriaEnum getAssimetriaQuadril() {
        return assimetriaQuadril;
    }

    public void setAssimetriaQuadril(AssimetriaEnum assimetriaQuadril) {
        this.assimetriaQuadril = assimetriaQuadril;
    }
}
