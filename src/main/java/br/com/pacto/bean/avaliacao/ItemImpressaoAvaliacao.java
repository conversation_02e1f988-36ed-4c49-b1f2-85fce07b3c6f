package br.com.pacto.bean.avaliacao;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON> on 08/02/2017.
 */
public class ItemImpressaoAvaliacao {
    private String descricao;
    private Map<String, String> valor = new HashMap<String, String>();

    public ItemImpressaoAvaliacao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Map<String, String> getValor() {
        return valor;
    }

    public void setValor(Map<String, String> valor) {
        this.valor = valor;
    }
}
