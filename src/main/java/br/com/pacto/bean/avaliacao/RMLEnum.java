package br.com.pacto.bean.avaliacao;

/**
 * Created by <PERSON><PERSON> on 01/05/2017.
 * Atualizada by Anna Carolina em 09/2023 separando o RML entre homem e mulher, foi usada essas tabelas como referência:
 * http://www.saudeemmovimento.com.br/saude/tabelas/tabela_de_referencia_rbraco.htm
 * http://www.saudeemmovimento.com.br/saude/tabelas/tabela_de_referencia_rabdominal.htm
 * Fonte: Pollock, M. L. & Wilmore J. H., 1993
 */
public enum RMLEnum {
    BRACOS_15(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO, RMLLinha.CAT_15_19, 10, 19, 33, 25, 18, 12, 0),
    BRACOS_20(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO, RMLLinha.CAT_20_29,20, 29, 30, 21, 15, 10, 0),
    BRACOS_30(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO, RMLLinha.CAT_30_39,30, 39, 27, 20, 13, 8, 0),
    BRACOS_40(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO, RMLLinha.CAT_40_49,40, 49, 24, 15, 11, 5, 0),
    BRACOS_50(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO, RMLLinha.CAT_50_59,50, 59, 21, 11, 07, 2, 0),
    BRACOS_60(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO, RMLLinha.CAT_60_69,60, 150, 17, 12, 05, 2, 0),
    ABDOMEN_15(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN, RMLLinha.CAT_15_19,10, 19, 42, 36, 32, 27, 0),
    ABDOMEN_20(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN, RMLLinha.CAT_20_29,20, 29, 36, 31, 25, 21, 0),
    ABDOMEN_30(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN, RMLLinha.CAT_30_39,30, 39, 29, 24, 20, 15, 0),
    ABDOMEN_40(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN, RMLLinha.CAT_40_49,40, 49, 25, 20, 15, 07, 0),
    ABDOMEN_50(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN, RMLLinha.CAT_50_59,50, 59, 19, 12, 05, 03, 0),
    ABDOMEN_60(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN, RMLLinha.CAT_60_69,60, 150, 16, 12, 04, 02, 0),

    BRACOS_15_HOMEM(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_HOMEM, RMLLinha.CAT_15_19,10, 19, 39, 29, 23, 18, 0),
    BRACOS_20_HOMEM(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_HOMEM, RMLLinha.CAT_20_29,20, 29, 36, 29, 22, 17, 0),
    BRACOS_30_HOMEM(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_HOMEM, RMLLinha.CAT_30_39,30, 39, 30, 22, 17, 12, 0),
    BRACOS_40_HOMEM(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_HOMEM, RMLLinha.CAT_40_49,40, 49, 22, 17, 13, 10, 0),
    BRACOS_50_HOMEM(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_HOMEM, RMLLinha.CAT_50_59,50, 59, 21, 13, 10, 7, 0),
    BRACOS_60_HOMEM(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_HOMEM, RMLLinha.CAT_60_69,60, 150, 18, 11, 8, 5, 0),

    ABDOMEN_15_HOMEM(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_HOMEM, RMLLinha.CAT_15_19,10, 19, 48, 42, 38, 33, 0),
    ABDOMEN_20_HOMEM(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_HOMEM, RMLLinha.CAT_20_29,20, 29, 43, 37, 33, 29, 0),
    ABDOMEN_30_HOMEM(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_HOMEM, RMLLinha.CAT_30_39,30, 39, 36, 31, 27, 22, 0),
    ABDOMEN_40_HOMEM(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_HOMEM, RMLLinha.CAT_40_49,40, 49, 31, 26, 22, 17, 0),
    ABDOMEN_50_HOMEM(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_HOMEM, RMLLinha.CAT_50_59,50, 59, 26, 22, 18, 13,0),
    ABDOMEN_60_HOMEM(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_HOMEM, RMLLinha.CAT_60_69,60, 150, 23, 17, 12, 7, 0),

    BRACOS_15_MULHER(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_MULHER, RMLLinha.CAT_15_19,10, 19, 33, 25, 18, 12, 0),
    BRACOS_20_MULHER(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_MULHER, RMLLinha.CAT_20_29,20, 29, 30, 21, 15, 10, 0),
    BRACOS_30_MULHER(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_MULHER, RMLLinha.CAT_30_39,30, 39, 27, 20, 13, 8, 0),
    BRACOS_40_MULHER(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_MULHER, RMLLinha.CAT_40_49,40, 49, 24, 15, 11, 5, 0),
    BRACOS_50_MULHER(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_MULHER, RMLLinha.CAT_50_59,50, 59, 21, 11, 7, 2, 0),
    BRACOS_60_MULHER(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_BRACO_MULHER, RMLLinha.CAT_60_69,60, 150, 17, 12, 5, 2, 0),

    ABDOMEN_15_MULHER(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_MULHER, RMLLinha.CAT_15_19,10, 19, 42, 36, 32, 27, 0),
    ABDOMEN_20_MULHER(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_MULHER, RMLLinha.CAT_20_29,20, 29, 36, 31, 25, 21, 0),
    ABDOMEN_30_MULHER(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_MULHER, RMLLinha.CAT_30_39,30, 39, 29, 24, 20, 15, 0),
    ABDOMEN_40_MULHER(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_MULHER, RMLLinha.CAT_40_49,40, 49, 25, 20, 15, 7, 0),
    ABDOMEN_50_MULHER(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_MULHER, RMLLinha.CAT_50_59,50, 59, 19, 12, 5, 3, 0),
    ABDOMEN_60_MULHER(ItemAvaliacaoFisicaEnum.RESISTENCIA_MUSCULAR_ABDOMEN_MULHER, RMLLinha.CAT_60_69,60, 150, 16, 12, 4, 2, 0),
    ;


    private Integer idadeMin;
    private Integer idadeMax;
    private Integer excelente;
    private Integer acimaMedia;
    private Integer media;
    private Integer abaixoMedia;
    private Integer fraco;
    private ItemAvaliacaoFisicaEnum tipo;
    private RMLLinha faixa;

    RMLEnum(ItemAvaliacaoFisicaEnum tipo,
            RMLLinha faixa,
            Integer idadeMin,
            Integer idadeMax, Integer excelente, Integer acimaMedia, Integer media, Integer abaixoMedia, Integer fraco) {
        this.idadeMin = idadeMin;
        this.idadeMax = idadeMax;
        this.excelente = excelente;
        this.acimaMedia = acimaMedia;
        this.media = media;
        this.abaixoMedia = abaixoMedia;
        this.fraco = fraco;
        this.tipo = tipo;
        this.faixa = faixa;
    }



    public Integer getIdadeMin() {
        return idadeMin;
    }

    public void setIdadeMin(Integer idadeMin) {
        this.idadeMin = idadeMin;
    }

    public Integer getIdadeMax() {
        return idadeMax;
    }

    public void setIdadeMax(Integer idadeMax) {
        this.idadeMax = idadeMax;
    }

    public Integer getExcelente() {
        return excelente;
    }

    public void setExcelente(Integer excelente) {
        this.excelente = excelente;
    }

    public Integer getAcimaMedia() {
        return acimaMedia;
    }

    public void setAcimaMedia(Integer acimaMedia) {
        this.acimaMedia = acimaMedia;
    }

    public Integer getMedia() {
        return media;
    }

    public void setMedia(Integer media) {
        this.media = media;
    }

    public Integer getAbaixoMedia() {
        return abaixoMedia;
    }

    public void setAbaixoMedia(Integer abaixoMedia) {
        this.abaixoMedia = abaixoMedia;
    }

    public Integer getFraco() {
        return fraco;
    }

    public void setFraco(Integer fraco) {
        this.fraco = fraco;
    }

    public ItemAvaliacaoFisicaEnum getTipo() {
        return tipo;
    }

    public void setTipo(ItemAvaliacaoFisicaEnum tipo) {
        this.tipo = tipo;
    }

    public RMLLinha getFaixa() {
        return faixa;
    }
}
