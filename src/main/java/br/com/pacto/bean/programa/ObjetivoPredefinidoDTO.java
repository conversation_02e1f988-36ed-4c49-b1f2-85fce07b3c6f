package br.com.pacto.bean.programa;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados de um objetivo predefinido para treino")
public class ObjetivoPredefinidoDTO {

    @ApiModelProperty(value = "Código identificador único do objetivo predefinido", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome descritivo do objetivo predefinido", example = "Perda de Peso")
    private String nome;

    public ObjetivoPredefinidoDTO() {

    }
    public ObjetivoPredefinidoDTO(ObjetivoPredefinido obj) {
        this.id = obj.getCodigo();
        this.nome = obj.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
