/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.gestaopersonal;

import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
public class ConfiguracaoPersonalEmpresa implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Integer codigoEmpresaZW;
    private Boolean mostrarFotoMonitor = Boolean.FALSE;
    private Boolean consumirCreditoPorAluno = Boolean.FALSE;
    private Boolean fotoParaPersonal = Boolean.FALSE;
    private Integer tempoCheckOutAutomatico = 0;
    private Integer duracaoCredito = 0;
    private Boolean obrigatorioAssociarAluno = Boolean.FALSE;

    public ConfiguracaoPersonalEmpresa() {
    }

    public Integer getCodigoEmpresaZW() {
        return codigoEmpresaZW;
    }

    public void setCodigoEmpresaZW(Integer codigoEmpresaZW) {
        this.codigoEmpresaZW = codigoEmpresaZW;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getMostrarFotoMonitor() {
        return mostrarFotoMonitor;
    }

    public void setMostrarFotoMonitor(Boolean mostrarFotoMonitor) {
        this.mostrarFotoMonitor = mostrarFotoMonitor;
    }

    public Boolean getConsumirCreditoPorAluno() {
        return consumirCreditoPorAluno;
    }

    public void setConsumirCreditoPorAluno(Boolean consumirCreditoPorAluno) {
        this.consumirCreditoPorAluno = consumirCreditoPorAluno;
    }

    public Boolean getFotoParaPersonal() {
        return fotoParaPersonal;
    }

    public void setFotoParaPersonal(Boolean fotoParaPersonal) {
        this.fotoParaPersonal = fotoParaPersonal;
    }

    public Integer getTempoCheckOutAutomatico() {
        return tempoCheckOutAutomatico;
    }

    public void setTempoCheckOutAutomatico(Integer tempoCheckOutAutomatico) {
        this.tempoCheckOutAutomatico = tempoCheckOutAutomatico;
    }

    public Integer getDuracaoCredito() {
        return duracaoCredito;
    }

    public void setDuracaoCredito(Integer duracaoCredito) {
        this.duracaoCredito = duracaoCredito;
    }

    public Boolean getObrigatorioAssociarAluno() {
        return obrigatorioAssociarAluno;
    }

    public void setObrigatorioAssociarAluno(Boolean obrigatorioAssociarAluno) {
        this.obrigatorioAssociarAluno = obrigatorioAssociarAluno;
    }

    
}
