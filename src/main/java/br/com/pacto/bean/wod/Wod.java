package br.com.pacto.bean.wod;

/**
 * Created by Rafael on 20/07/2016.
 */

import br.com.pacto.bean.aparelho.AparelhoWod;
import br.com.pacto.bean.atividade.AtividadeWod;
import br.com.pacto.bean.benchmark.Benchmark;
import br.com.pacto.bean.crossfit.EventoCrossfit;
import br.com.pacto.bean.tipowod.TipoWod;
import br.com.pacto.controller.json.crossfit.AparelhoWodJSON;
import br.com.pacto.controller.json.crossfit.AtividadeWodJSON;
import br.com.pacto.controller.json.crossfit.RankingJSON;
import br.com.pacto.enumerador.crossfit.OrigemExercicio;
import br.com.pacto.enumerador.crossfit.TipoWodEnum;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Entity
@Table
public class Wod implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    private Integer empresa;
    private Boolean compartilharRede = Boolean.FALSE;
    private String idRede;
    private String usuarioLancouRede;
    private String chaveLancouRede;
    private String unidadeLancou;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 9999)
    private String descricaoExercicios;
    @Enumerated(EnumType.ORDINAL)
    private TipoWodEnum tipoWod;
    private String urlImagem;
    @Enumerated(EnumType.ORDINAL)
    private OrigemExercicio origemExercicio;
    @ManyToOne
    private Benchmark benchmark;
    @Lob
    @Type(type="org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 9999)
    private String observacao;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="dd/MM/yyyy")
    private Date dia;
    @Transient
    boolean temResultado = false;
    @Transient
    private RankingJSON ranking;
    @OneToOne
    private TipoWod tipoWodTabela;
    @ManyToOne
    private EventoCrossfit evento;
    @Transient
    private List<ScoreTreino> scoresTreino = new ArrayList<ScoreTreino>();
    @Column(columnDefinition = "text")
    private String alongamento;
    @Column(columnDefinition = "text")
    private String aquecimento;
    @Column(columnDefinition = "text")
    private String parteTecnicaskill;
    @JsonIgnore
    @OneToMany(cascade = {CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, mappedBy = "wod", targetEntity = AtividadeWod.class)
    private List<AtividadeWod> atividades = new ArrayList<AtividadeWod>();
    @JsonIgnore
    @OneToMany(cascade = {CascadeType.MERGE, CascadeType.REMOVE}, fetch = FetchType.LAZY, mappedBy = "wod", targetEntity = AparelhoWod.class)
    private List<AparelhoWod> aparelhos = new ArrayList<AparelhoWod>();
    @Transient
    private List<AtividadeWodJSON> atividadeWodJSON;
    @Transient
    private List<AparelhoWodJSON> aparelhoWodJSON;
    @Transient
    private List<ComentarioWod> comentarios;
    @Column(columnDefinition = "text")
    private String complexEmom;
    @Transient
    private boolean temResultadoGeral = false;
    @Transient
    private boolean contratoCrossfit;
    private String nivelWod;


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricaoExercicios() {
        return descricaoExercicios;
    }

    public void setDescricaoExercicios(String descricaoExercicios) {
        this.descricaoExercicios = descricaoExercicios;
    }

    public TipoWodEnum getTipoWod() {
        return tipoWod;
    }

    public void setTipoWod(TipoWodEnum tipoWod) {
        this.tipoWod = tipoWod;
    }

    public String getUrlImagemRetorno() {
        Calendar calendar = Calendar.getInstance();
        return urlImagem == null || urlImagem.trim().isEmpty() ? ""
                : !urlImagem.contains("?") ? urlImagem + "?" + System.currentTimeMillis() : urlImagem;
    }

    private String removerPrefixosIndevidos(String fotoKey) {
        if (UteisValidacao.emptyString(fotoKey)) {
            return "";
        }
        String[] prefixos = {
                Aplicacao.PREFIX_URL_IMAGEM_CDN,
                Aplicacao.PREFIX_URL_IMAGEM_CLOUDFRONT,
                Aplicacao.PREFIX_URL_IMAGEM_S3
        };
        boolean prefixoExiste;
        int tentativas = 0;
        do {
            prefixoExiste = false;
            for (String prefix : prefixos) {
                if (fotoKey.startsWith(prefix)) {
                    fotoKey = fotoKey.substring(prefix.length());
                    prefixoExiste = true;
                }
            }
            tentativas++;
        } while (prefixoExiste && tentativas < 3);

        return fotoKey;
    }

    public String getKeyImagem() {
        return removerPrefixosIndevidos(urlImagem);
    }

    public void setUrlImagem(String urlImagem) {
        this.urlImagem = urlImagem;
    }

    public OrigemExercicio getOrigemExercicio() {
        return origemExercicio;
    }

    public void setOrigemExercicio(OrigemExercicio origemExercicio) {
        this.origemExercicio = origemExercicio;
    }

    public Benchmark getBenchmark() {
        return benchmark;
    }

    public void setBenchmark(Benchmark benchmark) {
        this.benchmark = benchmark;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public RankingJSON getRanking() {
        return ranking;
    }

    public void setRanking(RankingJSON ranking) {
        this.ranking = ranking;
    }

    public boolean isTemResultado() {
        return temResultado;
    }

    public void setTemResultado(boolean temResultado) {
        this.temResultado = temResultado;
    }

    public String getDiaApresentar(){
        return Uteis.getData(this.getDia());
    }

    public TipoWod getTipoWodTabela() {
        return tipoWodTabela;
    }

    public void setTipoWodTabela(TipoWod tipoWodTabela) {
        this.tipoWodTabela = tipoWodTabela;
    }

    public List<ScoreTreino> getScoresTreino() {
        if (scoresTreino == null) {
            scoresTreino = new ArrayList<ScoreTreino>();
        }
        return scoresTreino;
    }

    public void setScoresTreino(List<ScoreTreino> scoresTreino) {
        this.scoresTreino = scoresTreino;
    }

    public String getAlongamento() {
        if (alongamento == null) {
            alongamento = "";
        }
        return alongamento;
    }

    public void setAlongamento(String alongamento) {
        this.alongamento = alongamento;
    }

    public String getAquecimento() {
        if (aquecimento == null) {
            aquecimento = "";
        }
        return aquecimento;
    }

    public String getParteTecnicaSkill() {
        if (parteTecnicaskill == null) {
            parteTecnicaskill = "";
        }
        return parteTecnicaskill;
    }

    public void setAquecimento(String aquecimento) {
        this.aquecimento = aquecimento;
    }

    public void setParteTecnicaSkill(String parteTecnicaskill) {
        this.parteTecnicaskill = parteTecnicaskill;
    }

    public List<AparelhoWod> getAparelhos() {
        if (aparelhos == null) {
            aparelhos = new ArrayList<AparelhoWod>();
        }
        return aparelhos;
    }

    public void setAparelhos(List<AparelhoWod> aparelhos) {
        this.aparelhos = aparelhos;
    }

    public List<AtividadeWod> getAtividades() {
        if (atividades == null) {
            atividades = new ArrayList<AtividadeWod>();
        }
        return atividades;
    }

    public void setAtividades(List<AtividadeWod> atividades) {
        this.atividades = atividades;
    }


    public List<AtividadeWodJSON> getAtividadeWodJSON() {
        if (atividadeWodJSON == null){
            atividadeWodJSON = new ArrayList<AtividadeWodJSON>();
        }
        return atividadeWodJSON;
    }

    public void setAtividadeWodJSON(List<AtividadeWodJSON> atividadeWodJSON) {
        this.atividadeWodJSON = atividadeWodJSON;
    }

    public List<AparelhoWodJSON> getAparelhoWodJSON() {
        if (aparelhoWodJSON == null){
            aparelhoWodJSON = new ArrayList<AparelhoWodJSON>();
        }
        return aparelhoWodJSON;
    }

    public void setAparelhoWodJSON(List<AparelhoWodJSON> aparelhoWodJSON) {
        this.aparelhoWodJSON = aparelhoWodJSON;
    }

    public List<ComentarioWod> getComentarios() {
        if (comentarios == null) {
            comentarios = new ArrayList<ComentarioWod>();
        }
        return comentarios;
    }

    public void setComentarios(List<ComentarioWod> comentarios) {
        this.comentarios = comentarios;
    }

    public String getComplexEmom() {
        if (complexEmom == null) {
            complexEmom = "";
        }
        return complexEmom;
    }

    public void setComplexEmom(String complexEmom) {
        this.complexEmom = complexEmom;
    }

    public boolean isTemResultadoGeral() {
        return temResultadoGeral;
    }

    public void setTemResultadoGeral(boolean temResultadoGeral) {
        this.temResultadoGeral = temResultadoGeral;
    }

    public EventoCrossfit getEvento() {
        return evento;
    }

    public void setEvento(EventoCrossfit evento) {
        this.evento = evento;
    }

    public boolean isContratoCrossfit() {
        return contratoCrossfit;
    }

    public void setContratoCrossfit(boolean contratoCrosfit) {
        this.contratoCrossfit = contratoCrosfit;
    }

    public Boolean getCompartilharRede() {
        if(compartilharRede == null){
            compartilharRede = Boolean.FALSE;
        }
        return compartilharRede;
    }

    public void setCompartilharRede(Boolean compartilharRede) {
        this.compartilharRede = compartilharRede;
    }

    public String getIdRede() {
        return idRede;
    }

    public void setIdRede(String idRede) {
        this.idRede = idRede;
    }

    public String getUsuarioLancouRede() {
        return usuarioLancouRede;
    }

    public void setUsuarioLancouRede(String usuarioLancouRede) {
        this.usuarioLancouRede = usuarioLancouRede;
    }

    public String getChaveLancouRede() {
        return chaveLancouRede;
    }

    public void setChaveLancouRede(String chaveLancouRede) {
        this.chaveLancouRede = chaveLancouRede;
    }

    public String getUnidadeLancou() {
        return unidadeLancou;
    }

    public void setUnidadeLancou(String unidadeLancou) {
        this.unidadeLancou = unidadeLancou;
    }


    public String getDescricaoParaLog(Wod v2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, v2));
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    public String getNivelWod() {
        return nivelWod;
    }

    public void setNivelWod(String nivelWod) {
        this.nivelWod = nivelWod;
    }
}
