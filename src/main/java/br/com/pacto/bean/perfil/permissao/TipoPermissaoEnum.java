/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.perfil.permissao;

import io.swagger.annotations.ApiModel;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Enumerador que define os tipos de permissões disponíveis para recursos do sistema. \n\n" +
        "<strong>Valores disponíveis</strong>\n" +
        "- CONSULTAR (Consultar)\n" +
        "- INCLUIR (Incluir)\n" +
        "- EDITAR (Editar)\n" +
        "- EXCLUIR (Excluir)\n" +
        "- TOTAL_EXCETO_EXCLUIR (Total, ex. exclusão)\n" +
        "- TOTAL (Total)")
public enum TipoPermissaoEnum {

    CONSULTAR(0, "Consultar"),
    INCLUIR(1, "Incluir"),
    EDITAR(2, "Editar"),
    EXCLUIR(3, "Excluir"),
    TOTAL_EXCETO_EXCLUIR(5, "Total, ex. exclusão"),
    TOTAL(4, "Total");
    private Integer id;
    private String nome;

    private TipoPermissaoEnum(Integer id, String nome) {
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public static TipoPermissaoEnum getFromId(int id){
        for(TipoPermissaoEnum tipo : values()){
            if(tipo.getId().intValue() == id){
                return tipo;
            }
        }
        return null;
    }

    public static TipoPermissaoEnum getFromOrdinal(int id){
        for(TipoPermissaoEnum tipo : values()){
            if(tipo.ordinal() == id){
                return tipo;
            }
        }
        return null;
    }

}
