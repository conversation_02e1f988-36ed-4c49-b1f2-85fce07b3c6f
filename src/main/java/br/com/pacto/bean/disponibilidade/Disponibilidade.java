package br.com.pacto.bean.disponibilidade;

import br.com.pacto.bean.aula.Ambiente;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.controller.json.disponibilidade.*;
import br.com.pacto.controller.json.turma.AmbienteDTO;
import br.com.pacto.objeto.Aplicacao;
import org.hibernate.annotations.LazyCollection;
import org.hibernate.annotations.LazyCollectionOption;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Entity
public class Disponibilidade implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nome;
    private String cor;
    private Integer intervaloDiasEntreAgendamentos;
    private Integer intervaloMinimoDiasCasoFalta;
    private Integer comportamento;
    @OneToMany(mappedBy = "disponibilidade", cascade = CascadeType.ALL, orphanRemoval = true, targetEntity = HorarioDisponibilidade.class)
    private List<HorarioDisponibilidade> horarios;
    @LazyCollection(LazyCollectionOption.FALSE)
    @OneToMany(mappedBy = "disponibilidade", cascade = CascadeType.ALL, orphanRemoval = true, targetEntity = ItemValidacaoDisponibilidade.class)
    private List<ItemValidacaoDisponibilidade> itensValidacao;
    private Integer tipoHorario;
    private Integer duracao;
    private Integer duracaoMinima;
    private Integer duracaoMaxima;
    private Integer tipoValidacao;
    private Date inicio;
    private Date fim;
    private String diasSemana;
    private String descricao;
    private String fotoKey;
    private String identificadorFotoKey;

    public DisponibilidadeDTO toDTO(Disponibilidade obj) {
        DisponibilidadeDTO dto = new DisponibilidadeDTO();
        dto.setCodigo(obj.getCodigo());
        dto.setNome(obj.getNome());
        dto.setCor(obj.getCor());
        dto.setIntervaloDiasEntreAgendamentos(obj.getIntervaloDiasEntreAgendamentos());
        dto.setIntervaloMinimoDiasCasoFalta(obj.getIntervaloMinimoDiasCasoFalta());

        dto.setHorarios(new ArrayList<>());
        obj.getHorarios().forEach(h -> {
            HorarioDisponibilidadeDTO hDD = new HorarioDisponibilidadeDTO();
            hDD.setCodigo(h.getCodigo());
            ProfessorResponseTO pR = new ProfessorResponseTO();
            pR.setId(h.getProfessor().getCodigo());
            pR.setCodigoColaborador(h.getProfessor().getCodigoColaborador());
            pR.setNome(h.getProfessor().getNome());
            hDD.setProfessor(pR);
            if (h.getAmbiente() != null) {
                AmbienteDTO aT = new AmbienteDTO();
                aT.setId(h.getAmbiente().getCodigo());
                aT.setNome(h.getAmbiente().getNome());
                hDD.setAmbiente(aT);
            }
            hDD.setDiaSemana(h.getDiaSemana());
            hDD.setHoraInicial(h.getHoraInicial());
            hDD.setHoraFinal(h.getHoraFinal());
            hDD.setPermieAgendarAppTreino(h.getPermieAgendarAppTreino());
            hDD.setApenasAlunosCarteira(h.getApenasAlunosCarteira());
            hDD.setAtivo(h.getAtivo());
            dto.getHorarios().add(hDD);
        });

        dto.setComportamento(obj.getComportamento());

        dto.setItensValidacao(new ArrayList<>());
        obj.getItensValidacao().forEach(i ->{
            ItemValidacaoDisponibilidadeDTO iVD = new ItemValidacaoDisponibilidadeDTO();
            iVD.setCodigo(i.getCodigo());
            iVD.setPlano(i.getPlano());
            iVD.setProduto(i.getProduto());
            iVD.setDescricao(i.getDescricao());
            dto.getItensValidacao().add(iVD);
        });

        dto.setTipoHorario(obj.getTipoHorario());
        dto.setDuracao(obj.getDuracao());
        dto.setDuracaoMinima(obj.getDuracaoMinima());
        dto.setDuracaoMaxima(obj.getDuracaoMaxima());
        dto.setTipoValidacao(obj.getTipoValidacao());
        dto.setDataInicial(obj.getInicio());
        dto.setDataFinal(obj.getFim());
        dto.setDiasSemana(obj.getDiasSemana());
        dto.setDescricao(obj.getDescricao());

        if (isNotBlank(obj.getFotoKey())) {
            dto.setUrlImagem(Aplicacao.getProp(Aplicacao.urlFotosNuvem) + "/" + obj.getFotoKey());
        }

        return dto;
    }

    public Disponibilidade toEntity(Disponibilidade entity, DisponibilidadeDTO dto) {
        Disponibilidade obj = new Disponibilidade();

        obj.setCodigo(dto.getCodigo());
        obj.setNome(dto.getNome());
        obj.setCor(dto.getCor());
        obj.setIntervaloDiasEntreAgendamentos(dto.getIntervaloDiasEntreAgendamentos());
        obj.setIntervaloMinimoDiasCasoFalta(dto.getIntervaloMinimoDiasCasoFalta());

        obj.setHorarios(new ArrayList<>());
        if (dto.getHorarios() != null) {
            dto.getHorarios().forEach(h -> {
                HorarioDisponibilidade hD = new HorarioDisponibilidade();
                hD.setCodigo(h.getCodigo());
                ProfessorSintetico pR = new ProfessorSintetico();
                pR.setCodigo(h.getProfessor().getId());
                hD.setProfessor(pR);
                if(h.getAmbiente() != null) {
                    Ambiente aT = new Ambiente();
                    aT.setCodigo(h.getAmbiente().getId());
                    hD.setAmbiente(aT);
                }
                hD.setDiaSemana(h.getDiaSemana());
                hD.setHoraInicial(h.getHoraInicial());
                hD.setHoraFinal(h.getHoraFinal());
                hD.setPermieAgendarAppTreino(h.getPermieAgendarAppTreino());
                hD.setApenasAlunosCarteira(h.getApenasAlunosCarteira());
                hD.setAtivo(h.getAtivo());
                hD.setDisponibilidade(obj);
                obj.getHorarios().add(hD);
            });

            Set<String> diasSemanaUnico = new HashSet<>();
            obj.getHorarios().forEach(h -> {
                diasSemanaUnico.add(h.getDiaSemana());
            });
            diasSemanaUnico.forEach(dia -> {
                obj.setDiasSemana((obj.getDiasSemana() != null ? obj.getDiasSemana() : "") + "," + dia);
            });
            obj.setDiasSemana(obj.getDiasSemana().replaceFirst(",", ""));
        }

        obj.setComportamento(dto.getComportamento());

        obj.setItensValidacao(new ArrayList<>());
        if (dto.getItensValidacao() != null) {
            dto.getItensValidacao().forEach(i -> {
                ItemValidacaoDisponibilidade iVD = new ItemValidacaoDisponibilidade();
                iVD.setCodigo(i.getCodigo());
                iVD.setPlano(i.getPlano());
                iVD.setProduto(i.getProduto());
                iVD.setDescricao(i.getDescricao());
                iVD.setDisponibilidade(obj);
                obj.getItensValidacao().add(iVD);
            });
        }

        obj.setTipoHorario(dto.getTipoHorario());
        obj.setDuracao(dto.getDuracao());
        obj.setDuracaoMinima(dto.getDuracaoMinima());
        obj.setDuracaoMaxima(dto.getDuracaoMaxima());
        obj.setTipoValidacao(dto.getTipoValidacao());
        obj.setInicio(dto.getDataInicial());
        obj.setFim(dto.getDataFinal());
        obj.setDescricao(dto.getDescricao());

        if (entity != null) {
            obj.setIdentificadorFotoKey(entity.getIdentificadorFotoKey());
            obj.setFotoKey(entity.getFotoKey());
        }

        return obj;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public Integer getIntervaloDiasEntreAgendamentos() {
        return intervaloDiasEntreAgendamentos;
    }

    public void setIntervaloDiasEntreAgendamentos(Integer intervaloDiasEntreAgendamentos) {
        this.intervaloDiasEntreAgendamentos = intervaloDiasEntreAgendamentos;
    }

    public Integer getIntervaloMinimoDiasCasoFalta() {
        return intervaloMinimoDiasCasoFalta;
    }

    public void setIntervaloMinimoDiasCasoFalta(Integer intervaloMinimoDiasCasoFalta) {
        this.intervaloMinimoDiasCasoFalta = intervaloMinimoDiasCasoFalta;
    }

    public Integer getComportamento() {
        return comportamento;
    }

    public void setComportamento(Integer comportamento) {
        this.comportamento = comportamento;
    }

    public List<ItemValidacaoDisponibilidade> getItensValidacao() {
        return itensValidacao;
    }

    public void setItensValidacao(List<ItemValidacaoDisponibilidade> itensValidacao) {
        this.itensValidacao = itensValidacao;
    }

    public Integer getTipoHorario() {
        return tipoHorario;
    }

    public void setTipoHorario(Integer tipoHorario) {
        this.tipoHorario = tipoHorario;
    }

    public Integer getTipoValidacao() {
        return tipoValidacao;
    }

    public void setTipoValidacao(Integer tipoValidacao) {
        this.tipoValidacao = tipoValidacao;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public String getDiasSemana() {
        return diasSemana;
    }

    public void setDiasSemana(String diasSemana) {
        this.diasSemana = diasSemana;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getDuracaoMinima() {
        return duracaoMinima;
    }

    public void setDuracaoMinima(Integer duracaoMinima) {
        this.duracaoMinima = duracaoMinima;
    }

    public Integer getDuracaoMaxima() {
        return duracaoMaxima;
    }

    public void setDuracaoMaxima(Integer duracaoMaxima) {
        this.duracaoMaxima = duracaoMaxima;
    }

    public List<HorarioDisponibilidade> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<HorarioDisponibilidade> horarios) {
        this.horarios = horarios;
    }

    public String getIdentificadorFotoKey() {
        return identificadorFotoKey;
    }

    public void setIdentificadorFotoKey(String identificadorFotoKey) {
        this.identificadorFotoKey = identificadorFotoKey;
    }
}
