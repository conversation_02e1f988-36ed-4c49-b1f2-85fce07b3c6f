/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.enumerador.agenda;

import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public enum StatusAgendamentoEnum {

    AGUARDANDO_CONFIRMACAO(0, "Aguardando confirmação", 1, "#E36700", IndicadorDashboardEnum.AG_CONFIRMACAO),
    CONFIRMADO(1, "Confirmado", 2, "#1F7740", IndicadorDashboardEnum.CONFIRMARAM),
    EXECUTADO(2, "Executado", 3, "#009BDC", IndicadorDashboardEnum.COMPARECERAM),
    CANCELADO(3, "Cancelado", 5, "#FF0000", IndicadorDashboardEnum.CANCELARAM),
    FALTOU(4, "Faltou", 4, "#E8B7E8", IndicadorDashboardEnum.REAGENDARAM),
    REAGENDADO(5, "Reagendado", 5, "#003366", IndicadorDashboardEnum.REAGENDARAM);
    private Integer id;
    private String descricao;
    private Integer ordem;
    private String cor;
    private IndicadorDashboardEnum indicador;

    private StatusAgendamentoEnum(Integer id, final String descricao, Integer ordem,
            final String cor, final IndicadorDashboardEnum indicador) {
        this.id = id;
        this.descricao = descricao;
        this.ordem = ordem;
        this.cor = cor;
        this.indicador = indicador;
    }

    public static StatusAgendamentoEnum getInstance(String codigo) {
        if (!StringUtils.isBlank(codigo)) {
            for (StatusAgendamentoEnum statusAgendamento : StatusAgendamentoEnum.values()) {
                if (codigo.equals(statusAgendamento)) {
                    return statusAgendamento;
                }
            }
        }
        return null;
    }

    public IndicadorDashboardEnum getIndicador() {
        return indicador;
    }

    public void setIndicador(IndicadorDashboardEnum indicador) {
        this.indicador = indicador;
    }
    
    public String getName(){
        return this.name();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public static StatusAgendamentoEnum getFromId(Integer id) {
        for (StatusAgendamentoEnum tipo : values()) {
            if (tipo.getId().equals(id)) {
                return tipo;
            }
        }
        return null;
    }
    
    public static StatusAgendamentoEnum getFromStr(String str) {
        //            {"cancelaram":12,"faltaram":6,"disponibilidade":62,"tipo":"Prescrição","confirmado":1,"executaram":27,"aguardandoConfirmacao":9},
        return str.equals("cancelaram") ? CANCELADO
                : str.equals("faltaram") ? FALTOU 
                : str.equals("executaram") ? EXECUTADO
                : str.equals("confirmado") ? CONFIRMADO
                : str.equals("aguardandoConfirmacao") ? AGUARDANDO_CONFIRMACAO
                : str.equals("reagendaram") ? REAGENDADO
                : null;
    }

    public String getDescricaoPequena() {
        if (descricao.length() > 15) {
            return (descricao.substring(0, 12) + "...");
        }
        return descricao;
    }
}
