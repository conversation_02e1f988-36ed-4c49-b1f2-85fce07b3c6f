package br.com.pacto.enumerador.cliente;

public enum TiposAcompanhamentoEnum {
    TREINO_WEB(1, "Treino Web"),
    APP(2, "App"),
    ORION(3, "Orion");

    private Integer codigo;
    private String descricao;

    public static TiposAcompanhamentoEnum getFromCodigo(Integer codigo){
        for(TiposAcompanhamentoEnum tipo : values()){
            if(tipo.getCodigo().equals(codigo)){
                return tipo;
            }
        }
        return null;
    }

    TiposAcompanhamentoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
