/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.to;

import br.com.pacto.bean.bi.IndicadorDashboardEnum;
import java.io.Serializable;
import java.util.EnumMap;

/**
 *
 * <AUTHOR>
 */
public class RankingProfessoresTO implements Serializable{
    
    private String nomeProfessor;
    private String urlFoto;
    private Integer codigoProfessor;
    private EnumMap<IndicadorDashboardEnum, Double> mapaValores;
    private Double valorFinal;

    public String getNomeProfessor() {
        return nomeProfessor;
    }

    public Double valorComPeso(IndicadorDashboardEnum i, Double peso){
        return mapaValores.get(i)*peso;
    }
    public Integer getCodigoProfessor() {
        return codigoProfessor;
    }

    public void setCodigoProfessor(Integer codigoProfessor) {
        this.codigoProfessor = codigoProfessor;
    }
    
    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public EnumMap<IndicadorDashboardEnum, Double> getMapaValores() {
        return mapaValores;
    }

    public void setMapaValores(EnumMap<IndicadorDashboardEnum, Double> mapaValores) {
        this.mapaValores = mapaValores;
    }

    public Double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = valorFinal;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }
}
