package br.com.pacto.controller.json.disponibilidade;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.controller.json.agendamento.FiltroDisponibilidadeDTO;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.DisponibilidadeService;
import br.com.pacto.swagger.respostas.disponibilidade.ExemploRespostaListaDisponibilidades;
import br.com.pacto.swagger.respostas.disponibilidade.ExemploRespostaDisponibilidade;
import br.com.pacto.swagger.respostas.disponibilidade.ExemploRespostaBoolean;
import br.com.pacto.swagger.respostas.disponibilidade.ExemploRespostaListItemValidacao;
import br.com.pacto.swagger.respostas.disponibilidade.ExemploRespostaRemoverDisponibilidade;
import br.com.pacto.swagger.respostas.disponibilidade.ExemploRespostaMigrarDados;
import springfox.documentation.annotations.ApiIgnore;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@Api(tags = "Agenda de Aulas")
@Controller
@RequestMapping("/psec/disponibilidade")
public class DisponibilidadeController extends SuperController {

    private DisponibilidadeService disponibilidadeService;

    @Autowired
    public DisponibilidadeController(DisponibilidadeService disponibilidadeService){
        Assert.notNull(disponibilidadeService, "O serviço de disponibilidade não foi injetado corretamente");
        this.disponibilidadeService = disponibilidadeService;
    }

    @ApiOperation(
            value = "Listar disponibilidades de horários",
            notes = "Lista todas as disponibilidades de horários cadastradas no sistema com suporte a filtros e paginação. " +
                    "Retorna informações detalhadas das disponibilidades incluindo horários, professores e validações.",
            tags = "Agenda de Aulas"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListaDisponibilidades.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarDisponibilidades(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para código ou nome da disponibilidade.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).\n" +
                    "- <strong>professoresId:</strong> Filtra por um ou mais professores (Deve ser informado como uma lista de códigos/IDs ex: [10, 15]).\n" +
                    "- <strong>tiposAgendamentoId:</strong> Filtra por um ou mais tipos de agendamento (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                    "- <strong>filtrosNew:</strong> Objeto com filtros avançados incluindo comportamento, tipo de validação, tipo de horário e dias da semana.",
                    defaultValue = "{\"quicksearchValue\":\"Musculação\", \"quicksearchFields\":[\"nome\"], \"professoresId\":[10], \"tiposAgendamentoId\":[1], \"filtrosNew\":{\"comportamentoFc\":[{\"id\":1}], \"tipoValidacaoFc\":{\"id\":1, \"nome\":\"Plano\"}}}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiParam(value = "ID da empresa para filtrar as disponibilidades", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroDisponibilidadeDTO filters = new FiltroDisponibilidadeDTO(filtros);
            return ResponseEntityFactory.ok(disponibilidadeService.findAllDisponibilidades(empresaId, paginadorDTO, filters), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar turmas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Buscar disponibilidade de horário por código",
            notes = "Busca uma disponibilidade específica através do seu código identificador. " +
                    "Retorna todas as informações da disponibilidade incluindo horários, professores e itens de validação.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaDisponibilidade.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/{codigo}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> findDisponibilidadeById(
            @ApiParam(value = "Código único identificador da disponibilidade", defaultValue = "123", required = true)
            @PathVariable(value = "codigo") Integer codigo) throws JSONException {
        try {
            return ResponseEntityFactory.ok(disponibilidadeService.findDisponibilidadeById(codigo));
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar disponibilidade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Salvar ou atualizar disponibilidade",
            notes = "Salva uma nova disponibilidade ou atualiza uma existente no sistema. " +
                    "Se o código for informado e maior que 0, realiza a atualização. Caso contrário, cria uma nova disponibilidade.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaDisponibilidade.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdateDisponibilidade(
            @ApiParam(value = "Dados da disponibilidade para salvar ou atualizar", required = true)
            @RequestBody DisponibilidadeDTO dto) throws JSONException {
        try {
            if(dto.getCodigo() != null && dto.getCodigo() > 0){
                return ResponseEntityFactory.ok(disponibilidadeService.updateDisponibilidade(dto));
            }
            return ResponseEntityFactory.ok(disponibilidadeService.insertDisponibilidade(dto));
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar persistir disponibilidade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Remover disponibilidade",
            notes = "Remove uma disponibilidade do sistema definindo uma data de fim para ela. " +
                    "A disponibilidade não é excluída fisicamente, mas marcada como finalizada.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaRemoverDisponibilidade.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/removerDisponibilidade", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerDisponibilidade(
            @ApiParam(value = "Dados da disponibilidade a ser removida", required = true)
            @RequestBody DisponibilidadeDTO dto) throws JSONException {
        try {
            return ResponseEntityFactory.ok(disponibilidadeService.removerDisponibilidade(dto));
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar persistir disponibilidade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Verificar existência de agendamento em horário de disponibilidade",
            notes = "Verifica se existe algum agendamento de aluno conflitante com os horários de disponibilidade informados. " +
                    "Utilizado para validar se um horário específico está disponível para agendamento.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/existeAgendamentoAlunoHorarioDisponibilidade", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> existeAgendamentoAlunoHorarioDisponibilidade(
            @ApiParam(value = "ID do horário a ser verificado", defaultValue = "123", required = true)
            @RequestParam(value = "idHorario") Integer idHorario,
            @ApiParam(value = "Lista de horários de disponibilidade para verificação", required = true)
            @RequestBody List<HorarioDisponibilidadeDTO> horarios) throws JSONException {
        try {
            return ResponseEntityFactory.ok(disponibilidadeService.existeAgendamentoAlunoHorarioDisponibilidade(idHorario, horarios));
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar validar agendamento disponibilidade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar itens de validação",
            notes = "Lista os itens de validação disponíveis para disponibilidades baseado no tipo informado. " +
                    "Tipo 1 retorna validações por plano, tipo 2 retorna validações por produto.",
            tags = "Agenda de Aulas"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "descricao,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListItemValidacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{tipo}/itensValidacao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> itensValidacao(
            @ApiParam(value = "Filtros de busca em formato JSON", defaultValue = "{}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiParam(value = "Tipo de validação: 1 para Plano, 2 para Produto", defaultValue = "1", required = true)
            @PathVariable Integer tipo,
            @ApiParam(value = "Tipos de produto para filtrar (apenas quando tipo = 2)", defaultValue = "1,2,3")
            @RequestParam(value = "tiposProduto", required = false) String tipos,
            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            if (tipo == 1) {
                return ResponseEntityFactory.ok(disponibilidadeService.findAllTipoValidacaoPlano(paginadorDTO, filtros), paginadorDTO);
            } else if (tipo == 2) {
                return ResponseEntityFactory.ok(disponibilidadeService.findAllTipoValidacaoProduto(paginadorDTO, filtros, tipos), paginadorDTO);
            } else {
                return ResponseEntityFactory.ok(new ArrayList<>(), paginadorDTO);
            }
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar itens de validacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Migrar dados de agendamento",
            notes = "Migra dados de agendamentos do modelo antigo para o novo modelo de disponibilidade. " +
                    "Processo de migração que converte tipos de evento em disponibilidades.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaMigrarDados.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/migrarDados", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> migrarDados() throws JSONException {
        try {
            return ResponseEntityFactory.ok(disponibilidadeService.migrarDadosAgendamentoModeloNovoDisponibilidade());
        } catch (ServiceException e) {
            Logger.getLogger(DisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar itens de validacao", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
