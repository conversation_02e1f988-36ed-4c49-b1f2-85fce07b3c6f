package br.com.pacto.controller.json.disponibilidade;

import br.com.pacto.bean.programa.ProfessorResponseTO;

import java.io.Serializable;

public class ProfessorDisponibilidadeDTO implements Serializable {

    private Integer codigo;
    private Professor<PERSON><PERSON>po<PERSON><PERSON> professor;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ProfessorResponseTO getProfessor() {
        return professor;
    }

    public void set<PERSON>rofessor(ProfessorResponseTO professor) {
        this.professor = professor;
    }
}
