package br.com.pacto.controller.json.agendamento;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do professor que irá substituir uma aula")
public class SubstituirProfessorDTO {

    @ApiModelProperty(value = "Código identificador do professor que irá substituir uma aula", example = "10")
    private Integer professorId;
    @ApiModelProperty(value = "Justificativa da substituição da aula", example = "Professor original da aula está de atestado médico")
    private String justificativa;

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }
}
