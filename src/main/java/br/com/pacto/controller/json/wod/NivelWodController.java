package br.com.pacto.controller.json.wod;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.wod.FiltroNivelWodJSON;
import br.com.pacto.bean.wod.NivelWodTO;
import br.com.pacto.controller.json.benchmark.BenchmarkController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.nivelwod.NivelWodService;
import br.com.pacto.swagger.respostas.nivelWod.ExemploRespostaListNivelWodResponseTOPaginacao;
import br.com.pacto.swagger.respostas.nivelWod.ExemploRespostaNivelWodResponseTO;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Denis Silva on 28/05/2024.
 */
@Controller
@RequestMapping("/psec/nivel-wod")
public class NivelWodController {

    private final NivelWodService nivelWodService;

    @Autowired
    public NivelWodController(NivelWodService nivelWodService) {
        Assert.notNull(nivelWodService, "O serviço de tipos de wod não foi injetado corretamente");
        this.nivelWodService = nivelWodService;
    }


    @ApiOperation(value = "Consultar níveis WOD (Workout of the Day)",
            notes = "Lista os níveis WOD (Workout of the Day) cadastrados no sistema com filtros e paginação",
            tags = "Níveis de WOD")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de níveis WOD obtida com sucesso", response = ExemploRespostaListNivelWodResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarNivelsWod(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do nível WOD.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).",
                    defaultValue = "{\"quicksearchValue\":\"Iniciante\", \"quicksearchFields\":[\"nome\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroNivelWodJSON filtroNivelWodJSON = new FiltroNivelWodJSON(filtros);

            return ResponseEntityFactory.ok(nivelWodService.listarNiveisWod(filtroNivelWodJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(BenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar Níveis wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar nível WOD (Workout of the Day) por ID",
            notes = "Busca um nível WOD específico pelo seu código identificador",
            tags = "Níveis de WOD")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Nível WOD encontrado com sucesso", response = ExemploRespostaNivelWodResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarTiposWod(@ApiParam(value = "Código identificador único do nível WOD", required = true, defaultValue = "1")
                                                              @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(nivelWodService.buscarNivelWod(id));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar o Nível wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(value = "Cadastrar novo nível WOD",
            notes = "Cadastra um novo nível WOD no sistema",
            tags = "Níveis de WOD")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Nível WOD cadastrado com sucesso", response = ExemploRespostaNivelWodResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarNivelWod(@RequestBody NivelWodTO nivelWodTO) {
        try {
            return ResponseEntityFactory.ok(nivelWodService.cadastrarNivelWod(nivelWodTO));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar Nível do wod", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Reverter nível WOD (Workout of the Day) para padrão",
            notes = "Reverte um nível WOD personalizado para seu estado padrão original",
            tags = "Níveis de WOD")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Nível WOD revertido com sucesso", response = ExemploRespostaNivelWodResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "reverter/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> reverterNivelWod(@ApiParam(value = "Código identificador único do nível WOD", required = true, defaultValue = "1")
                                                                @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(nivelWodService.reverterNivelWod(id));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar Nível do wod", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Alterar nível WOD (Workout of the Day)",
            notes = "Altera os dados de um nível WOD (Workout of the Day)  existente",
            tags = "Níveis de WOD")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Nível WOD alterado com sucesso", response = ExemploRespostaNivelWodResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> auterarNivelWod(@RequestBody NivelWodTO nivelWodTO) {
        try {
            return ResponseEntityFactory.ok(nivelWodService.editarNivelWod(nivelWodTO));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar Nível do wod", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Excluir nível WOD (Workout of the Day) ",
            notes = "Exclui um nível WOD do sistema (apenas níveis personalizados podem ser excluídos)",
            tags = "Níveis de WOD")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Nível WOD excluído com sucesso", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> deleteNivelWod(@ApiParam(value = "Código identificador único do nível WOD", required = true, defaultValue = "1")
                                                              @PathVariable("id") Integer id) {
        try {
            nivelWodService.excluirNivelWod(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar deletar Nível do wod", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
}
