package br.com.pacto.controller.json.base;

import br.com.pacto.base.oamd.OAMD;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.service.intf.empresa.EmpresaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;

@Controller
@RequestMapping("/")
public class HealthJSONControle extends SuperControle {

    @Autowired
    private EmpresaService es;

    @RequestMapping(value = "/health", method = {RequestMethod.GET})
    public @ResponseBody
    ModelMap health(HttpServletResponse response) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute("health", OAMD.health());
            mm.addAttribute("version", Aplicacao.getProp(Aplicacao.version));
        } catch (Exception e) {
            response.setStatus(500);
            mm.addAttribute(STATUS_ERRO, "Error connecting to the OAMD: "+e.getMessage());
            e.printStackTrace();
        }
        return mm;
    }

    @RequestMapping(value = "/health/{chaves}", method = {RequestMethod.GET})
    public @ResponseBody
    ModelMap health(HttpServletResponse response, @PathVariable String chaves) {
        ModelMap mm = new ModelMap();
        HashMap<String, String> checks = new HashMap<>();
        try {
            String[] arrayChaves = chaves.split(",");
            for(String chave: arrayChaves){
                checks.put(chave, es.health(chave));
            }
            mm.addAttribute("health", checks);
            mm.addAttribute("version", Aplicacao.getProp(Aplicacao.version));
        } catch (Exception e) {
            response.setStatus(500);
            mm.addAttribute(STATUS_ERRO, "Error connecting to the "+chaves+" keys: "+e.getMessage());
            e.printStackTrace();
        }
        return mm;
    }
}
