/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.ficha;

import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.MetodoExecucaoEnum;
import br.com.pacto.bean.ficha.CategoriaFicha;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.nivel.Nivel;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.programa.ObjetivoPredefinido;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.TipoExecucaoEnum;
import br.com.pacto.bean.sincronizacao.TipoClassSincronizarEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.atividade.TemaAtividade;
import br.com.pacto.controller.json.atividade.read.SerieJSON;
import br.com.pacto.controller.json.atividade.write.AtividadeFichaWriteJSON;
import br.com.pacto.controller.json.atividade.write.SerieWriteJSON;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.ficha.read.TipoExecucaoJSON;
import br.com.pacto.controller.json.ficha.write.FichaWriteAppJSON;
import br.com.pacto.controller.json.ficha.write.FichaWriteJSON;
import br.com.pacto.controller.json.nivel.NivelWriteJSON;
import br.com.pacto.controller.json.objetivos.ObjetivosPredfinidosAppJSON;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoFichaJSON;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.ficha.CategoriaFichaService;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.ficha.SerieService;
import br.com.pacto.service.intf.nivel.NivelService;
import br.com.pacto.service.intf.programa.ObjetivoPredefinidoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.sincronizacao.SincronizacaoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.swagger.respostas.ficha.ExemploRespostaAlterarSerie;
import com.google.common.collect.ImmutableList;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/ficha")
public class FichaJSONControle extends SuperControle {

    @Autowired
    private FichaService fs;
    @Autowired
    private SerieService srs;
    @Autowired
    private CategoriaFichaService cf;
    @Autowired
    private SincronizacaoService ss;
    @Autowired
    private UsuarioService us;
    @Autowired
    private NivelService ns;
    @Autowired
    private ObjetivoPredefinidoService ops;
    @Autowired
    private ProgramaTreinoService ps;

    @RequestMapping(value = "{ctx}/todas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap todas(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            List<FichaWriteJSON> arrJSON = new ArrayList<FichaWriteJSON>();
            List<Ficha> fichas = fs.obterFichasPredefinidas(ctx, true);
            for (Ficha f : fichas) {
                if (!f.getAtividades().isEmpty()) {
                    arrJSON.add(new FichaWriteJSON(f));
                }
            }
            mm.addAttribute(RETURN, arrJSON);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/consultarFichasPreDefinidas", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarFichasPreDefinidasApp(@PathVariable String ctx,
                                            @RequestParam(required = false) Integer index,
                                            @RequestParam(value = "max", required = false) Integer maxResult,
                                            @RequestParam(value = "nomeFicha", required = false) String nomeFicha){

        ModelMap mm = new ModelMap();
        try {
            List<FichaWriteAppJSON> arrJSON = new ArrayList<>();
            List<Ficha> fichas;
            fichas = fs.obterFichasPredefinidasApp(ctx, true, index, maxResult, nomeFicha);
            final String urlBase = TemaAtividade.getURLBase(ctx);
            for (Ficha f : fichas) {
                if (!f.getAtividades().isEmpty()) {
                    f.setUsarComoPredefinida(true);
                    f.getAtividades().sort(Comparator.comparing(AtividadeFicha::getOrdem));
                    List<AtividadeFicha>  atividadeFichas = f.getAtividades();
                    for(AtividadeFicha atividadeFicha: atividadeFichas)
                    {
                        atividadeFicha.getSeries().sort(Comparator.comparing(serie -> {
                            if (null != serie.getOrdem()){
                                return serie.getOrdem();
                            } else {
                                return 0;
                            }
                        }));
                        if((atividadeFicha.getTriSet() || atividadeFicha.getBiSet()))
                        {
                            if(!UteisValidacao.emptyString(atividadeFicha.getSetId())) {
                                String[] associadas = atividadeFicha.getSetId().split("\\|");
                                StringBuilder novoSetid = new StringBuilder();
                                for (String associada : associadas) {
                                    List<AtividadeFicha> ativs = f.getAtividades();
                                    for (AtividadeFicha ativ : ativs) {
                                        if ( null != ativ.getCodigo() && 0 == associada.compareTo(ativ.getCodigo().toString())) {
                                            novoSetid.append(ativ.getOrdem().toString()).append("|");
                                        }
                                    }
                                }
                                atividadeFicha.setSetId(novoSetid.toString());
                            }else{
                                atividadeFicha.setMetodoExecucao(null);
                            }
                        }
                    }
                    arrJSON.add(new FichaWriteAppJSON(f, urlBase));
                }
            }
            mm.addAttribute("sucesso", arrJSON);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/categorias", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap categorias(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, cf.obterTodos(ctx));
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/categorias/sync", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap categorias_sync(@PathVariable String ctx, @RequestParam String dataHora) {
        ModelMap mm = new ModelMap();
        try {
            Date dataBase = Calendario.getDate(Calendario.MASC_DATAHORA, dataHora);
            List<CategoriaFicha> categorias = ss.obterLista(ctx,
                    TipoClassSincronizarEnum.CategoriaFicha, dataBase);
            mm.addAttribute(RETURN, categorias);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/tiposExecucao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap metodosExecucao(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            List<TipoExecucaoEnum> tipos = Arrays.asList(TipoExecucaoEnum.values());
            List<TipoExecucaoJSON> retorno = new ArrayList<TipoExecucaoJSON>();
            for (TipoExecucaoEnum t : tipos) {
                retorno.add(new TipoExecucaoJSON(t.getId(), t.getDescricao()));
            }
            mm.addAttribute(RETURN, retorno);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/sync", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap sync(@PathVariable String ctx, @RequestParam String dataHora) {
        ModelMap mm = new ModelMap();
        List<FichaWriteJSON> arrJSON = new ArrayList();
        try {
            //super.init(ctx, token);
            Date dataBase = Calendario.getDate(Calendario.MASC_DATAHORA, dataHora);
            List<Ficha> fichas = ss.obterLista(ctx,
                    TipoClassSincronizarEnum.Ficha, dataBase);
            for (Ficha f : fichas) {
                FichaWriteJSON fwJSON = new FichaWriteJSON(f);
                arrJSON.add(fwJSON);
            }
            mm.addAttribute(RETURN, arrJSON);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Alterar série de exercício",
                  notes = "Permite alterar os dados de uma série específica de exercício, incluindo carga, repetições, " +
                         "tempo de descanso e outras configurações. Requer permissão de edição de programa de treino. " +
                         "Retorna confirmação da alteração realizada.",
                  tags = "Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Série alterada com sucesso", response = ExemploRespostaAlterarSerie.class)
    })
    @RequestMapping(value = "{ctx}/serie/alterar", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap alterarSerie(@ApiParam(value = "Contexto da empresa", required = true, example = "academia123")
                         @PathVariable String ctx,
                         @ApiParam(value = "Dados da série a ser alterada", required = true)
                         @RequestBody final SerieJSON serieJSON) {
        ModelMap mm = new ModelMap();
        try {
            srs.alterarSerie(ctx, serieJSON);
            mm.addAttribute(RETURN, "Serie alterada com sucesso.");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            ex.printStackTrace();
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/persistir", method = RequestMethod.POST)
    public @ResponseBody

    ModelMap persistir(@PathVariable String ctx,
                       @RequestBody final FichaWriteJSON fichaGravar,
                       @RequestParam (required = false) final Integer codUsuario) {
        ModelMap mm = new ModelMap();
        try {
            if (fichaGravar.getUsarComoPredefinida()) {
                for (AtividadeFichaWriteJSON atividadeFichaWriteJSON : fichaGravar.getAtividadesFicha()) {
                    atividadeFichaWriteJSON.setCodigo(null);
                    for (SerieWriteJSON serieWriteJSON : atividadeFichaWriteJSON.getSeries()) {
                        serieWriteJSON.setCodigo(null);
                        serieWriteJSON.setAtividadeFicha(null);
                    }
                }
            }
            Usuario usuario;
            if (UteisValidacao.emptyNumber(codUsuario)){
                usuario = us.obterPorAtributo(ctx, "username", fichaGravar.getUsername());
            } else {
                usuario = us.obterPorId(ctx,codUsuario);
            }
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                Uteis.logar(null, "FichaJSONControle.persistir1 -> " + fichaGravar.toJSON());
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EDITAR);
                Ficha f = fs.prepararPersistenciaJSON(ctx, fichaGravar);
                if (fichaGravar.getCodigo() != null) {
                    f = fs.efetuarExclusoesJSON(ctx, fichaGravar, f);
                }
                f = fs.alterar(ctx, f, false);                
                f = fs.salvarAtividadesDaFicha(ctx, f);
                if (f.getCodigo() != null) {
                    f = fs.obterPorId(ctx, f.getCodigo());
                }
                FichaWriteJSON json = new FichaWriteJSON(f);
                json.setUsername(fichaGravar.getUsername());
                mm.addAttribute(STATUS_SUCESSO, json);
                Uteis.logar(null, "FichaJSONControle.persistir2 -> " + json.toJSON());
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ValidacaoException ve) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ve));
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ve);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            ex.printStackTrace();
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/get", method = RequestMethod.POST)
    public ModelMap getFicha(@PathVariable String contexto,
                             @RequestParam (required = false) final String username,
                             @RequestParam final Integer codigoFicha,
                             @RequestParam (required = false) final Integer codUsuario) {

        ModelMap modelMap = new ModelMap();
        Ficha ficha;
        Usuario usuario;
        boolean usarUsername = UteisValidacao.emptyNumber(codUsuario);
        try {
            if (usarUsername){
                usuario = us.obterPorAtributo(contexto, "username", username);
            } else {
                usuario = us.obterPorId(contexto,codUsuario);
            }
            if (usuarioNaoProfessor( usuario, modelMap) || codigoFicha == null || codigoFicha <= 0) {
                return modelMap;
            }
            ficha = fs.obterPorId(contexto, codigoFicha);
        } catch (ServiceException ex) {
            modelMap.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            return modelMap;
        }

        if (ficha == null) {
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, MSG_DADOS_NAOENCONTRADOS);
            return modelMap.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(MSG_DADOS_NAOENCONTRADOS));
        }

        FichaWriteJSON json = new FichaWriteJSON(ficha);
        if (usarUsername){
            json.setUsername(username);
        } else {
            json.setUsername(usuario.getUserName());
        }
        return modelMap.addAttribute(STATUS_SUCESSO, json);
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/app/getFicha", method = RequestMethod.GET)
    public ModelMap getFichaApp(@PathVariable String ctx,
                                @RequestParam (required = false) final String userName,
                                @RequestParam final Integer codigoFicha,
                                @RequestParam (required = false) final Integer codUsuario) {

        ModelMap modelMap = new ModelMap();
        FichaWriteJSON ficha;
        Usuario usuario;
        boolean usarUsername = UteisValidacao.emptyNumber(codUsuario);
        try {
            if (usarUsername){
                usuario = us.obterPorAtributo(ctx, "username", userName);
            } else {
                usuario = us.obterPorId(ctx,codUsuario);
            }
            if (usuarioNaoProfessor(usuario, modelMap)) {
                modelMap.addAttribute(STATUS_ERRO, "Usuário inválido!");
                return modelMap;
            }
            if(codigoFicha == null || codigoFicha <= 0){
                modelMap.addAttribute(STATUS_ERRO, "Ficha inválida!");
                return  modelMap;
            }
            ficha = fs.obterPorIdApp(ctx, codigoFicha);
            ficha = setidIdParaOrdem(ficha);
        } catch (ServiceException ex) {
            modelMap.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
            return modelMap;
        }

        if (ficha == null) {
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, MSG_DADOS_NAOENCONTRADOS);
            return modelMap.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(MSG_DADOS_NAOENCONTRADOS));
        }
        if (usarUsername){
            ficha.setUsername(userName);
        } else {
            ficha.setUsername(usuario.getUserName());
        }
        return modelMap.addAttribute(STATUS_SUCESSO, ficha);
    }

    @RequestMapping(value = "{ctx}/excluir", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap excluir(@PathVariable String ctx,
                     @RequestParam (required = false) final String username,
                     @RequestParam final Integer codigoFicha,
                     @RequestParam (required = false) final Integer codUsuario) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario;
            if (UteisValidacao.emptyNumber(codUsuario)){
                usuario = us.obterPorAtributo(ctx, "username", username);
            } else {
                usuario = us.obterPorId(ctx,codUsuario);
            }
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EDITAR);
                if (codigoFicha != null && codigoFicha > 0) {
                    Ficha f = fs.obterPorId(ctx, codigoFicha);
                    if (f != null) {
                        fs.excluir(ctx, f);
                        mm.addAttribute(STATUS_SUCESSO, MSG_DADOS_EXCLUIDOS);
                    } else {
                        throw new ServiceException(MSG_DADOS_NAOENCONTRADOS);
                    }
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    private boolean usuarioNaoProfessor(Usuario usuario, ModelMap modelMap) {
        if (usuario == null || usuario.getProfessor() == null || usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)) {
            String mensagemErro = getViewUtils().getMensagem("mobile.usuarioinvalido");
            modelMap.addAttribute(STATUS_ERRO, mensagemErro);
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, mensagemErro);
            return true;
        }
        return false;
    }

    @RequestMapping(value = "{ctx}/app/consultarConfiguracaoFichaApp", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarConfiguracaoFichaApp(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        ModelMap sucesso = new ModelMap();
        // Obter tipos de execução
        try {
            List<TipoExecucaoEnum> tipos = Arrays.asList(TipoExecucaoEnum.values());
            List<TipoExecucaoJSON> retorno = new ArrayList<TipoExecucaoJSON>();
            for (TipoExecucaoEnum t : tipos) {
                retorno.add(new TipoExecucaoJSON(t.getId(), t.getDescricao()));
            }
            mm.addAttribute("tiposDeExecucao", retorno);

            // Obter categorias
            mm.addAttribute("categorias", cf.obterTodos(ctx));

            // Obter niveis
            List<Nivel> niveis = ns.obterTodos(ctx);
            List<NivelWriteJSON> nivelWriteJSONS = new ArrayList<NivelWriteJSON>();
            for(Nivel nivel : niveis)
            {
                nivelWriteJSONS.add(new NivelWriteJSON(nivel));
            }
            mm.addAttribute("niveis", nivelWriteJSONS);

          // Obter objetivos
            List<ObjetivoPredefinido> objetivos = ops.obterTodos(ctx);
            List<ObjetivosPredfinidosAppJSON> objetivoJSONS = new ArrayList<ObjetivosPredfinidosAppJSON>();
            for(ObjetivoPredefinido objetivo : objetivos)
            {
                objetivoJSONS.add(new ObjetivosPredfinidosAppJSON(objetivo));
            }
            mm.addAttribute("objetivos", objetivoJSONS);

            sucesso.addAttribute("sucesso", mm);
        }
         catch(ServiceException ex) {
            sucesso.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return sucesso;
    }

    @RequestMapping(value = "{ctx}/app/persistirFicha", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap persistirFichaApp(@PathVariable String ctx,
                               @RequestBody final FichaWriteJSON fichaGravar,
                               @RequestParam(required=false) final Integer codigoPrograma,
                               @RequestParam (required = false) final Integer codUsuario) {
        ModelMap mm = new ModelMap();
        String step = "inicio";
        Boolean usarPredefinida = fichaGravar.getUsarComoPredefinida();
        try {
            Usuario usuario;
            if (UteisValidacao.emptyNumber(codUsuario)){
                usuario = us.obterPorAtributo(ctx, "username", fichaGravar.getUsername());
            } else {
                usuario = us.obterPorId(ctx,codUsuario);
            }
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                Uteis.logar(null, "FichaJSONControle.persistir1 -> " + fichaGravar.toJSON());
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EDITAR);
                fichaGravar.setUsarComoPredefinida(false);

                List<String> setids = obterListaSetids(fichaGravar.getAtividadesFicha());

                step = "montar-programa";
                if(UteisValidacao.emptyList(fichaGravar.getProgramasFicha()))
                {
                    ProgramaTreinoFichaJSON ptf = new ProgramaTreinoFichaJSON();
                    ptf.setPrograma(codigoPrograma);
                    fichaGravar.getProgramasFicha().add(ptf);
                }
                step = "prepararPersistenciaAppJSON";
                Ficha f = fs.prepararPersistenciaAppJSON(ctx, fichaGravar);
                step = "efetuarExclusoesJSON";
                if (fichaGravar.getCodigo() != null) {
                    f = fs.efetuarExclusoesJSON(ctx, fichaGravar, f);
                }
                if(f.getCodigo() == null || f.getCodigo() <= 0) {
                    step = "inserir";
                    f = fs.cadastrarApp(ctx, f);
                    step = "inserirAtividadeFicha";
                    List<AtividadeFicha> atividadeFichas = f.getAtividades();
                    for (AtividadeFicha atividadeFicha : atividadeFichas) {
                        fs.inserirAtividadeFicha(ctx, atividadeFicha);
                    }
                }else{
                    step = "alterar";
                    f = fs.alterar(ctx, f, true);
                }
                f = setidOrdemParaId(f, setids, ctx);
                if(usarPredefinida)
                {
                    step = "tornarPreDefinidaApp";
                    Ficha fichapredefinida = fs.tornarPreDefinidaApp(ctx, f);
                }
                FichaWriteJSON json = new FichaWriteJSON(f);
                json.setAtividadesFicha(Ordenacao.ordenarLista(json.getAtividadesFicha(), "ordem"));
                json = setidIdParaOrdem(json);
                json.setUsername(fichaGravar.getUsername());
                mm.addAttribute(STATUS_SUCESSO, json);
                Uteis.logar(null, "FichaJSONControle.persistir2 -> " + json.toJSON());
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            ex.printStackTrace();
//            TelegramService.enviarErro(ctx, ex, "*persistirFicha da MASTER *step:" +
//                    step +
//                    " - codigoPrograma:" + codigoPrograma + "- fichaGravar:"+fichaGravar.toJSON() + "usarPredefinida: " + usarPredefinida);

            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/persistirFichaPredefinida", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap persistirFichaPredefinidaApp(@PathVariable String ctx,
                                          @RequestBody final FichaWriteJSON fichaGravar,
                                          @RequestParam (required = false) final Integer codUsuario) {
        ModelMap mm = new ModelMap();
        String step = "inicio";
        try {
            step = "logar usuário";
            Usuario usuario;
            if (UteisValidacao.emptyNumber(codUsuario)){
                usuario = us.obterPorAtributo(ctx, "username", fichaGravar.getUsername());
            } else {
                usuario = us.obterPorId(ctx,codUsuario);
            }
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                Uteis.logar(null, "FichaJSONControle.persistir1 -> " + fichaGravar.toJSON());
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EDITAR);
                List<String> setids =  obterListaSetids(fichaGravar.getAtividadesFicha());
                step = "prepararPersistenciaAppJSON";
                Ficha f = fs.prepararPersistenciaAppJSON(ctx, fichaGravar);
                f.setUsarComoPredefinida(true);
                step = "efetuarExclusoesJSON";
                if (fichaGravar.getCodigo() != null) {
                    f = fs.efetuarExclusoesJSON(ctx, fichaGravar, f);
                }
                if(f.getCodigo() == null || f.getCodigo() <= 0) {
                    step = "inserir";
                    f = fs.cadastrarApp(ctx, f);
                    step = "inserirAtividadeFicha";
                    List<AtividadeFicha> atividadeFichas = f.getAtividades();
                    for (AtividadeFicha atividadeFicha : atividadeFichas) {
                        fs.inserirAtividadeFicha(ctx, atividadeFicha);
                    }
                }else{
                    step = "alterar";
                    f = fs.alterar(ctx, f, true);
                }
                f = setidOrdemParaId(f, setids, ctx);

                FichaWriteJSON json = new FichaWriteJSON(f);
                json = setidIdParaOrdem(json);
                json.setUsername(fichaGravar.getUsername());
                mm.addAttribute(STATUS_SUCESSO, json);
                Uteis.logar(null, "FichaJSONControle.persistir2 -> " + json.toJSON());
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
//            TelegramService.enviarErro(ctx, ex, "*persistirFichaPredefinida*MASTER* step: " +
//                    step +
//                    " - fichaGravar:"+fichaGravar.toJSON());
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            ex.printStackTrace();
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/deletarFicha", method = RequestMethod.DELETE)
    public @ResponseBody
    ModelMap excluirFichaApp(@PathVariable String ctx,
                             @RequestParam (required = false) final String username,
                             @RequestParam final Integer codigoDaFicha,
                             @RequestParam (required = false) final Integer codUsuario) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario;
            if (UteisValidacao.emptyNumber(codUsuario)){
                usuario = us.obterPorAtributo(ctx, "username", username);
            } else {
                usuario = us.obterPorId(ctx,codUsuario);
            }
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EDITAR);
                if (codigoDaFicha != null && codigoDaFicha > 0) {
                    Ficha f = fs.obterPorId(ctx, codigoDaFicha);
                    if (f != null) {
                        ProgramaTreino programa = fs.obterProgramaPorFicha(ctx, f.getCodigo());
                        fs.excluir(ctx, f);
                        if(programa != null)
                        {
                            ps.atualizarNrTreinosRealizados(ctx, programa.getCodigo());
                            ps.atualizarVersaoPrograma(ctx, programa);
                        }
                        mm.addAttribute(STATUS_SUCESSO, MSG_DADOS_EXCLUIDOS);
                    } else {
                        throw new ServiceException(MSG_DADOS_NAOENCONTRADOS);
                    }
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(FichaJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    List<String> obterListaSetids(List<AtividadeFichaWriteJSON> atividadeFichaWriteJSONS){
        List<String> setids = new ArrayList<>();
        for(AtividadeFichaWriteJSON atividadeFichaWriteJSON: atividadeFichaWriteJSONS) {
            Integer metodoExecucao = atividadeFichaWriteJSON.getMetodoExecucao();
            if (metodoExecucao != null){
                if (metodoExecucao == 5 || metodoExecucao == 6) {
                    setids.add(atividadeFichaWriteJSON.getSetid());
                    atividadeFichaWriteJSON.setMetodoExecucao(null);
                }
            }
        }
        return setids;
    }

    Ficha setidOrdemParaId(Ficha f, List<String> setids, String ctx) throws ServiceException {
        if(!UteisValidacao.emptyList(setids)){
            for(String setid: setids) {
                if (!UteisValidacao.emptyString(setid)) {
                    String[] associadas = setid.split("\\|");
                    List<Integer> codigosAtividadeFicha = new ArrayList<>();
                    String novoSetid = "";
                    List<AtividadeFicha> atividadeFichas = ImmutableList.copyOf(f.getAtividades());
                    for (String associada : associadas) {
                        Integer ordem = Integer.parseInt(associada);
                        for (AtividadeFicha atividadeFicha : atividadeFichas) {
                            if (atividadeFicha.getOrdem() == ordem) {
                                novoSetid += atividadeFicha.getCodigo().toString() + "|";
                                codigosAtividadeFicha.add(atividadeFicha.getCodigo());
                            }
                        }
                    }
                    Integer novoMetodoExecucao = codigosAtividadeFicha.size() == 2 ? 5 : 6;
                    for (AtividadeFicha atividadeFicha : atividadeFichas) {
                        for (Integer codigo : codigosAtividadeFicha) {
                            if (codigo == atividadeFicha.getCodigo()) {
                                atividadeFicha.setMetodoExecucao(MetodoExecucaoEnum.getFromId(novoMetodoExecucao));
                                atividadeFicha.setSetId(novoSetid);
                                fs.alterarAtividadeFicha(ctx, atividadeFicha);
                            }
                        }
                    }
                }
            }
        }
        return f;
    }

    FichaWriteJSON setidIdParaOrdem(FichaWriteJSON json)
    {
        List<AtividadeFichaWriteJSON>  atividadeFichas = json.getAtividadesFicha();
        for(AtividadeFichaWriteJSON atividadeFicha: atividadeFichas)
        {
            Integer metodoExecucao = atividadeFicha.getMetodoExecucao();
            if(!UteisValidacao.emptyNumber(metodoExecucao)){
                if(metodoExecucao == 5 || metodoExecucao == 6){
                    if(!UteisValidacao.emptyString(atividadeFicha.getSetid())){
                        String[] associadas = atividadeFicha.getSetid().split("\\|");
                        String novoSetid = "";
                        for (String associada : associadas) {
                            List<AtividadeFichaWriteJSON> ativs = json.getAtividadesFicha();
                            for (AtividadeFichaWriteJSON ativ : ativs) {
                                if (0 == associada.compareTo(ativ.getCodigo().toString())) {
                                    novoSetid += ativ.getOrdem().toString() + "|";
                                }
                            }
                        }
                        atividadeFicha.setSetid(novoSetid);
                    }
                    else{
                        atividadeFicha.setMetodoExecucao(null);
                    }
                }
            }
        }
        return json;
    }
}
