package br.com.pacto.controller.json.base;

import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para configurações de Inteligência Artificial do sistema")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracoesIaDTO {
    @ApiModelProperty(value = "Define se deve permitir criação de treino automatizado por IA", example = "true")
    private String permitir_criar_treino_automatizado_ia;

    @ApiModelProperty(value = "Tempo em horas para aprovação automática do treino", example = "24")
    private String tempo_aprovacao_automatica;

    @ApiModelProperty(value = "Tempo máximo em horas para revisão do treino", example = "72")
    private String tempo_maximo_revisao;

    @ApiModelProperty(value = "Define se deve habilitar obrigatoriedade de aprovação do professor", example = "true")
    private String habilitar_obrigatoriedade_aprovacao_professor;

    @ApiModelProperty(value = "Quantidade de fichas necessárias para habilitar treino por IA", example = "3")
    private String quantidade_fichas_habilitar_treino_ia;

    @ApiModelProperty(value = "Código do nível iniciante", example = "1")
    private String nivel_iniciante;

    @ApiModelProperty(value = "Código do nível intermediário", example = "2")
    private String nivel_intermediario;

    @ApiModelProperty(value = "Código do nível avançado", example = "3")
    private String nivel_avancado;

    @ApiModelProperty(value = "Define se deve permitir aluno criar treino por IA no aplicativo", example = "false")
    private String permitir_aluno_criar_treino_ia_app;

    public Boolean getPermitir_criar_treino_automatizado_ia() {
        if (!UteisValidacao.emptyString(permitir_criar_treino_automatizado_ia)) {
            return permitir_criar_treino_automatizado_ia.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_criar_treino_automatizado_ia(String permitir_criar_treino_automatizado_ia) {
        this.permitir_criar_treino_automatizado_ia = permitir_criar_treino_automatizado_ia;
    }

    public String getTempo_aprovacao_automatica() {
        return tempo_aprovacao_automatica;
    }

    public void setTempo_aprovacao_automatica(String tempo_aprovacao_automatica) {
        this.tempo_aprovacao_automatica = tempo_aprovacao_automatica;
    }

    public String getTempo_maximo_revisao() {
        return tempo_maximo_revisao;
    }

    public void setTempo_maximo_revisao(String tempo_maximo_revisao) {
        this.tempo_maximo_revisao = tempo_maximo_revisao;
    }

    public Boolean getHabilitar_obrigatoriedade_aprovacao_professor() {
        if (!UteisValidacao.emptyString(habilitar_obrigatoriedade_aprovacao_professor)) {
            return habilitar_obrigatoriedade_aprovacao_professor.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setHabilitar_obrigatoriedade_aprovacao_professor(String habilitar_obrigatoriedade_aprovacao_professor) {
        this.habilitar_obrigatoriedade_aprovacao_professor = habilitar_obrigatoriedade_aprovacao_professor;
    }

    public Boolean getQuantidade_fichas_habilitar_treino_ia() {
        if (!UteisValidacao.emptyString(quantidade_fichas_habilitar_treino_ia)) {
            return quantidade_fichas_habilitar_treino_ia.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setQuantidade_fichas_habilitar_treino_ia(String quantidade_fichas_habilitar_treino_ia) {
        this.quantidade_fichas_habilitar_treino_ia = quantidade_fichas_habilitar_treino_ia;
    }

    public String getNivel_iniciante() {
        return nivel_iniciante;
    }

    public void setNivel_iniciante(String nivel_iniciante) {
        this.nivel_iniciante = nivel_iniciante;
    }

    public String getNivel_intermediario() {
        return nivel_intermediario;
    }

    public void setNivel_intermediario(String nivel_intermediario) {
        this.nivel_intermediario = nivel_intermediario;
    }

    public String getNivel_avancado() {
        return nivel_avancado;
    }

    public void setNivel_avancado(String nivel_avancado) {
        this.nivel_avancado = nivel_avancado;
    }

    public Boolean getPermitir_aluno_criar_treino_ia_app() {
        if (!UteisValidacao.emptyString(permitir_aluno_criar_treino_ia_app)) {
            return permitir_aluno_criar_treino_ia_app.trim().equals("true");
        }
        return false;
    }

    public void setPermitir_aluno_criar_treino_ia_app(String permitir_aluno_criar_treino_ia_app) {
        this.permitir_aluno_criar_treino_ia_app = permitir_aluno_criar_treino_ia_app;
    }
}
