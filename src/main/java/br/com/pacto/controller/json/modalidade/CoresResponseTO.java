package br.com.pacto.controller.json.modalidade;


import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(value = "Informações da cor")
public class CoresResponseTO {
    @ApiModelProperty(value = "Código único identificador da cor", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome identificador da cor", example = "Azul")
    private String nome;
    @ApiModelProperty(value = "Valor da cor", example = "1E64C8")
    private String valor;

    public CoresResponseTO(PaletaCoresEnum cor) {
        this.id = cor.getId();
        this.nome = cor.getDescricao();
        this.valor = cor.getCor();
    }

    public CoresResponseTO(Integer id, String nome, String valor) {
        this.id = id;
        this.nome = nome;
        this.valor = valor;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }
}
