package br.com.pacto.controller.json.controller;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.colaborador.ColaboradorTO;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.usuario.UsuarioJSON;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dto.UsuarioEncriptadoDTO;
import br.com.pacto.security.dto.UsuarioLoginAppDTO;
import br.com.pacto.security.dto.UsuarioLoginDTO;
import br.com.pacto.security.dto.UsuarioLoginV2DTO;
import br.com.pacto.security.service.LoginService;
import br.com.pacto.security.service.UsuarioAutenticadoDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.login.ExemploRespostaStringLogin;
import br.com.pacto.swagger.respostas.login.ExemploRespostaUsuarioAutenticadoDTO;
import br.com.pacto.swagger.respostas.login.ExemploRespostaUsuarioJSON;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.json.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Controle de Login da aplicação rest
 *
 * <AUTHOR> Karlus
 * @since 19/07/2018
 */

@Controller
@RequestMapping("login")
public class LoginController {

    private final LoginService loginService;

    /**
     * @param loginService {@link LoginService}
     */
    @Autowired
    public LoginController(LoginService loginService) {
        this.loginService = loginService;
    }

    @ApiOperation(
            value = "Realizar login no sistema",
            notes = "Autentica um usuário no sistema utilizando chave da empresa, nome de usuário e senha. " +
                    "Retorna token de acesso e informações do usuário autenticado.",
            tags = "Autenticação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaStringLogin.class)
    })
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> login(
            @ApiParam(value = "Dados de login do usuário", required = true)
            @RequestBody UsuarioLoginDTO usuarioDTO) {
        try {
            return ResponseEntityFactory.ok(loginDecode(usuarioDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    private String loginDecode(UsuarioLoginDTO usuarioDTO) throws ServiceException{
        try {
            return loginService.login(usuarioDTO.getChave(), URLDecoder.decode(usuarioDTO.getUsername(), "UTF-8"), usuarioDTO.getSenha(), true);
        }catch (Exception e){
            return loginService.login(usuarioDTO.getChave(), usuarioDTO.getUsername(), usuarioDTO.getSenha(), true);
        }
    }

    @ApiOperation(
            value = "Login para aplicativo móvel",
            notes = "Autentica usuário especificamente para uso em aplicativos móveis. " +
                    "Retorna informações simplificadas do usuário e configurações específicas para mobile.",
            tags = "Autenticação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaStringLogin.class)
    })
    @ResponseBody
    @RequestMapping(value = "/app", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> loginApp(
            @ApiParam(value = "Dados de login para aplicativo móvel", required = true)
            @RequestBody UsuarioLoginDTO usuarioDTO) {
        try {
            return ResponseEntityFactory.ok(loginService.app(usuarioDTO.getChave(), usuarioDTO.getUsername()));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/ef92095f05323869f2d5e207faadb37a", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> loginAppCriptografado(@RequestBody String conteudoCriptografado) {
        try {
            String jsonDescriptografado = Uteis.decryptUserData(conteudoCriptografado);
            JSONObject obj = new JSONObject(jsonDescriptografado);

            String chave = obj.optString("chave");
            String username = obj.optString("username");
            String time = obj.optString("time");

            if (UteisValidacao.emptyString(time)) {
                throw new ServiceException("Parâmetro obrigatório 'time' não pode estar vazio.");
            }

            return ResponseEntityFactory.ok(Uteis.encryptUserData(loginService.app(chave, username)));
        } catch (Exception e) {
            Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, "Erro no loginApp", e);

            JSONObject erro = new JSONObject();
            try {
                erro.put("erro", e.getMessage());
            } catch (Exception ignored) {
                Logger.getLogger(this.getClass().getName()).log(Level.WARNING, "Error creating error JSON", e);
            }

            String encryptedError = "";
            try {
                encryptedError = Uteis.encryptUserData(erro.toString());
                String erroLoginApp = Uteis.encryptUserData("erro_login_app");
                return ResponseEntityFactory.erroInterno(erroLoginApp, encryptedError);
            } catch (Exception encryptionException) {
                Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, "Error encrypting error message", encryptionException);
                encryptedError = "{\"erro\": \"Erro interno ao criptografar\"}";
            }
            return ResponseEntityFactory.erroInterno("Erro ao criptografar", encryptedError);
        }
    }

    @ApiOperation(
            value = "Login para aplicativo móvel v3",
            notes = "Versão 3 do login para aplicativo móvel utilizando código do usuário. " +
                    "Método otimizado para autenticação rápida em dispositivos móveis.",
            tags = "Autenticação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaStringLogin.class)
    })
    @ResponseBody
    @RequestMapping(value = "v3/app", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> loginApp(
            @ApiParam(value = "Dados de login v3 para aplicativo móvel", required = true)
            @RequestBody UsuarioLoginAppDTO usuarioDTO) {
        try {
            return ResponseEntityFactory.ok(loginService.app(usuarioDTO.getChave(), usuarioDTO.getCodUsuario()));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Validar usuário para dispositivos móveis",
            notes = "Valida se um usuário existe e está habilitado para uso em dispositivos móveis. " +
                    "Retorna resultado da validação sem realizar login completo.",
            tags = "Autenticação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaStringLogin.class)
    })
    @ResponseBody
    @RequestMapping(value = "/validar-usuario-movel", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarUsuarioMovel(
            @ApiParam(value = "Dados do usuário para validação móvel", required = true)
            @RequestBody UsuarioLoginDTO usuarioDTO) {
        try {
            return ResponseEntityFactory.ok(validarUsuario(usuarioDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    private String validarUsuario(UsuarioLoginDTO usuarioDTO) throws ServiceException{
        try {
            return loginService.validarUsuarioMovel(usuarioDTO.getChave(),
                    URLDecoder.decode(usuarioDTO.getUsername(), "UTF-8"));
        }catch (Exception e){
            return loginService.validarUsuarioMovel(usuarioDTO.getChave(),
                    usuarioDTO.getUsername());
        }
    }

    @ApiOperation(
            value = "Gerar token para usuário ZW",
            notes = "Gera um token de autenticação para usuário do sistema ZW (ZillionWeb) utilizando código encriptado. " +
                    "Retorna token JWT para acesso às funcionalidades do sistema.",
            tags = "Autenticação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaStringLogin.class)
    })
    @ResponseBody
    @RequestMapping(value = "/gerar-token-usuario-zw", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> gerarTokenUsuarioZw(
            @ApiParam(value = "Dados do usuário encriptado para geração do token", required = true)
            @RequestBody UsuarioEncriptadoDTO usuario) {
        try {
            return ResponseEntityFactory.ok(loginService.gerarTokenZw(usuario));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter dados do usuário autenticado",
            notes = "Realiza login e retorna dados completos do usuário autenticado ou obtém dados de usuário ZW específico. " +
                    "Se o parâmetro 'czw' for informado, retorna dados do usuário ZW correspondente.",
            tags = "Autenticação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaUsuarioAutenticadoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/dados", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> dados(
            @ApiParam(value = "Dados de login do usuário", required = true)
            @RequestBody UsuarioLoginDTO usuarioDTO,
            @ApiParam(value = "Código do usuário ZW para consulta específica", defaultValue = "null")
            @RequestParam(required = false) Integer czw) {
        try {
            if(UteisValidacao.emptyNumber(czw)){
                return ResponseEntityFactory.ok(loginService.login(usuarioDTO.getChave(), usuarioDTO.getUsername(), usuarioDTO.getSenha()));
            }
            return ResponseEntityFactory.ok(loginService.usuarioZw(usuarioDTO.getChave(), czw));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Adicionar usuário à sessão",
            notes = "Adiciona um usuário autenticado à sessão do sistema, registrando suas permissões e token de acesso. " +
                    "Operação necessária para manter o estado de autenticação do usuário.",
            tags = "Autenticação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "/addsessao", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> adicionarSessao(
            @ApiParam(value = "Dados do usuário autenticado para adicionar à sessão", required = true)
            @RequestBody UsuarioAutenticadoDTO dto,
            @ApiParam(value = "Chave de identificação da empresa", required = true)
            @RequestParam String chave) {
        try {
            loginService.addUsuarioSessao(chave, dto);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter dados do usuário autenticado versão 2",
            notes = "Versão 2 do endpoint de obtenção de dados do usuário, utilizando estrutura aprimorada de login. " +
                    "Retorna dados completos do usuário autenticado com informações de ZW e TW.",
            tags = "Autenticação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaUsuarioAutenticadoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/v2/dados", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> dadosV2(
            @ApiParam(value = "Dados de login versão 2 do usuário", required = true)
            @RequestBody UsuarioLoginV2DTO dto) {
        try {
            return ResponseEntityFactory.ok(loginService.loginV2(dto));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Login para aplicativo móvel versão 2",
            notes = "Versão 2 do login para aplicativo móvel sem necessidade de senha. " +
                    "Valida usuário por email e retorna dados completos para uso no aplicativo móvel.",
            tags = "Autenticação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaUsuarioJSON.class)
    })
    @ResponseBody
    @RequestMapping(value = "/v2/app", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> loginAppSemSenha(
            @ApiParam(value = "Dados de login para aplicativo móvel sem senha", required = true)
            @RequestBody UsuarioLoginDTO dto) {
        try {
            UsuarioService usuarioService = UtilContext.getBean(UsuarioService.class);
            Usuario u = usuarioService.validarUsuarioEmail(dto.getChave(), dto.getUsername(), false);
            UsuarioJSON uJSON = new UsuarioJSON(u, usuarioService.empresasApp(dto.getChave(), u));
            return ResponseEntityFactory.ok(uJSON.toJSON());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Importar e autenticar colaborador",
            notes = "Cria ou consulta um colaborador no sistema através de importação e realiza login automático. " +
                    "Utilizado para integração com sistemas externos que precisam criar usuários dinamicamente.",
            tags = "Autenticação"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaStringLogin.class)
    })
    @ResponseBody
    @RequestMapping(value = "/importacao/usuario/{chave}/{empresa}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> usuarioImportacao(
            @ApiParam(value = "Dados do colaborador para importação", required = true)
            @RequestBody ColaboradorTO colaboradorTO,
            @ApiParam(value = "Chave de identificação da empresa", required = true)
            @PathVariable String chave,
            @ApiParam(value = "Código da empresa para associação do colaborador", required = true)
            @PathVariable Integer empresa,
            HttpServletRequest request) {
        try {
            UsuarioService usuarioService = UtilContext.getBean(UsuarioService.class);
            usuarioService.criarOuConsultarSeExisteImportacao(chave, colaboradorTO,
                    empresa, request);
            return ResponseEntityFactory.ok(loginService.login(chave, colaboradorTO.getAppUserName(), colaboradorTO.getAppPassword(), true));
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno("erro_obter_usuario_importacao", e.getMessage());
        }
    }
}
