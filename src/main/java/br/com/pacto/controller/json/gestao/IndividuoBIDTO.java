package br.com.pacto.controller.json.gestao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO contendo dados básicos de um indivíduo para Business Intelligence.")
public class IndividuoBIDTO {

    @ApiModelProperty(value = "Identificador do indivíduo (matrícula).", example = "123456")
    private String id;

    @ApiModelProperty(value = "Nome completo do indivíduo.", example = "<PERSON>")
    private String nome;

    public IndividuoBIDTO(String id, String nome) {
        this.id = id;
        this.nome = nome;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
