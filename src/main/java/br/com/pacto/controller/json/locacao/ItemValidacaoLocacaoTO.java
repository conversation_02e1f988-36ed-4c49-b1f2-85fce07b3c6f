package br.com.pacto.controller.json.locacao;

import br.com.pacto.bean.locacao.ItemValidacaoLocacao;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(description = "Item de validação de uma locação, definindo regras de acesso e valores.")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ItemValidacaoLocacaoTO implements Serializable {

    @ApiModelProperty(value = "Código único do item de validação.", example = "1")
    private Integer codigo;

    @ApiModelProperty(value = "Código do plano associado à validação.", example = "10")
    private Integer plano;

    @ApiModelProperty(value = "Código do produto associado à validação.", example = "5")
    private Integer produto;

    @ApiModelProperty(value = "Descrição do item de validação.", example = "Plano Mensal")
    private String descricao;

    @ApiModelProperty(value = "Valor final da validação.", example = "150.0")
    private Double valorFinal;

    public ItemValidacaoLocacaoTO() {}

    public ItemValidacaoLocacaoTO(ItemValidacaoLocacao itemValidacaoLocacao) {
        this.codigo = itemValidacaoLocacao.getCodigo();
        this.plano = itemValidacaoLocacao.getPlano();
        this.produto = itemValidacaoLocacao.getProduto();
        this.descricao = itemValidacaoLocacao.getDescricao();
        this.valorFinal = itemValidacaoLocacao.getValorFinal();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = valorFinal;
    }
}
