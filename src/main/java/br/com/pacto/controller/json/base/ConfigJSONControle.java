/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.base;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.impl.EntityManagerFactoryService;
import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.base.jpa.MigracaoDadosJPAService;
import br.com.pacto.base.jpa.service.intf.PovoadorLogService;
import br.com.pacto.base.oamd.OAMDService;
import br.com.pacto.base.util.Propagador;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.json.configuracao.ConfigJSON;
import br.com.pacto.controller.json.configuracao.ConfiguracaoSistemaJSON;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.selfloops.SelfloopsDTO;
import br.com.pacto.email.UteisEmail;
import br.com.pacto.swagger.respostas.configuracao.*;
import io.swagger.annotations.*;
import springfox.documentation.annotations.ApiIgnore;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.security.service.impl.JWTTokenService;
import br.com.pacto.service.discovery.DiscoveryMsService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.agenda.DisponibilidadeService;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.atividade.AtividadeService;
import br.com.pacto.service.intf.aulapersonal.ConfiguracaoPersonalEmpresaService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.avaliacao.BIAvaliacaoFisicaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.ficha.SerieService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.memcached.CachedManagerInterfaceFacade;
import br.com.pacto.service.intf.processo.ProcessoAtualizarAtividadesAlternativasService;
import br.com.pacto.service.intf.processo.ProcessoAtualizarGIFsAtividadesService;
import br.com.pacto.service.intf.processo.ProcessoSincronizarTreinoAcesso;
import br.com.pacto.service.intf.processo.ProcessoSincronizarVinculosAlunoProfessorTW;
import br.com.pacto.service.intf.processo.ProcessosService;
import br.com.pacto.service.intf.processo.SincronizacaoEmpresaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.selfloops.SelfloopsConfiguracoesService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.ConfigsEmail;
import br.com.pacto.util.FileUtilities;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.UtilReflection;
import org.hibernate.exception.ConstraintViolationException;
import org.json.JSONArray;
import org.json.JSONObject;
import org.postgresql.util.PSQLException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import servicos.integracao.adm.AdmWSConsumer;
import servicos.integracao.admapp.AdmAppWSConsumer;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
import servicos.integracao.zw.IntegracaoTurmasWSConsumer;

import javax.jws.WebParam;
import javax.persistence.PersistenceException;
import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/config")
public class ConfigJSONControle extends SuperControle {

    @Autowired
    private ConfiguracaoSistemaService cs;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private EmpresaService es;
    @Autowired
    private EntityManagerService entityService;
    @Autowired
    private MigracaoDadosJPAService migracaoService;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private DisponibilidadeService disponibilidadeService;
    @Autowired
    private AtividadeService as;
    @Autowired
    private ProcessosService processosService;
    @Autowired
    private ProcessoSincronizarVinculosAlunoProfessorTW sincronizarVinculosAlunoProfessorTW;
    @Autowired
    private OAMDService oamdService;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private SelfloopsConfiguracoesService selfloopsConfiguracoesService;
    @Autowired
    private PovoadorLogService povoadorLogService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ConfiguracaoPersonalEmpresaService configuracaoPersonalEmpresaService;
    @Autowired
    private ProcessoAtualizarGIFsAtividadesService processoAtualizarGIFsAtividadesService;
    @Autowired
    private ProcessoAtualizarAtividadesAlternativasService processoAtualizarAtividadesAlternativasService;
    @Autowired
    private ProcessoSincronizarTreinoAcesso processoSincronizarTreinoAcesso;
    @Autowired
    private CachedManagerInterfaceFacade memcached;

    @ApiOperation(value = "Recarregar configurações do sistema",
                  notes = "Recarrega todas as configurações do sistema, incluindo cache, integrações e configurações de rede. " +
                          "Utilizado para aplicar mudanças de configuração sem reiniciar o sistema.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Configurações recarregadas com sucesso", response = ExemploRespostaReloadConfiguracao.class)
    })
    @RequestMapping(value = "reload", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap reload(HttpServletRequest request,
                   @ApiParam(value = "Chave da empresa para limpeza específica de cache",
                            defaultValue = "academia123")
                   @RequestParam(required = false) final String key) {
        ModelMap mm = new ModelMap();
        try {
            Aplicacao.init();
            EntityManagerFactoryService.init();
            conexaoZWService.init();
            IntegracaoTurmasWSConsumer integracaoTurmasWSConsumer = UtilContext.getBean(IntegracaoTurmasWSConsumer.class);
            integracaoTurmasWSConsumer.init();
            AdmAppWSConsumer.init();
            UtilContext.getBean(AdmWSConsumer.class).init();
            UtilContext.getBean(JWTTokenService.class).reload();
            oamdService.limparMapaDeRedes();
            memcached.remover(key, "chaveFranqueadora-" + key);
            Uteis.init();
            EntityManagerFactoryService.resetMapaTimezone();
            DiscoveryMsService.clearnCache(key);
            AdmWSConsumer.purgeCache(key, null);
            if (key != null) {
                as.purgeAtividadesCache(key);
                IntegracaoTurmasWSConsumer.purgeCache(key);
                cs.purgeCache(key);
            }
            Propagador.propagar(request);
            mm.addAttribute(RETURN, String.format("Configurações recarregadas com sucesso! Instance: %s", Aplicacao.getInstanceName()));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Recarregar configurações de rede",
                  notes = "Recarrega especificamente as configurações de rede do sistema, limpando o mapa de redes.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Configurações de rede recarregadas com sucesso", response = ExemploRespostaReloadRede.class)
    })
    @RequestMapping(value = "reload-rede", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap reloadRede(HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            oamdService.limparMapaDeRedes();
            Propagador.propagar(request);
            mm.addAttribute(RETURN, String.format("Configurações de rede recarregadas com sucesso! Instance: %s", Aplicacao.getInstanceName()));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Recarregar cache de usuário",
                  notes = "Remove o cache específico de um usuário do sistema, forçando o recarregamento das informações na próxima consulta.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Cache do usuário removido com sucesso", response = ExemploRespostaReloadUsuario.class)
    })
    @RequestMapping(value = "reload-usuario", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap reloadUsuario(@ApiParam(value = "Chave da empresa", defaultValue = "academia123")
                          @RequestParam(required = false) final String key,
                          @ApiParam(value = "ID do usuário para limpeza específica de cache", defaultValue = "1")
                          @RequestParam(required = false) final Integer u,
                          HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            Propagador.propagar(request);
            AdmWSConsumer.purgeCache(key, u);
            DiscoveryMsService.clearnCache(key);
            mm.addAttribute(RETURN, String.format("Cache usuário removido com sucesso! Key: "+ key + " | Instance: %s", Aplicacao.getInstanceName()));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Remover factory de entidade",
                  notes = "Remove a factory de entidade específica de uma empresa, fechando conexões e limpando recursos associados.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Factory removido com sucesso", response = ExemploRespostaRemoveFactory.class)
    })
    @RequestMapping(value = "{ctx}/removeFactory", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap removeEntityFactory(@ApiParam(value = "Chave da empresa", defaultValue = "academia123")
                                @PathVariable final String ctx,
                                HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            EntityManagerFactoryService.close(ctx);
            EntityManagerFactoryService.getEmpresas().remove(ctx);

            entityService.getStatus().remove(ctx);
            entityService.close(ctx);

            conexaoZWService.closeDatasource(ctx);
            conexaoZWService.init();

            mm.addAttribute(STATUS_SUCESSO, "Factory " + ctx + " removido com sucesso!");
            Propagador.propagar(request);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Atualizar informações das instâncias",
                  notes = "Atualiza as informações das instâncias do sistema quando configurado para carregar do cloud.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Instâncias atualizadas com sucesso", response = ExemploRespostaUpdateInstances.class)
    })
    @RequestMapping(value = "/updateInstances", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap updateInstances(HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            if (Aplicacao.getProp(Aplicacao.loadInstancesFromCloud).equals("true")) {
                mm.addAttribute(STATUS_SUCESSO, Aplicacao.atualizarInfoInstancias());
                Propagador.propagar(request);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/force-ddl", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap forceDDL(@PathVariable final String ctx) {
        ModelMap mm = new ModelMap();
        try {
            entityService.getEntityManager(ctx, false, true);
            mm.addAttribute(STATUS_SUCESSO, "Banco de dados da Empresa " + ctx + " atualizado com sucesso!");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/updateBD", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap updateBD(@PathVariable final String ctx) {
        ModelMap mm = new ModelMap();
        try {
            migracaoService.atualizar(ctx);
            mm.addAttribute(STATUS_SUCESSO, "Banco de dados da Empresa " + ctx + " atualizado com sucesso!");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/updateBDVersao/{versao}", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap updateBDVersao(@PathVariable final String ctx, @PathVariable final String versao) {
        ModelMap mm = new ModelMap();
        try {
            UtilReflection.invoke(migracaoService,
                    String.format("migracaoVersao%s", versao),
                    new Class[]{String.class},
                    new Object[]{ctx});

            mm.addAttribute(STATUS_SUCESSO, String.format("Banco de dados da Empresa %s executou migracao %s com sucesso !",
                    ctx, versao));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/consultar-versao-bd", method = {RequestMethod.GET})
    public @ResponseBody
    ModelMap consultarVersaoBd(@PathVariable final String ctx) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(STATUS_SUCESSO, cs.consultarVersaoBd(ctx));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/statelogs/{ativo}", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap statelogs(@PathVariable Boolean ativo, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            Uteis.debug = ativo;
            Propagador.propagar(request);
            mm.addAttribute(STATUS_SUCESSO, Uteis.debug);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Obter configurações do sistema",
                  notes = "Retorna todas as configurações do sistema para uma empresa específica, incluindo configurações de módulos, " +
                          "avaliação física e outras configurações operacionais. As configurações são adaptadas para aplicativos móveis quando detectado.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Configurações obtidas com sucesso", response = ExemploRespostaGetConfigs.class)
    })
    @RequestMapping(value = "{ctx}/getconfigs", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap getconfigsCache(@ApiParam(value = "Chave da empresa", defaultValue = "academia123")
                            @PathVariable String ctx,
                            @RequestHeader(value = "User-Agent", required = false) String userAgent) {
        ModelMap mm = new ModelMap();
        try {
            String mobileUserAgentRegex = "(?:[A-Za-z0-9_]+)_(ANDROID|IOS) / v:\\d+\\.\\d+\\.\\d+ \\(BuildNumber:\\d+\\).*";

            boolean isMobileApp = !UteisValidacao.emptyString(userAgent) && userAgent.matches(mobileUserAgentRegex);

            if (cs.isOnCache(ctx)) {
                ConfiguracaoSistemaJSON cache = cs.getCache(ctx);
                if(isMobileApp) {
                    cache = cs.alterarConfiguracoesParaApp(cache);
                }

                mm.addAttribute(RETURN, cache);
            } else {
                List<ConfiguracaoSistema> lista = cs.consultarPorTipos(ctx,
                        ConfiguracoesEnum.MOBILE_SEMPRE_ATUALIZAR_CARGA_FICHA,
                        ConfiguracoesEnum.EMITIR_FICHA_APOS_VENCIMENTO_TREINO,
                        ConfiguracoesEnum.NUMERO_IMPRESSAO_FICHA,
                        ConfiguracoesEnum.MODULO_AULAS,
                        ConfiguracoesEnum.MODULO_AULAS_ABA_SALDO,
//                  ConfiguracoesEnum.MODULO_AULAS_ABA_AULAS_COLETIVAS,
                        ConfiguracoesEnum.MODULO_AULAS_ABA_TURMAS,
                        ConfiguracoesEnum.MODULO_TREINAR,
                        ConfiguracoesEnum.BLOQUEAR_IMPRESSAO_FICHA_APOS_TODAS_EXECUCOES,
                        ConfiguracoesEnum.ALUNO_MARCAR_PROPRIA_AULA,
                        ConfiguracoesEnum.HABILITAR_CROSSFIT,
                        ConfiguracoesEnum.MODULO_PRINCIPAL_APLICATIVO,
                        ConfiguracoesEnum.USAR_INTEGRACAO_BIOIMPEDANCIA,
                        ConfiguracoesEnum.NAO_EXIBIR_NUMERO_DE_VEZES_NO_APP,
                        ConfiguracoesEnum.HABILITAR_FILA_ESPERA,
                        ConfiguracoesEnum.PERMITIR_VISUALIZAR_WOD,
                        ConfiguracoesEnum.PERMITIR_VISUALIZAR_CREF,
                        ConfiguracoesEnum.PERMITIR_VISUALIZAR_PAR_Q_10_PERGUNTAS,
                        ConfiguracoesEnum.PERMITIR_VISUALIZAR_LEI_PARQ,
                        ConfiguracoesEnum.HABILITAR_VER_WOD_TODAS_EMPRESAS_APP,
                        ConfiguracoesEnum.PERMITIR_VISUALIZAR_AVISO_DE_PENDENCIAS,
                        ConfiguracoesEnum.FORCAR_CRIAR_NOVO_PROGRAMA,
                        ConfiguracoesEnum.PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA,
                        ConfiguracoesEnum.TEMPO_APROVACAO_AUTOMATICA,
                        ConfiguracoesEnum.TEMPO_MAXIMO_REVISAO,
                        ConfiguracoesEnum.HABILITAR_OBRIGATORIEDADE_APROVACAO_PROFESSOR,
                        ConfiguracoesEnum.PERMITIR_ALUNO_CRIAR_TREINO_IA_APP
                        );

                List<ConfiguracaoSistema> listaAvaliacao = cs.consultarPorTipos(ctx,
                        ConfiguracoesEnum.LANCAR_AGENDAMENTO_PROXIMA_AVALIACAO,
                        ConfiguracoesEnum.LANCAR_PRODUTO_AVALIACAO,
                        ConfiguracoesEnum.VALIDAR_PRODUTO_AVALIACAO,
                        ConfiguracoesEnum.PRODUTO_AVALIACAO,
                        ConfiguracoesEnum.CFG_OBJETIVOS_ANAMNESE,
                        ConfiguracoesEnum.CFG_PESO_ALTURA_PA_FC,
                        ConfiguracoesEnum.CFG_PARQ,
                        ConfiguracoesEnum.CFG_DOBRAS_CUTANEAS,
                        ConfiguracoesEnum.CFG_PERIMETRIA,
                        ConfiguracoesEnum.CFG_COMPOSICAO_CORPORAL,
                        ConfiguracoesEnum.CFG_FLEXIBILIDADE,
                        ConfiguracoesEnum.CFG_POSTURA,
                        ConfiguracoesEnum.CFG_RML,
                        ConfiguracoesEnum.CFG_RML_SEPARADO,
                        ConfiguracoesEnum.CFG_RML_OPCOES,
                        ConfiguracoesEnum.CFG_VO2MAX,
                        ConfiguracoesEnum.CFG_RECOMENDACOES,
                        ConfiguracoesEnum.CFG_VENTILOMETRIA,
                        ConfiguracoesEnum.CFG_TESTES_CAMPO,
                        ConfiguracoesEnum.CFG_TESTE_BIKE,
                        ConfiguracoesEnum.CFG_SOMATOTIPIA,
                        ConfiguracoesEnum.CFG_TESTE_QUEENS,
                        ConfiguracoesEnum.LANCAR_PRODUTO_AVALIACAO_DATA_VENCIMENTO,
                        ConfiguracoesEnum.LANCAR_ATESTADO_ZILLYONWEB,
                        ConfiguracoesEnum.PRODUTO_ATESTADO,
                        ConfiguracoesEnum.TRAZER_ULTIMA_AVALIACAO_INTEGRADA,
                        ConfiguracoesEnum.DESCRICAO_AVALIACAO_INTEGRADA,
                        ConfiguracoesEnum.MODELO_EVOLUCAO_FISICA,
                        ConfiguracoesEnum.USAR_PRESSAO_SISTOLICA_DIASTOLICA,
                        ConfiguracoesEnum.USAR_ORDEM_DOBRAS_CUTANEAS,
                        ConfiguracoesEnum.OBRIGAR_CAMPOS_DOBRAS_BIOIMPEDANCIA
                );

                List<ConfiguracaoSistema> listaConfig = Stream.concat(lista.stream(), listaAvaliacao.stream()).collect(Collectors.toList());
                ConfiguracaoSistemaJSON json = new ConfiguracaoSistemaJSON(ctx, listaConfig);
                /* A conf. MODULO_AULAS_ABA_AULAS_COLETIVAS eh utlizada para habilidar ou desabilidatar o botao marcar aula pelo aluno no aplicativo.
                 * Nao eh utilizada no Novo Treino e a falta dela faz o botao ser desabilitado.
                 * Futuramente sera removido.
                 */
                json.getConfiguracoes().add(new ConfigJSON(ConfiguracoesEnum.MODULO_AULAS_ABA_AULAS_COLETIVAS.name(),
                        "true", ConfiguracoesEnum.MODULO_AULAS_ABA_AULAS_COLETIVAS.getTipo().name()));
                cs.putsOnCache(ctx, json);

                if(isMobileApp) {
                    json = cs.alterarConfiguracoesParaApp(json);
                }
                mm.addAttribute(RETURN, json);
            }
        } catch (Exception ex) {
            mm.addAttribute("erro", ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Obter configuração específica",
                  notes = "Retorna uma configuração específica do sistema baseada no tipo de configuração solicitado.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Configuração específica obtida com sucesso", response = ExemploRespostaGetConfigEspecifica.class)
    })
    @RequestMapping(value = "{ctx}/getconfigs/{cfg}", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap getconfigs(@ApiParam(value = "Chave da empresa", defaultValue = "academia123")
                       @PathVariable String ctx,
                       @ApiParam(value = "Tipo de configuração a ser consultada", defaultValue = "MODULO_AULAS")
                       @PathVariable String cfg) {
        ModelMap mm = new ModelMap();
        try {
                List<ConfiguracaoSistema> lista = cs.consultarPorTipos(ctx,
                        ConfiguracoesEnum.valueOf(cfg)
                );
                ConfiguracaoSistemaJSON json = new ConfiguracaoSistemaJSON(ctx, lista);
                mm.addAttribute(RETURN, json);
        } catch (Exception ex) {
            mm.addAttribute("erro", ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Obter status do sistema",
                  notes = "Retorna o status atual do sistema, incluindo informações sobre conexões e serviços.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Status do sistema obtido com sucesso", response = ExemploRespostaGetStatus.class)
    })
    @RequestMapping(value = "/getstatus", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap getstatus() {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, entityService.getStatus());
        } catch (Exception ex) {
            mm.addAttribute("erro", ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Ler opções globais",
                  notes = "Retorna as opções globais de configuração do sistema.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Opções globais obtidas com sucesso", response = ExemploRespostaReadGlobalOptions.class)
    })
    @RequestMapping(value = "/readGlobalOptions", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap readGlobalOptions() {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, Aplicacao.listGlobalOptions());
        } catch (Exception ex) {
            mm.addAttribute("erro", ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/baixarFotosTurma", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap baixarFotosTurma(@PathVariable String ctx, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            AgendaTotalService agendaService = (AgendaTotalService) UtilContext.getBean(AgendaTotalService.class);
            agendaService.baixarFotosAlunos(ctx, request);
            mm.addAttribute(RETURN, "Fotos baixadas com sucesso!");
            Propagador.propagar(request);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    /**
     * @param ctx
     * @param codigopessoa
     * @param removerFoto
     * @return
     * @Deprecated
     */
    @RequestMapping(value = "{ctx}/baixarFotoAluno", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap baixarFotoAluno(@PathVariable String ctx, @RequestParam String codigopessoa,
                             @RequestParam(required = false) Boolean removerFoto,
                             HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(ClienteSinteticoService.class);
            if (removerFoto != null && removerFoto) {
                clienteService.removerFotoAluno(ctx, request, codigopessoa, false);
                mm.addAttribute(RETURN, "Foto removida com sucesso!");
            } else {
                clienteService.baixarFotoAluno(ctx, request, codigopessoa, null, false);
                mm.addAttribute(RETURN, "Foto baixada com sucesso!");
            }
            Propagador.propagar(request);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/baixarFotoAlunoPorFotoKey", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap baixarFotoAlunoPorFotoKey(@PathVariable String ctx, @RequestParam String codigopessoa,
                                       @RequestParam(value = "fotoKey") String fotoKey,
                                       @RequestParam(required = false) Boolean removerFoto,
                                       @RequestParam(required = false, defaultValue = "false") Boolean atualizarFotoApp,
                                       HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(ClienteSinteticoService.class);
            ProfessorSinteticoService professorService = (ProfessorSinteticoService) UtilContext.getBean(ProfessorSinteticoService.class);

            ClienteSintetico cliente = clienteService.consultarPorCodigoPessoa(ctx, codigopessoa);

            if(cliente != null){
                if (removerFoto != null && removerFoto) {
                    clienteService.removerFotoAluno(ctx, request, codigopessoa, atualizarFotoApp);
                    mm.addAttribute(RETURN, "Foto removida com sucesso!");
                } else {
                    clienteService.baixarFotoAluno(ctx, request, codigopessoa, fotoKey, atualizarFotoApp);
                    mm.addAttribute(RETURN, "Foto baixada com sucesso!");
                }
            }else{
                if(removerFoto != null && removerFoto) {
                    professorService.removerFotoAluno(ctx, request, codigopessoa);
                    mm.addAttribute(RETURN, "Foto removida com sucesso!");
                }else{
                    professorService.baixarFotoAluno(ctx, request,codigopessoa, fotoKey);
                    mm.addAttribute(RETURN, "Foto baixada com sucesso!");
                }
            }

            ProfessorSintetico professor = professorService.consultarPorCodigoPessoa(ctx,codigopessoa);
            if(professor !=null){
                professor.getPessoa().setFotoKey(fotoKey+ "?time=" + Calendario.hoje().getTime());
                professorService.alterar(ctx,professor);
            }

            Propagador.propagar(request);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/obterEmailUsuarios", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap obterEmailUsuarios(@PathVariable String ctx, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            UsuarioService usuarioService = (UsuarioService) UtilContext.getBean(UsuarioService.class);
            List<String> emails = usuarioService.consultarEmails(ctx);
            JSONArray array = new JSONArray();
            for (String email : emails) {
                array.put(email);
            }
            mm.addAttribute(RETURN, array.toString());
            Propagador.propagar(request);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Consultar configurações de manutenção",
                  notes = "Retorna as configurações específicas de manutenção do sistema, incluindo informações sobre aplicativo personalizado e URLs.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Configurações de manutenção obtidas com sucesso", response = ExemploRespostaConfiguracoesManutencao.class)
    })
    @RequestMapping(value = "/{ctx}/manutencao", method = RequestMethod.GET)
    public
    @ResponseBody
    ModelMap consultarConfiguracoesManutencao(@ApiParam(value = "Chave da empresa", defaultValue = "academia123")
                                             @PathVariable final String ctx) {
        ModelMap mm = new ModelMap();
        try {
            JSONObject json = new JSONObject();
            UsuarioSimplesDTO usuarioSimplesDTO = new UsuarioSimplesDTO();
            usuarioSimplesDTO.setChave(ctx);
            sessaoService.setUsuarioAtual(usuarioSimplesDTO);
            Object obj = configuracaoSistemaService.configsDTO(ConfiguracoesManutencaoDTO.class, new ConfiguracoesManutencaoDTO());
            json.put("aplicativo_personalizado", ((ConfiguracoesManutencaoDTO) obj).getAplicativo_personalizado());
            json.put("aplicativo_personalizado_nome", ((ConfiguracoesManutencaoDTO) obj).getAplicativo_personalizado_nome());
            json.put("aplicativo_personalizado_url", ((ConfiguracoesManutencaoDTO) obj).getAplicativo_personalizado_url());
            json.put("app_url_email", br.com.pacto.controller.json.base.SuperControle.getAppUrlEmail(ctx));
            mm.addAttribute("return", json.toString());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/closeFactory", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap closeFactory(@PathVariable String ctx, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
            try {
                ems.closeQuietly(ctx);
            } catch (Exception e) {
                throw new ServiceException(e.getMessage());
            }
            Propagador.propagar(request);
            mm.addAttribute(STATUS_SUCESSO, String.format("Instance: %s", Aplicacao.getInstanceName()));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/atualizarConfigDisponibilidade", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap atualizarConfigDisponibilidade() {
        ModelMap mm = new ModelMap();
        try {
            Set<String> keys = EntityManagerFactoryService.getEmpresas().keySet();
            for (String ctx : keys) {
                try {
                    disponibilidadeService.gerarConfigDisponibilidadePorAgendamentos(ctx);
                    mm.addAttribute(RETURN, ctx + " " + STATUS_SUCESSO);
                } catch (Exception ex) {
                    mm.addAttribute(RETURN, ctx + " " + STATUS_ERRO);
                    continue;
                }
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/atualizarConfigDisponibilidadePorChave", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap atualizarConfigDisponibilidadePorChave(@WebParam(name = "chave") String chave) {
        ModelMap mm = new ModelMap();
        try {
            disponibilidadeService.gerarConfigDisponibilidadePorAgendamentos(chave);
            mm.addAttribute(RETURN, chave + " " + STATUS_SUCESSO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Listar empresas da rede",
                  notes = "Retorna a lista de todas as empresas ativas da rede, incluindo informações básicas como código, nome e foto.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Lista de empresas obtida com sucesso", response = ExemploRespostaListaEmpresas.class)
    })
    @RequestMapping(value = "{ctx}/empresas", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap empresas(@ApiParam(value = "Chave da empresa", defaultValue = "academia123")
                     @PathVariable String ctx,
                     HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            List<Empresa> todas = es.obterTodos(ctx);
            List<Map<String, Object>> empresas = new ArrayList<Map<String, Object>>();
            for (Empresa emp : todas) {
                Map<String, Object> empresa = new HashMap<String, Object>();
                empresa.put("codigo", emp.getCodZW());
                empresa.put("nome", emp.getNome());
                empresa.put("fotoKey",
                        emp.getKeyImgEmpresa() != null && !emp.getKeyImgEmpresa().isEmpty()
                                ? "https://cdn1.pactorian.net/".concat(emp.getKeyImgEmpresa())
                                : "");
                empresa.put("codigoFinanceiro", emp.getCodFinanceiro());
                empresa.put("ativa", true);
                empresas.add(empresa);
            }
            mm.addAttribute(RETURN, empresas);
            Propagador.propagar(request);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/horaEmpresa", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap empresas(@PathVariable String ctx, @RequestParam Integer empresa, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            if (empresa == null || empresa == 0) {
                throw new Exception("Se você quer a hora da empresa, por que não informá-la?");
            }
            String timeZone = EntityManagerFactoryService.getTimeZoneEmpresa(ctx, empresa);
            if (timeZone == null) {
                Empresa empresaZW = es.obterPorIdZW(ctx, empresa);
                if (empresaZW.getTimeZoneDefault() != null && !empresaZW.getTimeZoneDefault().isEmpty()) {
                    timeZone = empresaZW.getTimeZoneDefault();
                    EntityManagerFactoryService.setTimeZoneEmpresa(ctx, empresa, timeZone);
                }
            }
            mm.addAttribute(RETURN, Uteis.getDataAplicandoFormatacao(Calendario.hoje(timeZone), "dd/MM/yyyy HH:mm:ss"));
            Propagador.propagar(request);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/deletarAlunosSemZW", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap deletarAlunosSemZW(@PathVariable String ctx, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSinteticoService cs = (ClienteSinteticoService) UtilContext.getBean(ClienteSinteticoService.class);
            List<String> alunos = cs.deletarAlunosForaZW(ctx);
            mm.addAttribute(RETURN, alunos);
            Propagador.propagar(request);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/importall/{empresa}", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap importall(@PathVariable String ctx,
                       @PathVariable Integer empresa,
                       @RequestParam(required = false) Integer matricula,
                       HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            DashboardBIService cs = (DashboardBIService) UtilContext.getBean(DashboardBIService.class);
            if (!UteisValidacao.emptyNumber(matricula)) {
                mm.addAttribute(RETURN, cs.importarAlunoForaTreino(ctx, empresa, matricula));
            } else {
                cs.importarAlunosForaTreinoAll(ctx, empresa);
                mm.addAttribute(RETURN, "Processo iniciado, aguarde e confie");
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/treino-em-casa/{documentkey}", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap treinoEmCasa(@PathVariable String ctx,
                       @PathVariable String documentkey) {
        ModelMap mm = new ModelMap();
        try {
            DashboardBIService cs = (DashboardBIService) UtilContext.getBean(DashboardBIService.class);
            cs.replicarTreinoEmCasa(ctx, documentkey);
            mm.addAttribute(RETURN, "Processo iniciado, seu treino será replicado na rede dentro de minutos.");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/ajusteprofessor/{empresa}", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap ajusteProfessor(@PathVariable String ctx,
                       @PathVariable Integer empresa,
                       HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            SincronizacaoEmpresaService sincronizacaoService = (SincronizacaoEmpresaService) UtilContext.getBean(SincronizacaoEmpresaService.class);
            JSONObject l = new JSONObject();
            sincronizacaoService.sincronizarAtivosProfessoresEmpresa(ctx, empresa, l);
            mm.addAttribute(RETURN, "Ajuste professor, processo iniciado, aguarde e confie");
            mm.addAttribute("log", l.toString());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/descontinuar/{valor}", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap descontinuar(@PathVariable String ctx,
                          @PathVariable String valor) {
        ModelMap mm = new ModelMap();
        try {
            ConfiguracaoSistemaService configuracaoSistemaService = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema descontinuar = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.DESCONTINUAR_TREINO);
            descontinuar.setValorAsBoolean(Boolean.valueOf(valor));
            configuracaoSistemaService.alterar(ctx, descontinuar);
            mm.addAttribute(RETURN, "pronto");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/ajust-import/{empresa}", method = {RequestMethod.GET}, produces = MediaType.TEXT_HTML_VALUE)
    public @ResponseBody
    String normalize(@PathVariable String ctx,
                     @PathVariable Integer empresa,
                     HttpServletRequest request) {
        try {
            InputStream is = getClass().getResourceAsStream("/br/com/pacto/util/resources/confia.txt");
            StringBuilder confia = new StringBuilder(new String(FileUtilities.obterBytesInputStream(is)));
            ConfiguracaoSistemaService cs = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            cs.normalizarImportacao(ctx, empresa);
            return confia.toString();
        } catch (Exception ex) {

            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "<div>erro</div>";
    }

    @RequestMapping(value = "{ctx}/fotosParaAtualizar", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap fotosParaAtualizar(@PathVariable String ctx, @RequestParam String dataHora) {
        ModelMap mm = new ModelMap();
        try {
            Date dataBase = Calendario.getDate(Calendario.MASC_DATAHORA, dataHora);
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(ClienteSinteticoService.class);
            List<ClienteSintetico> clienteSinteticos = clienteService.consultarFotosAtualizar(ctx, dataBase);
            List<String> urls = new ArrayList<String>();
            for (ClienteSintetico clienteSintetico : clienteSinteticos) {
                urls.add(Aplicacao.getUrlFoto(ctx, clienteSintetico.getCodigoPessoa(), true));
            }

            mm.addAttribute(RETURN, urls);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/corrigirSeries", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap corrigirSeries(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            SerieService s = (SerieService) UtilContext.getBean(SerieService.class);
            s.corrigirSerieZerada(ctx);
            mm.addAttribute(RETURN, "Corrigido");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/obterProdutoAvaliacao", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap obterProdutoAvaliacao(@PathVariable String ctx,
                                        @RequestParam String matricula) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico cliente = clienteSinteticoService.consultarPorMatricula(ctx, matricula);

            if (cliente == null) {
                throw new Exception("Aluno não encontrado");
            }

            ConfiguracaoSistema cfgProduto = cs.consultarPorTipo(ctx, ConfiguracoesEnum.PRODUTO_AVALIACAO);
            if (cfgProduto != null) {
                mm.addAttribute(RETURN, cfgProduto.getValorAsInteger());
            } else {
                mm.addAttribute(RETURN, 0);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Obter va lor do produto de avaliação",
                  notes = "Consulta o valor monetário do produto de avaliação física configurado para um aluno específico. " +
                          "Retorna o valor em reais ou 0.0 caso não esteja configurado para lançar produto de avaliação.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Valor do produto de avaliação obtido com sucesso", response = ExemploRespostaValorProdutoAvaliacao.class)
    })
    @RequestMapping(value = "{ctx}/obterValorProdutoAvaliacao", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap obterValorProdutoAvaliacao(@ApiParam(value = "Chave da empresa", defaultValue = "academia123")
                                       @PathVariable String ctx,
                                       @ApiParam(value = "Matrícula do aluno para consulta do valor", defaultValue = "2024001")
                                       @RequestParam String matricula) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico cliente = clienteSinteticoService.consultarPorMatricula(ctx, matricula);

            if (cliente == null) {
                throw new Exception("Aluno não encontrado");
            }

            ConfiguracaoSistema lancaProduto = cs.consultarPorTipo(ctx, ConfiguracoesEnum.LANCAR_PRODUTO_AVALIACAO);

            if (lancaProduto.getValorAsBoolean()) {
                ConfiguracaoSistema cfgProduto = cs.consultarPorTipo(ctx, ConfiguracoesEnum.PRODUTO_AVALIACAO);

                Integer codigoProduto = cfgProduto.getValorAsInteger();
                IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                String retorno = integracaoWS.obterValorProdutoCfgEmpresa(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, codigoProduto, cliente.getEmpresa());
                if (retorno.startsWith("ERRO")) {
                    throw new Exception(retorno);
                }
                mm.addAttribute(RETURN, new Double(retorno));
            } else {
                mm.addAttribute(RETURN, 0.0);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Obter URL do serviço de balança",
                  notes = "Retorna a URL configurada para o serviço de balança de bioimpedância.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "URL do serviço de balança obtida com sucesso", response = ExemploRespostaUrlServicoBalanca.class)
    })
    @RequestMapping(value = "{ctx}/urlservicobalanca", method = {RequestMethod.GET})
    public @ResponseBody
    ModelMap urlservicobalanca(@ApiParam(value = "Chave da empresa", defaultValue = "academia123")
                              @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute("url", Aplicacao.getProp(Aplicacao.urlsocketaux));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @ApiOperation(value = "Verificar se treino é independente",
                  notes = "Verifica se o sistema está configurado para funcionar de forma independente.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Status do treino independente obtido com sucesso", response = ExemploRespostaTreinoIndependente.class)
    })
    @RequestMapping(value = "{ctx}/treinoindependente", method = {RequestMethod.GET})
    public @ResponseBody
    ModelMap treinoindependente(@ApiParam(value = "Chave da empresa", defaultValue = "academia123")
                               @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute("treinoindependente", independente(ctx));
        } catch (Exception e) {
            mm.addAttribute("treinoindependente", false);
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, e);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/sincronizarEmpresas", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap sincronizarEmpresas(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            SincronizacaoEmpresaService sincronizacaoService = (SincronizacaoEmpresaService) UtilContext.getBean(SincronizacaoEmpresaService.class);
            mm.addAttribute(RETURN, sincronizacaoService.sincronizarAtivosEmpresas(ctx));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/sincronizarErro", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap sincronizarErro(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            SincronizacaoEmpresaService sincronizacaoService = (SincronizacaoEmpresaService) UtilContext.getBean(SincronizacaoEmpresaService.class);
            mm.addAttribute(RETURN, sincronizacaoService.sincronizarPorProblemaZW(ctx));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/sincronizacoes", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap sincronizacoes(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            SincronizacaoEmpresaService sincronizacaoService = (SincronizacaoEmpresaService) UtilContext.getBean(SincronizacaoEmpresaService.class);
            mm.addAttribute(RETURN, sincronizacaoService.ultimas(ctx));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/getconfig/bloqueio-mesmo-ambiente", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap getconfigBloquear(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            ConfiguracaoSistema bloqueio = cs.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_MESMO_AMBIENTE);
            mm.addAttribute(RETURN, bloqueio.getValorAsBoolean());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Atualizar dados de avaliação dos alunos",
                  notes = "Atualiza os resultados de evolução e dados de BI de avaliação física para todos os alunos de uma empresa.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Dados de avaliação dos alunos atualizados com sucesso", response = ExemploRespostaAtualizarAlunosAvaliacao.class)
    })
    @RequestMapping(value = "{ctx}/atualizar-alunos-avaliacao/{empresa}", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap atualizarAlunosAvaliacao(@ApiParam(value = "Chave da empresa", defaultValue = "academia123")
                                     @PathVariable String ctx,
                                     @ApiParam(value = "Código da empresa", defaultValue = "1")
                                     @PathVariable Integer empresa) {
        ModelMap mm = new ModelMap();
        try {
            List<ClienteSintetico> clientes = clienteSinteticoService.obterTodos(ctx, empresa);
            AvaliacaoFisicaService avaliacaoFisicaService = UtilContext.getBean(AvaliacaoFisicaService.class);
            for (ClienteSintetico cliente : clientes) {
                avaliacaoFisicaService.atualizarResultadoEvolucaoAluno(ctx, cliente);
            }
            BIAvaliacaoFisicaService biAvaliacaoFisicaService = UtilContext.getBean(BIAvaliacaoFisicaService.class);
            biAvaliacaoFisicaService.atualizarBase(ctx, empresa);
            mm.addAttribute("processo", "atualizado");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Gerenciar configurações do Memcached",
                  notes = "Permite habilitar, desabilitar ou consultar o status do serviço de cache Memcached.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Configurações do Memcached obtidas com sucesso", response = ExemploRespostaMemcached.class)
    })
    @RequestMapping(value = "memcached", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap memcached(@ApiParam(value = "Operação a ser realizada", allowableValues = "enable,disable", defaultValue = "enable")
                      @RequestParam(required = false) String op) {
        ModelMap mm = new ModelMap();
        try {
            CachedManagerInterfaceFacade memCachedService = UtilContext.getBean(CachedManagerInterfaceFacade.class);
            mm.addAttribute("ip", Aplicacao.getProp(Aplicacao.ipServidoresMemCached));
            if(op != null && op.equals("disable")){
                memCachedService.disable();
            } else  if(op != null && op.equals("enable")){
                memCachedService.enable();
            }
            mm.addAttribute("on", memCachedService.getMemcachedClient() != null);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @ApiOperation(value = "Limpar cache do Memcached",
                  notes = "Limpa todo o cache armazenado no serviço Memcached.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Cache do Memcached limpo com sucesso", response = ExemploRespostaClearMemcached.class)
    })
    @RequestMapping(value = "memcached/clear", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap clear() {
        ModelMap mm = new ModelMap();
        try {
            CachedManagerInterfaceFacade memCachedService = UtilContext.getBean(CachedManagerInterfaceFacade.class);
            memCachedService.clear();

        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @ApiOperation(value = "Recarregar indicadores de BI",
                  notes = "Recarrega os indicadores de Business Intelligence do dashboard.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Indicadores de BI recarregados com sucesso", response = ExemploRespostaReloadIndicadores.class)
    })
    @RequestMapping(value = "{ctx}/reload-indicadores", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap reloadIndicadores(@ApiParam(value = "Chave da empresa", defaultValue = "academia123")
                              @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            DashboardBIService dashboardBIService = UtilContext.getBean(DashboardBIService.class);
            mm.addAttribute("processo", dashboardBIService.reloadIndicadores(ctx));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/sincronizarProfessor/{empresa}", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap sincronizarProfessor(@PathVariable String ctx, @PathVariable Integer empresa) {
        ModelMap mm = new ModelMap();
        try {
            SincronizacaoEmpresaService sincronizacaoService = UtilContext.getBean(SincronizacaoEmpresaService.class);
            mm.addAttribute(RETURN, sincronizacaoService.sincronizarProfessoresEmpresa(ctx, empresa));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/emailteste", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap emailTeste(@PathVariable String ctx,
                        @RequestParam String email) {
        ModelMap mm = new ModelMap();
        try {
            final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            JSONObject configsEmail = integracaoWS.configsEmail(url);
            UteisEmail u = new UteisEmail();
            u.preencherConfiguracaoEmailPadrao(configsEmail, "Remetente Pacto teste");
            u.enviarEmail(email, "Pacto Teste", "mensagem de teste", "", "Avaliação Física");

            ConfigsEmail cfgEmail = new ConfigsEmail();
            cfgEmail.setLogin(configsEmail.getString("loginServidorSmtp"));
            cfgEmail.setSenha(configsEmail.getString("senhaServidorSmtp"));
            cfgEmail.setRemetente(configsEmail.getString("emailRemet"));
            cfgEmail.setEmail_padrao(configsEmail.getString("smtpPadrao"));
            cfgEmail.setConexao_segura(configsEmail.getBoolean("conexaoSegura"));
            cfgEmail.setIniciarTLS(configsEmail.getBoolean("iniciarTLS"));

            mm.addAttribute(RETURN, cfgEmail);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/create-unaccent", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap createUnaccent(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, processosService.createUnaccent(ctx));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/sincronizar-vinculos-aluno-zw-tw", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap sincronizarVinculosAluno(@PathVariable String ctx, @RequestParam int empresaZW, @RequestParam boolean vaiSincronizar) {
        ModelMap mm = new ModelMap();
        try {
            // vaiSincronizar = false para quando somente for analisar o retorno e true quando for para sincronizar
            sincronizarVinculosAlunoProfessorTW.start(ctx, empresaZW, mm, vaiSincronizar);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/atualizarAtividade", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap atualizarAtividade(@PathVariable String ctx, @RequestBody String content) {
        ModelMap mm = new ModelMap();
        try {
            processoAtualizarGIFsAtividadesService.start(ctx, content);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/atualizarAtividadeAlternativa", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap atualizarAtividadeAlternativa(@PathVariable String ctx, @RequestBody String content) {
        ModelMap mm = new ModelMap();
        try {
            processoAtualizarAtividadesAlternativasService.start(ctx, content);
            mm.addAttribute(RETURN, "Atividades atualizadas com sucesso!");
        } catch (PersistenceException e) {
            String mensagem = e.getMessage();
            if (e.getCause() instanceof ConstraintViolationException) {
                ConstraintViolationException constraintViolationException = (ConstraintViolationException) e.getCause();
                if (constraintViolationException.getCause() instanceof PSQLException) {
                    PSQLException psqlException = (PSQLException) constraintViolationException.getCause();
                    mensagem = psqlException.getLocalizedMessage();
                }
            }
            mm.addAttribute(STATUS_ERRO, mensagem);
        } catch (Exception ex) {
            if(ex.getMessage().contains("Erro ao atualizar atividade")) {
                mm.addAttribute(STATUS_ERRO, ex.getMessage());
            }
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/agenda-webservice", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap agendaWS() {
        ModelMap mm = new ModelMap();
        Aplicacao.setProp(Aplicacao.modoConsultaAgenda, "webservice");
        mm.addAttribute(RETURN, Aplicacao.getProp(Aplicacao.modoConsultaAgenda));
        return mm;
    }

    @RequestMapping(value = "/set-prop", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap setProp(@RequestParam String prop, @RequestParam String value ) {
        ModelMap mm = new ModelMap();
        Aplicacao.setProp(prop, value);
        mm.addAttribute(RETURN, prop + "="+value);
        return mm;
    }

    @RequestMapping(value = "/agenda-bd", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap agendaBD() {
        ModelMap mm = new ModelMap();
        Aplicacao.setProp(Aplicacao.modoConsultaAgenda, "consultaBD");
        mm.addAttribute(RETURN, Aplicacao.getProp(Aplicacao.modoConsultaAgenda));
        return mm;
    }

    @RequestMapping(value = "/modo-agenda", method = {RequestMethod.GET})
    public @ResponseBody
    ModelMap modoAgenda() {
        ModelMap mm = new ModelMap();
        mm.addAttribute(RETURN, Aplicacao.getProp(Aplicacao.modoConsultaAgenda));
        return mm;
    }

    @RequestMapping(value = "/setar-webservice-dashbi", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap setarDashBIWS() {
        ModelMap mm = new ModelMap();
        Aplicacao.setProp(Aplicacao.modoConsultaDashBI, "webservice");
        mm.addAttribute(RETURN, Aplicacao.getProp(Aplicacao.modoConsultaDashBI));
        return mm;
    }

    @RequestMapping(value = "/setar-consultabd-dashbi", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap setarDashBIBD() {
        ModelMap mm = new ModelMap();
        Aplicacao.setProp(Aplicacao.modoConsultaDashBI, "consultaBD");
        mm.addAttribute(RETURN, Aplicacao.getProp(Aplicacao.modoConsultaDashBI));
        return mm;
    }

    @RequestMapping(value = "/modo-bd-dashbi", method = {RequestMethod.GET})
    public @ResponseBody
    ModelMap modoDashBI() {
        ModelMap mm = new ModelMap();
        mm.addAttribute(RETURN, Aplicacao.getProp(Aplicacao.modoConsultaDashBI));
        return mm;
    }

    @RequestMapping(value = "/limpar-pool-zw", method = {RequestMethod.GET})
    public @ResponseBody
    ModelMap poolZW() {
        ModelMap mm = new ModelMap();
        ConexaoZWService service = UtilContext.getBean(ConexaoZWService.class);
        service.limparMapaPools();
        mm.addAttribute("pool", "vazio");
        return mm;
    }

    @RequestMapping(value = "/povoar-log/{chave}/{entidade}", method = {RequestMethod.PUT, RequestMethod.GET})
    public @ResponseBody
    ModelMap povoar(@PathVariable String chave,
                    @PathVariable String entidade, @RequestParam String inicio, @RequestParam String fim) {
        ModelMap mm = new ModelMap();
        try {
            povoadorLogService.povoar(chave, EntidadeLogEnum.valueOf(entidade), Uteis.getDate(inicio), Uteis.getDate(fim));
            mm.addAttribute(RETURN, "povoado");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        mm.addAttribute(RETURN, "povoado");
        return mm;
    }

    @RequestMapping(value = "/reverter-proj-usuario/{chave}", method = {RequestMethod.PUT, RequestMethod.GET})
    public @ResponseBody
    ModelMap reverterProjUsuario(@PathVariable String chave) {
        ModelMap mm = new ModelMap();
        try {
            empresaService.reverterAlteracoesProjetoUsuario(chave);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        mm.addAttribute(RETURN, "revertido");
        return mm;
    }

    @RequestMapping(value = "{ctx}/atualizar-configuracao-personal-empresa", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap atualizarConfiguracaoPersonalEmpresa(@PathVariable String ctx, @RequestBody String dadosConfig) {
        ModelMap mm = new ModelMap();
        try {
            configuracaoPersonalEmpresaService.atualizarCfgEmpresaV2(ctx, dadosConfig);
            mm.addAttribute(RETURN, "ok");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Obter configurações do aplicativo",
                  notes = "Retorna as configurações específicas do aplicativo móvel, incluindo nome e URL para emails.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Configurações do aplicativo obtidas com sucesso", response = ExemploRespostaConfiguracoesAplicativo.class)
    })
    @RequestMapping(value = "{ctx}/configuracao-aplicativo", method = {RequestMethod.GET})
    public @ResponseBody
    ModelMap configuracoesAplicativo(@ApiParam(value = "Chave da empresa", defaultValue = "academia123")
                                    @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            JSONObject json = new JSONObject();
            json.put("nomeAppParaEmail", SuperControle.getNomeAppParaEmail(ctx));
            json.put("appUrlEmail", SuperControle.getAppUrlEmail(ctx));
            mm.addAttribute(RETURN, json.toString());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/sincronizar-acesso-treinos-finalizados", method = {RequestMethod.POST})
    public @ResponseBody
    ModelMap sincronizarAcessoTreinosFinalizados(@PathVariable String ctx, @RequestParam("dataInicio") String dataInicio,
                                                 @RequestParam("dataFim") String dataFim) {
        ModelMap mm = new ModelMap();
        try {
            processoSincronizarTreinoAcesso.start(ctx, dataInicio, dataFim,mm);
            mm.addAttribute(RETURN, "ok");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ConfigJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Salvar integração com Selfloops",
                  notes = "Salva o código de integração com a plataforma Selfloops para uma empresa específica.",
                  tags = "Configurações Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Integração Selfloops salva com sucesso", response = String.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/salvar-code-integracao-selfloops", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> salvarIntegracoesSelfloops(
            @ApiParam(value = "Chave da empresa", defaultValue = "academia123")
            @PathVariable("ctx") String chave,
            @ApiParam(value = "Código de integração do Selfloops", defaultValue = "SLF123456")
            @RequestParam String code,
            @ApiParam(value = "Código da empresa", defaultValue = "1")
            @RequestParam Integer empresa) {
        try {
            SelfloopsDTO selfloopsDTO = new SelfloopsDTO();
            selfloopsDTO.setCode(code);
            selfloopsDTO.setEmpresa(empresa);
            selfloopsConfiguracoesService.salvarIntegracoesSelfloops(chave, Collections.singletonList(selfloopsDTO));
            return ResponseEntityFactory.ok("OK");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
