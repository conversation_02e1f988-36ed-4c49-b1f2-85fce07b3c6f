package br.com.pacto.controller.json.configDisponibilidade;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.agendamento.DisponibilidadeConfigGeradoraDTO;
import br.com.pacto.controller.json.agendamento.FiltroDisponibilidadeDTO;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.DisponibilidadeService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.configDisponibilidade.ExemploRespostaConfigDisponibilidade;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * Created by paulo 30/08/2019
 */
@Api(tags = "Configurar Disponibilidades")
@Controller
@RequestMapping("/psec/disponibilidade-config")
public class ConfigDisponibilidadeController {

    private DisponibilidadeService disponibilidadeService;

    @Autowired
    public ConfigDisponibilidadeController(DisponibilidadeService disponibilidadeService) {
        Assert.notNull(disponibilidadeService, "O serviço de disponibilidade não foi injetado corretamente");
        this.disponibilidadeService = disponibilidadeService;
    }

    @ApiOperation(
            value = "Criar nova configuração de disponibilidade",
            notes = "Cria uma nova configuração de disponibilidade no sistema de agendamento. " +
                    "Define horários, professores, ambientes e regras de validação para agendamentos. " +
                    "<br/><br/><strong>Permissão necessária:</strong> AGENDA_DISPONIBILIDADE " +
                    "<br/><strong>Autenticação:</strong> Requer autenticação via token de sessão " +
                    "<br/><strong>Retorno:</strong> Retorna os dados da configuração de disponibilidade criada",
            tags = "Configurar Disponibilidades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)",response = ExemploRespostaConfigDisponibilidade.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA_DISPONIBILIDADE)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarDisponibilidade(
            @ApiParam(value = "Dados da configuração de disponibilidade a ser criada", required = true)
            @RequestBody DisponibilidadeConfigGeradoraDTO disponibilidadeConfig) {

        try {
            return ResponseEntityFactory.ok(disponibilidadeService.criarAlterarDisponibilidadeConfig(disponibilidadeConfig));
        } catch (ServiceException e) {
            Logger.getLogger(ConfigDisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar disponibilidade", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Editar configuração de disponibilidade existente",
            notes = "Atualiza uma configuração de disponibilidade existente no sistema. " +
                    "Permite modificar horários, professores, ambientes e regras de validação.",
            tags = "Configurar Disponibilidades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaConfigDisponibilidade.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA_DISPONIBILIDADE)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editarDisponibilidade(
            @ApiParam(value = "ID da configuração de disponibilidade a ser editada", defaultValue = "123", required = true)
            @PathVariable(value = "id") Integer disponibilidadeId,
            @ApiParam(value = "Dados atualizados da configuração de disponibilidade", required = true)
            @RequestBody DisponibilidadeConfigGeradoraDTO disponibilidadeConfig) {

        try {
            disponibilidadeConfig.setId(disponibilidadeId);
            return ResponseEntityFactory.ok(disponibilidadeService.criarAlterarDisponibilidadeConfig(disponibilidadeConfig));
        } catch (ServiceException e) {
            Logger.getLogger(ConfigDisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar disponibilidade", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Remover configuração de disponibilidade",
            notes = "Remove uma configuração de disponibilidade do sistema. " +
                    "A remoção afeta apenas a configuração, não os agendamentos já realizados.",
            tags = "Configurar Disponibilidades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA_DISPONIBILIDADE)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerDisponibilidade(
            @ApiParam(value = "Código da empresa", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId") Integer empresaId,
            @ApiParam(value = "ID da configuração de disponibilidade a ser removida", defaultValue = "123", required = true)
            @PathVariable(value = "id") Integer disponibilidadeId) {

        try {
            disponibilidadeService.removerDisponibilidade(empresaId, disponibilidadeId, false);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ConfigDisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover disponibilidade geradora", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar configurações de disponibilidade com paginação",
            notes = "Lista todas as configurações de disponibilidade da empresa com suporte a filtros e paginação. " +
                    "Permite filtrar por professor, ambiente, tipo de evento e outros critérios.",
            tags = "Configurar Disponibilidades"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada", dataType = "int", paramType = "query", defaultValue = "0"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página", dataType = "int", paramType = "query", defaultValue = "20"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC' (ex: 'professor,ASC'). " +
                    "Campos disponíveis: <br/>" +
                    "<b>professor</b> - Nome do professor<br/>" +
                    "<b>ambiente</b> - Nome do ambiente<br/>" +
                    "<b>tipoEvento</b> - Tipo de evento",
                    dataType = "string", paramType = "query", defaultValue = "professor,ASC")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaConfigDisponibilidade.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA_DISPONIBILIDADE)
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarDisponibilidade(
            @ApiParam(value = "Código da empresa para consulta das configurações", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId") Integer empresaId,
            @ApiParam(value = "Filtros de pesquisa em formato JSON. Campos disponíveis: " +
                    "professorId (ID do professor), ambienteId (ID do ambiente), " +
                    "tipoEventoId (ID do tipo de evento), dataInicio, dataFim",
                    defaultValue = "{\"professorId\":123,\"ambienteId\":456}", required = true)
            @RequestParam(value = "filters") JSONObject filters,
            @ApiIgnore
            PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroDisponibilidadeDTO filtros = new FiltroDisponibilidadeDTO(filters);
            return ResponseEntityFactory.ok(disponibilidadeService.todasDisponibilidades(empresaId, paginadorDTO, filtros), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ConfigDisponibilidadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar disponibilidades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
