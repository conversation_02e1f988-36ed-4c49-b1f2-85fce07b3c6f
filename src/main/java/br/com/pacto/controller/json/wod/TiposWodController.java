package br.com.pacto.controller.json.wod;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.wod.FiltroTipoWodJSON;
import br.com.pacto.bean.wod.TipoWodTO;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.tipowod.TipoWodService;
import br.com.pacto.swagger.respostas.tiposWod.ExemploRespostaListTipoWodResponseTO;
import br.com.pacto.swagger.respostas.tiposWod.ExemploRespostaTipoWodResponseTO;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 21/08/2018.
 */
@Api(tags = "Tipos WOD")
@Controller
@RequestMapping("/psec/tipos-wod")
public class TiposWodController {

    private final TipoWodService tipoWodService;

    @Autowired
    public TiposWodController(TipoWodService tipoWodService) {
        Assert.notNull(tipoWodService, "O serviço de tipos de wod não foi injetado corretamente");
        this.tipoWodService = tipoWodService;
    }

    @ApiOperation(
            value = "Consultar tipos de WOD (Workout of the Day)",
            notes = "Consulta todos os tipos de WOD (Workout of the Day) cadastrados no sistema com suporte a filtros de busca. " +
                    "Permite filtrar por nome do tipo de WOD para facilitar a localização de tipos específicos.",
            tags = "Tipos WOD"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de tipos de WOD)", response = ExemploRespostaListTipoWodResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTiposWod(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.<br/><br/>" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong><br/><br/>" +
                    "<strong>Filtros disponíveis:</strong><br/>" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do tipo de WOD.<br/>" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).",
                    defaultValue = "{\"quicksearchValue\":\"AMRAP\", \"quicksearchFields\":[\"nome\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros) throws JSONException {
        try {
            FiltroTipoWodJSON filtroTipoWodJSON = new FiltroTipoWodJSON(filtros);
            return ResponseEntityFactory.ok(tipoWodService.listarTiposWod(filtroTipoWodJSON));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os tipos de wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar tipo de WOD (Workout of the Day) por ID",
            notes = "Consulta um tipo de WOD específico através do seu ID único. " +
                    "Retorna todas as informações detalhadas do tipo de WOD incluindo configurações de ranking e ordenamentos.",
            tags = "Tipos WOD"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Detalhes do tipo de WOD)", response = ExemploRespostaTipoWodResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarTiposWod(
            @ApiParam(value = "ID único do tipo de WOD a ser consultado", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(tipoWodService.buscarTiposWod(id));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar o tipo de wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Cadastrar novo tipo de WOD (Workout of the Day)",
            notes = "Cadastra um novo tipo de WOD no sistema com suas configurações de ranking e ordenamentos. " +
                    "Permite definir se o tipo usará ranking, se profissionais ficam em primeiro lugar e quais ordenamentos serão aplicados.",
            tags = "Tipos WOD"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Tipo de WOD cadastrado com sucesso)", response = ExemploRespostaTipoWodResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarTipoWod(
            @ApiParam(value = "Dados completos do tipo de WOD para cadastro", required = true)
            @RequestBody TipoWodTO tipoWodTO) {
        try {
            return ResponseEntityFactory.ok(tipoWodService.cadastrarTipoWod(tipoWodTO));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar tipo de wod", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Atualizar tipo de WOD (Workout of the Day) existente",
            notes = "Atualiza as informações de um tipo de WOD existente incluindo nome, configurações de ranking e ordenamentos. " +
                    "Todos os campos enviados serão atualizados conforme os dados fornecidos.",
            tags = "Tipos WOD"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Tipo de WOD atualizado com sucesso)", response = ExemploRespostaTipoWodResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarTipoWod(
            @ApiParam(value = "ID único do tipo de WOD a ser atualizado", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Dados atualizados do tipo de WOD", required = true)
            @RequestBody TipoWodTO tipoWodTO) {
        try {
            tipoWodTO.setId(id);
            return ResponseEntityFactory.ok(tipoWodService.atualizarTipoWod(tipoWodTO));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar tipo de wod", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Remover tipo de WOD (Workout of the Day) ",
            notes = "Remove um tipo de WOD do sistema através do seu ID único. " +
                    "Esta operação é irreversível e o tipo de WOD será permanentemente excluído do sistema.",
            tags = "Tipos WOD"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Tipo de WOD removido com sucesso)", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAtividade(
            @ApiParam(value = "ID único do tipo de WOD a ser removido", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id) {
        try {
            tipoWodService.removerTipoWod(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir tipo de wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
