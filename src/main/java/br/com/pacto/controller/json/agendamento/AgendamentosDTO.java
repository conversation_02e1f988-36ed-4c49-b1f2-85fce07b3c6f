package br.com.pacto.controller.json.agendamento;

import java.util.List;

public class AgendamentosDTO {

    private Integer dia;
    private Integer ano;
    private Integer mes;
    private List<AgendamentoEventoDTO> agendamentos;

    public Integer getDia() {
        return dia;
    }

    public void setDia(Integer dia) {
        this.dia = dia;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public List<AgendamentoEventoDTO> getAgendamentos() {
        return agendamentos;
    }

    public void setAgendamentos(List<AgendamentoEventoDTO> agendamentos) {
        this.agendamentos = agendamentos;
    }
}
