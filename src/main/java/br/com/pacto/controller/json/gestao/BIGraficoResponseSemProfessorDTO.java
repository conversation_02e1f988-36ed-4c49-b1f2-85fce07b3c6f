package br.com.pacto.controller.json.gestao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 18/10/2019
 */
@ApiModel(description = "Dados de indicadores de gráfico BI para um período específico sem separação por professor")
public class BIGraficoResponseSemProfessorDTO {
    @ApiModelProperty(value = "Período de referência dos dados (formato: MM/yyyy)", example = "01/2024")
    private String periodo;

    @ApiModelProperty(value = "Valores dos indicadores para o período especificado")
    private BIGraficoResponseIndicadoresDTO valor;

     public BIGraficoResponseSemProfessorDTO( ){

     }

    public String getPeriodo() {
        return periodo;
    }

    public void setPeriodo(String periodo) {
        this.periodo = periodo;
    }

    public BIGraficoResponseIndicadoresDTO getValor() {
        return valor;
    }

    public void setValor(BIGraficoResponseIndicadoresDTO valor) {
        this.valor = valor;
    }
}
