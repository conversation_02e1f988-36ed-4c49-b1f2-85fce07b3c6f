package br.com.pacto.controller.json.locacao;

import br.com.pacto.bean.usuario.Usuario;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados básicos de um usuário responsável por horários de locação.")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioTO {

    @ApiModelProperty(value = "Código único do usuário.", example = "10")
    private Integer codigo;

    @ApiModelProperty(value = "Nome do usuário.", example = "João Silva")
    private String nome;

    public UsuarioTO() {}

    public UsuarioTO(Usuario usuario) {
        this.codigo = usuario.getCodigo();
        this.nome = usuario.getProfessor().getNome();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
