package br.com.pacto.controller.json.aulaDia;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Mapa de equipamentos/aparelhos vinculados à turma")
public class TurmaMapaEquipamentoAparelhoDTO {
    @ApiModelProperty(value = "Código identificador do mapa de equipamento/aparelho", example = "1")
    private Integer codigo;
    @ApiModelProperty(value = "Código do aparelho de treino", example = "100")
    private Integer codigo_aparelhotreino;
    @ApiModelProperty(value = "Código identificador da turma", example = "50")
    private Integer turma;
    @ApiModelProperty(value = "Descrição ou nome do mapa de equipamento", example = "Mapa de bicicletas Spinning")
    private String mapaequipamento;

    public TurmaMapaEquipamentoAparelhoDTO() { }

    public TurmaMapaEquipamentoAparelhoDTO(Integer codigo, Integer codigo_aparelhotreino, Integer turma, String mapaequipamento) {
        this.codigo = codigo;
        this.codigo_aparelhotreino = codigo_aparelhotreino;
        this.turma = turma;
        this.mapaequipamento = mapaequipamento;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigo_aparelhotreino() {
        return codigo_aparelhotreino;
    }

    public void setCodigo_aparelhotreino(Integer codigo_aparelhotreino) {
        this.codigo_aparelhotreino = codigo_aparelhotreino;
    }

    public Integer getTurma() {
        return turma;
    }

    public void setTurma(Integer turma) {
        this.turma = turma;
    }

    public String getMapaequipamento() {
        return mapaequipamento;
    }

    public void setMapaequipamento(String mapaequipamento) {
        this.mapaequipamento = mapaequipamento;
    }

}
