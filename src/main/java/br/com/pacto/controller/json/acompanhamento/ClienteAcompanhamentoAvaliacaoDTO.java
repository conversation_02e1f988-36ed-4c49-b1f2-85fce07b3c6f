package br.com.pacto.controller.json.acompanhamento;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

@ApiModel(description = "Dados para avaliação do acompanhamento de treino pelo aluno")
public class ClienteAcompanhamentoAvaliacaoDTO implements Serializable {

    @ApiModelProperty(value = "Código identificador do acompanhamento que está sendo avaliado", required = true, example = "1234")
    private Integer acompanhamentoId;

    @ApiModelProperty(value = "Código identificador do cliente que está fazendo a avaliação", required = true, example = "567")
    private Integer clienteSinteticoId;

    @ApiModelProperty(value = "Nota da avaliação do acompanhamento (escala de 1 a 5)", required = true, example = "4")
    private Integer nota;

    @ApiModelProperty(value = "Comentário opcional sobre o acompanhamento recebido", example = "<PERSON><PERSON><PERSON> acompanhamento, professor muito atencioso!")
    private String comentario;

    public ClienteAcompanhamentoAvaliacaoDTO() {
    }

    public Integer getAcompanhamentoId() {
        return acompanhamentoId;
    }

    public void setAcompanhamentoId(Integer acompanhamentoId) {
        this.acompanhamentoId = acompanhamentoId;
    }

    public Integer getClienteSinteticoId() {
        return clienteSinteticoId;
    }

    public void setClienteSinteticoId(Integer clienteSinteticoId) {
        this.clienteSinteticoId = clienteSinteticoId;
    }

    public Integer getNota() {
        return nota;
    }

    public void setNota(Integer nota) {
        this.nota = nota;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }
}
