package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.agenda.ConfigDisponibilidade;

public class AgendamentoDisponibilidadeDTO {

    private Integer id;
    private Long dia;
    private Integer professorId;
    private Integer tipoAgendamentoId;
    private Integer horarioInicial; //Informações em minutos
    private Integer horarioFinal; //Informações em minutos
    private DisponibilidadeConfigDTO disponibilidadeConfigGeradora;

    public AgendamentoDisponibilidadeDTO() {

    }

    public AgendamentoDisponibilidadeDTO(Agendamento agendamento, ConfigDisponibilidade configDisponibilidade, Boolean treinoIndependente) {
        this.id = agendamento.getCodigo();
        this.dia = agendamento.getInicio().getTime();
        this.professorId = agendamento.getProfessor().getCodigo();
        this.tipoAgendamentoId = agendamento.getTipoEvento().getCodigo();
        this.horarioInicial = agendamento.getMinutosInicio();
        this.horarioFinal = agendamento.getMinutosFim();
//        this.disponibilidadeConfigGeradora = configDisponibilidade == null ?
//                new DisponibilidadeConfigDTO(agendamento, treinoIndependente) : configDisponibilidade.toDTO(treinoIndependente);
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getDia() {
        return dia;
    }

    public void setDia(Long dia) {
        this.dia = dia;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public Integer getTipoAgendamentoId() {
        return tipoAgendamentoId;
    }

    public void setTipoAgendamentoId(Integer tipoAgendamentoId) {
        this.tipoAgendamentoId = tipoAgendamentoId;
    }

    public Integer getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(Integer horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public Integer getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(Integer horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public DisponibilidadeConfigDTO getDisponibilidadeConfigGeradora() {
        return disponibilidadeConfigGeradora;
    }

    public void setDisponibilidadeConfigGeradora(DisponibilidadeConfigDTO disponibilidadeConfigGeradora) {
        this.disponibilidadeConfigGeradora = disponibilidadeConfigGeradora;
    }
}
