package br.com.pacto.controller.json.locacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Enumeração que representa os tipos de horário de locação.")
public enum TipoHorarioLocacaoEnum {

    @ApiModelProperty(value = "Locação com horário livre")
    LIVRE(0, "Livre"),
    @ApiModelProperty(value = "Locação com horário do tipo Play")
    PLAY(1, "Play"),
    @ApiModelProperty(value = "Locação com horário pré-definido")
    PRE_DEFINIDO(2, "Pré-definido")
    ;

    private Integer codigo;
    private String descricao;

    TipoHorarioLocacaoEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static TipoHorarioLocacaoEnum getByCodigo(Integer codigo) {
        for (TipoHorarioLocacaoEnum tipoHorarioLocacao: TipoHorarioLocacaoEnum.values()) {
            if (tipoHorarioLocacao.getCodigo().equals(codigo)) {
                return tipoHorarioLocacao;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
