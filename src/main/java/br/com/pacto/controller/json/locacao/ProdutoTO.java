package br.com.pacto.controller.json.locacao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados de um produto associado a uma locação.")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProdutoTO {

    @ApiModelProperty(value = "Código único do produto.", example = "10")
    private Integer codigo;

    @ApiModelProperty(value = "Descrição do produto.", example = "Locação de Quadra")
    private String descricao;

    @ApiModelProperty(value = "Valor final do produto.", example = "150.0")
    private Double valorFinal;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = valorFinal;
    }
}
