package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Fila de espera associada a um horário de turma e aluno")
public class FilaDeEsperaDTO extends SuperJSON {

    @ApiModelProperty(value = "Código do horário da turma", example = "12345")
    private Integer codigoHorarioTurma;

    @ApiModelProperty(value = "Dia do horário da fila (Formato: yyyyMMdd)", example = "20250610")
    private String dia;

    @ApiModelProperty(value = "Código do aluno", example = "789")
    private Integer codigoAluno;

    @ApiModelProperty(value = "Código do passivo", example = "1")
    private Integer passivo;

    @ApiModelProperty(value = "Matrícula do aluno", example = "2021")
    private Integer matricula;

    @ApiModelProperty(value = "Nome do aluno", example = "<PERSON>")
    private String nomeAluno;

    @ApiModelProperty(value = "Situação do aluno.<br/> <strong>Valores disponíveis</strong>" +
            "<ul>" +
            "- AT - Ativo\n" +
            "- IN - Inativo\n" +
            "- VI - Visitante\n" +
            "- TR - Trancado\n" +
            "- AE - Atestado\n" +
            "- CA - Cancelado\n" +
            "- CR - Carência\n" +
            "- DE - Desistente\n" +
            "- VE - Vencido\n" +
            "- OU - Outros\n" +
            "</ul>", example = "AT", allowableValues = "AT,IN,VI,TR,AE,CA,CR,DE,VE,OU")
    private String situacaoAluno;

    @ApiModelProperty(value = "Situação do contrato do aluno.<br/> Valores disponíveis" +
            "<ul>" +
            "- NO - Normal\n" +
            "- AV - A vencer\n" +
            "- ATM - Atestado médico\n" +
            "- CR - Carência\n" +
            "- CA - Cancelado\n" +
            "- DE - Desistente\n" +
            "- VE - Contrato Inativo / Vencido\n" +
            "- TV - Contrato Trancado / Vencido\n" +
            "- DI - Contrato de diária\n" +
            "- PE - Free Pass\n" +
            "- AA - Aula Avulsa\n" +
            "</ul>", example = "NO", allowableValues = "NO, AV, ATM, CR, CA, DE, VE, TV, DI, PE, AA")
    private String situacaoContrato;

    @ApiModelProperty(value = "Código da fila de espera", example = "9988")
    private Integer codigoFila;

    @ApiModelProperty(value = "Ordem do aluno na fila", example = "1")
    private Integer ordem;

    @ApiModelProperty(value = "Situação de vinculo de aula do cliente.\n\n" +
            "<strong>Valores disponíveis</strong>" +
            "- 1 - MATRICULADO\n" +
            "- 2 - REPOSICAO\n" +
            "- 3 - DESMARCADO\n" +
            "- 4 - AULA_EXPERIMENTAL\n" +
            "- 5 - DIARIA\n" +
            "- 6 - DIARIA_GYMPASS\n" +
            "- 7 - DIARIA_TOTALPASS\n" +
            "- 8 - MARCACAO\n" +
            "- 9 - VISITANTE\n" +
            "- 10 - DEPENDENTE\n" +
            "- 11 - DESAFIO\n" +
            "- 12 - INTEGRACAO\n" +
            "- 13 - PARTICIPANTE_FIXO\n" +
            "- 14 - ESPERA", example = "MATRICULADO", allowableValues = " MATRICULADO,REPOSICAO,DESMARCADO,AULA_EXPERIMENTAL,DIARIA,DIARIA_GYMPASS,DIARIA_TOTALPASS,MARCACAO,VISITANTE,DEPENDENTE,DESAFIO,INTEGRACAO,PARTICIPANTE_FIXO, ESPERA")
    private String vinculoComAula;

    @ApiModelProperty(value = "Código do usuário que registrou", example = "501")
    private Integer codigoUsuario;

    @ApiModelProperty(value = "Código da origem do sistema.\n\n" +
            "<strong>Valores disponíveis</strong>" +
            "- 1 - ZW (ZillyonWeb)" +
            "- 2 - AULA_CHEIA (Aula Cheia)" +
            "- 3 - TREINO (Treino)" +
            "- 4 - APP_TREINO (Pacto Treino)" +
            "- 5 - APP_PROFESSOR (Aplicativo Professor)" +
            "- 6 - AUTO_ATENDIMENTO (Autoatendimento)" +
            "- 7 - SITE (Site Vendas)" +
            "- 8 - BUZZLEAD (Buzz Leads)" +
            "- 9 - VENDAS_ONLINE_2 (Vendas 2.0)" +
            "- 10 - APP_CONSULTOR (App do consultor)" +
            "- 11 - BOOKING_GYMPASS (Booking Gympass)" +
            "- 12 - FILA_ESPERA (Fila de espera)" +
            "- 13 - IMPORTACAO_API (Importação API)" +
            "- 14 - HUBSPOT (Hubspot Lead)" +
            "- 15 - CRM_META_DIARIA (CRM Meta Diaria)" +
            "- 16 - APP_FLOW (Pacto Flow)" +
            "- 17 - NOVA_TELA_NEGOCIACAO (Nova Tela de Negociação)",
            example = "3")
    private Integer origemSistema;
    private boolean parqPositivo;

    public Integer getCodigoHorarioTurma() {
        return codigoHorarioTurma;
    }

    public void setCodigoHorarioTurma(Integer codigoHorarioTurma) {
        this.codigoHorarioTurma = codigoHorarioTurma;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public Integer getCodigoAluno() {
        return codigoAluno;
    }

    public void setCodigoAluno(Integer codigoAluno) {
        this.codigoAluno = codigoAluno;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(String situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public Integer getCodigoFila() {
        return codigoFila;
    }

    public void setCodigoFila(Integer codigoFila) {
        this.codigoFila = codigoFila;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getVinculoComAula() {
        return vinculoComAula;
    }

    public void setVinculoComAula(String vinculoComAula) {
        this.vinculoComAula = vinculoComAula;
    }

    public Integer getCodigoUsuario() {
        return codigoUsuario;
    }

    public void setCodigoUsuario(Integer codigoUsuario) {
        this.codigoUsuario = codigoUsuario;
    }

    public Integer getOrigemSistema() {
        return origemSistema;
    }

    public void setOrigemSistema(Integer origemSistema) {
        this.origemSistema = origemSistema;
    }

    public Integer getPassivo() {
        return passivo;
    }

    public void setPassivo(Integer passivo) {
        this.passivo = passivo;
    }

    public boolean isParqPositivo() { return parqPositivo; }

    public void setParqPositivo(boolean parqPositivo) { this.parqPositivo = parqPositivo; }

}
