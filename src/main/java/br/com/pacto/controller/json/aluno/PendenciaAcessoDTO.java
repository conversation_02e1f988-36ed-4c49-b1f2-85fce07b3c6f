package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Representa uma pendência relacionada ao acesso do cliente.")
public class PendenciaAcessoDTO {

    @ApiModelProperty(
            value = "Chave identificadora da pendência.",
            example = "parq-assinado"
    )
    private String chave;

    @ApiModelProperty(
            value = "Descrição detalhada da pendência.",
            example = "Cliente não assinou o PAR-Q."
    )
    private String descricao;
    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
