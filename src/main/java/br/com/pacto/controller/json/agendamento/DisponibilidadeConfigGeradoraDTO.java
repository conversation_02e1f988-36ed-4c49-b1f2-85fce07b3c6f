package br.com.pacto.controller.json.agendamento;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Dados para criação/edição de configuração de disponibilidade")
public class DisponibilidadeConfigGeradoraDTO {

    @ApiModelProperty(value = "ID da configuração (para edição)", example = "123")
    private Integer id;

    @ApiModelProperty(value = "Data do dia em timestamp (milissegundos)", example = "1718467200000")
    private Long dia;

    @ApiModelProperty(value = "ID do professor responsável", example = "456")
    private Integer professorId;

    @ApiModelProperty(value = "ID do tipo de agendamento", example = "789")
    private Integer tipoAgendamentoId;

    @ApiModelProperty(value = "Horário inicial em minutos (ex: 480 = 08:00)", example = "480")
    private Integer horarioInicial; //informações em minutos

    @ApiModelProperty(value = "Horário final em minutos (ex: 540 = 09:00)", example = "540")
    private Integer horarioFinal; //informações em minutos

    @ApiModelProperty(value = "Lista de dias da semana para repetir. Valores possíveis: DOMINGO, SEGUNDA, TERCA, QUARTA, QUINTA, SEXTA, SABADO")
    private List<AgendaDiaSemana> repetirDias;

    @ApiModelProperty(value = "Data limite para repetição em timestamp (milissegundos)", example = "1735689599000")
    private Long repetirDataLimite;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getDia() {
        return dia;
    }

    public void setDia(Long dia) {
        this.dia = dia;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public Integer getTipoAgendamentoId() {
        return tipoAgendamentoId;
    }

    public void setTipoAgendamentoId(Integer tipoAgendamentoId) {
        this.tipoAgendamentoId = tipoAgendamentoId;
    }

    public Integer getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(Integer horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public Integer getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(Integer horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public List<AgendaDiaSemana> getRepetirDias() {
        return repetirDias;
    }

    public void setRepetirDias(List<AgendaDiaSemana> repetirDias) {
        this.repetirDias = repetirDias;
    }

    public Long getRepetirDataLimite() {
        return repetirDataLimite;
    }

    public void setRepetirDataLimite(Long repetirDataLimite) {
        this.repetirDataLimite = repetirDataLimite;
    }
}
