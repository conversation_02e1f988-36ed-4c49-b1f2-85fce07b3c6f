package br.com.pacto.controller.json.selfloops;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Objeto que representa uma equipe/time do Selfloops")
public class TeamsSelfloopsDTO {

    @ApiModelProperty(value = "ID único da equipe no Selfloops", example = "TEAM_001")
    private String id;

    @ApiModelProperty(value = "Nome curto da equipe", example = "Elite Team")
    private String name;

    @ApiModelProperty(value = "Nome completo da equipe", example = "Academia Elite CrossFit Team")
    private String fullname;

    @ApiModelProperty(value = "Descrição completa da equipe", example = "Equipe de alta performance para atletas de CrossFit")
    private String full_descrition;

    public TeamsSelfloopsDTO() {}

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getFull_descrition() {
        return full_descrition;
    }

    public void setFull_descrition(String full_descrition) {
        this.full_descrition = full_descrition;
    }
}
