package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.anamnese.Anamnese;
import br.com.pacto.bean.anamnese.RespostaCliente;
import br.com.pacto.bean.avaliacao.*;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.empresa.IdiomaBancoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.ConfiguracaoController;
import br.com.pacto.dao.intf.avaliacao.AvaliacaoFisicaDao;
import br.com.pacto.dao.intf.avaliacao.ParQDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.anamnese.AnamneseService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.avaliacao.EvolucaoFisicaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.ExemploRespostaString;
import br.com.pacto.swagger.respostas.avaliacao.*;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import io.swagger.annotations.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.ServletContextAware;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.pacto.controller.json.base.SuperControle.STATUS_ERRO;


/**
 * Created by paulo 09/11/2018
 */
@Controller
@RequestMapping("/psec/avaliacoes-fisica")
public class AvaliacaoFisicaController implements ServletContextAware {

    private final AvaliacaoFisicaService avaliacaoFisicaService;
    private final ClienteSinteticoService clienteSinteticoService;
    private final UsuarioService usuarioService;
    private final SessaoService sessaoService;
    private final EvolucaoFisicaService evolucaoFisicaService;
    private ServletContext sc;
    @Autowired
    private AvaliacaoFisicaDao avaliacaoFisicaDao;
    @Autowired
    private ClienteSinteticoService clienteService;

    @Autowired
    private ParQDao parQDao;

    @Autowired
    private AnamneseService anamneseService;

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.sc = servletContext;
    }

    @Autowired
    public AvaliacaoFisicaController(AvaliacaoFisicaService avaliacaoFisicaService, ClienteSinteticoService clienteSinteticoService,
                                     UsuarioService usuarioService,
                                     SessaoService sessaoService, EvolucaoFisicaService evolucaoFisicaService) {
        this.evolucaoFisicaService = evolucaoFisicaService;
        Assert.notNull(usuarioService, "O serviço de usuario não foi injetado corretamente");
        Assert.notNull(avaliacaoFisicaService, "O serviço de avaliação física não foi injetado corretamente");
        Assert.notNull(sessaoService, "O serviço de sessaoService não foi injetado corretamente");
        Assert.notNull(clienteSinteticoService, "O serviço de clienteSinteticoService não foi injetado corretamente");
        this.avaliacaoFisicaService = avaliacaoFisicaService;
        this.clienteSinteticoService = clienteSinteticoService;
        this.sessaoService = sessaoService;
        this.usuarioService = usuarioService;
    }

    @ApiOperation(
            value = "Consultar catálogo de alunos de uma avaliação física",
            notes = "Consulta o catálogo de alunos de uma avaliação física",
            tags = {"Avaliação Física"}
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida).", response = ExemploRespostaListAvaliacaoCatalogoAlunoDTO.class)
    )
    @ResponseBody
    @RequestMapping(value = "/alunos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterCatalogoAlunos(@ApiParam(value = "Filtra os alunos pelo nome", defaultValue = "Lucas")
                                                                   @RequestParam(value = "nome", required = false) String filtroNome,
                                                                   @ApiParam(value = "Cóidgo da empresa que os alunos estão vinculados", defaultValue = "1")
                                                                   @RequestHeader(value = "empresaId") Integer empresaId,
                                                                   HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterCatalogoAlunosAvaliacaoFisica(request, filtroNome, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar catalogar os alunos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Criar nova avaliação física para o aluno",
            notes = "Cria uma nova avaliação física completa para o aluno especificado, incluindo anamnese, medidas antropométricas, testes físicos e definição de metas. O sistema calcula automaticamente os resultados baseados nos protocolos selecionados e gera as classificações de acordo com idade e sexo do aluno.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Avaliação física criada com sucesso)", response = ExemploRespostaAvaliacaoFisicaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos/{id}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EnvelopeRespostaDTO> salvarAvaliacaoFisica(
            @ApiParam(value = "Identificador único do aluno para o qual será criada a avaliação física", defaultValue = "12345", required = true)
            @PathVariable(value = "id") Integer id,
            @ApiParam(value = "Dados completos da avaliação física a ser criada, incluindo anamnese, medidas e testes", required = true)
            @RequestBody AvaliacaoFisicaDTOUpdate avaliacaoFisica) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.inserir(avaliacaoFisica, id));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar a avaliação física", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar avaliação física existente",
            notes = "Atualiza os dados de uma avaliação física já existente, permitindo modificar medidas, resultados de testes, metas e recomendações. O sistema recalcula automaticamente os resultados baseados nas alterações realizadas.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Avaliação física alterada com sucesso)", response = ExemploRespostaAvaliacaoFisicaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EnvelopeRespostaDTO> alterarAvaliacaoFisica(
            @ApiParam(value = "Identificador único da avaliação física a ser alterada", defaultValue = "789", required = true)
            @PathVariable(value = "id") Integer id,
            @ApiParam(value = "Dados atualizados da avaliação física", required = true)
            @RequestBody AvaliacaoFisicaDTOUpdate avaliacaoFisica) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.alterar(avaliacaoFisica, id));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar a avaliação física", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar dados de VO2 da avaliação física",
            notes = "Atualiza especificamente os dados de consumo de oxigênio (VO2) de uma avaliação física existente, utilizando o protocolo Astrand. O sistema recalcula automaticamente a classificação cardiovascular baseada nos novos valores.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Dados de VO2 alterados com sucesso)", response = ExemploRespostaAvaliacaoFisicaDTO.class),
            @ApiResponse(code = 400, message = "Dados de VO2 inválidos"),
            @ApiResponse(code = 404, message = "Avaliação física não encontrada")
    })
    @ResponseBody
    @RequestMapping(value = "/{id}/vo2", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EnvelopeRespostaDTO> alterarVo2AvaliacaoFisica(
            @ApiParam(value = "Identificador único da avaliação física", defaultValue = "789", required = true)
            @PathVariable(value = "id") Integer id,
            @ApiParam(value = "Dados do teste de VO2 pelo protocolo Astrand", required = true)
            @RequestBody AvaliacaoVo2AstrandDTO dados) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.alterarVo2(id, dados));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar cálcular Vo2 da avaliação física", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar avaliação física mais recente do aluno",
            notes = "Retorna os dados da avaliação física mais recente do aluno especificado. Caso o aluno não possua nenhuma avaliação física, retorna null. Esta consulta é útil para acompanhar o estado atual do aluno.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Avaliação física mais recente encontrada)", response = ExemploRespostaAvaliacaoFisicaDTO.class),
    })
    @ResponseBody
    @RequestMapping(value = "/alunos/{id}/avaliacao-recente", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> avaliacaoRecente(
            @ApiParam(value = "Identificador único do aluno", defaultValue = "12345", required = true)
            @PathVariable("id") final Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            return ResponseEntityFactory.ok(avaliacaoFisicaService.avaliacaoAtual(ctx, id, usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId())));

        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter a avaliação vigente", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar todas as avaliações físicas do aluno",
            notes = "Retorna uma lista completa com todas as avaliações físicas realizadas pelo aluno especificado, ordenadas da mais recente para a mais antiga. Esta consulta permite acompanhar a evolução histórica do aluno.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de avaliações físicas do aluno)", response = ExemploRespostaListAvaliacaoFisicaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos/{id}/todas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> todas(
            @ApiParam(value = "Identificador único do aluno", defaultValue = "12345", required = true)
            @PathVariable("id") final Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(avaliacaoFisicaService.todasCliente(ctx, id, usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId())));

        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter a avaliação vigente", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Gerar relatório PDF da avaliação física",
            notes = "Gera um relatório completo em PDF da avaliação física especificada, incluindo todos os dados coletados, resultados calculados, gráficos de evolução e recomendações. O relatório pode ser gerado em diferentes idiomas.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (URL do relatório PDF gerado)", response = ExemploRespostaUrlPdfAvaliacaoFisica.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}/imprimir", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> imprimir(
            @ApiParam(value = "Identificador único da avaliação física", defaultValue = "789", required = true)
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Idioma do relatório. Valores: 0=Português, 1=Inglês, 2=Espanhol", defaultValue = "0", required = false)
            @RequestParam(value = "idiomaBanco", required = false) final String idiomaBanco,
            HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            String language = UteisValidacao.emptyString(idiomaBanco) ? "PT" : IdiomaBancoEnum.getFromOrdinal(Integer.valueOf(idiomaBanco)).name();
            return ResponseEntityFactory.ok(avaliacaoFisicaService.comporUrlPdf(ctx, id, request, true, language));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Gerar relatório comparativo em PDF de múltiplas avaliações físicas",
            notes = "Gera um relatório comparativo em PDF entre múltiplas avaliações físicas do mesmo aluno, permitindo visualizar a evolução dos resultados ao longo do tempo. O relatório inclui gráficos e tabelas comparativas de todos os parâmetros avaliados.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (URL do relatório comparativo PDF gerado)", response = ExemploRespostaUrlPdfComparativoAvaliacaoFisica.class)
    })
    @ResponseBody
    @RequestMapping(value = "/imprimirComparar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> imprimirComparar(
            @ApiParam(value = "Lista de identificadores das avaliações físicas para comparação", defaultValue = "[123, 456, 789]", required = true)
            @RequestParam(value = "avaliacoes", required = false) List<Integer> avaliacoes, HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            List<AvaliacaoFisica> listaAvaliacao = new ArrayList<>();
            for (Integer i : avaliacoes) {
                AvaliacaoFisica avaliacaoFisica = new AvaliacaoFisica();
                avaliacaoFisica = avaliacaoFisicaService.obterPorId(ctx, i);
                listaAvaliacao.add(avaliacaoFisica);
            }
            ViewUtils bean = UtilContext.getBean(ViewUtils.class);
            return ResponseEntityFactory.ok(avaliacaoFisicaService.gerarPDFComparativo(
                    ctx, listaAvaliacao, usuario, bean, request,
                    null, false));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Enviar relatório comparativo de avaliações físicas",
            notes = "Envia um relatório comparativo de múltiplas avaliações físicas por e-mail ou gera links para compartilhamento via WhatsApp. O relatório permite comparar a evolução do aluno ao longo do tempo em todos os parâmetros avaliados.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Relatório enviado com sucesso ou links gerados)", response = ExemploRespostaLinkWhatsAppComparativoAvaliacaoFisica.class)
    })
    @ResponseBody
    @RequestMapping(value = "/enviarComparativo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> enviarComparativo(
            @ApiParam(value = "Lista de identificadores das avaliações físicas para comparação", defaultValue = "[123, 456, 789]", required = true)
            @RequestParam(value = "avaliacoes", required = false) List<Integer> avaliacoes,
            boolean email,
            Integer alunoId, HttpServletRequest request) {
        try {
            if (email) {
                avaliacaoFisicaService.enviarComparativoSpa(avaliacoes, request, sc);
                return ResponseEntityFactory.ok();
            } else {
                return ResponseEntityFactory.ok(avaliacaoFisicaService.montarLinksComparativoWpp(alunoId, avaliacoes, request, UtilContext.getBean(ViewUtils.class)));
            }
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Enviar avaliação física por e-mail",
            notes = "Envia o relatório completo de uma avaliação física específica por e-mail para o endereço informado. O relatório é gerado em PDF no idioma selecionado e inclui todos os dados da avaliação, gráficos e recomendações.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (E-mail enviado com sucesso)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}/enviar-por-email", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> enviar(
            @ApiParam(value = "Identificador único da avaliação física", defaultValue = "789", required = true)
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Endereço de e-mail de destino", defaultValue = "<EMAIL>", required = true)
            @RequestParam("email") final String email,
            @ApiParam(value = "Código da empresa", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request,
            @ApiParam(value = "Idioma do relatório. \n\n<strong>Valores disponíveis:</strong>\n- 0: Português\n- 1: Inglês\n- 2: Espanhol", defaultValue = "0", required = false)
            @RequestParam(value = "idiomaBanco", required = false) final String idiomaBanco) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            IdiomaBancoEnum language = IdiomaBancoEnum.getFromOrdinal(Integer.valueOf((UteisValidacao.emptyString(idiomaBanco) ? "0" : idiomaBanco)));
            avaliacaoFisicaService.enviarEmailAvaliacao(empresaId, ctx, id, email, usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId()), request, sc, language);
            return ResponseEntityFactory.ok();
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Gerar link para envio de avaliação física via WhatsApp",
            notes = "Gera um link formatado para WhatsApp contendo o relatório da avaliação física. O link pode ser usado para compartilhar facilmente o relatório com o aluno através do WhatsApp, incluindo uma mensagem personalizada e o link para download do PDF.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Link do WhatsApp gerado com sucesso)", response = ExemploRespostaLinkWhatsAppAvaliacaoFisica.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}/enviar-por-wpp", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> enviarWpp(
            @ApiParam(value = "Identificador único da avaliação física", defaultValue = "789", required = true)
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Número de telefone com WhatsApp (formato: +5511999999999)", defaultValue = "+5511999999999", required = true)
            @RequestParam("telefone") final String telefone, HttpServletRequest request,
            @ApiParam(value = "Idioma do relatório. \n\n<strong>Valores disponíveis:</strong>\n- 0: Português\n- 1: Inglês\n- 2: Espanhol", defaultValue = "0", required = false)
            @RequestParam(value = "idiomaBanco", required = false) final String idiomaBanco) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.montarLinksWpp(id, telefone, request, (UteisValidacao.emptyString(idiomaBanco) ? "0" : idiomaBanco)));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Excluir avaliação física",
            notes = "Remove permanentemente uma avaliação física do sistema. Esta operação não pode ser desfeita e remove todos os dados associados à avaliação, incluindo medidas, testes e resultados calculados.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Avaliação física excluída com sucesso)", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirAnamnese(
            @ApiParam(value = "Identificador único da avaliação física a ser excluída", defaultValue = "789", required = true)
            @PathVariable("id") final Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            avaliacaoFisicaService.excluir(ctx, avaliacaoFisicaService.obterPorId(ctx, id), null);
            return ResponseEntityFactory.ok(true);
        } catch (ValidacaoException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Excluir avaliação física por origem específica",
            notes = "Remove permanentemente uma avaliação física do sistema baseada em sua origem específica. Esta operação é utilizada para exclusões controladas por origem (ex: importações, integrações) e não pode ser desfeita.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Avaliação física excluída com sucesso)", response = ExemploRespostaBoolean.class)
    })
    @RequestMapping(value = "origem/{id}/{origem}", method = RequestMethod.DELETE)
    public @ResponseBody
    ResponseEntity<EnvelopeRespostaDTO> excluirAnamneseOrigem(
            @ApiParam(value = "Identificador único da avaliação física a ser excluída", defaultValue = "789", required = true)
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Origem específica da avaliação física (ex: IMPORTACAO, INTEGRACAO)", defaultValue = "IMPORTACAO", required = true)
            @PathVariable("origem") final String origem) {

        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            avaliacaoFisicaService.excluir(ctx, avaliacaoFisicaService.obterPorId(ctx, id), origem);
            return ResponseEntityFactory.ok(true);
        } catch (ValidacaoException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar perguntas do questionário PAR-Q",
            notes = "Retorna a lista de perguntas do questionário PAR-Q (Physical Activity Readiness Questionnaire) para avaliação de prontidão para atividade física. O PAR-Q é um questionário de triagem que identifica adultos que devem procurar orientação médica antes de iniciar um programa de exercícios.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de perguntas PAR-Q)", response = ExemploRespostaListPerguntaResponseTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/parq-perguntas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterPerguntasParQ(
            @ApiParam(value = "Código da anamnese específica para obter as perguntas PAR-Q", defaultValue = "15", required = false)
            @RequestParam(required = false) final Integer codigoAnamnese) throws Exception {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.obterPerguntasParQ(null, codigoAnamnese));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter parq-perguntas anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar evolução física do aluno",
            notes = "Retorna dados consolidados da evolução física do aluno baseados em suas avaliações físicas ao longo do tempo. Inclui análises de composição corporal, perímetros, dobras cutâneas, histórico de IMC e grupos musculares trabalhados nos treinos.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Dados de evolução física do aluno)", response = ExemploRespostaEvolucaoFisicaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/evolucao-fisica/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterEvolucaoFisica(
            @ApiParam(value = "Matrícula do aluno para análise de evolução física", defaultValue = "12345", required = true)
            @PathVariable("id") Integer id) {
        try {
            return ResponseEntityFactory.ok(evolucaoFisicaService.obterEvolucaoFisica(id));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter evolucao fésica", e);
            if (e.getMessage().equalsIgnoreCase("Não contém avaliacao evolucão")) {
                return ResponseEntityFactory.erroRegistroNotFoun(e.getChaveExcecao(), e.getMessage());
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar avaliação física por identificador",
            notes = "Retorna os dados completos de uma avaliação física específica através do seu identificador único. Inclui todas as medidas, testes realizados, resultados calculados e classificações.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Avaliação física encontrada)", response = ExemploRespostaAvaliacaoFisicaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAvaliacaoFisica(
            @ApiParam(value = "Identificador único da avaliação física", defaultValue = "789", required = true)
            @PathVariable() Integer id) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.obterAvaliacaoFisica(id));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter avaliacão física");
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar tabela de valores de referência RML para o aluno",
            notes = "Retorna a tabela de valores de referência para testes de Resistência Muscular Localizada (RML) baseada na idade e sexo do aluno. Inclui as faixas de classificação (excelente, acima da média, média, abaixo da média, fraco) para flexões de braço e abdominais.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Tabela de valores RML para o aluno)", response = ExemploRespostaRMLConfigDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/valores-rml/{alunoId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterValoresRML(
            @ApiParam(value = "Identificador único do aluno", defaultValue = "12345", required = true)
            @PathVariable() Integer alunoId) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.obterTabelaRML(alunoId));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter avaliacão física");
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Verificar produto de avaliação física vigente do aluno",
            notes = "Verifica se o aluno possui um produto de avaliação física vigente e se ainda há avaliações disponíveis para uso. Retorna informações sobre a disponibilidade do produto e quantas avaliações já foram utilizadas.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Status do produto vigente)", response = ExemploRespostaStatusProdutoVigenteAvaliacaoFisica.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos/{id}/produto-vigente", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> produtoVigente(
            @ApiParam(value = "Identificador único do aluno", defaultValue = "12345", required = true)
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Identificador do produto de avaliação física", defaultValue = "100", required = true)
            @RequestParam("idProduto") final String idProduto) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            String vigente = "true";
            Integer produtoLancado = null;
            IntegracaoCadastrosWSConsumer integracaoWS = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            ClienteSinteticoService cliente = UtilContext.getBean(ClienteSinteticoService.class);
            ClienteSintetico clienteSintetico = cliente.obterPorId(ctx, id);
            String produtoVigente = integracaoWS.verificarAlunoTemProdutoVigenteRetornandoQuantidade(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, clienteSintetico.getCodigoCliente(),
                    Integer.valueOf(idProduto));
            JSONArray produtos = new JSONArray(produtoVigente);
            if (produtos.length() == 0) {
                vigente = "O aluno não tem um produto de Avaliação Física vigente.";
            } else {
                for (int i = 0; i < produtos.length(); i++) {
                    List<AvaliacaoFisica> avLancada = avaliacaoFisicaService.obterAvaliacaoMovProduto(ctx, produtos.getJSONObject(i).getInt("produtoId"));
                    if (UteisValidacao.emptyList(avLancada) || produtos.getJSONObject(i).getInt("quantidade") > avLancada.size()) {
                        produtoLancado = produtos.getJSONObject(i).getInt("produtoId");
                    }
                }
                if (produtoLancado == null) {
                    vigente = "O produto vigente já foi usado.";
                }
            }
            return ResponseEntityFactory.ok(vigente);

        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter a avaliação vigente", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Criar nova avaliação física vinculada a produto específico",
            notes = "Cria uma nova avaliação física completa para o aluno especificado, vinculando-a a um produto específico de avaliação física. Esta operação consome uma unidade do produto vigente do aluno e registra a avaliação no sistema de controle de produtos.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Avaliação física criada e vinculada ao produto)", response = ExemploRespostaAvaliacaoFisicaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/alunos/{id}/{idProduto}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EnvelopeRespostaDTO> salvarAvaliacaoFisicaComProduto(
            @ApiParam(value = "Identificador único do aluno", defaultValue = "12345", required = true)
            @PathVariable(value = "id") Integer id,
            @ApiParam(value = "Dados completos da avaliação física a ser criada", required = true)
            @RequestBody AvaliacaoFisicaDTOUpdate avaliacaoFisica,
            @ApiParam(value = "Identificador do produto de avaliação física", defaultValue = "100", required = true)
            @PathVariable(value = "idProduto") Integer idProduto) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.inserir(avaliacaoFisica, id, idProduto));
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar a avaliação física", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Extrair dados de avaliação por bioimpedância",
            notes = "Processa e extrai informações de uma avaliação por bioimpedância a partir de um arquivo enviado. O sistema interpreta os dados do equipamento e converte para o formato utilizado nas avaliações físicas, incluindo composição corporal, percentual de gordura e massa magra.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Dados extraídos da bioimpedância)", response = ExemploRespostaBioimpedanciaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/extrairAvaliacaoBioimpedancia", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> extrairAvaliacaoBioimpedancia(
            @ApiParam(value = "Dados do arquivo de bioimpedância para processamento", required = true)
            @RequestBody ImportarAvaliacaoBioimpedanciaDTO avaliacaoBioimpedanciaDTO) {
        try {
            return ResponseEntityFactory.ok(avaliacaoFisicaService.extrairInformacoesAvaliacaoBioimpedancia(avaliacaoBioimpedanciaDTO));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao tentar extrair informações da avaliação bioimpedância", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Remover avaliações posturais duplicadas",
            notes = "Processo administrativo para remoção de avaliações posturais duplicadas no sistema. Pode remover todas as duplicatas encontradas ou apenas uma avaliação postural específica. Esta operação é irreversível e deve ser usada com cuidado.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Resultado do processo de remoção)", response = ExemploRespostaString.class)
    })
    @ResponseBody
    @RequestMapping(value = "/processo/remover-avaliacao-postural-duplicada", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAvaliacaoPosturalDuplicada(
            @ApiParam(value = "Indica se deve remover todas as duplicatas (true) ou apenas uma específica (false)", defaultValue = "false", required = true)
            @RequestParam("todas") final boolean todas,
            @ApiParam(value = "Código da avaliação postural específica a ser removida (obrigatório quando 'todas' = false)", defaultValue = "123", required = true)
            @RequestParam("codigoAvaliacaoPostural") final Integer codigoAvaliacaoPostural) {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            String retorno = avaliacaoFisicaService.processoRemoverAvaliacaoPosturalDuplicada(ctx, todas, codigoAvaliacaoPostural);
            return ResponseEntityFactory.ok(retorno);
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo de remoção de avaliacaopostural duplicadas", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar questionários PAR-Q do aluno com paginação",
            notes = "Consulta todos os questionários PAR-Q (Physical Activity Readiness Questionnaire) respondidos por um aluno específico, com suporte a paginação. Retorna o histórico completo de respostas PAR-Q do aluno para acompanhamento de sua prontidão para atividade física.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista paginada de PAR-Q do aluno)", response = ExemploRespostaListParQDTO.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "<strong>Campos disponíveis para ordenação:</strong>" +
                    "<ul>" +
                    "<li>dataresposta - Data da resposta do questionário PAR-Q</li>" +
                    "<li>codigo - Código da resposta PAR-Q</li>" +
                    "<li>parQPositivo - Status do resultado PAR-Q</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "dataresposta,desc", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "empresaId", value = "Código identificador da empresa", defaultValue = "1", paramType = "header", dataType = "long", required = true)
    })
    @ResponseBody
    @RequestMapping(value = "/v2/listarAlunosParQ/{codigoCliente}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAlunosParQV2(
            @ApiParam(value = "Código do cliente para consulta dos PAR-Q", defaultValue = "12345", required = true)
            @PathVariable final Integer codigoCliente,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer empresa = sessaoService.getUsuarioAtual().getEmpresaAtual();

            ClienteSintetico cliente = clienteSinteticoService.consultarSimplesPorCodigoCliente(ctx, codigoCliente);
            if (cliente == null || UteisValidacao.emptyNumber(cliente.getCodigo())) {
                return ResponseEntityFactory.erroRegistroNotFoun("Cliente não encontrado", "Cliente não encontrado");
            } else {
                JSONObject resultado = avaliacaoFisicaService.consultarParQsAluno(ctx, empresa, cliente.getCodigo(), paginadorDTO);
                Map<String, Object> resultadoMap = resultado.toMap();
                EnvelopeRespostaDTO envelope = EnvelopeRespostaDTO.of(resultadoMap, paginadorDTO);
                return ResponseEntityFactory.ok(envelope);
            }
        } catch (ServiceException e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao listar alunos com PAR-Q", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Gerar PDF do PAR-Q com assinatura digital",
            notes = "Gera um documento PDF do questionário PAR-Q preenchido pelo aluno, incluindo a assinatura digital quando disponível. O documento contém todas as perguntas, respostas e a assinatura eletrônica do aluno para fins de comprovação legal.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (PDF gerado com sucesso)", response = ExemploRespostaModelMapParQPDF.class)
    })
    @RequestMapping(value = "/{ctx}/imprimirParQAssinaturaDigital", method = RequestMethod.GET)
    public
    @ResponseBody
    ModelMap imprimirParQAssinaturaDigital(
            @ApiParam(value = "Contexto da sessão do usuário", defaultValue = "ctx123", required = true)
            @PathVariable final String ctx,
            @ApiParam(value = "Matrícula do aluno", defaultValue = "12345", required = true)
            @RequestParam final String matricula,
            @ApiParam(value = "Código específico da resposta PAR-Q (opcional - se não informado, busca a mais recente)", defaultValue = "789", required = false)
            @RequestParam(required = false) final String codigoRespostaParq,
            HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            Integer codigoResposta = UteisValidacao.emptyString(codigoRespostaParq) ? 0 : Integer.parseInt(codigoRespostaParq);
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matricula);
            RespostaClienteParQ rcp = new RespostaClienteParQ();
            if (!UteisValidacao.emptyNumber(codigoResposta)) {
                rcp = parQDao.consultarRespostaParQPorCodigo(ctx, codigoResposta);
            } else {
                rcp = parQDao.consultarRespostaParQPorCliente(ctx, cliente.getCodigo());
            }

            Anamnese questionarioParq = anamneseService.consultarParq(ctx, rcp.getUsuario_codigo(), getViewUtils());
            questionarioParq.setPerguntas(anamneseService.obterPerguntasAnamnese(ctx, questionarioParq.getCodigo()));
            List<RespostaCliente> respostasCliente = avaliacaoFisicaService.obterRespostasCliente(ctx, cliente.getCodigo(), rcp.getCodigo());
            mm.addAttribute("return", avaliacaoFisicaService.gerarPDFParQAssinaturaDigital(ctx, questionarioParq, respostasCliente, rcp, getViewUtils(), request, sc, true, cliente.getCodigo()));
            if (!UteisValidacao.emptyString(rcp.getUrlAssinatura())) {
                mm.addAttribute("assinatura", rcp.getFullUrlAssinatura());
            } else {
                mm.addAttribute("assinatura", "");
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    public ViewUtils getViewUtils() {
        return UtilContext.getBean(ViewUtils.class);
    }

}
