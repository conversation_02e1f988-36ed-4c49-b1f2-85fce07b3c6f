package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.atividade.AtividadeMusculo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by joa<PERSON> moita on 03/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do músculo exercitado em uma atividade física")
public class AtividadeMusculoResponseTO {

    @ApiModelProperty(value = "Código único identificador do músculo exercitado na atividade", example = "15")
    private Integer id;
    @ApiModelProperty(value = "Nome do músculo exercitado na atividade", example = "Bíceps braquial")
    private String nome;

    public AtividadeMusculoResponseTO() {

    }

    public AtividadeMusculoResponseTO(AtividadeMusculo am) {
        this.id = am.getMusculo().getCodigo();
        this.nome = am.getMusculo().getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
