package br.com.pacto.controller.json.agendamento;

import br.com.pacto.controller.json.aluno.SituacaoAlunoEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados do aluno em uma locação play incluindo situação e informações de check-in")
public class AlunoLocacaoPlayDTO {

    @ApiModelProperty(value = "Código identificador único do aluno", example = "1001")
    private Integer id;

    @ApiModelProperty(value = "Número da matrícula do aluno no sistema ZW", example = "2024001")
    private Integer matriculaZW;

    @ApiModelProperty(value = "Nome completo do aluno", example = "Maria Silva Santos")
    private String nome;

    @ApiModelProperty(value = "Nome da unidade onde o aluno está matriculado", example = "Unidade Centro")
    private String unidade;

    @ApiModelProperty(value = "URL da foto do aluno", example = "https://exemplo.com/fotos/maria.jpg")
    private String imageUri;

    @ApiModelProperty(value = "Situação do aluno. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- AT (ATIVO)\n" +
            "- IN (INATIVO)\n" +
            "- VI (VISITANTE)\n" +
            "- TR (TRANCADO)\n" +
            "- AE (ATESTADO)\n" +
            "- CA (CANCELADO)\n" +
            "- CR (CARENCIA)\n" +
            "- DE (DESISTENTE)\n" +
            "- VE (VENCIDO)\n" +
            "- OU (OUTROS)\n", example = "AT")
    private SituacaoAlunoEnum situacaoAluno;

    @ApiModelProperty(value = "Indica se o aluno fez check-in na locação play", example = "true")
    private Boolean checkin;

    public AlunoLocacaoPlayDTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMatriculaZW() {
        return matriculaZW;
    }

    public void setMatriculaZW(Integer matriculaZW) {
        this.matriculaZW = matriculaZW;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getUnidade() {
        return unidade;
    }

    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public SituacaoAlunoEnum getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(SituacaoAlunoEnum situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public Boolean getCheckin() {
        return checkin;
    }

    public void setCheckin(Boolean checkin) {
        this.checkin = checkin;
    }
}
