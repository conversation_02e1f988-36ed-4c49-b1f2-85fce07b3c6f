package br.com.pacto.controller.json.agendamento;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.cliente.perfil.FichaDoDiaDTO;
import br.com.pacto.service.intf.agendatotal.AgendaCardsService;
import br.com.pacto.service.intf.agendatotal.AlunosAulaService;
import br.com.pacto.service.intf.locacao.AlunoLocacaoHorarioPlayService;
import br.com.pacto.swagger.respostas.agendamento.ExemploRespostaAulaDetalhada;
import br.com.pacto.swagger.respostas.agendamento.ExemploRespostaDisponibilidades;
import br.com.pacto.swagger.respostas.agendamento.ExemploRespostaDisponibilidadesV2;
import br.com.pacto.swagger.respostas.agendamento.ExemploRespostaLocacaoPlayDetalhada;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by alcides on 14/07/2023.
 */
@Controller
@Api(tags = "Agendamentos")
@RequestMapping("/psec/agenda-cards")
public class AgendaCardsController {

    @Autowired
    private AgendaCardsService agendaCardsService;
    @Autowired
    private AlunosAulaService alunosAulaService;
    @Autowired
    private AlunoLocacaoHorarioPlayService alunoLocacaoHorarioPlayService;

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @ApiOperation(value="Retorna a agenda", notes = "Este método retorna a agenda", tags = "Agendamentos")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Agenda encontrada", response = ObterCardsResponseDTO.class),
    })
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterCards(@ApiParam(value = "ID da empresa", defaultValue = "1", required = true) @RequestHeader(value = "empresaId") Integer empresaId,
                                                          @ApiParam(value = "Referência", defaultValue = "20250310", required = true) @RequestParam(value = "ref") Long ref,
                                                          @ApiParam(value = "Período", defaultValue = "DIA", required = true) @RequestParam(value = "periodo") PeriodoFiltrarEnum periodo,
                                                          @ApiParam(value = "Filtros", defaultValue = "{\"tipo\" : \"TODAS\", \"professoresIds\" : [1, 2], \"ambientesIds;\" : [1, 2], \"modalidadesIds;\" : [1, 2], \"turmaId;\" : 1, \"turno\" : \"\", \"search\" : \"\", \"situacaoHorario\" : \"\"}", required = false) @RequestParam(value = "filtros", required = false) String filtros,
                                                          HttpServletRequest request) throws JSONException {
        try {
            FiltroTurmaDTO filtroTurmaDTO = new FiltroTurmaDTO(new JSONObject(filtros));
            return ResponseEntityFactory.ok(agendaCardsService.montarCards(request, empresaId,
                        Calendario.getDate("yyyyMMdd", ref.toString()),
                        periodo, filtroTurmaDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AgendaController.class.getName()).log(Level.SEVERE, "Erro ao tentar montar agenda cards", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno("erro_agenda", e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @ApiOperation(value = "Retorna os alunos de uma aula detalhada",
                  notes = "Este método retorna a lista de alunos de uma aula específica em um determinado dia. " +
                          "Requer permissão de AGENDA. O parâmetro empresaId é obrigatório no header da requisição.",
                  tags = "Agendamentos")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "0", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de alunos da aula encontrada", response = ExemploRespostaAulaDetalhada.class),
    })
    @RequestMapping(value = "/aula-detalhada/{horario-turma-id}/{dia}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aulaDetalhada(
            @ApiParam(value = "Código identificador da empresa", defaultValue = "1", required = true) @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código do horário da turma", defaultValue = "123", required = true) @PathVariable("horario-turma-id") Integer aulaHorario,
            @ApiParam(value = "Data da aula no formato YYYYMMDD", defaultValue = "20250310", required = true) @PathVariable("dia") String dia,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(alunosAulaService.alunosAula(empresaId,
                    Calendario.getDate("yyyyMMdd", dia), aulaHorario, paginadorDTO, request), paginadorDTO);
        } catch (ServiceException e) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @ApiOperation(value = "Retorna os alunos de uma locação play detalhada",
                  notes = "Este método retorna a lista de alunos de uma locação play específica em um determinado dia e ambiente. " +
                          "Requer permissão de AGENDA. O parâmetro empresaId é obrigatório no header da requisição.",
                  tags = "Agendamentos")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "0", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de alunos da locação play encontrada", response = ExemploRespostaLocacaoPlayDetalhada.class),
    })
    @RequestMapping(value = "/locacao-play-detalhada/{locacao-horario-id}/{ambiente}/{dia}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> locacaoPlayDetalhada(
            @ApiParam(value = "Código identificador da empresa", defaultValue = "1", required = true) @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Código do horário da locação", defaultValue = "456", required = true) @PathVariable("locacao-horario-id") Integer locacaoHorario,
            @ApiParam(value = "Código do ambiente", defaultValue = "789", required = true) @PathVariable("ambiente") Integer ambiente,
            @ApiParam(value = "Data da locação no formato YYYYMMDD", defaultValue = "20250310", required = true) @PathVariable("dia") String dia,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(alunoLocacaoHorarioPlayService.alunosLocacaoPlay(empresaId,
                    Calendario.getDate("yyyyMMdd", dia), locacaoHorario, ambiente, paginadorDTO, request), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (ParseException e) {
            return ResponseEntityFactory.erroInterno("erro_format data", "Erro ao formatar a data de ref");
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @ApiOperation(value = "Retorna as disponibilidades de agenda",
                  notes = "Este método retorna as disponibilidades de agenda para um tipo específico em um determinado dia e intervalo de horário. " +
                          "Requer permissão de AGENDA. O parâmetro empresaId é obrigatório no header da requisição.",
                  tags = "Agendamentos")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "0", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "horarioInicial,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de disponibilidades encontrada", response = ExemploRespostaDisponibilidades.class),
    })
    @RequestMapping(value = "/disponibilidades/{tipo}/{dia}/{horaSelecionada}/{horaSelecionadaFinal}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidades(
            @ApiParam(value = "Código identificador da empresa", defaultValue = "1", required = true) @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Tipo de disponibilidade", defaultValue = "AULA", required = true) @PathVariable("tipo") String tipo,
            @ApiParam(value = "Data no formato timestamp", defaultValue = "1709251200000", required = true) @PathVariable("dia") String dia,
            @ApiParam(value = "Hora inicial selecionada no formato HHhMM", defaultValue = "09h00", required = true) @PathVariable("horaSelecionada") String horaSelecionada,
            @ApiParam(value = "Hora final selecionada no formato HHhMM", defaultValue = "18h00", required = true) @PathVariable("horaSelecionadaFinal") String horaSelecionadaFinal,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(agendaCardsService.disponibilidades(empresaId,
                    new Date(Long.parseLong(dia)), horaSelecionada, horaSelecionadaFinal, tipo, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @ApiOperation(value = "Retorna as disponibilidades de agenda (versão 2)",
                  notes = "Este método retorna as disponibilidades de agenda para um tipo específico em um determinado dia e intervalo de horário, utilizando uma versão aprimorada do algoritmo. " +
                          "Requer permissão de AGENDA. O parâmetro empresaId é obrigatório no header da requisição.",
                  tags = "Agendamentos")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "0", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "horarioInicial,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Lista de disponibilidades encontrada", response = ExemploRespostaDisponibilidadesV2.class),
    })
    @RequestMapping(value = "/disponibilidadesV2/{tipo}/{dia}/{horaSelecionada}/{horaSelecionadaFinal}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> disponibilidadesV2(
            @ApiParam(value = "Código identificador da empresa", defaultValue = "1", required = true) @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Tipo de disponibilidade", defaultValue = "AULA", required = true) @PathVariable("tipo") String tipo,
            @ApiParam(value = "Data no formato timestamp", defaultValue = "1709251200000", required = true) @PathVariable("dia") String dia,
            @ApiParam(value = "Hora inicial selecionada no formato HHhMM", defaultValue = "09h00", required = true) @PathVariable("horaSelecionada") String horaSelecionada,
            @ApiParam(value = "Hora final selecionada no formato HHhMM", defaultValue = "18h00", required = true) @PathVariable("horaSelecionadaFinal") String horaSelecionadaFinal,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(agendaCardsService.disponibilidadesV2(empresaId,
                    new Date(Long.parseLong(dia)), horaSelecionada, horaSelecionadaFinal, tipo, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
