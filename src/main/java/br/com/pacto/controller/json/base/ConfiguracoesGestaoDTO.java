package br.com.pacto.controller.json.base;

import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para configurações de gestão do sistema")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracoesGestaoDTO {

    @ApiModelProperty(value = "Período em dias utilizado para análises de Business Intelligence", example = "30")
    private String periodo_usado_bi;

    @ApiModelProperty(value = "Quantidade de dias para considerar alunos como inativos", example = "15")
    private String inativos_a_x_dias;

    @ApiModelProperty(value = "Define se deve considerar somente alunos com contrato desistente", example = "true")
    private String somente_aluno_contrato_desistente;

    public String getPeriodo_usado_bi() {
        return periodo_usado_bi;
    }

    public void setPeriodo_usado_bi(String periodo_usado_bi) {
        this.periodo_usado_bi = periodo_usado_bi;
    }

    public String getInativos_a_x_dias() {
        return inativos_a_x_dias;
    }

    public void setInativos_a_x_dias(String inativos_a_x_dias) {
        this.inativos_a_x_dias = inativos_a_x_dias;
    }

    public Boolean getSomente_aluno_contrato_desistente() {
        if (!UteisValidacao.emptyString(somente_aluno_contrato_desistente)) {
            return somente_aluno_contrato_desistente.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setSomente_aluno_contrato_desistente(String somente_aluno_contrato_desistente) {
        this.somente_aluno_contrato_desistente = somente_aluno_contrato_desistente;
    }
}
