/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.acompanhamento;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Dados de desempenho de uma série específica de treino")
public class SerieDesempenhoJSON extends SuperJSON {

    @ApiModelProperty(value = "Carga total da série calculada (repetições × peso em gramas)", example = "7500")
    private Integer carga;

    @ApiModelProperty(value = "Data da execução da série no formato dd/MM", example = "20/06")
    private String data;

    SerieDesempenhoJSON(int carga, String data) {
        this.carga = carga;
        this.data = data;
    }

    public Integer getCarga() {
        return carga;
    }

    public void setCarga(Integer carga) {
        this.carga = carga;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
}
