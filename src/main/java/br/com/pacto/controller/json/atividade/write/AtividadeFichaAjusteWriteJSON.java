/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.atividade.write;

import br.com.pacto.controller.json.base.SuperJSON;

/**
 *
 * <AUTHOR>
 */
public class AtividadeFichaAjusteWriteJSON extends SuperJSON {

    private Integer codigo;
    private Integer atividadeFicha;
    private String nome;
    private String valor;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getAtividadeFicha() {
        return atividadeFicha;
    }

    public void setAtividadeFicha(Integer atividadeFicha) {
        this.atividadeFicha = atividadeFicha;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }
}
