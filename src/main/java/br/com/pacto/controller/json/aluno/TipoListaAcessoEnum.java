package br.com.pacto.controller.json.aluno;

public enum TipoListaAcessoEnum {

    TODOS_ALUNOS(1),
    ALUNOS_VINCULADOS(2);

    private final Integer tipo;

    TipoListaAcessoEnum(Integer tipo) {
        this.tipo = tipo;
    }

    public Integer getTipo() {
        return tipo;
    }

    public static TipoListaAcessoEnum getTipo(Integer tipo) {
        for (TipoListaAcessoEnum t : TipoListaAcessoEnum.values()) {
            if (t.getTipo().equals(tipo)) {
                return t;
            }
        }
        return null;
    }

}
