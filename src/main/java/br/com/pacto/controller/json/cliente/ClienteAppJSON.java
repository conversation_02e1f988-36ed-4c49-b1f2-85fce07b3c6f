package br.com.pacto.controller.json.cliente;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.controller.json.serialization.JsonDateSerializerYYYYMMDD;
import br.com.pacto.controller.json.base.SuperJSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.sql.Timestamp;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ClienteAppJSON extends SuperJSON {

    private Integer codigo;
    private Integer codigoPessoaZW;
    private Integer codigoClienteZW;
    private Integer codigoContrato;
    private Integer matricula;
    private String nome;
    private Timestamp dataNascimento;
    private Integer idade;
    private String profissao;
    private String colaboradores;
    private String professor;
    private String situacao;
    private String situacaoContrato;
    private Timestamp dataUltimoAcesso;
    private Timestamp dataUltimaModificacaoObjetivos;
    private Timestamp dataInicioPeriodoAcesso;
    private Timestamp dataFimPeriodoAcesso;
    private Integer empresa;
    private String email = "";
    private String telefones;
    private String andamentoTreino;
    private String urlFoto;
    private String userName;
    private Timestamp dia;
    private String nivel;
    private String nomeEmpresa;
    private Integer versao;
    private String codigoAcesso;
    private String vencPlano;
    private List<String> listaTelefones;
    private List<String> listaEmails;
    private String sexo;
    private String objetivos;
    private String dadosAvaliacao;
    private Boolean parQ;
    private Double altura;
    private Double peso;
    private Double percentualGordura;
    private Integer status;
    private Timestamp dataUltimaAvaliacao;
    private String nomeAvaliadorFisico;

    public ClienteAppJSON(final ClienteSintetico clienteSintetico) {
        if(clienteSintetico != null) {
            this.andamentoTreino = clienteSintetico.getAndamentoTreino();
            this.codigo = clienteSintetico.getCodigo();
            this.codigoClienteZW = clienteSintetico.getCodigoCliente();
            this.codigoContrato = clienteSintetico.getCodigoContrato();
            this.codigoPessoaZW = clienteSintetico.getCodigoPessoa();
            this.colaboradores = clienteSintetico.getColaboradores();
            if(clienteSintetico.getDataFimPeriodoAcesso() != null) {
                this.dataFimPeriodoAcesso = new Timestamp(clienteSintetico.getDataFimPeriodoAcesso().getTime());
            }else{
                this.dataFimPeriodoAcesso = null;
            }
            if(clienteSintetico.getDataFimPeriodoAcesso() != null) {
                this.dataInicioPeriodoAcesso = new Timestamp(clienteSintetico.getDataInicioPeriodoAcesso().getTime());
            }else{
                this.dataInicioPeriodoAcesso = null;
            }
            if(clienteSintetico.getDataNascimento() != null) {
                this.dataNascimento = new Timestamp(clienteSintetico.getDataNascimento().getTime());
            }else{
                this.dataNascimento = null;
            }
            if(clienteSintetico.getDataUltimoacesso() != null) {
                this.dataUltimoAcesso = new Timestamp(clienteSintetico.getDataUltimoacesso().getTime());
            }else{
                this.dataUltimoAcesso = null;
            }

            this.empresa = clienteSintetico.getEmpresa();
            this.idade = clienteSintetico.getIdade();
            this.matricula = clienteSintetico.getMatricula();
            this.nome = clienteSintetico.getNome();
            this.professor = clienteSintetico.getProfessorSintetico() != null ? clienteSintetico.getProfessorSintetico().getNome() : "";
            this.profissao = clienteSintetico.getProfissao();
            this.situacao = clienteSintetico.getSituacao();
            this.situacaoContrato = clienteSintetico.getSituacaoContrato();
            this.dia = clienteSintetico.getDia() != null ? new Timestamp(clienteSintetico.getDia().getTime()) : null;
            this.nivel = clienteSintetico.getNivelAluno() != null ? clienteSintetico.getNivelAluno().getNome() : "";
            this.versao = clienteSintetico.getVersao();
            this.codigoAcesso = clienteSintetico.getCodigoAcesso();
            this.vencPlano = clienteSintetico.getDataVigenciaAteAjustadaApresentar();
            this.listaEmails = clienteSintetico.getListaEmails();
            this.listaTelefones = clienteSintetico.getListaTelefones();
            this.sexo = clienteSintetico.getSexo();
            this.objetivos = clienteSintetico.getObjetivos() != null ? clienteSintetico.getObjetivos().toString() : "";
            this.dadosAvaliacao = clienteSintetico.getDadosAvaliacao() != null ? clienteSintetico.getDadosAvaliacao() : "";
            this.parQ = clienteSintetico.getParq() == null ? false : clienteSintetico.getParq();
            this.urlFoto = clienteSintetico.getUrlFoto();
            this.altura = clienteSintetico.getAltura();
            this.peso = clienteSintetico.getPeso();
            this.percentualGordura = clienteSintetico.getPercentualGordura();
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoPessoaZW() {
        return codigoPessoaZW;
    }

    public void setCodigoPessoaZW(Integer codigoPessoaZW) {
        this.codigoPessoaZW = codigoPessoaZW;
    }

    public Integer getCodigoClienteZW() {
        return codigoClienteZW;
    }

    public void setCodigoClienteZW(Integer codigoClienteZW) {
        this.codigoClienteZW = codigoClienteZW;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class, nullsUsing = JsonDateSerializerYYYYMMDD.class)
    public Timestamp getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Timestamp dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Integer getIdade() {
        return idade;
    }

    public void setIdade(Integer idade) {
        this.idade = idade;
    }

    public String getProfissao() {
        return profissao;
    }

    public void setProfissao(String profissao) {
        this.profissao = profissao;
    }

    public String getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(String colaboradores) {
        this.colaboradores = colaboradores;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class, nullsUsing = JsonDateSerializerYYYYMMDD.class)
    public Timestamp getDataUltimoAcesso() {
        return dataUltimoAcesso;
    }

    public void setDataUltimoAcesso(Timestamp dataUltimoAcesso) {
        this.dataUltimoAcesso = dataUltimoAcesso;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class, nullsUsing = JsonDateSerializerYYYYMMDD.class)
    public Timestamp getDataInicioPeriodoAcesso() {
        return dataInicioPeriodoAcesso;
    }

    public void setDataInicioPeriodoAcesso(Timestamp dataInicioPeriodoAcesso) {
        this.dataInicioPeriodoAcesso = dataInicioPeriodoAcesso;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class, nullsUsing = JsonDateSerializerYYYYMMDD.class)
    public Timestamp getDataFimPeriodoAcesso() {
        return dataFimPeriodoAcesso;
    }

    public void setDataFimPeriodoAcesso(Timestamp dataFimPeriodoAcesso) {
        this.dataFimPeriodoAcesso = dataFimPeriodoAcesso;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefones() {
        if (telefones == null) {
            telefones = "";
        }
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public String getAndamentoTreino() {
        return andamentoTreino;
    }

    public void setAndamentoTreino(String andamentoTreino) {
        this.andamentoTreino = andamentoTreino;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class)
    public Timestamp getDia() {
        return dia;
    }

    public void setDia(Timestamp dia) {
        this.dia = dia;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public String getVencPlano() {
        return vencPlano;
    }

    public void setVencPlano(String vencPlano) {
        this.vencPlano = vencPlano;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(String objetivos) {
        this.objetivos = objetivos;
    }

    public List<String> getListaTelefones() {
        return listaTelefones;
    }

    public void setListaTelefones(List<String> listaTelefones) {
        this.listaTelefones = listaTelefones;
    }

    public List<String> getListaEmails() {
        return listaEmails;
    }

    public void setListaEmails(List<String> listaEmails) {
        this.listaEmails = listaEmails;
    }

    public String getDadosAvaliacao() {
        return dadosAvaliacao;
    }

    public void setDadosAvaliacao(String dadosAvaliacao) {
        this.dadosAvaliacao = dadosAvaliacao;
    }

    public Boolean getParQ() {
        return parQ;
    }

    public void setParQ(Boolean parQ) {
        this.parQ = parQ;
    }

    public Double getAltura() {
        return altura;
    }

    public void setAltura(Double altura) {
        this.altura = altura;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getPercentualGordura() {
        return percentualGordura;
    }

    public void setPercentualGordura(Double percentualGordura) {
        this.percentualGordura = percentualGordura;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Timestamp getDataUltimaAvaliacao() {
        return dataUltimaAvaliacao;
    }

    public void setDataUltimaAvaliacao(Timestamp dataUltimaAvaliacao) {
        this.dataUltimaAvaliacao = dataUltimaAvaliacao;
    }

    public String getNomeAvaliadorFisico() {
        return nomeAvaliadorFisico;
    }

    public void setNomeAvaliadorFisico(String nomeAvaliadorFisico) {
        this.nomeAvaliadorFisico = nomeAvaliadorFisico;
    }

    public Timestamp getDataUltimaModificacaoObjetivos() {
        return dataUltimaModificacaoObjetivos;
    }

    public void setDataUltimaModificacaoObjetivos(Timestamp dataUltimaModificacaoObjetivos) {
        this.dataUltimaModificacaoObjetivos = dataUltimaModificacaoObjetivos;
    }
}
