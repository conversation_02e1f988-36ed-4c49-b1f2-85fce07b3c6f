package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.objeto.Calendario;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ApiModel(description = "Dados de disponibilidade de agenda incluindo professor, horários e tipos de atividades")
@JsonIgnoreProperties(ignoreUnknown = true)
public class AgendaDisponibilidadeDTO {

    @ApiModelProperty(value = "Código identificador único da disponibilidade", example = "1001")
    private Integer id;

    @ApiModelProperty(value = "Data da disponibilidade no formato YYYYMMDD", example = "20250310")
    private String dia;

    @ApiModelProperty(value = "Informações do professor responsável pela disponibilidade")
    private ColaboradorSimplesTO professor;

    @ApiModelProperty(value = "Horário inicial da disponibilidade no formato HH:mm", example = "09:00")
    private String horarioInicial;

    @ApiModelProperty(value = "Horário final da disponibilidade no formato HH:mm", example = "10:00")
    private String horarioFinal;

    @ApiModelProperty(value = "Data e hora de início da disponibilidade")
    private Date inicioDate;

    @ApiModelProperty(value = "Data e hora de fim da disponibilidade")
    private Date fimDate;

    @ApiModelProperty(value = "Código da configuração de disponibilidade da agenda", example = "501")
    private Integer agendaDisponibilidadeConfigId;

    @ApiModelProperty(value = "Lista de tipos de atividades disponíveis neste horário")
    private List<TipoAtividadeDTO> tiposAtividades;

    @ApiModelProperty(value = "Nome do ambiente onde ocorre a disponibilidade", example = "Sala de Musculação")
    private String ambiente;

    @ApiModelProperty(value = "Código identificador do ambiente", example = "10")
    private Integer ambienteId;

    @ApiModelProperty(value = "Descrição adicional da disponibilidade", example = "Treino funcional avançado")
    private String descricao;

    @ApiModelProperty(value = "Tipo do horário da disponibilidade", example = "FIXO")
    private String tipoHorario;

    @ApiModelProperty(value = "Código do horário de disponibilidade", example = "123")
    private Integer horarioDisponibilidadeCod;

    @ApiModelProperty(value = "Indica se a disponibilidade está bloqueada", example = "false")
    private Boolean bloqueado = Boolean.FALSE;

    @ApiModelProperty(value = "Código do ambiente no sistema ZW", example = "15")
    private Integer codigoZwAmbiente;

    public AgendaDisponibilidadeDTO() {

    }

    public AgendaDisponibilidadeDTO(Agendamento agendamento) {
        this.id = agendamento.getCodigo();
        this.dia = Calendario.getData(agendamento.getInicio(), "yyyyMMdd");
        this.tiposAtividades = (agendamento.getTipoEvento() != null && agendamento.getTipoEvento().getCodigo() != null) ? new ArrayList(){{
            add(new TipoAtividadeDTO(agendamento.getTipoEvento()));
        }} : new ArrayList(){{
            add(new TipoAtividadeDTO(agendamento.getHorarioDisponibilidade().getDisponibilidade()));
        }};
        this.inicioDate = agendamento.getInicio();
        this.fimDate = agendamento.getFim();
        this.horarioInicial = agendamento.getHoraInicioApresentar();
        this.horarioFinal = agendamento.getHoraFimApresentar();
        this.professor = new ColaboradorSimplesTO(agendamento.getProfessor(), true);
        if(agendamento.getHorarioDisponibilidade() != null) {
            this.horarioDisponibilidadeCod = agendamento.getHorarioDisponibilidade().getCodigo();
        }
    }

    public AgendaDisponibilidadeDTO clone() {
        AgendaDisponibilidadeDTO clone = new  AgendaDisponibilidadeDTO();
        clone.id = this.id;
        clone.dia = this.dia;
        clone.tiposAtividades = new ArrayList(this.tiposAtividades);
        clone.inicioDate = this.inicioDate;
        clone.fimDate = this.fimDate;
        clone.horarioInicial = this.horarioInicial;
        clone.horarioFinal = this.horarioFinal;
        clone.professor = this.professor;
        clone.horarioDisponibilidadeCod = this.horarioDisponibilidadeCod;
        return clone;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public ColaboradorSimplesTO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorSimplesTO professor) {
        this.professor = professor;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public Integer getAgendaDisponibilidadeConfigId() {
        return agendaDisponibilidadeConfigId;
    }

    public void setAgendaDisponibilidadeConfigId(Integer agendaDisponibilidadeConfigId) {
        this.agendaDisponibilidadeConfigId = agendaDisponibilidadeConfigId;
    }

    public List<TipoAtividadeDTO> getTiposAtividades() {
        return tiposAtividades;
    }

    public void setTiposAtividades(List<TipoAtividadeDTO> tiposAtividades) {
        this.tiposAtividades = tiposAtividades;
    }

    public Date getInicioDate() {
        return inicioDate;
    }

    public void setInicioDate(Date inicioDate) {
        this.inicioDate = inicioDate;
    }

    public Date getFimDate() {
        return fimDate;
    }

    public void setFimDate(Date fimDate) {
        this.fimDate = fimDate;
    }

    public TipoAtividadeDTO getTipo(){
        try {
            return getTiposAtividades().get(0);
        }catch (Exception e){
            return new TipoAtividadeDTO();
        }
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public String getTipoHorario() {
        return tipoHorario;
    }

    public void setTipoHorario(String tipoHorario) {
        this.tipoHorario = tipoHorario;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getHorarioDisponibilidadeCod() {
        return horarioDisponibilidadeCod;
    }

    public void setHorarioDisponibilidadeCod(Integer horarioDisponibilidadeCod) {
        this.horarioDisponibilidadeCod = horarioDisponibilidadeCod;
    }

    public Boolean getBloqueado() {
        return bloqueado;
    }

    public void setBloqueado(Boolean bloqueado) {
        this.bloqueado = bloqueado;
    }

    public Integer getCodigoZwAmbiente() {
        return codigoZwAmbiente;
    }

    public void setCodigoZwAmbiente(Integer codigoZwAmbiente) {
        this.codigoZwAmbiente = codigoZwAmbiente;
    }

    public Integer getAmbienteId() {
        return ambienteId;
    }

    public void setAmbienteId(Integer ambienteId) {
        this.ambienteId = ambienteId;
    }
}
