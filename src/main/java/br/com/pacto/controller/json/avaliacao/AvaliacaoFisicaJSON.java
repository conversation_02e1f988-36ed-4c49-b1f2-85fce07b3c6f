package br.com.pacto.controller.json.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by alcides on 21/09/2017.
 */
@ApiModel(description = "Dados completos de uma avaliação física do aluno")
public class AvaliacaoFisicaJSON {

    @ApiModelProperty(value = "Código identificador único da avaliação física", example = "1001")
    private Integer id;

    @ApiModelProperty(value = "Data da avaliação física atual no formato dd/MM/yyyy", example = "15/07/2025")
    private String dataAtual;

    @ApiModelProperty(value = "Data da próxima avaliação física no formato dd/MM/yyyy", example = "15/10/2025")
    private String dataProxima;

    @ApiModelProperty(value = "Nome do profissional avaliador físico responsável pela avaliação", example = "<PERSON>. <PERSON>")
    private String avaliador;

    @ApiModelProperty(value = "Data da avaliação atual em formato timestamp (milissegundos)", example = "1721001600000")
    private long dataAtualLong;

    @ApiModelProperty(value = "Data da próxima avaliação em formato timestamp (milissegundos)", example = "1728777600000")
    private long dataProximaLong;

    @ApiModelProperty(value = "Altura do aluno em metros", example = "1.75")
    private Double altura;

    @ApiModelProperty(value = "Peso corporal do aluno em quilogramas", example = "70.5")
    private Double peso;

    @ApiModelProperty(value = "Índice de Massa Corporal (IMC) calculado", example = "23.02")
    private Double imc;

    @ApiModelProperty(value = "Categoria de classificação do percentual de gordura corporal", example = "Normal")
    private String categoriaGordura;

    @ApiModelProperty(value = "Categoria de classificação do IMC", example = "Peso Normal")
    private String categoriaIMC;

    @ApiModelProperty(value = "Legenda explicativa da categoria do IMC", example = "Peso adequado para a altura")
    private String legendaIMC;

    @ApiModelProperty(value = "Idade metabólica calculada com base na composição corporal", example = "28.5")
    private Double idadeMetabolica;

    @ApiModelProperty(value = "Percentual de gordura corporal", example = "15.8")
    private Double percGordura;

    @ApiModelProperty(value = "Percentual de massa residual (órgãos internos)", example = "24.1")
    private Double percResidual;

    @ApiModelProperty(value = "Percentual de massa muscular", example = "45.2")
    private Double percMuscular;

    @ApiModelProperty(value = "Percentual de massa óssea", example = "14.9")
    private Double percOsseo;

    @ApiModelProperty(value = "Percentual de água corporal", example = "60.3")
    private Double percAgua;

    @ApiModelProperty(value = "Percentual de massa magra (músculos + ossos)", example = "60.1")
    private Double percMassaMagra;

    @ApiModelProperty(value = "Peso da gordura corporal em quilogramas", example = "11.1")
    private Double pesoGordura;

    @ApiModelProperty(value = "Peso da massa residual em quilogramas", example = "17.0")
    private Double pesoResidual;

    @ApiModelProperty(value = "Peso da massa muscular em quilogramas", example = "31.9")
    private Double pesoMuscular;

    @ApiModelProperty(value = "Peso da massa óssea em quilogramas", example = "10.5")
    private Double pesoOsseo;

    @ApiModelProperty(value = "Resultado do teste de resistência muscular localizada para abdômen", example = "Excelente")
    private String rmlAbdomen;

    @ApiModelProperty(value = "Resultado do teste de resistência muscular localizada para braços", example = "Bom")
    private String rmlBracos;

    @ApiModelProperty(value = "Lista de medidas das dobras cutâneas realizadas na avaliação")
    private List<DobraCutaneaJSON> dobras;

    @ApiModelProperty(value = "Lista de medidas de perimetria (circunferências) realizadas na avaliação")
    private List<PerimetriaJSON> perimetria;

    @ApiModelProperty(value = "Lista de fotos da avaliação física para acompanhamento visual da evolução")
    private List<FotoAvaliacaoJSON> fotos;

    @ApiModelProperty(value = "Peso da massa muscular em quilogramas (campo alternativo)", example = "31.9")
    private Double musculos;

    @ApiModelProperty(value = "Peso da gordura corporal em quilogramas (campo alternativo)", example = "11.1")
    private Double gordura;

    @ApiModelProperty(value = "Peso dos resíduos corporais em quilogramas", example = "17.0")
    private Double residuos;

    @ApiModelProperty(value = "Peso da massa óssea em quilogramas (campo alternativo)", example = "10.5")
    private Double ossos;

    @ApiModelProperty(value = "Peso de componentes não informados ou não classificados", example = "0.0")
    private Double naoInformado;

    @ApiModelProperty(value = "Indica se a avaliação foi realizada com bioimpedância", example = "true")
    private Boolean bioimpedancia = Boolean.FALSE;

    @ApiModelProperty(value = "Dados da avaliação postural realizada")
    private AvaliacaoPosturalJSON avaliacaoPosturalJSON;

    public Double getAltura() {
        return altura;
    }

    public void setAltura(Double altura) {
        this.altura = altura;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getImc() {
        return imc;
    }

    public void setImc(Double imc) {
        this.imc = imc;
    }

    public String getCategoriaGordura() {
        return categoriaGordura;
    }

    public void setCategoriaGordura(String categoriaGordura) {
        this.categoriaGordura = categoriaGordura;
    }

    public String getCategoriaIMC() {
        return categoriaIMC;
    }

    public void setCategoriaIMC(String categoriaIMC) {
        this.categoriaIMC = categoriaIMC;
    }

    public Double getPercGordura() {
        return percGordura;
    }

    public void setPercGordura(Double percGordura) {
        this.percGordura = percGordura;
    }

    public Double getPercResidual() {
        return percResidual;
    }

    public void setPercResidual(Double percResidual) {
        this.percResidual = percResidual;
    }

    public Double getPercMuscular() {
        return percMuscular;
    }

    public void setPercMuscular(Double percMuscular) {
        this.percMuscular = percMuscular;
    }

    public Double getPercOsseo() {
        return percOsseo;
    }

    public void setPercOsseo(Double percOsseo) {
        this.percOsseo = percOsseo;
    }

    public Double getPesoGordura() {
        return pesoGordura;
    }

    public void setPesoGordura(Double pesoGordura) {
        this.pesoGordura = pesoGordura;
    }

    public Double getPesoResidual() {
        return pesoResidual;
    }

    public void setPesoResidual(Double pesoResidual) {
        this.pesoResidual = pesoResidual;
    }

    public Double getPesoMuscular() {
        return pesoMuscular;
    }

    public void setPesoMuscular(Double pesoMuscular) {
        this.pesoMuscular = pesoMuscular;
    }

    public Double getPesoOsseo() {
        return pesoOsseo;
    }

    public void setPesoOsseo(Double pesoOsseo) {
        this.pesoOsseo = pesoOsseo;
    }

    public String getRmlAbdomen() {
        return rmlAbdomen;
    }

    public void setRmlAbdomen(String rmlAbdomen) {
        this.rmlAbdomen = rmlAbdomen;
    }

    public String getRmlBracos() {
        return rmlBracos;
    }

    public void setRmlBracos(String rmlBracos) {
        this.rmlBracos = rmlBracos;
    }

    public List<DobraCutaneaJSON> getDobras() {
        return dobras;
    }

    public void setDobras(List<DobraCutaneaJSON> dobras) {
        this.dobras = dobras;
    }

    public List<PerimetriaJSON> getPerimetria() {
        return perimetria;
    }

    public void setPerimetria(List<PerimetriaJSON> perimetria) {
        this.perimetria = perimetria;
    }

    public List<FotoAvaliacaoJSON> getFotos() {
        return fotos;
    }

    public void setFotos(List<FotoAvaliacaoJSON> fotos) {
        this.fotos = fotos;
    }

    public void setDataAtual(String dataAtual) {
        this.dataAtual = dataAtual;
    }

    public void setDataProxima(String dataProxima) {
        this.dataProxima = dataProxima;
    }

    public long getDataAtualLong() {
        return dataAtualLong;
    }

    public void setDataAtualLong(long dataAtualLong) {
        this.dataAtualLong = dataAtualLong;
    }

    public long getDataProximaLong() {
        return dataProximaLong;
    }

    public void setDataProximaLong(long dataProximaLong) {
        this.dataProximaLong = dataProximaLong;
    }

    public String getDataAtual() {
        return dataAtual;
    }

    public String getDataProxima() {
        return dataProxima;
    }

    public String getAvaliador() {
        return avaliador;
    }

    public void setAvaliador(String avaliador) {
        this.avaliador = avaliador;
    }

    public String getLegendaIMC() {
        return legendaIMC;
    }

    public void setLegendaIMC(String legendaIMC) {
        this.legendaIMC = legendaIMC;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Double getIdadeMetabolica() {
        return idadeMetabolica;
    }

    public void setIdadeMetabolica(Double idadeMetabolica) {
        this.idadeMetabolica = idadeMetabolica;
    }

    public Double getPercAgua() {
        return percAgua;
    }

    public void setPercAgua(Double percAgua) {
        this.percAgua = percAgua;
    }

    public Double getMusculos() {
        return musculos;
    }

    public void setMusculos(Double musculos) {
        this.musculos = musculos;
    }

    public Double getGordura() {
        return gordura;
    }

    public void setGordura(Double gordura) {
        this.gordura = gordura;
    }

    public Double getResiduos() {
        return residuos;
    }

    public void setResiduos(Double residuos) {
        this.residuos = residuos;
    }

    public Double getOssos() {
        return ossos;
    }

    public void setOssos(Double ossos) {
        this.ossos = ossos;
    }

    public Boolean getBioimpedancia() {
        return bioimpedancia;
    }

    public void setBioimpedancia(Boolean bioimpedancia) {
        this.bioimpedancia = bioimpedancia;
    }

    public Double getNaoInformado() {
        return naoInformado;
    }

    public void setNaoInformado(Double naoInformado) {
        this.naoInformado = naoInformado;
    }

    public Double getPercMassaMagra() {
        return percMassaMagra;
    }

    public void setPercMassaMagra(Double percMassaMagra) {
        this.percMassaMagra = percMassaMagra;
    }

    public AvaliacaoPosturalJSON getAvaliacaoPosturalJSON() {
        return avaliacaoPosturalJSON;
    }

    public void setAvaliacaoPosturalJSON(AvaliacaoPosturalJSON avaliacaoPosturalJSON) {
        this.avaliacaoPosturalJSON = avaliacaoPosturalJSON;
    }
}
