package br.com.pacto.controller.json.ambiente;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do nível da turma")
public class NivelTurmaResponseTO {

    @ApiModelProperty(value = "Código único identificador do nível", example = "4")
    private Integer id;
    @ApiModelProperty(value = "Nome do nível da turma", example = "INTERMEDIÁRIO 1")
    private String nome;
    @ApiModelProperty(value = "Código MGB do nível", example = "MGB0123")
    private String codigomgb;

    public NivelTurmaResponseTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCodigomgb() {
        return codigomgb;
    }

    public void setCodigomgb(String codigomgb) {
        this.codigomgb = codigomgb;
    }
}
