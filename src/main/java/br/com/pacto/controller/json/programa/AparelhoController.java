package br.com.pacto.controller.json.programa;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.selfloops.SelfLoopsConfiguracoes;
import br.com.pacto.controller.json.atividade.AtividadeController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.aparelho.FiltroAparelhoJSON;
import br.com.pacto.service.intf.aparelho.AparelhoService;
import br.com.pacto.service.intf.selfloops.SelfloopsConfiguracoesService;
import br.com.pacto.swagger.respostas.aparelho.ExemploRespostaListAparelhoResponseTOPaginacao;
import br.com.pacto.swagger.respostas.aparelho.ExemploRespostaListAtividadeAparelhoResponseRecursiveTO;
import br.com.pacto.swagger.respostas.aparelho.ExemploRespostaAparelhoResponseTO;
import br.com.pacto.swagger.respostas.aparelho.ExemploRespostaBoolean;
import br.com.pacto.swagger.respostas.aparelho.ExemploRespostaListAparelhoResponseTO;
import br.com.pacto.swagger.respostas.aparelho.ExemploRespostaListSensoresSelfloop;
import br.com.pacto.util.UteisValidacao;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 26/07/2018.
 */
@Controller
@RequestMapping("/psec/aparelhos")
public class AparelhoController {

    private final AparelhoService aparelhoService;
    private final SelfloopsConfiguracoesService selfloopsConfiguracoesService;
    private final SessaoService sessaoService;

    @Autowired
    public AparelhoController(AparelhoService aparelhoService, SelfloopsConfiguracoesService selfloopsConfiguracoesService, SessaoService sessaoService){
        this.selfloopsConfiguracoesService = selfloopsConfiguracoesService;
        this.sessaoService = sessaoService;
        Assert.notNull(aparelhoService, "O serviço de aparelho não foi injetado corretamente");
        this.aparelhoService = aparelhoService;
    }


    @ApiOperation(
            value = "Consultar aparelhos",
            notes = "Consulta aparelhos com filtros e paginação. Permite buscar aparelhos por nome e filtrar por tipo (crossfit ou treino).",
            tags = "Aparelhos"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de aparelhos)", response = ExemploRespostaListAparelhoResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAparelhos(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do aparelho.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).\n" +
                    "- <strong>crossfit:</strong> Filtra por tipo de aparelho - true para crossfit, false para treino.",
                    defaultValue = "{\"quicksearchValue\":\"Esteira\", \"quicksearchFields\":[\"nome\"], \"crossfit\":false}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroAparelhoJSON filtroGestaoJSON = new FiltroAparelhoJSON(filtros);

            return ResponseEntityFactory.ok(aparelhoService.consultarAparelhos(filtroGestaoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os aparelhos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar todos os aparelhos",
            notes = "Lista todos os aparelhos disponíveis filtrados por tipo (crossfit ou treino). Retorna informações básicas dos aparelhos.",
            tags = "Aparelhos"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de todos os aparelhos)", response = ExemploRespostaListAtividadeAparelhoResponseRecursiveTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaTodosAparelhos(
            @ApiParam(value = "Filtro por tipo de aparelho - true para crossfit, false para treino", defaultValue = "false", required = true)
            boolean crossfit) {
        try {
            return ResponseEntityFactory.ok(aparelhoService.listaTodosAparelhos(crossfit));
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os aparelhos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar aparelho específico",
            notes = "Consulta um aparelho específico pelo seu código identificador. Retorna informações detalhadas do aparelho incluindo ajustes e atividades relacionadas.",
            tags = "Aparelhos"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Dados do aparelho)", response = ExemploRespostaAparelhoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAparelho(
            @ApiParam(value = "Código único identificador do aparelho", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(aparelhoService.consultarAparelho(id));
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o aparelho", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Incluir novo aparelho",
            notes = "Inclui um novo aparelho no sistema. Permite definir nome, tipo (crossfit/treino), ajustes, atividades relacionadas e configurações específicas.",
            tags = "Aparelhos"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Aparelho incluído com sucesso)", response = ExemploRespostaAparelhoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirAparelhos(
            @ApiParam(value = "Dados completos do aparelho para inclusão", required = true)
            @RequestBody AparelhoTO aparelhoTO) throws Exception {
        try {
            return ResponseEntityFactory.ok(aparelhoService.inserir(aparelhoTO));
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir aparelho", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Alterar aparelho existente",
            notes = "Altera um aparelho existente no sistema. Permite modificar nome, tipo, ajustes, atividades relacionadas e demais configurações.",
            tags = "Aparelhos"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Aparelho alterado com sucesso)", response = ExemploRespostaAparelhoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAparelhos(
            @ApiParam(value = "Código único identificador do aparelho a ser alterado", defaultValue = "1", required = true)
            @PathVariable("id") Integer id,
            @ApiParam(value = "Dados completos do aparelho para alteração", required = true)
            @RequestBody AparelhoTO aparelhoTO) {
        try {
            return ResponseEntityFactory.ok(aparelhoService.alterar(id, aparelhoTO));

        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar aparelho", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Excluir aparelho",
            notes = "Exclui um aparelho do sistema pelo seu código identificador. A exclusão remove permanentemente o aparelho e suas associações.",
            tags = "Aparelhos"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Aparelho excluído com sucesso)", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirAparelho(
            @ApiParam(value = "Código único identificador do aparelho a ser excluído", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id){
        try {
            aparelhoService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir aparelho", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar aparelhos habilitados para reserva de equipamento",
            notes = "Lista todos os aparelhos que estão habilitados para reserva de equipamento. Retorna apenas aparelhos de treino (não crossfit) configurados para uso em reservas.",
            tags = "Aparelhos"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de aparelhos habilitados para reserva)", response = ExemploRespostaListAparelhoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "/habilitados-reserva-equipamento", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aparelhosHabilitadosReservaEquipamento(
            @ApiParam(value = "Código da empresa", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(aparelhoService.aparelhosHabilitadosReservaEquipamento());
        } catch (ServiceException e) {
            Logger.getLogger(AparelhoController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os aparelhos habilitados para reserva de equipamento", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar sensores Selfloop",
            notes = "Lista os sensores disponíveis na integração com Selfloop. Retorna informações dos sensores configurados para a empresa atual.",
            tags = "Aparelhos"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de sensores Selfloop)", response = ExemploRespostaListSensoresSelfloop.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.APARELHOS)
    @RequestMapping(value = "/integracaoSelfloop/sensores", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarSensores() throws JSONException {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            Integer empresaId = sessaoService.getUsuarioAtual().getEmpresaAtual();
            SelfLoopsConfiguracoes configs = selfloopsConfiguracoesService.obterPorEmpresa(chave, empresaId);
            if(configs != null && !UteisValidacao.emptyString(configs.getCodeSelfloops()) && !UteisValidacao.emptyString(configs.getEmpresaSelfloops())){
                return ResponseEntityFactory.ok(selfloopsConfiguracoesService.obterSensoresSelfloops(configs, chave, configs.getEmpresa().getCodigo()));
            }else {
                return ResponseEntityFactory.ok(new ArrayList<>());
            }
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}

