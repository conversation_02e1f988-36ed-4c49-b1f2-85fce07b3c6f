package br.com.pacto.controller.json.aluno;

import br.com.pacto.objeto.Uteis;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR> 16/01/2019
 */
@ApiModel(description = "Representa a porcentagem de treinos executados em um determinado dia da semana.")
public class PorcentagemDiaSemana {

    @ApiModelProperty(value = "Porcentagem de treinos executados no dia da semana", example = "42.5")
    private Double porcentagemExecutada = 0.0;

    public Double getPorcentagemExecutada() {
        return porcentagemExecutada;
    }

    public void setPorcentagemExecutada(Double porcentagemExecutada) {
        this.porcentagemExecutada = Uteis.arredondarForcando2CasasDecimais(porcentagemExecutada);
    }
}
