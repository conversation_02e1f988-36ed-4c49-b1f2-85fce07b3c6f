/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.acompanhamento;

import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoFicha;
import br.com.pacto.bean.programa.ProgramaTreinoResumo;
import br.com.pacto.bean.serie.SerieRealizada;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.programa.ProgramaTreinoJSONControle;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.swagger.respostas.acompanhamento.ExemploRespostaAcompanhamentoCompleto;
import br.com.pacto.swagger.respostas.acompanhamento.ExemploRespostaAvaliacaoAcompanhamento;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/acompanhamento")
public class AcompanhamentoJSONControle extends SuperControle {

    @Autowired
    private ProgramaTreinoService service;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;

    @ApiOperation(value = "Consultar acompanhamento completo de treino por ficha",
                  notes = "Retorna dados completos do acompanhamento de treino do aluno, incluindo estatísticas de presença, " +
                         "programas realizados e desempenho agrupado por ficha de treino. " +
                         "O desempenho é calculado com base nas últimas 4 datas de execução de cada ficha.",
                  tags = "Acompanhamento")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Acompanhamento consultado com sucesso",
                    response = ExemploRespostaAcompanhamentoCompleto.class)
    })
    @RequestMapping(value = "{ctx}/get", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap acompanhamento(@ApiParam(value = "Código de contexto da empresa", required = true, defaultValue = "1")
                           @PathVariable String ctx,
                           @ApiParam(value = "Nome de usuário do aluno para consulta do acompanhamento", required = true, defaultValue = "joao.silva")
                           @RequestParam String username) {
        //super.init(ctx, token);
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = usuarioService.obterPorAtributo(ctx, "username", username);
            if (usuario != null) {
                AcompanhamentoCompletoJSON acomp = new AcompanhamentoCompletoJSON();
                acomp.setData(Uteis.getData(Calendario.hoje(), Calendario.MASC_DATA));
                AcompanhamentoDadosJSON dados = new AcompanhamentoDadosJSON();
                ProgramaTreinoResumo resumo = service.obterResumo(ctx, usuario.getCliente());
                dados.setAtividades(resumo.getAtividades());
                dados.setDiasDeTreinamento(resumo.getDiasTreinamento());
                dados.setDiasPresencaSemana(resumo.getDiasPresencaSemana());
                dados.setDiasProgramaAtual(resumo.getDiasProgramaAtual());
                dados.setDiasProgramaAtualTotal(resumo.getDiasProgramaAtualTotal());
                dados.setExpectativaSemana(resumo.getExpecativaSemana());
                dados.setFaltasNaSemana(resumo.getFaltasSemana());
                dados.setProgramas(resumo.getProgramas());
                acomp.setDados(dados);

                ProgramaTreino programa = service.obterUltimoProgramaVigente(ctx, usuario.getCliente());

                if ((programa == null)) {
                    throw new ServiceException(getViewUtils().getMensagem("mobile.programanaoencontrado"));
                }
                
                List<ProgramaTreinoFicha> listaFichas = service.obterFichaPorPrograma(ctx, programa.getCodigo(), null, false);
                service.ordenarProgramaTreinoFichas(ctx, programa.getCodigo(), listaFichas);
                for (ProgramaTreinoFicha treinoFicha : listaFichas) {
                    List<Date> listaDatasFicha = service.obterDatasSeriesAgrupadasDaFicha(ctx, treinoFicha.getFicha().getCodigo(), 4);
                    
                    Collections.sort(listaDatasFicha);
                    List<SerieDesempenhoJSON> tmp = new ArrayList<SerieDesempenhoJSON>();
                    String nomeFicha = treinoFicha.getFicha().getNome();
                    for (Date date : listaDatasFicha) {
                        List<SerieRealizada> listaSeries = service.obterSeriesDaFichaPorData(ctx, treinoFicha.getFicha().getCodigo(), date);
                        if (listaSeries != null) {
                            int carga = 0;
                            for (SerieRealizada s : listaSeries) {
                                carga += s.getRepeticao() * s.getCarga();
                            }
                            DesempenhoJSON desemp = new DesempenhoJSON();
                            desemp.setNome(treinoFicha.getFicha().getNome());
                            tmp.add(new SerieDesempenhoJSON(carga, Calendario.getData(date, "dd/MM")));
                        }
                    }
                    DesempenhoJSON d = new DesempenhoJSON();
                    d.setNome(nomeFicha);
                    d.setDados(tmp);
                    acomp.getDesempenho().add(d);      
                }
                
                
                mm.addAttribute("acompanhamento", acomp);
                //
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, e);
        }
        return mm;
    }

    @ApiOperation(value = "Consultar acompanhamento completo de treino por execução",
                  notes = "Retorna dados completos do acompanhamento de treino do aluno, incluindo estatísticas de presença, " +
                         "programas realizados e desempenho agrupado por treino realizado. " +
                         "O desempenho é calculado com base nos últimos 4 treinos executados pelo aluno.",
                  tags = "Acompanhamento")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Acompanhamento consultado com sucesso",
                    response = ExemploRespostaAcompanhamentoCompleto.class)
    })
    @RequestMapping(value = "{ctx}/get2", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap acompanhamento2(@ApiParam(value = "Código de contexto da empresa", required = true, defaultValue = "1")
                            @PathVariable String ctx,
                            @ApiParam(value = "Nome de usuário do aluno para consulta do acompanhamento", required = true, defaultValue = "joao.silva")
                            @RequestParam String username) {
        //super.init(ctx, token);
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = usuarioService.obterPorAtributo(ctx, "username", username);
            if (usuario != null) {
                ProgramaTreinoResumo resumo = service.obterResumo(ctx, usuario.getCliente());
                AcompanhamentoCompletoJSON acomp = new AcompanhamentoCompletoJSON();
                acomp.setData(Uteis.getData(Calendario.hoje(), Calendario.MASC_DATA));
                AcompanhamentoDadosJSON dados = new AcompanhamentoDadosJSON();
                dados.setAtividades(resumo.getAtividades());
                dados.setDiasDeTreinamento(resumo.getDiasTreinamento());
                dados.setDiasPresencaSemana(resumo.getDiasPresencaSemana());
                dados.setDiasProgramaAtual(resumo.getDiasProgramaAtual());
                dados.setDiasProgramaAtualTotal(resumo.getDiasProgramaAtualTotal());
                dados.setExpectativaSemana(resumo.getExpecativaSemana());
                dados.setFaltasNaSemana(resumo.getFaltasSemana());
                dados.setProgramas(resumo.getProgramas());
                acomp.setDados(dados);

                ProgramaTreino programa = service.obterUltimoProgramaVigente(ctx, usuario.getCliente());
                if (programa != null) {
                    List<TreinoRealizado> listaTreinos = service.obterUltimosTreinosRealizados(ctx, programa.getCodigo(), 4);
                    for (TreinoRealizado tr : listaTreinos) {
                        DesempenhoJSON desemp = new DesempenhoJSON();
                        desemp.setNome(tr.getProgramaTreinoFicha().getFicha().getNome());
                        List<SerieRealizada> listaSeries = service.obterSeriesDoTreino(ctx, tr.getCodigo());
                        if (listaSeries != null) {
                            List<SerieDesempenhoJSON> series = new ArrayList<SerieDesempenhoJSON>();
                            for (SerieRealizada serieRealizada : listaSeries) {
                                //TODO: alterar em seriedesempenhojson a carga de inteiro para double
                                SerieDesempenhoJSON serieDesempenhoJSON = new SerieDesempenhoJSON(
                                        serieRealizada.getRepeticao() * serieRealizada.getCarga().intValue(),
                                        Calendario.getData(tr.getDataInicio(), Calendario.MASC_DATA));
                                series.add(serieDesempenhoJSON);
                            }
                            desemp.setDados(series);
                        }
                        acomp.getDesempenho().add(desemp);
                    }
                }
                mm.addAttribute("acompanhamento", acomp);
                //
            }
        } catch (Exception e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
        }
        return mm;
    }

    @ApiOperation(value = "Avaliar acompanhamento de treino",
                  notes = "Permite que o aluno avalie o acompanhamento de treino recebido. " +
                         "A avaliação deve ser feita até as 23:59 do dia do treino e cada acompanhamento " +
                         "pode ser avaliado apenas uma vez.",
                  tags = "Acompanhamento")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Avaliação registrada com sucesso",
                    response = ExemploRespostaAvaliacaoAcompanhamento.class)
    })
    @RequestMapping(value = "{ctx}/avaliar", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap avaliarAcompanhamento(@ApiParam(value = "Código de contexto da empresa", required = true, defaultValue = "1")
                                   @PathVariable String ctx,
                                   @ApiParam(value = "Dados da avaliação do acompanhamento", required = true)
                                   @RequestBody ClienteAcompanhamentoAvaliacaoDTO avaliacaoDTO) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = usuarioService.consultarPorCliente(ctx, avaliacaoDTO.getClienteSinteticoId());
            if (usuario == null) {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }

            clienteSinteticoService.avaliarAcompanhamento(ctx, avaliacaoDTO, usuario);

            mm.addAttribute(STATUS_SUCESSO, "Sua avaliação foi registrada com sucesso!");

        } catch (Exception e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(AcompanhamentoJSONControle.class.getName()).log(Level.SEVERE, "Erro ao avaliar acompanhamento.", e);
        }
        return mm;
    }
}