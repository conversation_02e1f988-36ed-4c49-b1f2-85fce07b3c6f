package br.com.pacto.controller.json.usuario;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.usuario.NotificacaoRecursoEmpresaTO;
import br.com.pacto.bean.usuario.UsuarioColaboradorTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.login.TokenDTO;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.usuario.ExemploRespostaListUsuarioResponseTOPaginacao;
import br.com.pacto.swagger.respostas.usuario.ExemploRespostaStringUrlAtendimento;
import br.com.pacto.swagger.respostas.usuario.ExemploRespostaTokenDTO;
import br.com.pacto.swagger.respostas.usuario.ExemploRespostaUsuarioColaboradorResponseTO;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * paulo 19/10/2018
 */

@Controller
@RequestMapping("/psec/usuarios")
public class UsuarioController {

    private UsuarioService usuarioService;
    private SessaoService sessaoService;

    @Autowired
    public UsuarioController(UsuarioService usuarioService, SessaoService sessaoService) {
        Assert.notNull(usuarioService, "O serviço de usuário não foi injetado corretamente");
        Assert.notNull(usuarioService, "O serviço de sessão não foi injetado corretamente");
        this.usuarioService = usuarioService;
        this.sessaoService = sessaoService;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/colaboradores", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "Consultar usuários colaboradores", notes = "Lista usuários colaboradores com filtros e paginação", tags = "Usuário")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de usuários colaboradores retornada com sucesso", response = ExemploRespostaListUsuarioResponseTOPaginacao.class)
    })
    public ResponseEntity<EnvelopeRespostaDTO> listaUsuarioColaborador(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome ou nome de usuário do colaborador.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\", \"userName\"]).\n" +
                    "- <strong>situacoes:</strong> Filtra por situação do colaborador (Deve ser informado como uma lista ex: [\"ATIVO\", \"INATIVO\"]).\n" +
                    "- <strong>tipoColaborador:</strong> Tipo específico do colaborador (PR, TW, PT, OR, CO, PI, PE, TE, ES, FO, CR, MD, FC, AD).",
                    defaultValue = "{\"quicksearchValue\":\"João\", \"quicksearchFields\":[\"nome\"], \"situacoes\":[\"ATIVO\"], \"tipoColaborador\":\"PR\"}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiParam(value = "Código da empresa para consulta dos usuários colaboradores", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiIgnore
            PaginadorDTO paginadorDTO
    ) throws JSONException {
        try {
            FiltroColaboradorJSON filtros = new FiltroColaboradorJSON(filters);
            return ResponseEntityFactory.ok(
                    usuarioService.listaUsuarioColaborador(filtros, paginadorDTO, empresaId),
                    paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar usuários de colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/colaboradores/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "Consultar detalhes de usuário colaborador", notes = "Retorna os detalhes completos de um usuário colaborador específico", tags = "Usuário")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Detalhes do usuário colaborador retornados com sucesso", response = ExemploRespostaUsuarioColaboradorResponseTO.class)
    })
    public ResponseEntity<EnvelopeRespostaDTO> detalhesUsuarioColaborador(
            @ApiParam(value = "ID do usuário colaborador", example = "1", required = true)
            @PathVariable("id") Integer id,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(usuarioService.obterUsuarioColaborador(id, request));
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar usuário de colaboradore", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/colaboradores", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "Cadastrar usuário colaborador", notes = "Cadastra um novo usuário colaborador no sistema", tags = "Usuário")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Usuário colaborador cadastrado com sucesso", response = ExemploRespostaUsuarioColaboradorResponseTO.class)
    })
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarUsuarioColaborador(
            @ApiParam(value = "Dados do usuário colaborador a ser cadastrado", required = true)
            @RequestBody UsuarioColaboradorTO usuarioColaboradorTO,
            @ApiParam(value = "Código da empresa", example = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request) throws Exception {
        try {

            return ResponseEntityFactory.ok(usuarioService.cadastrarUsuarioColaborador(sessaoService.getUsuarioAtual().getChave(), usuarioColaboradorTO, empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar usuário", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/colaboradores/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "Atualizar usuário colaborador", notes = "Atualiza os dados de um usuário colaborador existente", tags = "Usuário")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Usuário colaborador atualizado com sucesso", response = ExemploRespostaUsuarioColaboradorResponseTO.class)
    })
    public ResponseEntity<EnvelopeRespostaDTO> atualizarUsuarioColaborador(
            @ApiParam(value = "ID do usuário colaborador", example = "1", required = true)
            @PathVariable("id") Integer id,
            @ApiParam(value = "Dados atualizados do usuário colaborador", required = true)
            @RequestBody UsuarioColaboradorTO usuarioColaboradorTO,
            @ApiParam(value = "Código da empresa", example = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request) {
        try {

            return ResponseEntityFactory.ok(usuarioService.alterarUsuarioColaborador(request, id, usuarioColaboradorTO, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar o usuário", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/solicitar-atendimento", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "Obter link para solicitar atendimento", notes = "Retorna URL para abertura de chamado de suporte", tags = "Usuário")
    @ApiResponses({
            @ApiResponse(code = 200, message = "URL para solicitação de atendimento retornada com sucesso", response = ExemploRespostaStringUrlAtendimento.class)
    })
    public ResponseEntity<EnvelopeRespostaDTO> linkAberturaChamado(
            @ApiParam(value = "Código da empresa", example = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(usuarioService.urlSolicitarAtendimento(empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao carregar url de abertura de chamado", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/notificarRecursoEmpresa", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "Notificar recurso da empresa", notes = "Registra notificação de uso de recurso específico da empresa", tags = "Usuário")
    public void cadastrarUsuarioColaborador(
            @ApiParam(value = "Dados da notificação de recurso da empresa", required = true)
            @RequestBody NotificacaoRecursoEmpresaTO notificacaoRecurso) {
        try {
            SuperControle.notificarRecursoEmpresa(notificacaoRecurso.getChave(), notificacaoRecurso.getRecursoNotificar(),
                    notificacaoRecurso.getNomeUsuario(), notificacaoRecurso.getEmpresaId(), notificacaoRecurso.getNomeEmpresa(),
                    notificacaoRecurso.getCidade(), notificacaoRecurso.getEstado(), notificacaoRecurso.getPais());
        } catch (Exception e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao tentar notificar recurso: ", notificacaoRecurso.getRecursoNotificar());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/codigo-verificacao-email/{id}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "Solicitar código de verificação de email", notes = "Envia código de verificação para alteração de email do usuário", tags = "Usuário")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Código de verificação enviado com sucesso", response = ExemploRespostaTokenDTO.class)
    })
    public ResponseEntity<EnvelopeRespostaDTO> alterarEmail(
            @ApiParam(value = "ID do usuário", example = "1", required = true)
            @PathVariable(value = "id") Integer idUsuario,
            @ApiParam(value = "Novo email para verificação", example = "<EMAIL>", required = true)
            @RequestParam String email,
            HttpServletRequest request
    ) {
        try {
            return ResponseEntityFactory.ok(usuarioService.solicitarCodigoVerficacaoEmail(sessaoService.getUsuarioAtual().getChave(), idUsuario, email, request));
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro no envio do código de verificação do e-mail", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/validar-codigo-verificacao/{id}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "Validar código de verificação de email", notes = "Valida o código de verificação enviado para alteração de email", tags = "Usuário")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Código de verificação validado com sucesso", response = ExemploRespostaVazia.class)
    })
    public ResponseEntity<EnvelopeRespostaDTO> validarCodigoVerificacaoEmail(
            @ApiParam(value = "ID do usuário", example = "1", required = true)
            @PathVariable(value = "id") Integer idUsuario,
            @ApiParam(value = "Token com código de verificação", required = true)
            @RequestBody TokenDTO tokenDTO,
            HttpServletRequest request
    ) {
        try {
            usuarioService.processarToken(sessaoService.getUsuarioAtual().getChave(), idUsuario, tokenDTO, request);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao validar o código de verificação!", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/recuperar-senha/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "Recuperar senha do usuário", notes = "Solicita recuperação de senha para o usuário especificado", tags = "Usuário")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Solicitação de recuperação de senha processada com sucesso", response = ExemploRespostaVazia.class)
    })
    public ResponseEntity<EnvelopeRespostaDTO> recuperarSenha(
            @ApiParam(value = "ID do usuário", example = "1", required = true)
            @PathVariable("id") Integer idUsuario,
            HttpServletRequest request) {
        try {
            usuarioService.recuperarSenhaNovoLogin(sessaoService.getUsuarioAtual().getChave(), idUsuario, request);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro solicitar a recuperação de senha!", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.DESVINCULAR_USUARIO)
    @RequestMapping(value = "/desvincular-usuario/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "Desvincular usuário", notes = "Desvincula usuário do sistema de login unificado", tags = "Usuário")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Usuário desvinculado com sucesso", response = ExemploRespostaVazia.class)
    })
    public ResponseEntity<EnvelopeRespostaDTO> desvincularUsuarioNovoLogin(
            @ApiParam(value = "ID do usuário", example = "1", required = true)
            @PathVariable("id") Integer idUsuario,
            HttpServletRequest request) {
        try {
            usuarioService.desvincularUsuarioNovoLogin(sessaoService.getUsuarioAtual().getChave(), idUsuario, request);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioController.class.getName()).log(Level.SEVERE, "Erro ao desvincular usuário da chave " + sessaoService.getUsuarioAtual().getChave(), e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
}
