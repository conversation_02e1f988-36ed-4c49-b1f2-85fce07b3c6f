package br.com.pacto.controller.json.termoaceite;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.termoaceite.TermoAceiteService;
import br.com.pacto.swagger.respostas.ExemploRespostaString;
import br.com.pacto.swagger.respostas.termoaceite.ExemploRespostaCadastroTermoAceite;
import br.com.pacto.swagger.respostas.termoaceite.ExemploRespostaListTermoAceiteAssinaturaDTO;
import br.com.pacto.swagger.respostas.termoaceite.ExemploRespostaListTermoAceiteDTO;
import br.com.pacto.swagger.respostas.termoaceite.ExemploRespostaRegistroAssinaturaTermoAceite;
import br.com.pacto.swagger.respostas.termoaceite.ExemploRespostaTermoAceiteDTO;
import br.com.pacto.util.UteisValidacao;
import org.json.JSONObject;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/termo-aceite")
public class TermoAceiteControle {

    private TermoAceiteService service;

    @Autowired
    public TermoAceiteControle(TermoAceiteService service) {
        this.service = service;
    }

    @ApiOperation(value = "Consultar todos os termos de aceite cadastrados",
                  notes = "Retorna uma lista com todos os termos de aceite disponíveis no sistema",
                  tags = "Modelo de Contrato")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Lista de termos de aceite retornada com sucesso", response = ExemploRespostaListTermoAceiteDTO.class)
    })
    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaTermos() {
        try {
            return ResponseEntityFactory.ok(service.findAll());
        } catch (Exception e) {
            Logger.getLogger(TermoAceiteControle.class.getName()).log(Level.SEVERE, "Erro ao consultar termos de aceite", e);
            return ResponseEntityFactory.erroInterno("erro_obter_termo_aceite_app", e.getMessage());
        }
    }

    @ApiOperation(value = "Consulta termo de aceite por código",
                  notes = "Retorna os dados de um termo de aceite específico através do seu código identificador",
                  tags = "Modelo de Contrato")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Termo de aceite encontrado com sucesso", response = ExemploRespostaTermoAceiteDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{codigo}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaTermosPorCodigo(
            @ApiParam(value = "Código identificador único do termo de aceite", required = true, example = "1")
            @PathVariable Integer codigo) {
        try {
            return ResponseEntityFactory.ok(service.encontraTermoPorCodigo(codigo));
        } catch (Exception e) {
            Logger.getLogger(TermoAceiteControle.class.getName()).log(Level.SEVERE, "Erro ao consultar termos de aceite", e);
            return ResponseEntityFactory.erroInterno("erro_obter_termo_aceite_app", e.getMessage());
        }
    }

    @ApiOperation(value = "Consulta todas as assinaturas de termos de aceite",
                  notes = "Retorna uma lista com todas as assinaturas de termos de aceite registradas no sistema",
                  tags = "Modelo de Contrato")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Lista de assinaturas de termos retornada com sucesso", response = ExemploRespostaListTermoAceiteAssinaturaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/assinaturas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaTermosAssinados() {
        try {
            return ResponseEntityFactory.ok(service.findAllAssinados());
        } catch (Exception e) {
            Logger.getLogger(TermoAceiteControle.class.getName()).log(Level.SEVERE, "Erro ao consultar termos de aceite", e);
            return ResponseEntityFactory.erroInterno("erro_obter_termo_aceite_app", e.getMessage());
        }
    }

    @ApiOperation(value = "Consulta assinaturas de termos por código de matrícula",
                  notes = "Retorna uma lista com todas as assinaturas de termos de aceite de um aluno específico através do código de matrícula",
                  tags = "Modelo de Contrato")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Lista de assinaturas do aluno retornada com sucesso", response = ExemploRespostaListTermoAceiteAssinaturaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/assinaturas/{codigoMatricula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscaTermosAssinados(
            @ApiParam(value = "Código de matrícula do aluno para buscar suas assinaturas de termos", required = true, example = "456")
            @PathVariable Integer codigoMatricula) {
        try {
            return ResponseEntityFactory.ok(service.findByCodigoMatricula(codigoMatricula));
        } catch (Exception e) {
            Logger.getLogger(TermoAceiteControle.class.getName()).log(Level.SEVERE, "Erro ao consultar termos de aceite", e);
            return ResponseEntityFactory.erroInterno("erro_obter_termo_aceite_app", e.getMessage());
        }
    }

    @ApiOperation(value = "Cadastra novo termo de aceite",
                  notes = "Salva um novo termo de aceite no sistema com a descrição fornecida",
                  tags = "Modelo de Contrato")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Termo de aceite cadastrado com sucesso", response = ExemploRespostaCadastroTermoAceite.class)
    })
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> save(
            @ApiParam(value = "Descrição completa do termo de aceite a ser cadastrado", required = true,
                     example = "Termo de aceite para uso das instalações da academia, incluindo normas de segurança e responsabilidades do usuário")
            @RequestParam String termo) {
        try {
            if (UteisValidacao.emptyString(termo)) {
                throw new Exception("A descrição do termo não pode ser vazia.");
            }
            service.save(termo);
            return ResponseEntityFactory.ok("Salvo com sucesso!");
        } catch (Exception e) {
            Logger.getLogger(TermoAceiteControle.class.getName()).log(Level.SEVERE, "Erro ao consultar termos de aceite", e);
            return ResponseEntityFactory.erroInterno("erro_obter_termo_aceite_app", e.getMessage());
        }
    }

    @ApiOperation(value = "Registra assinatura de termo de aceite",
                  notes = "Salva uma nova assinatura de termo de aceite com os dados do signatário e informações de auditoria",
                  tags = "Modelo de Contrato")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Assinatura de termo registrada com sucesso", response = ExemploRespostaRegistroAssinaturaTermoAceite.class)
    })
    @ResponseBody
    @RequestMapping(value = "/assinatura", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveAssinatura(
            @ApiParam(value = "Endereço IP do dispositivo utilizado para assinar o termo", required = true, example = "*************")
            @RequestParam String ip,
            @ApiParam(value = "Nome completo da pessoa que está assinando o termo", required = true, example = "João Silva Santos")
            @RequestParam String nome,
            @ApiParam(value = "CPF da pessoa que está assinando o termo (opcional)", required = false, example = "123.456.789-00")
            @RequestParam (required = false) String cpf,
            @ApiParam(value = "Data da assinatura em formato timestamp (milissegundos)", required = true, example = "1717958400000")
            @RequestParam String data,
            @ApiParam(value = "Código identificador do termo de aceite que está sendo assinado", required = true, example = "1")
            @RequestParam Integer termo,
            @ApiParam(value = "Endereço de email da pessoa que está assinando o termo (opcional)", required = false, example = "<EMAIL>")
            @RequestParam (required = false) String email,
            @ApiParam(value = "Código de matrícula do aluno que está assinando o termo", required = true, example = "456")
            @RequestParam Integer codigoMatricula) {
        try {
            if (UteisValidacao.emptyString(ip) || UteisValidacao.emptyString(nome) || UteisValidacao.emptyString(data)) {
                throw new ServiceException("Nenhum dos parâmetros obritatórios (nome, ip, data e código de matrícula) pode ser vazio.");
            }
            service.saveAssinatura(ip, nome, cpf, data, termo, email, codigoMatricula);
            return ResponseEntityFactory.ok("Salvo com sucesso!");
        } catch (Exception e) {
            Logger.getLogger(TermoAceiteControle.class.getName()).log(Level.SEVERE, "Erro ao consultar termos de aceite", e);
            return ResponseEntityFactory.erroInterno("erro_obter_termo_aceite_app", e.getMessage());
        }
    }

    @ApiIgnore
    @ResponseBody
    @RequestMapping(value = "/dd5047f18244c2a7b7a6d8c83c85999c", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveAssinaturaCriptografado(@RequestBody String content) {
        try {
            JSONObject json = new JSONObject(Uteis.decryptUserData(content));

            String ip = json.optString("ip");
            String nome = json.optString("nome");
            String cpf = json.optString("cpf");
            String data = json.optString("data");
            Integer termo = json.optInt("termo");
            String email = json.optString("email");
            Integer codigoMatricula = json.optInt("codigoMatricula");
            String time = json.optString("time");

            if (UteisValidacao.emptyString(ip) || UteisValidacao.emptyString(nome)
                    || UteisValidacao.emptyString(data) || UteisValidacao.emptyString(time) || UteisValidacao.emptyNumber(termo) || UteisValidacao.emptyNumber(codigoMatricula)) {
                throw new ServiceException("Parâmetros obrigatórios (nome, ip, data, time, matricula, termo) não podem estar vazios.");
            }

            service.saveAssinatura(ip, nome, cpf, data, termo, email, codigoMatricula);

            JSONObject resposta = new JSONObject();
            resposta.put("mensagem", "Salvo com sucesso!");

            String encryptedResponse = Uteis.encryptUserData(resposta.toString());
            return ResponseEntityFactory.ok(encryptedResponse);
        } catch (Exception e) {
            Logger.getLogger(TermoAceiteControle.class.getName())
                    .log(Level.SEVERE, "Erro ao salvar assinatura", e);

            JSONObject erro = new JSONObject();
            try {
                erro.put("erro", e.getMessage());
            } catch (Exception ignored) {
            }

            String encryptedError;
            try {
                encryptedError = Uteis.encryptUserData(erro.toString());
            } catch (Exception encryptionException) {
                encryptedError = "{\"erro\": \"Erro interno ao criptografar\"}";
            }

            return ResponseEntityFactory.erroInterno("erro_salvar_assinatura", encryptedError);
        }
    }

}
