package br.com.pacto.service.intf.benchmark;


import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.benchmark.Benchmark;
import br.com.pacto.controller.json.benchmark.BenchmarkResponseTO;
import br.com.pacto.controller.json.benchmark.BenchmarkTO;
import br.com.pacto.controller.json.benchmark.FiltroBenchmarkJSON;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;
import java.util.Map;

/**
 * Created by Rafael on 11/07/2016.
 */
public interface BenchmarkService {

    public static final String SERVICE_NAME = "BenchmarkService";

    public Benchmark inserir(final String ctx, Benchmark object) throws ServiceException;

    public Benchmark obterPorId(final String ctx, Integer id) throws ServiceException;

    public Benchmark alterar(final String ctx, Benchmark object) throws ServiceException;

    public void excluir(final String ctx, Benchmark object) throws ServiceException;

    public List<Benchmark> obterTodos(final String ctx) throws ServiceException;

    public List<Benchmark> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Benchmark> obterPorParam(final String ctx, String query,
                                        Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Benchmark obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public Benchmark obterPorNome(final String ctx,String nome) throws ServiceException;

    BenchmarkResponseTO cadastroBenchmark(BenchmarkTO benchmarkTO) throws ServiceException;


    List<BenchmarkResponseTO> listarTodosBenchmarks() throws ServiceException;

    BenchmarkResponseTO detalhesBenchmark(Integer id) throws ServiceException;

    List<BenchmarkResponseTO> listaBenchmark(FiltroBenchmarkJSON filtroBenchmarkJSON, PaginadorDTO paginadorDTO) throws ServiceException;

    BenchmarkResponseTO alterarBenchmark(Integer id, BenchmarkTO benchmarkTO) throws ServiceException;

    void removerBenchmark(Integer id) throws ServiceException;
}
