/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.tipoEvento;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.disponibilidade.Disponibilidade;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.bean.usuario.Usuario;

import br.com.pacto.controller.json.agendamento.TipoAgendamentoDuracaoDTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoCadDTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.log.LogTO;
import org.json.JSONObject;

import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface TipoEventoService {

    public static final String SERVICE_NAME = "TipoEventoService";

    public TipoEvento inserir(final String ctx, TipoEvento object) throws ServiceException;

    public TipoEvento obterPorId(final String ctx, Integer id) throws ServiceException;

    public TipoEvento alterar(final String ctx, TipoEvento object) throws ServiceException;

    public void excluir(final String ctx, TipoEvento object) throws ServiceException;

    public List<TipoEvento> obterTodos(final String ctx, boolean somenteAtivos) throws ServiceException;

    public List<TipoEvento> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<TipoEvento> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public TipoEvento obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;
    
    public List<TipoEvento> consultarQuaisTiposUsuarioTemPermissao(String ctx,
            Usuario usuario, boolean somenteAtivos) throws ServiceException;
    public List<TipoEvento> consultarTiposNoMesmoNSU(String ctx, Integer nsu) throws ServiceException;

    public List<TipoEvento> consultarPorTipo(String ctx, TipoAgendamentoEnum tipo) throws ServiceException;

    List<TipoAgendamentoDTO> coltultarTtipoAgendamento(String nomeTipoAgendamento, Boolean ativos, PaginadorDTO paginadorDTO, boolean agenda) throws ServiceException;

    List<TipoAgendamentoDTO> obterTodosAtivos() throws ServiceException;

    TipoAgendamentoCadDTO cadastrarTipoAgendamento(TipoAgendamentoCadDTO tipoAgendamento) throws ServiceException;

    TipoAgendamentoDTO buscarTiposAgendamento(Integer id) throws ServiceException;

    TipoAgendamentoDTO atualizarTipoAgendamento(TipoAgendamentoCadDTO tipoAgendamento) throws ServiceException;

    void removerTipoAgendamento(Integer id) throws ServiceException;

    List<TipoAgendamentoDTO> obterTodosAtivosApp(String ctx) throws ServiceException;

    List<TipoEvento> obterTodosApp(final String ctx, boolean somenteAtivos) throws ServiceException;

    List<Disponibilidade> obterTodasDisponibilidadesApp(final String ctx, boolean somenteAtivos) throws ServiceException;

    TipoAgendamentoDuracaoDTO obterPorId(Integer id) throws ServiceException;

    List<LogTO> listarLog(JSONObject filtros, PaginadorDTO paginadorDTO, Integer codigo) throws ServiceException;
}
