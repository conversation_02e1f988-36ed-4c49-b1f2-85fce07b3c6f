package br.com.pacto.service.intf.retiraFicha;

import br.com.pacto.bean.retiraficha.ConfiguracaoRetiraFichaLog;
import br.com.pacto.controller.json.retiraFicha.ConfiguracaoRetiraFichaJson;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

public interface ConfiguracaoRetiraFichaLogService {

    List<ConfiguracaoRetiraFichaLog> obter<PERSON>odos(final String ctx) throws ServiceException;

    void cadastrarLogConfiguracaoRetiraFicha(String ctx, ConfiguracaoRetiraFichaJson configurac<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Integer codigoUsuario) throws ServiceException;

    List<ConfiguracaoRetiraFichaLog> listarLogsAlteracoes(String ctx, Integer codigoConfiguracaoRetiraFicha) throws Exception;
}
