package br.com.pacto.service.intf.agendatotal;

import br.com.pacto.controller.json.agendamento.ServicoAgendamentoDTO;
import br.com.pacto.controller.json.agendamento.TurmaResponseDTO;
import br.com.pacto.controller.json.locacao.TipoHorarioLocacaoEnum;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.util.UteisValidacao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

@ApiModel(description = "DTO para representar um item de agenda")
public class HorarioItemAgendaDTO {

    @ApiModelProperty(value = "Código do item de agenda", example = "1")
    private Integer codigo;

    @ApiModelProperty(value = "Ocupação atual", example = "10")
    private Integer ocupacao;

    @ApiModelProperty(value = "Capacidade máxima", example = "20")
    private Integer capacidade;

    @ApiModelProperty(value = "Data (timestamp ou string) representando o dia do mês", example = "1741575600000")
    private String diaMes;

    @ApiModelProperty(value = "Dia da semana", example = "Segunda-feira")
    private String diaSemana;

    @ApiModelProperty(value = "Identificador da turma", example = "Turma A")
    private String turma;

    @ApiModelProperty(value = "Horário de início (formato HH:mm)", example = "08:00")
    private String inicio;

    @ApiModelProperty(value = "Horário de término (formato HH:mm)", example = "09:00")
    private String fim;

    @ApiModelProperty(value = "Tipo de período", example = "Normal")
    private String tipoPeriodo;

    @ApiModelProperty(value = "Nome do ambiente", example = "Sala de Treino")
    private String ambiente;

    @ApiModelProperty(value = "Código do ambiente", example = "101")
    private Integer ambienteCodigo;

    @ApiModelProperty(value = "Cor associada ao item de agenda", example = "#FF0000")
    private String cor;

    @ApiModelProperty(value = "Nome do professor", example = "João da Silva")
    private String professor;

    @ApiModelProperty(value = "Tipo do item de agenda", example = "disponibilidades")
    private TipoItemAgendaEnum tipo;

    @ApiModelProperty(value = "Lista de serviços disponíveis para agendamento", dataType = "List[TipoAgendamentoDTO]")
    private List<TipoAgendamentoDTO> servicosDisponiveis;

    @ApiModelProperty(value = "Serviço agendado, se houver", dataType = "ServicoAgendamentoDTO")
    private ServicoAgendamentoDTO servicoAgendado;

    @ApiModelProperty(value = "Indica se o agendamento de locação foi cancelado", example = "false")
    private Boolean agendamentoLocacaoCancelada = false;

    @ApiModelProperty(value = "Indica se é uma aula coletiva", example = "true")
    private Boolean aulaColetiva;

    @ApiModelProperty(value = "Indica se deve bloquear matrículas acima do limite", example = "false")
    private Boolean bloquearMatriculasAcimaLimite;

    @ApiModelProperty(value = "Tipo de horário de locação", example = "NORMAL")
    private TipoHorarioLocacaoEnum tipoHorario;

    @ApiModelProperty(value = "Idade máxima para participar da aula", example = "10")
    private Integer idadeMaxima;

    @ApiModelProperty(value = "Idade em meses (maximo) para participar da aula", example = "6")
    private Integer idadeMaximaMeses;

    @ApiModelProperty(value = "Idade minima para participar da aula", example = "18")
    private Integer idadeMinima;

    @ApiModelProperty(value = "Idade em meses (minimo) para participar da aula", example = "10")
    private Integer idadeMinimaMeses;

    @ApiModelProperty(value = "Código da locação do horário", example = "200")
    private Integer locacaoHorarioCodigo;

    @ApiModelProperty(value = "Indica se permite aula experimental", example = "true")
    private Boolean permiteAulaExperimental;

    @ApiModelProperty(value = "Quantidade máxima de alunos experimentais", example = "5")
    private Integer qtdeMaximaAlunoExperimental;

    @ApiModelProperty(value = "Quantidade de aulas experimentais disponíveis", example = "3")
    private Integer qtdeAulaExperimentalDisponivel;

    public HorarioItemAgendaDTO(String diaMes, String inicio,  String fim, List<TipoAgendamentoDTO> servicosDisponiveis) {
        this.diaMes = diaMes;
        this.inicio = inicio;
        this.fim = fim;
        this.tipo = TipoItemAgendaEnum.disponibilidades;
        this.servicosDisponiveis = servicosDisponiveis;
    }
    public HorarioItemAgendaDTO(Date dia, ServicoAgendamentoDTO servicoAgendado) {
        this.tipo = TipoItemAgendaEnum.servico;
        this.servicoAgendado = servicoAgendado;
        this.diaMes = String.valueOf(dia.getTime());
        this.codigo = servicoAgendado.getId();
        this.turma = servicoAgendado.getTipoAgendamento().getNome();
        this.inicio = servicoAgendado.getHorarioInicial();
        this.fim = servicoAgendado.getHorarioFinal();
        this.cor = servicoAgendado.getTipoAgendamento().getCor();
        this.professor = servicoAgendado.getProfessor().getNome();
    }

    public HorarioItemAgendaDTO() {
    }

    public HorarioItemAgendaDTO(TurmaResponseDTO t) {
        this.tipo = TipoItemAgendaEnum.aula;
        this.codigo = t.getHorarioTurmaId();
        this.ocupacao = t.getNumeroAlunos();
        this.capacidade = t.getCapacidade();
        this.diaMes = t.getDia();
        this.turma = t.getNome();
        this.inicio = t.getHorarioInicio();
        this.fim = t.getHorarioFim();
        this.ambiente = t.getAmbiente().getNome();
        this.cor = t.getModalidade().getCor();
        if (t.getProfessorSubstituto() != null && !UteisValidacao.emptyString(t.getProfessorSubstituto().getNome())){
            this.professor = t.getProfessorSubstituto().getNome();
        } else {
            this.professor = t.getProfessor().getNome();
        }
        this.aulaColetiva = t.getAulaColetiva();
        this.bloquearMatriculasAcimaLimite = t.getBloquearMatriculasAcimaLimite();
        this.idadeMaxima = t.getIdadeMaxima();
        this.idadeMaximaMeses = t.getIdadeMaximaMeses();
        this.idadeMinima = t.getIdadeMinima();
        this.idadeMinimaMeses = t.getIdadeMinimaMeses();
        this.permiteAulaExperimental = t.getPermitirAulaExperimental();
        this.qtdeMaximaAlunoExperimental = t.getQtdeMaximaAlunoExperimental();
        this.qtdeAulaExperimentalDisponivel = t.getQtdeAulaExperimentalDisponivel();
    }

    public String getDiaMes() {
        return diaMes;
    }

    public void setDiaMes(String diaMes) {
        this.diaMes = diaMes;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getTurma() {
        return turma;
    }

    public void setTurma(String turma) {
        this.turma = turma;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public TipoItemAgendaEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoItemAgendaEnum tipo) {
        this.tipo = tipo;
    }

    public List<TipoAgendamentoDTO> getServicosDisponiveis() {
        return servicosDisponiveis;
    }

    public void setServicosDisponiveis(List<TipoAgendamentoDTO> servicosDisponiveis) {
        this.servicosDisponiveis = servicosDisponiveis;
    }

    public ServicoAgendamentoDTO getServicoAgendado() {
        return servicoAgendado;
    }

    public void setServicoAgendado(ServicoAgendamentoDTO servicoAgendado) {
        this.servicoAgendado = servicoAgendado;
    }

    public String getTipoPeriodo() {
        return tipoPeriodo;
    }

    public void setTipoPeriodo(String tipoPeriodo) {
        this.tipoPeriodo = tipoPeriodo;
    }

    public Boolean getAgendamentoLocacaoCancelada() {
        if (this.agendamentoLocacaoCancelada == null) {
            return false;
        }
        return agendamentoLocacaoCancelada;
    }

    public void setAgendamentoLocacaoCancelada(Boolean agendamentoLocacaoCancelada) {
        if (agendamentoLocacaoCancelada == null) {
            agendamentoLocacaoCancelada = false;
        }
        this.agendamentoLocacaoCancelada = agendamentoLocacaoCancelada;
    }


    public Boolean getAulaColetiva() {
        return aulaColetiva;
    }

    public void setAulaColetiva(Boolean aulaColetiva) {
        this.aulaColetiva = aulaColetiva;
    }

    public Boolean getBloquearMatriculasAcimaLimite() {
        return bloquearMatriculasAcimaLimite;
    }

    public void setBloquearMatriculasAcimaLimite(Boolean bloquearMatriculasAcimaLimite) {
        this.bloquearMatriculasAcimaLimite = bloquearMatriculasAcimaLimite;
    }

    public TipoHorarioLocacaoEnum getTipoHorario() {
        return tipoHorario;
    }

    public void setTipoHorario(TipoHorarioLocacaoEnum tipoHorario) {
        this.tipoHorario = tipoHorario;
    }

    public Integer getAmbienteCodigo() {
        return ambienteCodigo;
    }

    public void setAmbienteCodigo(Integer ambienteCodigo) {
        this.ambienteCodigo = ambienteCodigo;
    }

    public Integer getLocacaoHorarioCodigo() {
        return locacaoHorarioCodigo;
    }

    public void setLocacaoHorarioCodigo(Integer locacaoHorarioCodigo) {
        this.locacaoHorarioCodigo = locacaoHorarioCodigo;
    }

    public Integer getIdadeMaxima() {
        return idadeMaxima;
    }

    public void setIdadeMaxima(Integer idadeMaxima) {
        this.idadeMaxima = idadeMaxima;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getIdadeMinima() {
        return idadeMinima;
    }

    public void setIdadeMinima(Integer idadeMinima) {
        this.idadeMinima = idadeMinima;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public Integer getQtdeMaximaAlunoExperimental() {
        return qtdeMaximaAlunoExperimental;
    }

    public Boolean getPermiteAulaExperimental() {
        return permiteAulaExperimental;
    }

    public void setPermiteAulaExperimental(Boolean permiteAulaExperimental) {
        this.permiteAulaExperimental = permiteAulaExperimental;
    }

    public Integer getQtdeAulaExperimentalDisponivel() {
        return qtdeAulaExperimentalDisponivel;
    }

    public void setQtdeAulaExperimentalDisponivel(Integer qtdeAulaExperimentalDisponivel) {
        this.qtdeAulaExperimentalDisponivel = qtdeAulaExperimentalDisponivel;
    }
}
