package br.com.pacto.service.intf.agendatotal;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.controller.json.agendamento.AlunoTurmaDTO;
import br.com.pacto.service.exception.ServiceException;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

public interface AlunosAulaService {

    List<AlunoTurmaDTO> alunosAula(Integer empresa,
                                   Date dia,
                                   Integer horarioTurma,
                                   PaginadorDTO paginadorDTO,
                                   HttpServletRequest request) throws ServiceException;
}
