/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.programa;

import br.com.pacto.bean.programa.Compromisso;
import br.com.pacto.service.exception.ServiceException;
import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface CompromissoService {

    public static final String SERVICE_NAME = "CompromissoService";

    public Compromisso inserir(final String ctx, Compromisso object) throws ServiceException;

    public Compromisso obterPorId(final String ctx, Integer id) throws ServiceException;

    public Compromisso alterar(final String ctx, Compromisso object) throws ServiceException;

    public void excluir(final String ctx, Compromisso object) throws ServiceException;

    public List<Compromisso> obterTodos(final String ctx) throws ServiceException;

    public List<Compromisso> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Compromisso> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Compromisso obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

}
