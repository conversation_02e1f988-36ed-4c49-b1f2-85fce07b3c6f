/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.aparelho;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.aparelho.AparelhoEmpresa;
import br.com.pacto.controller.json.programa.AparelhoResponseTO;
import br.com.pacto.controller.json.programa.AparelhoTO;
import br.com.pacto.controller.json.programa.AtividadeAparelhoResponseRecursiveTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.aparelho.FiltroAparelhoJSON;

import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface AparelhoService {

    public static final String SERVICE_NAME = "AparelhoService";

    public Aparelho inserir(final String ctx, Aparelho object) throws ServiceException;

    public Aparelho obterPorId(final String ctx, Integer id) throws ServiceException;

    public Aparelho alterar(final String ctx, Aparelho object) throws ServiceException;

    public void excluir(final String ctx, Aparelho object) throws ServiceException;

    public List<Aparelho> obterTodos(final String ctx, final boolean crossfit) throws ServiceException;

    public List<Aparelho> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Aparelho> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Aparelho obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;
    
    public AparelhoEmpresa obterIdentificadorEmpresa(final String ctx, final Aparelho aparelho,
            Integer empresa)throws ServiceException;
    
    public List<AparelhoEmpresa> obterIdentificadoresEmpresa(final String ctx, final Aparelho aparelho)throws ServiceException;

    List<AparelhoResponseTO>consultarAparelhos(FiltroAparelhoJSON filtroAparelhoJSON, PaginadorDTO paginadorDTO)throws ServiceException;

    AparelhoResponseTO inserir(AparelhoTO aparelhoTO) throws Exception;

    AparelhoResponseTO alterar(Integer id, AparelhoTO aparelhoTO) throws ServiceException;

    void excluir(Integer codigoAparelho) throws ServiceException;

    AparelhoResponseTO consultarAparelho(Integer codigoAparelho) throws ServiceException;

    List<AtividadeAparelhoResponseRecursiveTO> listaTodosAparelhos(boolean crossfit) throws ServiceException;

    List<AparelhoResponseTO> aparelhosHabilitadosReservaEquipamento() throws ServiceException;

}
