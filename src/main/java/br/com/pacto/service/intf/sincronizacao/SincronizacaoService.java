/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.intf.sincronizacao;

import br.com.pacto.bean.sincronizacao.TipoClassSincronizarEnum;
import br.com.pacto.service.exception.ServiceException;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface SincronizacaoService {

    public static final String SERVICE_NAME = "SincronizacaoService";

    public List obterLista(final String ctx, TipoClassSincronizarEnum tipoClass, Date dataBase) throws ServiceException;
}
