/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.musculo;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.musculo.GrupoMuscular;
import br.com.pacto.bean.musculo.GrupoMuscularResponseTO;
import br.com.pacto.bean.musculo.GrupoMuscularResumidoResponseTO;
import br.com.pacto.bean.musculo.GrupoMuscularTO;
import br.com.pacto.controller.json.musculo.FiltroGrupoMuscularJSON;
import br.com.pacto.controller.json.musculo.GrupoMuscularSimplesResponseTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;

import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface GrupoMuscularService {

    public static final String SERVICE_NAME = "GrupoMuscularService";

    public GrupoMuscular inserir(final String ctx, GrupoMuscular object) throws ServiceException, ValidacaoException;

    public GrupoMuscular obterPorId(final String ctx, Integer id) throws ServiceException;

    public GrupoMuscular alterar(final String ctx, GrupoMuscular object) throws ServiceException, ValidacaoException;

    public void excluir(final String ctx, GrupoMuscular object) throws ServiceException;

    public List<GrupoMuscular> obterTodos(final String ctx) throws ServiceException;

    public List<GrupoMuscular> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<GrupoMuscular> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public GrupoMuscular obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public void povoarPerimetros(String ctx) throws ServiceException;

    List<GrupoMuscularResumidoResponseTO>consultarGruposMusculares(FiltroGrupoMuscularJSON filtroGrupoMuscularJSON, PaginadorDTO paginadorDTO)throws ServiceException;
    GrupoMuscularResponseTO consultarGrupoMuscular(Integer id)throws ServiceException;
    void excluir(Integer id) throws ServiceException;
    GrupoMuscularResponseTO inserir(GrupoMuscularTO grupoMuscularTO) throws ServiceException;
    GrupoMuscularResponseTO alterar(Integer id, GrupoMuscularTO grupoMuscularTO) throws ServiceException;

    List<GrupoMuscularSimplesResponseTO> consultarTodosGruposMusculares() throws ServiceException;

    Map<Integer, List<GrupoMuscularResponseTO>> consultarGruposMuscularesPorAtividades(String ctx, List<Integer> codigosAtividades) throws ServiceException;

}
