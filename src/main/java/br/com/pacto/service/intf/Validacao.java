package br.com.pacto.service.intf;

import java.util.List;

public interface Validacao {

    public boolean isNull(Object object);

    public boolean isNotNull(Object object);

    public boolean isEqualValue(Object object1, Object object2);

    public boolean isNotEqualValue(Object object1, Object object2);

    public boolean isEmpty(Object object);

    public boolean isEmpty(List list);

    public boolean isNotEmpty(Object object);

    public boolean isValidTime(String txtHora);

    public boolean isValorIgual(Object obj1, Object obj2);

    public boolean emptyString(String str);

    public boolean emptyNumber(Number n);
}
