package br.com.pacto.service.intf.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "Retrospectiva anual completa do cliente com estatísticas de treino, evolução e conquistas")
public class RetrospectivaAnoVO {

    @ApiModelProperty(value = "Quantidade total de treinos realizados no ano", example = "156")
    private Integer quantidadeTreinosNoAno = 0;

    @ApiModelProperty(value = "Quantidade de atividades/exercícios concluídos durante o ano", example = "2340")
    private Integer quantidadeDeAtividadesConcluidas = 0;

    @ApiModelProperty(value = "Quantidade total de peso levantado no ano em quilogramas", example = "45600")
    private Integer quantidadeDePesoLevantado = 0;

    @ApiModelProperty(value = "Lista das 3 atividades mais realizadas pelo cliente", example = "[\"Supino\", \"Agachamento\", \"Levantamento Terra\"]")
    private List<String> top3Atividades = new ArrayList<>();

    @ApiModelProperty(value = "Quantidade total de calorias queimadas durante o ano", example = "78500")
    private Integer quantidadeDeCaloriasQueimadas = 0;

    @ApiModelProperty(value = "Total de aulas agendadas pelo cliente no ano", example = "180")
    private Integer totalDeAulasMarcadas = 0;

    @ApiModelProperty(value = "Aula/modalidade favorita do cliente baseada na frequência", example = "CrossFit")
    private String aulaFavorita = "";

    @ApiModelProperty(value = "Total de avaliações físicas realizadas no ano", example = "4")
    private Integer totalDeAvaliacoesFisicas = 0;

    @ApiModelProperty(value = "Diferença de peso entre a primeira e última avaliação física do ano", example = "-3.5")
    private Double pesoEntreAvaliacoes = 0.0;

    @ApiModelProperty(value = "Quantidade de WODs (Workout of the Day) realizados no ano", example = "89")
    private Integer wodsRealizados = 0;

    @ApiModelProperty(value = "Tempo total gasto em WODs durante o ano em minutos", example = "2670")
    private Integer tempoTotalWods = 0;

    @ApiModelProperty(value = "Quantidade de vezes que ficou em 3º lugar no ranking", example = "12")
    private Integer posicaoRanking3lugar = 0;

    @ApiModelProperty(value = "Quantidade de vezes que ficou em 2º lugar no ranking", example = "8")
    private Integer posicaoRanking2lugar = 0;

    @ApiModelProperty(value = "Quantidade de vezes que ficou em 1º lugar no ranking", example = "5")
    private Integer posicaoRanking1lugar = 0;

    @ApiModelProperty(value = "Quantidade de dias únicos que frequentou a academia no ano", example = "145")
    private Integer diasFrequentados = 0;

    @ApiModelProperty(value = "Quantidade de treinos realizados no período da manhã", example = "45")
    private Integer qtdTreinosManha = 0;

    @ApiModelProperty(value = "Quantidade de treinos realizados no período da tarde", example = "67")
    private Integer qtdTreinosTarde = 0;

    @ApiModelProperty(value = "Quantidade de treinos realizados no período da noite", example = "44")
    private Integer qtdTreinosNoite = 0;

    public Integer getQuantidadeTreinosNoAno() {
        return quantidadeTreinosNoAno;
    }

    public void setQuantidadeTreinosNoAno(Integer quantidadeTreinosNoAno) {
        this.quantidadeTreinosNoAno = quantidadeTreinosNoAno;
    }

    public Integer getQuantidadeDeAtividadesConcluidas() {
        return quantidadeDeAtividadesConcluidas;
    }

    public void setQuantidadeDeAtividadesConcluidas(Integer quantidadeDeAtividadesConcluidas) {
        this.quantidadeDeAtividadesConcluidas = quantidadeDeAtividadesConcluidas;
    }

    public Integer getQuantidadeDePesoLevantado() {
        return quantidadeDePesoLevantado;
    }

    public void setQuantidadeDePesoLevantado(Integer quantidadeDePesoLevantado) {
        this.quantidadeDePesoLevantado = quantidadeDePesoLevantado;
    }

    public List<String> getTop3Atividades() {
        return top3Atividades;
    }

    public void setTop3Atividades(List<String> top3Atividades) {
        this.top3Atividades = top3Atividades;
    }

    public Integer getQuantidadeDeCaloriasQueimadas() {
        return quantidadeDeCaloriasQueimadas;
    }

    public void setQuantidadeDeCaloriasQueimadas(Integer quantidadeDeCaloriasQueimadas) {
        this.quantidadeDeCaloriasQueimadas = quantidadeDeCaloriasQueimadas;
    }

    public Integer getTotalDeAulasMarcadas() {
        return totalDeAulasMarcadas;
    }

    public void setTotalDeAulasMarcadas(Integer totalDeAulasMarcadas) {
        this.totalDeAulasMarcadas = totalDeAulasMarcadas;
    }

    public String getAulaFavorita() {
        return aulaFavorita;
    }

    public void setAulaFavorita(String aulaFavorita) {
        this.aulaFavorita = aulaFavorita;
    }
    public Integer getTotalDeAvaliacoesFisicas() {
        return totalDeAvaliacoesFisicas;
    }

    public void setTotalDeAvaliacoesFisicas(Integer totalDeAvaliacoesFisicas) {
        this.totalDeAvaliacoesFisicas = totalDeAvaliacoesFisicas;
    }

    public Double getPesoEntreAvaliacoes() {
        return pesoEntreAvaliacoes;
    }

    public void setPesoEntreAvaliacoes(Double pesoEntreAvaliacoes) {
        this.pesoEntreAvaliacoes = pesoEntreAvaliacoes;
    }

    public Integer getWodsRealizados() {
        return wodsRealizados;
    }

    public void setWodsRealizados(Integer wodsRealizados) {
        this.wodsRealizados = wodsRealizados;
    }

    public Integer getTempoTotalWods() {
        return tempoTotalWods;
    }

    public void setTempoTotalWods(Integer tempoTotalWods) {
        this.tempoTotalWods = tempoTotalWods;
    }

    public Integer getPosicaoRanking3lugar() {
        return posicaoRanking3lugar;
    }

    public void setPosicaoRanking3lugar(Integer posicaoRanking3lugar) {
        this.posicaoRanking3lugar = posicaoRanking3lugar;
    }

    public Integer getPosicaoRanking2lugar() {
        return posicaoRanking2lugar;
    }

    public void setPosicaoRanking2lugar(Integer posicaoRanking2lugar) {
        this.posicaoRanking2lugar = posicaoRanking2lugar;
    }

    public Integer getPosicaoRanking1lugar() {
        return posicaoRanking1lugar;
    }

    public void setPosicaoRanking1lugar(Integer posicaoRanking1lugar) {
        this.posicaoRanking1lugar = posicaoRanking1lugar;
    }

    public Integer getDiasFrequentados() {
        return diasFrequentados;
    }

    public void setDiasFrequentados(Integer diasFrequentados) {
        this.diasFrequentados = diasFrequentados;
    }

    public Integer getQtdTreinosManha() {
        return qtdTreinosManha;
    }

    public void setQtdTreinosManha(Integer qtdTreinosManha) {
        this.qtdTreinosManha = qtdTreinosManha;
    }

    public Integer getQtdTreinosTarde() {
        return qtdTreinosTarde;
    }

    public void setQtdTreinosTarde(Integer qtdTreinosTarde) {
        this.qtdTreinosTarde = qtdTreinosTarde;
    }

    public Integer getQtdTreinosNoite() {
        return qtdTreinosNoite;
    }

    public void setQtdTreinosNoite(Integer qtdTreinosNoite) {
        this.qtdTreinosNoite = qtdTreinosNoite;
    }
}
