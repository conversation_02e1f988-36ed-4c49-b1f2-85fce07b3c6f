package br.com.pacto.service.intf.agenda;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.disponibilidade.Disponibilidade;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.controller.json.agendamento.*;
import br.com.pacto.controller.json.disponibilidade.HorarioDisponibilidadeDTO;
import br.com.pacto.controller.json.disponibilidade.ItemValidacaoDisponibilidadeDTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.service.exception.ServiceException;
import org.json.JSONObject;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface DisponibilidadeService {

    Map<String, List<ServicoAgendamentoDisponibilidadeDTO>> disponibilidades(Integer empresaId, Date dia, PeriodoFiltrarEnum periodo, FiltrosAgendamentosDTO filtros, HttpServletRequest request) throws Exception;

    List<DisponibilidadeConfigDTO> disponibilidadesTipoProfessor(Integer tipo, Integer professor) throws Exception;

    List<DisponibilidadeConfigDTO> criarAlterarDisponibilidadeConfig(DisponibilidadeConfigGeradoraDTO configs) throws ServiceException;

    List<DisponibilidadeConfigDTO> disponibilidadesTipo(Integer id) throws Exception;

    List<DisponibilidadeConfigDTO> todasDisponibilidades(Integer empresaId, PaginadorDTO paginadorDTO, FiltroDisponibilidadeDTO filtroDisponibilidadeDTO) throws ServiceException;

    void removerDisponibilidade(Integer empresaId, Integer id, Boolean posteriores) throws ServiceException;

    void gerarConfigDisponibilidadePorAgendamentos(String ctx) throws ServiceException;

    Map<String, List<AgendamentoDisponibilidadePersonalDTO>> disponibilidadesApp(String ctx, Integer empresaId, Date dia, PeriodoFiltrarEnum periodo, FiltrosAgendamentosDTO filtros, HttpServletRequest request, Integer sizeTipos, Boolean appTreino, Integer matricula) throws Exception;

    Map<String, List<AgendaDisponibilidadeDTO>> disponibilidadesAgendaV3(Integer empresaId,
                                                                         HttpServletRequest request,
                                                                         Date diaReferencia,
                                                                         PeriodoFiltrarEnum periodo,
                                                                         Boolean apenasDisponiveis,
                                                                         FiltrosAgendamentosDTO filtros, String ctx) throws ServiceException;

    void criarDisponibilidadeV2(Integer empresaId, br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO disponibilidade, String ctx, Integer usuarioId) throws ServiceException;

    void criarDisponibilidadeV2(Integer empresaId, br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO disponibilidade) throws ServiceException;

    DisponibilidadeDTO criarDisponibilidade(HttpServletRequest request, Integer empresaId, DisponibilidadeDTO disponibilidade) throws ServiceException;

    boolean configDisponibilidade(Integer id) throws Exception;

    void alterarDisponibilidade(Integer id, DisponibilidadeEditDTO edit) throws ServiceException;

    List<TipoAgendamentoDTO> consultarDisponibilidadesHorario(Date dia, Integer hora, Integer empresaId) throws ServiceException;

    List<ColaboradorSimplesTO> consultarDisponibilidadesHorarioProfessor(Date dia, Integer hora, Integer tipo, Integer empresaId) throws ServiceException;

    List<ColaboradorSimplesTO> consultarDisponibilidadesHorarioProfessorTipoAgendamento(Date dia, Integer hora, Integer tipo, Integer empresaId) throws ServiceException;

    AgendaDisponibilidadeDTO detalheDisponibilidade(Date dia, Integer hora, Integer tipo, Integer professor, Integer empresaId) throws ServiceException;

    List<TipoAgendamentoDTO> consultarDisponibilidadesHorario(Date inicio, Date fim, Integer empresaId, TipoEvento tipo, String nomeProfessor, String codigoProfessores) throws ServiceException;

    Map<String, Map<TipoAgendamentoEnum, List<AgendamentoDisponibilidadePersonalDTO>>> disponibilidadesPorComportamentoApp (String ctx, Integer empresaId, Date dia, PeriodoFiltrarEnum periodo, FiltrosAgendamentosDTO filtros, HttpServletRequest request, Integer sizeTipos) throws Exception;

    List<br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO> findAllDisponibilidades(Integer empresaId, PaginadorDTO paginadorDTO, FiltroDisponibilidadeDTO filtro) throws ServiceException;

    br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO findDisponibilidadeById(Integer codigo) throws ServiceException;

    List<ItemValidacaoDisponibilidadeDTO> findAllTipoValidacaoPlano(PaginadorDTO paginadorDTO, JSONObject filtro) throws ServiceException;

    List<ItemValidacaoDisponibilidadeDTO> findAllTipoValidacaoProduto(PaginadorDTO paginadorDTO, JSONObject filtro, String tipos) throws ServiceException;

    br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO insertDisponibilidade(br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO dto) throws ServiceException;

    br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO updateDisponibilidade(br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO dto) throws ServiceException;

    String removerDisponibilidade(br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO dto) throws ServiceException;

    Disponibilidade obterPorId(String ctx, Integer id) throws ServiceException;

    Disponibilidade obterPorIdHorario(String ctx, Integer id) throws ServiceException;

    List<TipoAgendamentoDTO> consultarDisponibilidadesHorarioTipoAgendamento(Date dia, Integer hora, Integer empresaId) throws ServiceException;

    TipoAgendamentoDuracaoDTO obterPorIdTipoAgendamentoDuracao(Integer id, Integer empresaId, Integer ambienteId, String inicio, String fim) throws ServiceException;

    AgendaDisponibilidadeDTO detalheDisponibilidadeAgenda(Date dia, Integer hora, Integer tipo, Integer professor, Integer empresaId) throws ServiceException;

    Boolean existeAgendamentoAlunoHorarioDisponibilidade(Integer idHorario, List<HorarioDisponibilidadeDTO> horarios) throws ServiceException;

    String migrarDadosAgendamentoModeloNovoDisponibilidade() throws ServiceException;
}
