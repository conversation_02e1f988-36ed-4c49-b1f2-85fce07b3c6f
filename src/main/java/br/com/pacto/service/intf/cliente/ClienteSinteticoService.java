/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.cliente;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.cliente.*;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.acompanhamento.ClienteAcompanhamentoAvaliacaoDTO;
import br.com.pacto.controller.json.agendamento.PerfilDISCDTO;
import br.com.pacto.controller.json.agendamento.PerfilDISCVO;
import br.com.pacto.controller.json.aluno.*;
import br.com.pacto.controller.json.gestao.ClienteDadosTotalPassDTO;
import br.com.pacto.controller.json.usuario.AlunoAppInfoDTO;
import br.com.pacto.enumerador.cliente.StatusClienteEnum;
import br.com.pacto.enumerador.cliente.TiposAcompanhamentoEnum;
import br.com.pacto.enumerador.cliente.TiposConsultaCatalogoAlunoEnum;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.ui.ModelMap;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
import servicos.integracao.zw.beans.UsuarioZW;
import servicos.integracao.zw.json.ColetorJSON;
import servicos.integracao.zw.json.VinculoJSON;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface ClienteSinteticoService {

    String SERVICE_NAME = "ClienteSinteticoService";

    ClienteSintetico inserir(final String ctx, ClienteSintetico object) throws ServiceException;

    ClienteSintetico inserir(final String ctx, ClienteSintetico object, String fotoKey) throws ServiceException;

    ClienteSintetico obterPorId(final String ctx, Integer id) throws ServiceException;

    ClienteSintetico obterPorCodigoPessoaZW(final String ctx, Integer codigoPessoa) throws ServiceException;

    ClienteSintetico alterar(final String ctx, ClienteSintetico object) throws ServiceException;

    ClienteSintetico alterar(final String ctx, ClienteSintetico clienteZW, ClienteSintetico clienteTreino) throws ServiceException;

    ClienteSintetico alterarAlgunsCampos(final String ctx,
                                                ClienteSintetico object, String[] campos, Object[] values) throws ServiceException;

    void excluir(final String ctx, ClienteSintetico object) throws ServiceException;

    List<ClienteSintetico> obterTodos(final String ctx, final Integer empresaZW, Integer... index) throws ServiceException;

    List<ClienteSintetico> obterPorParam(final String ctx, String queryS, Map<String, Object> params, Integer... index)
            throws ServiceException;

    List<ClienteSintetico> obterPorParam(final String ctx, String queryS, Map<String, Object> params, int max, int index)
            throws ServiceException;

    ClienteSintetico obterObjetoPorParam(final String ctx, String queryS, Map<String, Object> params)
            throws ServiceException;

    List<ClienteSintetico> consultarPorProfessor(final String ctx, Integer professor,Integer empresaZW, boolean apenasNome) throws ServiceException;

    List<ClienteSintetico> consultarPorCodigoPessoaProfessor(final String ctx, Integer pessoaProfessor, Integer empresaZW, String filtroNome, boolean apenasNome, Integer... index) throws ServiceException;

    void atualizarNivelAluno(final String ctx, Integer nivel, ClienteSintetico cliente) throws ServiceException;

    void iniciarAcompanhamento(final String ctx, final ClienteSintetico cliente, final ProfessorSintetico professor, TiposAcompanhamentoEnum tiposAcompanhamentoEnum) throws ServiceException;

    void finalizarAcompanhamento(final String ctx,
                                 final ClienteSintetico cliente,
                                 final ProfessorSintetico professor,
                                 final Integer programaId,
                                 final Integer fichaId) throws ServiceException;

    void avaliarAcompanhamento(final String ctx,
                               final ClienteAcompanhamentoAvaliacaoDTO avaliacaoDTO,
                               final Usuario usuarioLogado) throws ServiceException;

    List<ClienteSintetico> consultarPorAcompanhadosProfessor(final String ctx, Integer professor, String filtroNome, Date inicio, Date fim, Integer... index) throws ServiceException;

    List<ClienteSintetico> consultarPorAgendadosProfessor(final String ctx, final Integer empresaZW, final Integer professor, String filtroNome, Date inicio, Date fim, Integer... index) throws ServiceException;

    List<ClienteSintetico> consultarPorDesacompanhadosProfessor(final String ctx, final Integer empresaZW,
                                                                       final Integer professor, String filtroNome, Date inicio, Date fim, boolean somenteNaAcademia, PaginadorDTO paginadorDTO, String filter, HttpServletRequest request, Integer... index) throws ServiceException;

    List<ClienteSintetico> consultarPorAcompanhadosProfessor(String ctx,
                                                             Integer empresaZW, Integer professor, String filtroNome, Date inicio, Date fim,
                                                             boolean somenteNaAcademia, PaginadorDTO paginadorDTO, String filter, HttpServletRequest request, Integer... index) throws ServiceException;

    Integer consultarQuantidadePorDesacompanhadosProfessor(final String ctx, final Integer empresaZW,
                                                           final Integer professor, Date inicio, Date fim) throws ServiceException;

    Integer consultarQuantidadeEmAcompanhamentoProfessor(String ctx,
                                                         Integer empresaZW, Integer professor,
                                                         Date inicio, Date fim) throws ServiceException;

    List<ClienteSintetico> consultarAlunosSemObservacaoXDias(final String ctx, final Integer empresaZW,
                                                             final Integer professor, String filtroNome, Integer xdias, Integer... index) throws ServiceException;

    ClienteSintetico obterPorAtributo(final String ctx, final String atributo, final Object valor)throws ServiceException;

    String concluirCadastro(String ctx, ClienteSintetico clienteEscolhido,
                                   List<VinculoJSON> vinculos, IntegracaoCadastrosWSConsumer integracaoWS,
                                   String email, String senha, Integer novoProfessor, boolean usarAplicativoAgora, Integer codigoUsuario, String nomeEmpresa) throws ValidacaoException, ServiceException;

    String concluirCadastro(String ctx, ClienteSintetico clienteEscolhido,
                            List<VinculoJSON> vinculos, IntegracaoCadastrosWSConsumer integracaoWS,
                            String email, String senha, Integer novoProfessor, boolean usarAplicativoAgora, Integer codigoUsuario, boolean obrigarProfessor, String nomeEmpresa) throws ValidacaoException, ServiceException;

    ClienteSintetico consultarSimplesPorCodigoCliente(String ctx, Integer codigoCliente) throws ServiceException;

    ClienteSintetico consultarSimplesPorMatricula(String ctx, Integer matricula) throws ServiceException;

    ModelMap consultarTreinosRealizadosPorDiaSemana(String ctx, Long dataInicial, Long dataFinal, Integer codigoAluno, String username) throws ServiceException;

    List<ClienteSintetico> consultarPorMatriculaOuNome(final String ctx, final Integer empresaZW, final Integer professor, final String filtro, final Integer maxResults) throws ServiceException;

    /**
     * Consulta no banco os Clientes usando a lista de nomes ou a lista de matricula informados.<br/>
     * <b>ATENÇÃO:</b> apenas um dos dois dados pode ser nulo {@code matriculas} ou {@code nomes}. Os dois não podem
     * ser nulos ou vazios simultaneamente.
     *
     * @param contexto   Contexto em que os dados se encontram (chave do banco)
     * @param empresaZW  ID da empresa no ZW
     * @param professor  ID do professor (pode ser nulo)
     * @param matriculas Matrículas dos clientes que se deseja procurar (pode ser nulo ou vazio)
     * @param nomes      Nomes dos clientes que se deseja procurar (pode ser nulo ou vazio)
     * @param maxResults Número máximo de resultados
     * @param request    Requisição que chegou
     * @return O mapa de clientes encontrados com o identificador sendo o nome ou a matrícula
     * @throws ServiceException Caso ocorra qualquer problema durante a consulta ou se os {@code nomes} e as
     * {@code matriculas} forem nulos ou vazios simultaneamente.
     */
    Map<String, ClienteSintetico> consultarPorMatriculasOuNomes(final String contexto, final Integer empresaZW,
                                                                final Integer professor, final List<Integer> matriculas,
                                                                final List<String> nomes, final Integer maxResults,
                                                                final HttpServletRequest request) throws ServiceException;

    List<ClienteSintetico> consultarNaAcademia(final String ctx, final Integer empresaZW, final Integer professor, String filtroNome, final Date inicio, StatusClienteEnum status, final Integer... index) throws ServiceException;

    List<ClienteSintetico> consultarSimplesPorNome(final String ctx, final Integer empresaZW, final String nome) throws ServiceException;

    ClienteAcompanhamento obterAcompanhamento(final String ctx, final ClienteSintetico cliente) throws ServiceException ;

    List<ClienteSintetico> consultarSemTreino(final String ctx, final Integer empresaZW, final Integer professor, String filtroNome,
                                                     final Integer max,final Integer index) throws ServiceException;

    List<ClienteSintetico> consultarTreinoVencido(final String ctx, final Integer empresaZW, final Integer professor, String filtroNome, Integer max, Integer index) throws ServiceException;

    List<ClienteSintetico> consultarTodosSoNomeCodigo(final String ctx, final Integer empresaZW) throws ServiceException;

    boolean mostrarTodosAlunos(final Usuario usuario);

    List<ClienteSintetico> filtrarStatus(final String ctx, Usuario usuario, StatusClienteEnum status, String filtroNome, Integer max, Integer index) throws ServiceException;

    List<ClienteSintetico> filtrarStatusApp(final String ctx, Usuario usuario, StatusClienteEnum status, String filtroNome, Integer max, Integer index, Integer empresa) throws ServiceException;

    void atualizarNrTreinosRealizados(final String ctx, final Integer codCliente,
                                             final Integer nrTreinos) throws ServiceException;

    ClienteObservacao gravarObservacaoCliente(final String ctx, ClienteObservacao clienteObservacao) throws ServiceException;

    void excluirObservacaoCliente(final String ctx, ClienteObservacao clienteObservacao) throws ServiceException;

    ClienteObservacao consultarUltimaObservacaoCliente(final String ctx, ClienteSintetico cliente, boolean avaliacao) throws ServiceException;

    List<ClienteObservacao> consultarObservacoesCliente(final String ctx, ClienteSintetico cliente, boolean avaliacao) throws ServiceException;

    ClienteSintetico obterPorCodigoCliente(final String ctx, final Integer codigo) throws ServiceException;

    void excluirAluno(String ctx, Integer codigoCliente) throws ServiceException;

    void excluirAluno(String ctx, Integer codigoCliente, ClienteSintetico clienteSintetico) throws ServiceException;

    void excluirAlunoMatricula(String ctx, Integer matricula) throws ServiceException;

    ClienteObservacao gravarAlteracaoObservacaoCliente(final String ctx, ClienteSintetico cliente,
                                                              String obs, boolean avaliacaoFisica, Usuario usuario,Boolean importante,int codigo) throws ServiceException;

    ClienteObservacao alterarObservacaoCliente(final String ctx, ClienteObservacao obj) throws ServiceException;

    String addAluno(final String ctx, final Integer matricula, final Integer professor, boolean usarAplicativo, Integer codigoUsuario, Integer empresa) throws Exception;

    String addAluno(final String ctx, final Integer matricula, final Integer professor, boolean usarAplicativo, Integer codigoUsuario, Integer empresa, boolean obrigarProfessor) throws Exception;

    ClienteSintetico consultarPorMatricula(String ctx, String matricula) throws ServiceException;

    void atualizarProfessorAluno(final String ctx,
                                        IntegracaoCadastrosWSConsumer integracaoWS,
                                        Integer professor, ClienteSintetico cliente, Integer codigoUsuario, HttpServletRequest request) throws ServiceException;

    void inserirLogAlteracaoProfessorAluno(final String ctx, ProfessorSintetico profAntesAlteracao, ProfessorSintetico novoProfessor, Integer matriculaAluno, String username) throws ServiceException;

    List<ClienteSintetico> consultarPorNome(final String ctx, final String nome) throws ServiceException;

    ClienteSintetico obterPorCodigo(final String ctx, final Integer codigo) throws ServiceException;

    void baixarFotoAluno(String ctx, HttpServletRequest request, String codigopessoa, String fotoKey, Boolean atualizarFotoApp) throws Exception;

    JSONArray todosAlunos(String key) throws Exception;

    void incrementarVersao(final String ctx,
                                  int codigoCliente) throws ServiceException;

    boolean existeAlunoVinculadoProfessor(final String ctx, Integer professor) throws ServiceException;

    List<String> deletarAlunosForaZW(String ctx);

    ClienteSintetico consultarPorCodigoPessoa(String ctx, String codigopessoa) throws ServiceException;

    void removerFotoAluno(String ctx, HttpServletRequest request, String codigopessoa, Boolean atualizarFotoApp) throws Exception;

    List<ClienteSintetico> consultarFotosAtualizar(final String ctx, Date dataHora) throws ServiceException;

    void adicionarUsuarioServicoDescobrir(String ctx, String email) throws IOException;

    void atualizarDadosFC(final String ctx, final Integer fcMaxima, final Integer fcRepouso, final Integer codigoCliente) throws Exception;

    void executeNativeSQL(final String ctx, String sql) throws ServiceException;

    ClienteSintetico consultarPorCodigoExterno(String ctx, String codigoExterno) throws ServiceException;

    List<ClienteConsulta> consultaSimplificadaAlunos(String key, String param, Integer professor, Integer empresaZW, StatusClienteEnum status) throws Exception;

    List<AlunoCatalogoResponseTO> obterCatalogoAlunos(HttpServletRequest request, Integer empresaId, String filtroNome, TiposConsultaCatalogoAlunoEnum tipo) throws ServiceException;

    List<NivelAlunoResponseTO> obterNiveisAluno() throws ServiceException;

    AlunoResponseTO cadastrarAluno(String ctx, AlunoDTO AlunoDTO, Integer empresaId) throws ServiceException;

    AlunoResponseTO cadastrarAlunoPersonalFit(AlunoDTO alunoDTO, String ctx) throws ServiceException;

    List<ClienteObservacaoDTO> obterObservacoes(Integer alunoID, Boolean codigoClienteZw, Boolean matricula) throws ServiceException;

    String obterClienteMensagem(String key, Integer alunoID, String tipoMensagem);

    List<AtestadoResponseTO> obterAnexosAtestados(Integer alunoID) throws ServiceException;

    List<ClienteObservacaoAnexosDTO> obterObservacoesAnexos(Integer alunoID, Boolean buscarAnexoZw, PaginadorDTO paginadorDTO) throws ServiceException;

    ClienteObservacaoAnexosDTO obterObservacaoAnexos(Integer anexoID) throws ServiceException;

    AlunoResponseTO obterUmAluno(Integer alunoID, Integer matricula, Integer empresa, HttpServletRequest request) throws ServiceException;

    AlunoSimplesDTO obterAlunoSimples(Integer matricula) throws ServiceException;

    AlunoResponseTO obterSomenteCodigo(Integer matricula, boolean importarNaoExistir) throws ServiceException;

    AlunoObservacaoDTO gravarObservacao(Integer alunoId, AlunoObservacaoDTO observacaoDTO) throws ServiceException;

    AlunoObservacaoDTO gravarObservacaoAnexos(Integer alunoId, AlunoObservacaoDTO observacaoDTO) throws ServiceException;

    String downloadAnexoAtestado(Integer alunoId, Integer atestadoId)throws ServiceException;

    AlunoResponseTO alterarAluno(Integer id, AlunoDTO AlunoDTO, HttpServletRequest request) throws ServiceException;

    void deletarUmAluno(Integer id) throws ServiceException;

    void deletarObservacao(Integer id) throws ServiceException;

    List<AlunoResponseTO> listaAlunos(FiltroAlunoJSON filtros, PaginadorDTO paginadorDTO, HttpServletRequest request, Integer empresaId,
                                      Boolean permiteAlunoOutraEmpresa, Boolean incluirAutorizado) throws ServiceException;

    List<AlunoResponseTO> listaAlunosSelect(FiltroAlunoJSON filtros, PaginadorDTO paginadorDTO, HttpServletRequest request, Integer empresaId,
                                      Boolean permiteAlunoOutraEmpresa, Boolean incluirAutorizado) throws ServiceException;

    List<AlunoResponseTO> listaPassivosFila(FiltroAlunoJSON filtros, Integer empresaId) throws ServiceException;

    List<AlunoSimplesDTO> all() throws ServiceException;

    AvaliacaoAlunoResponseDTO carregarAvaliacoesAluno(Integer alunoId) throws ServiceException;

    List<LinhaDeTempoAlunoResponseDTO> carregarLinhaDeTempoAluno(Integer matriculaAluno, FiltroLinhaTempoDTO filtroLinhaTempoDTO) throws ServiceException;

    AvaliacaoAlunoResponseDTO alterarDataAvaliacaoFisica(Integer avaliacaoId, AvaliacaoAlunoDTO avaliacaoAlunoDTO) throws ServiceException;

    String reenviarConfirmacaoApp(Integer alunoId) throws ServiceException;
    
    void refresh(final String ctx, ClienteSintetico object) throws Exception;

    List<AvaliacaoCatalogoAlunoDTO> obterCatalogoAlunosAvaliacaoFisica(HttpServletRequest request, String filtroNome, Integer empresaId) throws ServiceException;

    DetalheTreinoAlunoDTO detalhesTreinamentoAluno(Integer alunoId) throws ServiceException;

    void editarSituacao(AlunoDTO alunoDTO) throws ServiceException;

    AlunoOlympiaDTO buscarAlunoOlympia(String codigo) throws ServiceException;

    Boolean validarCadastroOlympia(String codigoExterno) throws ServiceException;

    List<AlunoZWResponseDTO> obterAlunoZW(String nome, String cpf, Integer matricula, Integer empresaId, HttpServletRequest request) throws ServiceException;

    void cadastrarAlunoZW(AlunoZWDTO alunoZWDTO) throws ServiceException;

    List<ColetorJSON> consultarLocaisAcesso(final String ctx, final Integer empresa) throws Exception;

    ColetorJSON consultarLocalAcessoPorNFC(final String ctx, final String nfc) throws Exception;

    void criarUsuarioMovelAluno(UsuarioSimplesAlunoDTO usuarioSimples) throws ServiceException;

    String consultarOrfaos(final String ctx, final String codigosPessoa) throws Exception;

    ClienteSintetico consultarPorGympassUniqueToken(String ctx, String gympassUniqueToken) throws ServiceException;

    ClienteSintetico consultarGympassBooking(String ctx, Empresa empresa, String gympassUniqueToken, String email) throws ServiceException;

    ClienteSintetico consultarAtivoPorMatricula(String ctx, Integer matricula) throws ServiceException;

    List<ClienteSintetico> matriculas(String ctx, Integer empresa) throws ServiceException;

    void acao(final AcaoAlunoEnum acao, String matricula);

    void leaveAcao();

    String sincronizarDadosClienteSintetico(String ctx) throws Exception;

    String sincronizarMatriculaClienteSintetico(final String ctx, Integer empresaZW) throws Exception;

    String buscarAssinaturaDigitalContratosAluno(String ctx, String matricula, String empresa, Boolean validar) throws ServiceException;

    String buscarAssinaturaDigitalContratosAlunoByContrato(String ctx, String contrato, String empresa) throws ServiceException;

    String incluirAssinaturaDigitalContratoAluno(String ctx, String contrato, String assinatura) throws ServiceException;

    ClienteSintetico consultarPorCpf(final String ctx, final String cpf) throws Exception;

    List<AvaliacaoProfessorDTO> avaliaProfessores(List<AvaliacaoProfessorDTO> avaliacaoProfessorDTO, String ctx) throws Exception;
    AvaliacaoProfessorVO novaAvaliacaoProfessor(AvaliacaoProfessorDTO avaliacaoProfessorDTO, String ctx) throws Exception;

    AvaliacaoProfessorDTO avaliaProfessor(AvaliacaoProfessorDTO avaliacaoProfessorDTO, String ctx) throws Exception;

    List<AvaliacaoProfessorDTO> buscaAvaliacao(String codProfessores, String ctx) throws Exception;

    boolean matriculaExiste(final String ctx, final Integer matricula) throws ServiceException;

    Integer obterUltimaMatriculaCadastrada(final String ctx) throws ServiceException;

    void atualizarFotoAlunoApp(String key, UsuarioZW usuarioZW) throws Exception;

    AlunoAppInfoDTO alunoAppInfo(Integer alunoID, Integer pessoa, Integer cliente) throws ServiceException;

    void alterarNivelAluno(Integer matricula, Integer codNovoNivel) throws ServiceException;

    void alterarProfessorAluno(Integer matricula, Integer codNovoProfessor,
                                HttpServletRequest request) throws ServiceException;

    void alterarProfessorAlunos(HttpServletRequest request, JSONObject json) throws ServiceException;

    List<Integer> listaAlunosMatricula(FiltroAlunoJSON filtros, Integer empresaZw) throws ServiceException;

    String consultaModalidades(String ctx, String matricula) throws Exception;

    void enviaNotificacaoLembreteAulaEQuinzeMinutosAntesAula(String ctx, ClienteSintetico cliente, String inicio, String titulo, Integer codigoHorarioTurma, String nomeProfessor);
    String dataConvertidaFusoHorarioCliente(String ctx, Date dataSemAjusteFusoHorarioLocal, Integer empresaZW);

    Integer nivelAluno(final Integer codigoCliente) throws ServiceException;

    JSONObject consultarPassivo(String ctx, Integer codigoPassivo) throws ServiceException;

    JSONObject consultarIndicado(String ctx, Integer codigoIndicado) throws ServiceException;

    String autorizaAcessoTotalPass(ClienteDadosTotalPassDTO clienteDadosTotalPassDTO, String ctx) throws ServiceException;

    Integer consultarMetaAgendamentoPresencialAluno(String ctx, Integer matriculaCliente, Date dia) throws ServiceException;

    List<ClienteAcompanhamento> consultarClienteAcompanhamento(final String ctx, Date inicio, Date fim) throws ServiceException;

    List<ClienteAcompanhamentoJSON> consultarClienteAcompanhamentoJSON(final String ctx, Date inicio, Date fim) throws Exception;

    PerfilDISCVO salvarPerfilDISC(String ctx, PerfilDISCDTO perfilDISCDTO) throws ServiceException;

    List<PerfilDISCVO> buscarPerfilDISC(String ctx, Integer matricula) throws ServiceException;

    PerfilDISCVO atualizarPerfilDISC(String ctx, PerfilDISCDTO perfilDISCDTO, Integer codigo) throws ServiceException;

    List<String> obterAlunoGympassPorToken(String ctx, String codigoIndicado) throws Exception;

    Boolean verificaTotalPass(String ctx, Integer matricula);

    Boolean verificaGymPass(String ctx, Integer matricula);

    RetrospectivaAnoVO obterRetrospectiva(String ctx, Integer ano, Integer matricula, Boolean atualizaCache) throws Exception;

    void atualizarParQClienteZW(String ctx, Integer codigoCliZw, boolean isParQPositivo) throws Exception;

    HistoricoPresencasVO historicoPresenca(String ctx, Integer matricula, Integer empresa, Boolean atualizaCache) throws Exception;

    Integer obterPontosSaldo(String ctx, Integer matricula);

    List<BrindesVO> obterBrindes(String ctx, Boolean ativo);

    List<AvaliacaoProfessorVO> buscaAvaliacaoCliente(Integer codUsuario, String ctx) throws Exception;

    String obterSituacaoClienteZW(String ctx, Integer matricula);

    String consultaModalidadeDiaria(String ctx, String matricula, Date dia);

    ClienteSintetico obterPorEmailGympass(String ctx, String email) throws Exception;
}

