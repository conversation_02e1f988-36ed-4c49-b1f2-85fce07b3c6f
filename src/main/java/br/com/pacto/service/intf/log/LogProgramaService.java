package br.com.pacto.service.intf.log;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.log.AlteracoesTO;
import br.com.pacto.service.impl.log.LogTO;
import org.json.JSONObject;

import java.sql.ResultSet;
import java.util.List;

public interface LogProgramaService {

    String[] colunasAtividade = {"codigo","complementonomeatividade","descanso","intensidade","metodoexecucao","nome","nomeatividadealteradomanualmente","ordem","setid","versao"};
    String[] colunasFicha = {"versao","mensagemaluno","nome","sexo","versao","categoria_codigo","nivel_codigo"};
    String[] colunas = {
            "datainicio", "datalancamento", "dataproximarevisao",  "datarenovacao",
            "dataterminoprevisto", "diasporsemana", "nome",
            "situacao","totalaulasprevistas",
            "cliente_codigo","nivel_codigo", "professorcarteira_codigo", "professormontou_codigo", "genero" };

    List<LogTO> listarLogFichas(Integer codigoFicha, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    void tratarNomes(String ctx, List<AlteracoesTO> alteracoes,String campo, String sqlConsulta,String label);

    List<LogTO> listarLogProgramas(Integer codigoAluno, Integer codigoPrograma, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException;

    ResultSet descobrirQuemFoiAlteradoNoMomento(String chave, Long momento, String username, Integer codigo, Integer revtype) throws Exception;
}
