/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pacto.service.intf.nivel;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.nivel.Nivel;
import br.com.pacto.bean.nivel.NivelResponseTO;
import br.com.pacto.bean.nivel.NivelTO;
import br.com.pacto.controller.json.nivel.FiltroNivelJSON;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.log.LogTO;
import org.json.JSONObject;

import java.util.List;
import java.util.Map;



/**
 *
 * <AUTHOR>
 */
public interface NivelService {

    public static final String SERVICE_NAME = "NivelService";

    public Nivel inserir(final String ctx, Nivel object) throws ServiceException, ValidacaoException;

    public Nivel obterPorId(final String ctx, Integer id) throws ServiceException;

    public Nivel alterar(final String ctx, Nivel object) throws ServiceException, ValidacaoException;

    public void excluir(final String ctx, Nivel object) throws ServiceException;

    public List<Nivel> obterTodos(final String ctx) throws ServiceException;

    List<NivelResponseTO>consultarNivel(FiltroNivelJSON filtros, PaginadorDTO paginadorDTO)throws ServiceException;

    public List<Nivel> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    public List<Nivel> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException;

    public Nivel obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException;

    List<NivelResponseTO>consultar(String nome, Integer ordem)throws ServiceException;
    NivelResponseTO inserir(NivelTO nivelTO)throws ServiceException;
    NivelResponseTO alterar(Integer id, NivelTO nivelTO)throws ServiceException;
    void excluir(Integer id)throws ServiceException;

    List<NivelResponseTO> listarTodosNiveis() throws ServiceException;

    List<LogTO> listarLog(JSONObject filtros, PaginadorDTO paginadorDTO, Integer codigoNivel) throws ServiceException;

}
