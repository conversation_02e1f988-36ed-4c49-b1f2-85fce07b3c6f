package br.com.pacto.service.intf.processo;

import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gympass.ConfigGymPass;
import br.com.pacto.bean.gympass.HorarioGymPass;
import br.com.pacto.bean.processo.Sincronizacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.gympass.dto.BookingsDTO;
import br.com.pacto.service.impl.gympass.json.TurmaGymPassJSON;
import org.json.JSONObject;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/03/2020
 */
public interface SincronizacaoEmpresaService {

    public static final String SERVICE_NAME = "SincronizacaoEmpresaService";

    String sincronizarAtivosEmpresas(String ctx);

    List<Sincronizacao> ultimas(String ctx) throws Exception;

    String sincronizarPorProblemaZW(String ctx);

    String sincronizarProfessoresEmpresa(String ctx, Integer empresa);

    void sincronizarAtivosProfessoresEmpresa(String ctx, Integer empresa, JSONObject log);

}
