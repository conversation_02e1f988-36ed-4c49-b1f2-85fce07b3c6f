package br.com.pacto.service.impl.atividade;

import java.io.Serializable;
import java.util.List;

public class AtividadeNaoExcluidaInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer codigoAtividade;
    private String nomeAtividade;
    private List<Integer> codigosFicha;

    public AtividadeNaoExcluidaInfoDTO(Integer codigoAtividade, String nomeAtividade, List<Integer> codigosFicha) {
        this.codigoAtividade = codigoAtividade;
        this.nomeAtividade = nomeAtividade;
        this.codigosFicha = codigosFicha;
    }

    public Integer getCodigoAtividade() {
        return codigoAtividade;
    }

    public void setCodigoAtividade(Integer codigoAtividade) {
        this.codigoAtividade = codigoAtividade;
    }

    public String getNomeAtividade() {
        return nomeAtividade;
    }

    public void setNomeAtividade(String nomeAtividade) {
        this.nomeAtividade = nomeAtividade;
    }

    public List<Integer> getCodigosFicha() {
        return codigosFicha;
    }

    public void setCodigosFicha(List<Integer> codigosFicha) {
        this.codigosFicha = codigosFicha;
    }
}