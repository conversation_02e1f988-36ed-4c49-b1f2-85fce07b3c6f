package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 28/08/2018.
 */
public enum AlunoExcecoes implements ExcecaoSistema {

    ERRO_LISTAR_NIVEIS_ALUNO("erro_listar_niveis_aluno", "Ocorreu um erro ao listar os Níveis de Aluno"),
    ERRO_CADASTRAR_ALUNO("erro_cadastrar_aluno", "Ocorreu um erro ao cadastrar o Aluno informado"),
    ERRO_OBTER_ALUNO("erro_obter_aluno", "Ocorreu um erro ao obter o Aluno informado"),
    ERRO_CADASTRAR_OBSERVACAO("erro_cadastrar_observacao", "Ocorreu um erro ao cadastrar a observacao"),
    ERRO_DOWNLOAD_ARQUIVO("erro_download_arquivo", "Ocorreu um erro ao fazer o download do arquivo"),
    PROFESSOR_NAO_ENCONTRADO("professor_nao_encontrado", "Professor informado não foi encontrado"),
    NIVEL_NAO_ENCONTRADO("nivel_nao_encontrado", "Nível informado não foi encontrado"),
    ERRO_ALUNO_NAO_EXISTE("erro_aluno_nao_existe", "Erro o usúario não existe"),
    ERRO_BUSCAR_ALUNOS("erro_buscar_alunos", "Erro ao buscar os alunos"),
    ERRO_BUSCAR_DETALHES_PROGRAMA("erro_buscar_detalhes_programa", "Erro ao buscar detalhes do programa do aluno"),
    ERRO_ALUNO_JA_EXISTE("registro_duplicado", "registro_duplicado"),
    ERRO_ALUNO_NAO_ENCONTRADO("erro_aluno_nao-encontrado", "aluno_nao_encontrado"),
    ERRO_INFORME_FILTRO("erro_informe_filtro", "informe_um_filtro"),
    ERRO_ALUNO_INATIVO("erro_aluno_inativo", "aluno_inativo"),
    ERRO_CADASTRAR_ALUNO_ZW("erro_cadastrar_aluno_zw", "erro_cadastrar_aluno_zw"),
    ERRO_TOKEN_DIFERENTE_SESSAO("erro_token_diferente_sessao", "Foi detectado duas abas em aberto, favor feche e tente novamente."),
    ERRO_USUARIO_INVALIDO("erro_username_invalido", "username_invalido"),
    ERRO_AO_CRIAR_USUARIO("ERRO_AO_CRIAR_USUARIO", "Erro ao criar usuário do alunmo"),

    ERRO_USERNAME_ALUNO_NAO_INFORMADO("erro_username_aluno_nao_informado", "Erro o nome de usuário do aluno não foi informado"),
    ERRO_ALUNO_EMAIL_INVALIDO("erro_aluno_email_invalido", "Erro o e-mail do aluno informado é invalido"),
    ERRO_ALUNO_USERNAME_EM_USO("erro_aluno_username_em_uso", "usuario_duplicado"),
    ERRO_ALUNO_ALTERAR("erro_aluno_alterar", ""),
    ERRO_ALUNO_ALTERAR_SITUACAO("erro_aluno_alterar_situacao", "Erro ao alterar a situação do aluno"),
    ERRO_SITUACAO_OBRIGATORIO("erro_situacao_obrigatorio", "Erro situação é obrigatório."),
    SEXO_ALUNO_NAO_INFORMADO("sexo_aluno_nao_informado","Sexo não foi informado"),
    USERNAME_ALUNO_NAO_EMAIL_VALIDO("username_aluno_nao_email_valido" ,"O nome de usuário informado não e um e-mail"),

    ERRO_CARREGAR_AVALIACAO_FISICA("eero_carregar_avaliacao_fisica", "Erro ao carregar a avaliação fisíca do aluno."),
    ERRO_AVALIACAO_FISICA_NAO_INFORMADO("eero_avaliacao_fisica_nao_informado", "Erro avaliação física não informado"),
    ERRO_DATA_INICIAL_MAIOR_QUE_DATA_FINAL("erro_data_inicial_maior_que_data_final", "Erro data de inicio, não pode ser maior, que a data fim"),
    ERRO_AO_CARREGAR_LINHA_DE_TEMPO_DO_ALUNO("erro_ao_carregar_linha_de_tempo_do_aluno", "Erro ao carregar linha de tempo do aluno"),
    ERRO_AO_CARREGAR_ATESTADO_ALUNO("erro_ao_carregar_atestado_aluno", "Erro ao carregar atestados do aluno"),

    ERRO_DATA_INFORMADA_INFERIOR_DATA_ATUAL("erro_data_informada_inferior_data_atual", "Erro data informada não pode ser inferior a data atual"),

    ERRO_ALUNO_NAO_INFORMADO("erro_aluno_nao_informado", "Erro aluno não informado"),
    ERRO_REENVIAR_USER_APP("erro_reenviar_user_app", "Erro ao reenviar usuário do app"),
    ERRO_CONSULTAR_RESULTADO_EVOLUCAO_BI_AVALIACAO("erro_consultar_resultado_evolucao_bi_avaliacao", "Erro ao consultar resultado da evolução da avaliação fisica do aluno"),
    ERRO_OBSERVACAO_NAO_EXISTE("erro_observacao_nao_existe", "Erro a observação não existe"),
    ERRO_OBTER_OBSERVACAO("erro_obter_observacao", "Ocorreu um erro ao obter a Observação informada"),
    ;

    private String chave;
    private String descricao;

    AlunoExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }
}
