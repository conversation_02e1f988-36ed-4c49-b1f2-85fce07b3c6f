package br.com.pacto.service.impl.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(description = "Item de avaliação física para Business Intelligence, contendo informações resumidas do aluno e sua avaliação para análise estatística.")
public class ItemAvaliacaoFisicaTO {

    @ApiModelProperty(value = "Código único do cliente/aluno no sistema.", example = "12345")
    private Integer codCliente;

    @ApiModelProperty(value = "Nome completo do aluno (pode estar abreviado para exibição).", example = "João Silva")
    private String nome;

    @ApiModelProperty(value = "Data em que a avaliação física foi realizada.")
    private Date dataAvaliacao;

    @ApiModelProperty(value = "Data prevista para a próxima avaliação física.")
    private Date dataProxima;

    @ApiModelProperty(value = "Nome do avaliador físico responsável pela avaliação.", example = "Dr. <PERSON>")
    private String avaliador;

    @ApiModelProperty(value = "Nome do professor/personal trainer responsável pelo aluno.", example = "Prof. Maria Oliveira")
    private String professor;

    @ApiModelProperty(value = "Observações específicas relacionadas ao indicador consultado.", example = "Emagrecimento")
    private String obs;

    @ApiModelProperty(value = "Diferença/variação calculada para indicadores de evolução (ex: perda de peso, ganho de massa).", example = "-2.5kg")
    private String diff;

    @ApiModelProperty(value = "Matrícula única do aluno na academia.", example = "MAT001234")
    private String matricula;

    @ApiModelProperty(value = "Modalidades praticadas pelo aluno.", example = "Musculação, Pilates")
    private String modalidade;

    public ItemAvaliacaoFisicaTO(Integer codCliente, String nome, Date dataAvaliacao, Date dataProxima, String avaliador, String professor, String matricula) {
        this.codCliente = codCliente;
        this.nome = nome;
        this.dataAvaliacao = dataAvaliacao;
        this.dataProxima = dataProxima;
        this.avaliador = avaliador;
        this.professor = professor;
        this.matricula = matricula;
    }

    public ItemAvaliacaoFisicaTO(Integer codCliente, String matricula, String nome, String obs, String diff) {
        this.codCliente = codCliente;
        this.matricula = matricula;
        this.nome = nome;
        this.obs = obs;
        this.diff = diff;
    }

    public ItemAvaliacaoFisicaTO(Integer codCliente, String nome, String matricula) {
        this.codCliente = codCliente;
        this.nome = nome;
        this.matricula = matricula;
    }

    public ItemAvaliacaoFisicaTO(Integer codCliente, String nome, String matricula, String modalidade) {
        this.codCliente = codCliente;
        this.nome = nome;
        this.matricula = matricula;
        this.modalidade = modalidade;
    }

    public String getDiff() {
        return diff;
    }

    public void setDiff(String diff) {
        this.diff = diff;
    }

    public ItemAvaliacaoFisicaTO() {
    }

    public String getObs() {
        return obs;
    }

    public void setObs(String obs) {
        this.obs = obs;
    }

    public Integer getCodCliente() {
        return codCliente;
    }

    public void setCodCliente(Integer codCliente) {
        this.codCliente = codCliente;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Date getDataAvaliacao() {
        return dataAvaliacao;
    }

    public void setDataAvaliacao(Date dataAvaliacao) {
        this.dataAvaliacao = dataAvaliacao;
    }

    public Date getDataProxima() {
        return dataProxima;
    }

    public void setDataProxima(Date dataProxima) {
        this.dataProxima = dataProxima;
    }

    public String getAvaliador() {
        return avaliador;
    }

    public void setAvaliador(String avaliador) {
        this.avaliador = avaliador;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }
}
