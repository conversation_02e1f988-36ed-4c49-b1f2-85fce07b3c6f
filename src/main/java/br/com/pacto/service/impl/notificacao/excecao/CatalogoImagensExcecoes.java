package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 29/08/2018.
 */
public enum CatalogoImagensExcecoes implements ExcecaoSistema {

    ERRO_BUSCAR_IMAGENS("erro_buscar_imagens", "Ocorreu um erro ao buscar as Imagens");

    private String chave;
    private String descricao;

    CatalogoImagensExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }

    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
