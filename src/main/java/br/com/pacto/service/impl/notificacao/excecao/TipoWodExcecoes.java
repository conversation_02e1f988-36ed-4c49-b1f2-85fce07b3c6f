package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

/**
 * Created by ulisses on 22/08/2018.
 */
public enum TipoWodExcecoes implements ExcecaoSistema {

    TIPO_WOD_NAO_ENCONTRADO("tipo_wod_nao_encontrado", "Tipo de Wod informado não encontrado"),
    ERRO_CADASTRAR_TIPO_WOD("erro_cadastrar_tipo_wod", "Ocorreu um erro ao cadastrar o Tipo de Wod informado"),
    ERRO_ALTERAR_TIPO_WOD("erro_alterar_tipo_wod", "Ocorreu um erro ao alterar o Tipo de Wod informado"),
    ERRO_EXCLUIR_TIPO_WOD("erro_excluir_tipo_wod", "Ocorreu um erro ao excluir o Tipo de Wod informado"),
    ERRO_BUSCAR_TIPO_WOD("erro_buscar_tipo_wod", "Ocorreu um erro ao buscar o Tipo de Wod informado"),
    ERRO_BUSCAR_TIPOS_WOD("erro_buscar_tipos_wod", "Ocorreu um erro ao buscar os Tipos de Wod"),

    ERRO_NOME_NAO_ENCONTRADO("erro_nome_nao_encontrado", "O nome não encontrado"),

    ERRO_NOME_EXISTE("erro_nome_existe", "registro_duplicado");

    private String chave;
    private String descricao;

    TipoWodExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

}
