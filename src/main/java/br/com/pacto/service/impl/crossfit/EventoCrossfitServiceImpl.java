package br.com.pacto.service.impl.crossfit;

import br.com.pacto.bean.cliente.Prospect;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.crossfit.EquipeEvento;
import br.com.pacto.bean.crossfit.EventoCrossfit;
import br.com.pacto.bean.crossfit.ParticipanteEquipeEvento;
import br.com.pacto.bean.crossfit.PontuacaoGameCrossfit;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.wod.NivelCrossfitEnum;
import br.com.pacto.bean.wod.NivelWodResponseTO;
import br.com.pacto.bean.wod.ScoreTreino;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.dao.intf.cliente.ProspectDao;
import br.com.pacto.dao.intf.crossfit.EquipeEventoDao;
import br.com.pacto.dao.intf.crossfit.EventoCrossfitDao;
import br.com.pacto.dao.intf.crossfit.ParticipanteEquipeEventoDao;
import br.com.pacto.dao.intf.crossfit.PontuacaoGameCrossfitDao;
import br.com.pacto.dao.intf.wod.ScoreTreinoDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.crossfit.EventoCrossfitService;
import br.com.pacto.service.intf.nivelwod.NivelWodService;
import br.com.pacto.service.intf.wod.WodService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.Ordenacao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;

import java.util.*;

@Service
public class EventoCrossfitServiceImpl implements EventoCrossfitService {

    @Autowired
    private EventoCrossfitDao eventoCrossfitDao;
    @Autowired
    private ScoreTreinoDao scoreTreinoDao;
    @Autowired
    private WodService wodService;
    @Autowired
    private EquipeEventoDao equipeEventoDao;
    @Autowired
    private PontuacaoGameCrossfitDao pontuacaoDao;
    @Autowired
    private ProspectDao prospectDao;
    @Autowired
    private ParticipanteEquipeEventoDao participanteEquipeEventoDao;
    @Autowired
    private NivelWodService nivelWodService;
    @Override
    public void gravarPontuacao(final String ctx,
                                EventoCrossfit game,
                                Map<NivelCrossfitEnum, List<PontuacaoGameCrossfit>> pontuacoes) throws Exception {
        pontuacaoDao.deleteComParam(ctx, new String[]{"game.codigo"}, new Object[]{game.getCodigo()});

        for (NivelCrossfitEnum n : pontuacoes.keySet()) {
            for (PontuacaoGameCrossfit p : pontuacoes.get(n)) {
                p.setCodigo(null);
                p.setGame(game);
                p.setNivel(n);
                pontuacaoDao.insert(ctx, p);
            }
        }

    }

    public Map<NivelCrossfitEnum, List<PontuacaoGameCrossfit>> obterMapasPontuacoes(final String ctx, final EventoCrossfit game) throws Exception {
        Map<NivelCrossfitEnum, List<PontuacaoGameCrossfit>> mapa = new HashMap<NivelCrossfitEnum, List<PontuacaoGameCrossfit>>();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("game", game.getCodigo());
        List<PontuacaoGameCrossfit> pontuacoes = pontuacaoDao.findByParam(ctx,
                "select obj from PontuacaoGameCrossfit obj where game.codigo = :game",
                params);
        for (PontuacaoGameCrossfit p : pontuacoes) {
            List<PontuacaoGameCrossfit> pontuacoesNivel = mapa.get(p.getNivel());
            if (pontuacoesNivel == null) {
                pontuacoesNivel = new ArrayList<PontuacaoGameCrossfit>();
                mapa.put(p.getNivel(), pontuacoesNivel);
            }
            pontuacoesNivel.add(p);
        }
        return mapa;
    }

    @Override
    public EventoCrossfit inserir(String ctx, EventoCrossfit object) throws Exception {
        if (UteisValidacao.emptyString(object.getNome())) {
            throw new Exception("Nome do evento deve ser informado.");
        }
        return eventoCrossfitDao.insert(ctx, object);
    }

    @Override
    public EquipeEvento inserirEquipe(String ctx, EquipeEvento object, List<ParticipanteEquipeEvento> participantes) throws Exception {
        if (UteisValidacao.emptyString(object.getNome())) {
            throw new Exception("Nome da equipe deve ser informado.");
        }
        object = equipeEventoDao.insert(ctx, object);
        for (ParticipanteEquipeEvento pa : participantes) {
            pa.setEquipe(object);
            participanteEquipeEventoDao.insert(ctx, pa);
        }
        return object;
    }

    @Override
    public EventoCrossfit obterPorId(String ctx, Integer id) throws ServiceException {
        try {
            return eventoCrossfitDao.findById(ctx, id);
        }catch (Exception e){
            throw new ServiceException(e);
        }

    }

    @Override
    public EventoCrossfit alterar(String ctx, EventoCrossfit object) throws Exception {
        if (UteisValidacao.emptyString(object.getNome())) {
            throw new Exception("Nome do evento deve ser informado.");
        }
        return eventoCrossfitDao.update(ctx, object);
    }

    @Override
    public EquipeEvento alterarEquipe(String ctx, EquipeEvento object, List<ParticipanteEquipeEvento> participantes) throws Exception {
        if (UteisValidacao.emptyString(object.getNome())) {
            throw new Exception("Nome da equipe deve ser informado.");
        }
        object = equipeEventoDao.update(ctx, object);
        participanteEquipeEventoDao.deleteComParam(ctx, new String[]{"equipe.codigo"}, new Object[]{object.getCodigo()});
        for (ParticipanteEquipeEvento pa : participantes) {
            pa.setCodigo(null);
            pa.setEquipe(object);
            participanteEquipeEventoDao.insert(ctx, pa);
        }
        return object;

    }

    @Override
    public void excluir(String ctx, EventoCrossfit object) throws ServiceException {
        try {
            pontuacaoDao.deleteComParam(ctx, new String[]{"game.codigo"}, new Object[]{object.getCodigo()});
            eventoCrossfitDao.delete(ctx, object);
        } catch (Exception e) {
            throw new ServiceException(e);
        }

    }

    @Override
    public List<EventoCrossfit> obterTodos(String ctx) throws ServiceException {
        try {
            return eventoCrossfitDao.findListByAttributes(ctx, null, null, "nome", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<ScoreTreino> resultadosGamePorNivel(String key, EventoCrossfit game, NivelCrossfitEnum n, Wod wod) throws Exception {
        return null;
    }


    public List<ScoreTreino> resultadosGamePorNivel(final String key, final EventoCrossfit game, final NivelWodResponseTO n, final Wod wod) throws Exception {
        List<ScoreTreino> resultados = new ArrayList<ScoreTreino>();
        List<EquipeEvento> equipes = equipes(key, game);
        for (EquipeEvento ee : equipes) {
            if (!ee.getNivelWod().equals(n)) {
                continue;
            }
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("wod", wod.getCodigo());
            params.put("eqp", ee.getCodigo());

            List<ScoreTreino> scores = scoreTreinoDao.findByParam(key, "select obj from ScoreTreino obj where obj.wod.codigo = :wod and obj.equipe.codigo = :eqp", params);
            if (scores.isEmpty()) {
                ScoreTreino score = new ScoreTreino();
                score.setWod(wod);
                score.setNivelcrossfit(ee.getNivelWod());
                score.setEquipe(ee);
                score = scoreTreinoDao.insert(key, score);
                scores.add(score);
            }
        }
        Map<String, Object> paramsw = new HashMap<String, Object>();
        paramsw.put("wod", wod.getCodigo());
        paramsw.put("nivel", n);

        wodService.atualizarRanking(key, wod.getCodigo(), wod.getTipoWodTabela().getOrderBy());
        resultados = scoreTreinoDao.findByParam(key, "select obj from ScoreTreino obj where obj.wod.codigo = :wod " +
                "and obj.nivelCrossfit = :nivel", paramsw);
        resultados = Ordenacao.ordenarLista(resultados, "posicao");
        return resultados;

    }


    public Map<String, List<EquipeEvento>> mapaEquipes(final String ctx, EventoCrossfit game, List<Wod> wods) throws Exception {
        String[] sexos = game.getRankingSeparadoSexo() ? new String[]{"F", "M"} : new String[]{"g"};
        Map<String, List<EquipeEvento>> mapaEquipes = new HashMap<String, List<EquipeEvento>>();
        List<ScoreTreino> scoreTreinos = new ArrayList<ScoreTreino>();
        List<EquipeEvento> equipes = equipes(ctx, game);

        for (String s : sexos) {

            List<NivelWodResponseTO> listaNivelWod = nivelWodService.listarNiveisWod(null, null);

            for(NivelWodResponseTO n : listaNivelWod){
                for (Wod w : wods) {
                    List<ScoreTreino> scoreTreinosWod = resultadosGamePorNivel(ctx, game, n, w);

                    ScoreTreino ultimoScore = null;
                    int posicao = 1;
                    int soma = 1;
                    for (ScoreTreino sc : scoreTreinosWod) {
                        if(!s.equals("g") && !s.equals(sc.getEquipe().getSexo())){
                            scoreTreinosWod = new ArrayList<>();
                            continue;
                        }


                        if (ultimoScore == null) {
                            ultimoScore = sc;
                        } else if (ultimoScore.equals(sc)) {
                            soma++;
                        } else {
                            posicao = posicao + soma;
                            soma = 1;
                        }
                        sc.setPosicao(posicao);
                    }

                    scoreTreinos.addAll(scoreTreinosWod);
                }
                mapaEquipes.put(n.getNome().concat(s), new ArrayList<EquipeEvento>());
            }
        }

        Map<NivelCrossfitEnum, List<PontuacaoGameCrossfit>> mapaPontos = obterMapasPontuacoes(ctx, game);
        for (EquipeEvento e : equipes) {
            e.setPosicoesWods(new HashMap<Integer, Integer>());
            e.setPontosWods(new HashMap<Integer, Integer>());
            e.setTotalPontos(0);
            for (ScoreTreino sc : scoreTreinos) {
                if (sc.getEquipe().getCodigo().equals(e.getCodigo())) {
                    e.getPosicoesWods().put(sc.getWod().getCodigo(), sc.getPosicao());

                    List<PontuacaoGameCrossfit> pontuacaoGameCrossfits = mapaPontos.get(e.getNivelWod());

                    for (PontuacaoGameCrossfit p : pontuacaoGameCrossfits) {
                        if (p.getPosicao().equals(sc.getPosicao())) {
                            e.setTotalPontos(e.getTotalPontos() + p.getPontuacao());
                            e.getPontosWods().put(sc.getWod().getCodigo(), p.getPontuacao());
                        }
                    }
                }
            }
            mapaEquipes.get(e.getNivelWod().getNome().concat(game.getRankingSeparadoSexo() ? e.getSexo() : "g")).add(e);
        }

        for (String s : sexos) {
            for (NivelCrossfitEnum no : NivelCrossfitEnum.values()) {

                List<EquipeEvento> equipesGame = mapaEquipes.get(no.name().concat(s));
                equipesGame = Ordenacao.ordenarLista(equipesGame, "totalPontos");
                Collections.reverse(equipesGame);
                EquipeEvento ultima = null;
                int soma = 1;
                int posicao = 1;

                for (EquipeEvento eqp : equipesGame) {
                    if (ultima == null) {
                        ultima = eqp;
                    } else if (ultima.getTotalPontos().equals(eqp.getTotalPontos())) {
                        soma++;
                    } else {
                        posicao = posicao + soma;
                        soma = 1;
                    }
                    eqp.setPosicaoFinal(posicao);
                }

                mapaEquipes.put(no.name().concat(s), equipesGame);
            }

        }


        return mapaEquipes;
    }

    public List<EquipeEvento> equipes(final String ctx, EventoCrossfit game) throws Exception {
        try {
            List<EquipeEvento> equipes = equipeEventoDao.findListByAttributes(ctx,
                    new String[]{"evento.codigo"},
                    new Object[]{game.getCodigo()},
                    "nome", 0);
            for (EquipeEvento equipe : equipes) {
                List<ParticipanteEquipeEvento> participantes = participanteEquipeEventoDao.findListByAttributes(ctx, new String[]{"equipe.codigo"},
                        new Object[]{equipe.getCodigo()}, "codigo", 200);
                equipe.setParticipantes(participantes);
                switch (equipe.getNivelWod().getNome()) {
                    case "Iniciante":
                        equipe.setMaxParticipantes(game.getNrPartIniciante());
                        break;
                    case "RX":
                        equipe.setMaxParticipantes(game.getNrPartAvancado());
                        break;
                    case "Scaled":
                        equipe.setMaxParticipantes(game.getNrPartScaled());
                        break;
                    case "Junior":
                        equipe.setMaxParticipantes(game.getNrPartAmador());
                        break;
                }
            }
            return equipes;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<PontuacaoGameCrossfit> gerarPontuacao(final String ctx,
                                                      final Integer pontosPrimeiro,
                                                      final Integer passo,
                                                      final Integer colocado,
                                                      final NivelCrossfitEnum nivel) throws Exception {
        List<PontuacaoGameCrossfit> lista = new ArrayList<PontuacaoGameCrossfit>();
        Integer pontos = pontosPrimeiro.intValue();
        for (int i = 1; i <= colocado; i++) {
            PontuacaoGameCrossfit ponto = new PontuacaoGameCrossfit();
            ponto.setNivel(nivel);
            ponto.setPosicao(i);
            ponto.setPontuacao(pontos > 0 ? pontos : 0);
            pontos = pontos - passo;
            lista.add(ponto);
        }
        return lista;
    }

    public void gerarParcelaCapitao(final String key, EquipeEvento equipe, EventoCrossfit game, Usuario usuario) throws Exception {

        ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
        ConfiguracaoSistema cfgProduto = css.consultarPorTipo(key, ConfiguracoesEnum.PRODUTO_INSCRICAO_GAME);
        if (UteisValidacao.emptyNumber(cfgProduto.getValorAsInteger())) {
            throw new Exception("Não foi possível gerar a parcela pois o produto da inscrição não foi informado nas configurações.");
        }

        IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);

        List<ParticipanteEquipeEvento> participantes = participanteEquipeEventoDao.findListByAttributes(key, new String[]{"equipe.codigo"},
                new Object[]{equipe.getCodigo()}, "codigo", 200);

        Integer capitao = null;
        for (ParticipanteEquipeEvento pa : participantes) {
            if (pa.getCapitao() && pa.getUsuario().getCliente() == null) {
                throw new Exception("Para gerar a parcela, o capitão deve ser um aluno.");
            }
            if (pa.getCapitao()) {
                capitao = pa.getUsuario().getCliente().getCodigoCliente();
            }
        }

        if (UteisValidacao.emptyNumber(capitao)) {
            throw new Exception("Não foi possível gerar a parcela pois o capitão não foi informado.");
        }

        Double valor = equipe.getMaxParticipantes() * game.getValorPorParticipante();

        String lancarProdutoAluno = integracaoWS.lancarProdutoAlunoValor(Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg), key,
                capitao,
                cfgProduto.getValorAsInteger(), usuario.getUsuarioZW(), Calendario.hoje(), valor);


        if (lancarProdutoAluno.startsWith("ok")) {
            String[] split = lancarProdutoAluno.split("\\|");
            equipe.setParcela(Integer.valueOf(split[1]));
            equipeEventoDao.update(key, equipe);
        } else {
            throw new Exception(lancarProdutoAluno);
        }

    }

    public Prospect gravarProspect(final String key, Prospect prospect) throws Exception {
        if (UteisValidacao.emptyNumber(prospect.getCodigo())) {
            prospect = prospectDao.insert(key, prospect);
        } else {
            prospect = prospectDao.update(key, prospect);
        }
        return prospect;
    }

    public void gravarResultados(final String key, List<ScoreTreino> scoreTreinos) throws Exception {
        for (ScoreTreino s : scoreTreinos) {
            s.setarSegundos();
            scoreTreinoDao.update(key, s);
        }
    }

}
