package br.com.pacto.service.impl.notificacao.excecao;

import br.com.pacto.service.exception.ExcecaoSistema;

public enum AgendaExcecoes implements ExcecaoSistema {

    ERRO_SUBSTITUIR("erro_substituir_professor", "Erro ao substituir professor"),
    ERRO_ADICIONAR_ALUNO_AULA("erro_adicionar_aluno_aula", "Erro ao adicionar um aluno na aula"),
    ERRO_ENCERROU_PRAZO("erro_encerrou_prazo", "Você só pode marcar a aula com %s minutos de antecedência ou menos"),
    ERRO_ENCERROU_PRAZO_DESMARCAR("erro_encerrou_prazo_desmarcar_aula", "Você só pode desmarcar aula com %s minutos de antecedência"),
    ERRO_EXISTE_ALUNOS_NA_AULA("erro_existe_alunos_na_aula", "Você não pode excluir uma aula que tenha alunos marcados."),
    ERRO_REMOVER_ALUNO_AULA("erro_remover_aluno_aula", "Erro ao remover um aluno na aula"),
    ERRO_CONFIRMAR_ALUNO_AULA("erro_confirmar_aluno_aula", "Erro ao marcar presenca um aluno na aula"),
    ERRO_DESCONFIRMAR_ALUNO_AULA("erro_desconfirmar_aluno_aula", "Erro ao desmarcar presenca um aluno na aula"),
    ERRO_REMOVER_PRESENCA_ALUNO_AULA("erro_remover_presenca_aluno_aula", "Erro ao remover presenca de um aluno na aula"),
    ERRO_REMOVER_AULA("erro_remover_aula", "Erro ao remover uma aula"),
    ERRO_BUSCAR_AULA("erro_buscar_aula", "Não foi possível encontrar a aula"),
    ERRO_BUSCAR_TURMA("erro_buscar_turma", "Não foi possível listar as turmas"),
    ERRO_AULA_ESTA_CHEIA("erro_aula_esta_cheia", "Esta aula já esta cheia"),
    ERRO_ATUALIZAR_ALUNO_AULA("erro_atualizar_aluno_aula", "Erro ao atualizar situação do aluno a aula"),
    ERRO_FIXAR_ALUNO_AULA("erro_fixar_aluno_aula", "Erro ao fixar o aluno na aula"),
    ERRO_MARCAR_ALUNO("erro_marcar_aluno", "Erro ao inserir aluno a aula"),
    ERRO_CREDITO_EXCEDIDO("erro_credito_excedido", "credito_excedido"),
    ERRO_PRODUTO_ZW("erro_produto_zw", "Erro ao listar os produtos do ZW"),
    ERRO_LISTAR_AGENDAMENTOS("erro_listar_agendamentos", "Erro ao listar os agendamentos"),
    ERRO_REMOVER_AGENDAMENTO("ERRO_REMOVER_AGENDAMENTO", "Erro ao remover o agendamento do aluno"),
    ERRO_CONSULTAR_AGENDAMENTO("ERRO_CONSULTAR_AGENDAMENTO", "Erro ao consultar o agendamento do aluno"),
    ERRO_CONSULTAR_DISPONIBILIDADES("erro_consultar_disponibilidades", "Erro ao consultar as disponibilidades"),
    ERRO_VAGA_EXPERIMENTAL_LOTADA("erro_vaga_experimental_lotada","Todas as vagas para essa aula experimental já foram preenchidas"),

    ERRO_CAMPOS_OBRIGATORIO("erro_campos_obrigatorios", "Erro campos obrigatórios não preenchido"),
    ERRO_AGENDAMENTO_DUPLICADO("erro_agendamento_duplicado", "O aluno já possui compromisso neste horário"),
    ERRO_SALVAR_AGENDAMENTO("erro_salvar_agendamento", "Erro ao salvar agendamento"),
    ERRO_AO_INSERIR_CONFIG_AGENDA("erro_ao_inserir_config_agenda", "Erro ao salvar config agendamento"),
    ERRO_AO_OBTER_CONFIG_AGENDA("erro_ao_obter_config_agenda", "Erro ao obter config agendamento"),
    SEM_DISPONIBILIDADE_DATA("SEM_DISPONIBILIDADE_DATA", "Sem disponibilidade de data"),
    SEM_DISPONIBILIDADE_HORARIO("SEM_DISPONIBILIDADE_HORARIO", "Sem disponibilidade de horário"),
    HORARIO_DUPLICADO("HORARIO_DUPLICADO", "Horário duplicado"),
    FILA_DE_ESPERA_DESABILITADA("FILA_DE_ESPERA_DESABILITADA", "A fila de espera está desabilitada"),

    FILA_DE_ESPERA_INDISPONIVEL("FILA_DE_ESPERA_INDISPONIVEL", "A fila de espera é válida apenas para aulas do dia atual e com lotação. Antecipe a próxima aula e faça seu agendamento"),

    VALIDACAO_AGENDA_ALUNO_PLANO_INVALIDO("VALIDACAO_AGENDA_ALUNO_PLANO_INVALIDO", "Sem produto validação"),
    VALIDACAO_AGENDA_ALUNO_PRODUTO_INVALIDO("VALIDACAO_AGENDA_ALUNO_PRODUTO_INVALIDO", "Sem plano validação"),
    VALIDACAO_AGENDA_ALUNO_INTERVALO_DIAS("VALIDACAO_AGENDA_ALUNO_INTERVALO_DIAS", "Limite agendamento perido atingido"),
    VALIDACAO_AGENDA_ALUNO_INTERVALO_DIAS_FALTOU("VALIDACAO_AGENDA_ALUNO_INTERVALO_DIAS_FALTOU", "Limite agendamento falta perido atingido"),
    SEM_DISPONIBILIDADE_AMBIENTE("SEM_DISPONIBILIDADE_AMBIENTE", "Sem disponibilidade de ambiente"),
    ERRO_CONFIGURAR_DIAS_BLOQUEIO("ERRO_CONFIGURAR_DIAS_BLOQUEIO", "Erro ao configurar dias de Bloqueio"),
    ERRO_BUSCAR_CONFIGURACAO_DIAS_BLOQUEIO("ERRO_BUSCAR_CONFIGURACAO_DIAS_BLOQUEIO", "Erro ao tentar obter configuração dias de bloqueio"),
    ERRO_EQUIPAMENTO_JA_RESERVADO("ERRO_EQUIPAMENTO_JA_RESERVADO", "Equipamento já reservado"),
    ERRO_ENCERROU_PRAZO_PARA_ALTERAR_EQUIPAMENTO("ERRO_ENCERROU_PRAZO_PARA_ALTERAR_EQUIPAMENTO", "Equipamento só pode ser alterado com %s minutos antes da aula"),
    ERRO_DATA_RECORRENCIA_AGENDAMENTO("erro_data_recorrencia_agendamento", "Data fim da recorrência maior que o fim do contrato"),
    ERRO_REMOVER_RESERVA_EQUIPAMENTO("ERRO_REMOVER_RESERVA_EQUIPAMENTO", "Erro ao remover reserva de equipamento")
    ;

    private String chave;
    private String descricao;

    AgendaExcecoes(String chave, String descricao) {
        this.chave = chave;
        this.descricao = descricao;
    }

    @Override
    public String getChaveExcecao() {
        return chave;
    }
    @Override
    public String getDescricaoExcecao() {
        return descricao;
    }

    public void setDescricaoExcecao(String descricao) {
        this.descricao = descricao;
    }
}
