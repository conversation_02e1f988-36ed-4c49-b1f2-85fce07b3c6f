package br.com.pacto.service.impl.agenda;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.agenda.ConfigDisponibilidade;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.disponibilidade.Disponibilidade;
import br.com.pacto.bean.disponibilidade.HorarioDisponibilidade;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.AgendaDiaSemana;
import br.com.pacto.controller.json.agendamento.AgendaDisponibilidadeDTO;
import br.com.pacto.controller.json.agendamento.AgendamentoDisponibilidadePersonalDTO;
import br.com.pacto.controller.json.agendamento.DisponibilidadeConfigDTO;
import br.com.pacto.controller.json.agendamento.DisponibilidadeConfigGeradoraDTO;
import br.com.pacto.controller.json.agendamento.DisponibilidadeDTO;
import br.com.pacto.controller.json.agendamento.DisponibilidadeEditDTO;
import br.com.pacto.controller.json.agendamento.FiltroDisponibilidadeDTO;
import br.com.pacto.controller.json.agendamento.FiltrosAgendamentosDTO;
import br.com.pacto.controller.json.agendamento.PeriodoFiltrarEnum;
import br.com.pacto.controller.json.agendamento.ServicoAgendamentoDisponibilidadeDTO;
import br.com.pacto.controller.json.agendamento.TipoAgendamentoDuracaoDTO;
import br.com.pacto.controller.json.agendamento.TipoAtividadeDTO;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.disponibilidade.HorarioDisponibilidadeDTO;
import br.com.pacto.controller.json.disponibilidade.ItemValidacaoDisponibilidadeDTO;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.dao.intf.agenda.AgendamentoDao;
import br.com.pacto.dao.intf.agenda.ConfigDisponibilidadeDao;
import br.com.pacto.dao.intf.disponibilidade.DisponibilidadeDao;
import br.com.pacto.dao.intf.disponibilidade.HorarioDisponibilidadeDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.dao.intf.tipoevento.TipoEventoDao;
import br.com.pacto.enumerador.agenda.StatusAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.AgendaExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.DisponibilidadeExcecoes;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.agenda.DisponibilidadeService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.enumeradores.DiasSemana;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.impl.UtilReflection;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.ResultSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static br.com.pacto.objeto.Uteis.incluirLog;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.StringUtils.remove;

@Service
public class DisponibilidadeServiceImpl implements DisponibilidadeService {

    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private ConfigDisponibilidadeDao configDisponibilidadeDao;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private AgendamentoDao agendamentoDao;
    @Autowired
    private TipoEventoService tipoEventoService;
    @Autowired
    private AgendamentoService agendamentoService;
    @Autowired
    private ProfessorSinteticoDao professorsinteticoDao;
    @Autowired
    private TipoEventoDao tipoEventoDao;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private DisponibilidadeDao disponibilidadeDao;
    @Autowired
    private ConexaoZWService conexaoZWService;
    @Autowired
    private HorarioDisponibilidadeDao horarioDisponibilidadeDao;

    @Autowired
    LogDao logDao;

    public void gerarConfigDisponibilidadePorAgendamentos(String ctx) throws ServiceException {
        try {
            configDisponibilidadeDao.deleteAll(ctx);

            String hql = "select obj from Agendamento obj where obj.disponibilidade is true and obj.inicio >= :inicio";
            Map<String, Object> params = new HashMap<>();
            params.put("inicio", Calendario.getDate(Calendario.MASC_DATA, "18/08/2019"));
            List<Agendamento> disponibilidades = agendamentoDao.findByParam(ctx, hql, params);
            disponibilidades = Ordenacao.ordenarLista(disponibilidades, "inicio");
            Map<String, ConfigDisponibilidade> configs = new HashMap<>();
            Map<String, List<Agendamento>> disponibilidadesAgrupadas = new HashMap<>();

            for(Agendamento disponibilidade : disponibilidades){
                String chave = disponibilidade.getProfessor().getCodigo() + "-" + disponibilidade.getTipoEvento().getCodigo() +
                        "-" + Calendario.getHora(disponibilidade.getInicio(), "HH:mm") + "-" + Calendario.getHora(disponibilidade.getFim(), "HH:mm");
                ConfigDisponibilidade configDisponibilidade = configs.get(chave);
                Calendar dia = Calendar.getInstance();
                dia.setTime(disponibilidade.getInicio());
                String diaSemana = AgendaDiaSemana.getFromOrdinal(dia.get(Calendar.DAY_OF_WEEK)).name();
                if(configDisponibilidade == null){
                    configDisponibilidade = new ConfigDisponibilidade();
                    configDisponibilidade.setDiasSemana(diaSemana + "|");
                    configDisponibilidade.setDia(disponibilidade.getInicio().getTime());
                    configDisponibilidade.setInicioHoraFormatada(Calendario.getHora(disponibilidade.getInicio(), "HH:mm"));
                    configDisponibilidade.setDataLimite(disponibilidade.getFim().getTime());
                    configDisponibilidade.setFimHoraFormatada(Calendario.getHora(disponibilidade.getFim(), "HH:mm"));
                    configDisponibilidade.setProfessor(disponibilidade.getProfessor());
                    configDisponibilidade.setTipoEvento(disponibilidade.getTipoEvento());
                    configs.put(chave, configDisponibilidade);
                }else{
                    if(!configDisponibilidade.getDiasSemana().contains(diaSemana)){
                        configDisponibilidade.setDiasSemana(configDisponibilidade.getDiasSemana() + diaSemana + "|");
                    }
                    Date fim = new Date(configDisponibilidade.getDataLimite());
                    if(Calendario.maior(disponibilidade.getFim(), fim)){
                        configDisponibilidade.setDataLimite(disponibilidade.getFim().getTime());
                        configDisponibilidade.setFimHoraFormatada(Calendario.getHora(disponibilidade.getFim(), "HH:mm"));
                    }
                    Date inicio = new Date(configDisponibilidade.getDia());
                    if(Calendario.menor(disponibilidade.getInicio(), inicio)){
                        configDisponibilidade.setDia(disponibilidade.getInicio().getTime());
                        configDisponibilidade.setInicioHoraFormatada(Calendario.getHora(disponibilidade.getInicio(), "HH:mm"));
                    }
                }

                List<Agendamento> disponibilidadesAgrupadasChave = disponibilidadesAgrupadas.get(chave);
                if(disponibilidadesAgrupadasChave == null){
                    disponibilidadesAgrupadasChave = new ArrayList<>();
                    disponibilidadesAgrupadas.put(chave, disponibilidadesAgrupadasChave);
                }
                disponibilidadesAgrupadasChave.add(disponibilidade);

            }

            for(String chave : configs.keySet()){
                ConfigDisponibilidade configDisponibilidade = configs.get(chave);
                configDisponibilidade = configDisponibilidadeDao.insert(ctx, configDisponibilidade);
                List<Agendamento> disponibilidadesAgrupadasChave = disponibilidadesAgrupadas.get(chave);
                Set<Integer> agendamentosUpdate = new HashSet<>();
                for (Agendamento agendamento : disponibilidadesAgrupadasChave) {
                    agendamentosUpdate.add(agendamento.getCodigo());
                }
                if (!UteisValidacao.emptyList(agendamentosUpdate)) {
                    List<Integer> lista = new ArrayList<>();
                    lista.addAll(agendamentosUpdate);
                    agendamentoDao.atualizarAlgunsCamposLista(ctx, lista, configDisponibilidade.getCodigo());
                }
            }


        } catch (Exception ex) {
            throw new ServiceException(DisponibilidadeExcecoes.ERRO_GERAR_CONFIG_DISPONIBILIDADE, ex);
        }

    }

    private Map<Integer, List<Agendamento>> mapearPorTipoAgendamento(List<Agendamento> disponibilidades) {
        Map<Integer, List<Agendamento>> ret = new HashMap<>();
        Set<Integer> tiposAgendamentoId = new HashSet<>();
        for (Agendamento disponibilidade : disponibilidades) {
            tiposAgendamentoId.add(disponibilidade.getTipoEvento().getCodigo());
        }
        for (Integer tipoAgendamentoId : tiposAgendamentoId) {
            for (Agendamento disponibilidade : disponibilidades) {
                if (tipoAgendamentoId.equals(disponibilidade.getTipoEvento().getCodigo())) {
                    if (ret.get(tipoAgendamentoId) != null) {
                        ret.get(tipoAgendamentoId).add(disponibilidade);
                    } else {
                        List<Agendamento> disponibilidadesPorTipo = new ArrayList<>();
                        disponibilidadesPorTipo.add(disponibilidade);
                        ret.put(tipoAgendamentoId, disponibilidadesPorTipo);
                    }
                }
            }
        }
        return ret;
    }

    public List<DisponibilidadeConfigDTO> disponibilidadesTipoProfessor(Integer tipo, Integer professor) throws Exception{
        Map<String,Object> params = new HashMap<String,Object>();
        params.put("pr", professor);
        params.put("tp", tipo);
        String hql = "select obj from ConfigDisponibilidade obj where obj.tipoEvento.codigo = :tp and obj.professor.codigo = :pr";
        return this.obterDisponibilidades(hql, params);
    }

    public List<DisponibilidadeConfigDTO> disponibilidadesTipo(Integer tipo) throws Exception {
        Map<String,Object> params = new HashMap<String,Object>();
        params.put("tp", tipo);
        String hql = "select obj from ConfigDisponibilidade obj where obj.tipoEvento.codigo = :tp";
        return this.obterDisponibilidades(hql, params);
    }

    @Override
    public List<DisponibilidadeConfigDTO> todasDisponibilidades(Integer empresaId, PaginadorDTO paginadorDTO, FiltroDisponibilidadeDTO filtro) throws ServiceException {
       try {
           String ctx = sessaoService.getUsuarioAtual().getChave();
           if (isBlank(paginadorDTO.getSort())) {
               paginadorDTO.setSort("dia,DESC");
           }
           List<DisponibilidadeConfigDTO> ret = new ArrayList<>();
           List<ConfigDisponibilidade> configDisponibilidades = configDisponibilidadeDao.obterDisponibilidades(ctx, empresaId, filtro, paginadorDTO);
           for (ConfigDisponibilidade config : configDisponibilidades) {
               ret.add(config.toDTO(SuperControle.independente(ctx)));
           }

           return ret;
       } catch (Exception ex) {
           throw new ServiceException(DisponibilidadeExcecoes.ERRO_LISTAR_CONFIG_DISPONIBILIDADE, ex);
       }
    }

    @Override
    public List<br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO> findAllDisponibilidades(Integer empresaId, PaginadorDTO paginadorDTO, FiltroDisponibilidadeDTO filtro) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (!StringUtils.isBlank(paginadorDTO.getSort())) {
                paginadorDTO.setSort(paginadorDTO.getSort());
            } else {
                paginadorDTO.setSort("obj.nome,ASC");
            }
            List<br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO> ret = new ArrayList<>();
            disponibilidadeDao.getCurrentSession(ctx).clear();
            List<Disponibilidade> disponibilidades = disponibilidadeDao.findAllDisponibilidades(ctx, empresaId, filtro, paginadorDTO);
            for (Disponibilidade disp : disponibilidades) {
                ret.add(disp.toDTO(disp));
            }

            return ret;
        } catch (Exception ex) {
            throw new ServiceException(DisponibilidadeExcecoes.ERRO_LISTAR_CONFIG_DISPONIBILIDADE, ex);
        }
    }

    @Override
    public br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO findDisponibilidadeById(Integer codigo) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            disponibilidadeDao.getCurrentSession(ctx).clear();
            return new Disponibilidade().toDTO(disponibilidadeDao.findById(ctx, codigo));
        }catch (Exception ex){
            ex.printStackTrace();
            return null;
        }

    }

    @Override
    public br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO insertDisponibilidade(br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO dto) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer usuarioId = sessaoService.getUsuarioAtual().getId();
            disponibilidadeDao.getCurrentSession(ctx).clear();
            Disponibilidade obj = new Disponibilidade().toEntity(null, dto);
            povoarImagem(dto, ctx, obj);
            br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO disp = new Disponibilidade().toDTO(disponibilidadeDao.insert(ctx, obj));
            criarDisponibilidadeV2(null, disp, ctx, usuarioId);
            return disp;
        }catch (Exception ex){
            ex.printStackTrace();
            return null;
        }
    }

    @Override
    public String removerDisponibilidade(br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO dto) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer usuarioId = sessaoService.getUsuarioAtual().getId();
            disponibilidadeDao.getCurrentSession(ctx).clear();
            Disponibilidade disp = disponibilidadeDao.findById(ctx, dto.getCodigo());
            Disponibilidade dispFim = new Disponibilidade().toEntity(disp, dto);
            dispFim.setFim(Calendario.ontem());
            disponibilidadeDao.update(ctx, dispFim);
            recriarDisponibilidadesAgenda(ctx, dispFim, true, usuarioId);
            return "OK";
        }catch (Exception ex){
            ex.printStackTrace();
            return null;
        }
    }

    @Override
    public br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO updateDisponibilidade(br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO dto) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer usuarioId = sessaoService.getUsuarioAtual().getId();
            Disponibilidade obj = disponibilidadeDao.findById(ctx, dto.getCodigo());
            recriarDisponibilidadesAgenda(ctx, obj, true, usuarioId);
            obj = new Disponibilidade().toEntity(obj, dto);
            povoarImagem(dto, ctx, obj);
            Disponibilidade disp = disponibilidadeDao.update(ctx, obj);
            recriarDisponibilidadesAgenda(ctx, disp, false, usuarioId);
            return new Disponibilidade().toDTO(disp);
        }catch (Exception ex){
            ex.printStackTrace();
            return null;
        }
    }

    private void recriarDisponibilidadesAgenda(String ctx, Disponibilidade disp, boolean excluir, Integer usuarioId) {
        new Thread() {
            @Override
            public void run() {
                try {
                    List<HorarioDisponibilidade> horariosAtivos = disp.getHorarios().stream()
                            .filter(h -> h.getAtivo() != null && h.getAtivo())
                            .collect(Collectors.toList());

                    String diasAtivos = horariosAtivos.stream()
                            .map(HorarioDisponibilidade::getDiaSemana)
                            .distinct()
                            .collect(Collectors.joining(","));

                    final Disponibilidade[] dispWrapper = {disp};
                    dispWrapper[0].setDiasSemana(diasAtivos);

                    dispWrapper[0] = disponibilidadeDao.update(ctx, dispWrapper[0]);
                    if (excluir) {
                        AtomicReference<List<Agendamento>> agendamentosExcluir = new AtomicReference<>(new ArrayList<>());
                        HashMap<String, Object> param = new HashMap<>();
                        dispWrapper[0].getHorarios().forEach(h -> {
                            try {
                                param.put("codigoHorario", h.getCodigo());
                                agendamentosExcluir.get().addAll(agendamentoDao.findByParam(ctx, "select obj from Agendamento obj where obj.horarioDisponibilidade.codigo = :codigoHorario" +
                                        " and obj.inicio >= '" + Uteis.getDataJDBC(Calendario.hoje()) + " 00:00:00' and obj.disponibilidade is true", param));
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        });
                        agendamentoDao.deleteList(ctx, agendamentosExcluir.get());
                    } else {
                        dispWrapper[0].setHorarios(horariosAtivos);
                        criarDisponibilidadeV2(null, new Disponibilidade().toDTO(dispWrapper[0]), ctx, usuarioId);
                    }
                } catch (Exception e) {
                    Uteis.logar(e, this.getClass());
                }
            }
        }.start();

        // dalay de 3s para dar tempo de deletar os agedamentos
        if (excluir) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                Uteis.logar(e, this.getClass());
            }
        }
    }

    private void povoarImagem(br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO dto, String ctx, Disponibilidade obj) throws ServiceException {
        try {
            if (isNotBlank(obj.getIdentificadorFotoKey()) && isNotBlank(obj.getFotoKey()) && isBlank(dto.getUrlImagem())) {
                MidiaService.getInstance().deleteObject(obj.getFotoKey());
                obj.setIdentificadorFotoKey(null);
                obj.setFotoKey(null);
            }

            if (isNotBlank(dto.getBase64Imagem())) {
                String identificador = Calendario.getData(Calendario.hoje(), "ddMMyyyyhhMMss") + "-FotoDisponibilidade";
                MidiaEntidadeEnum tmidia = MidiaEntidadeEnum.obterPorExtensao("." + dto.getFormControlNomeImagem().split("\\.")[1]);
                obj.setIdentificadorFotoKey(identificador);
                obj.setFotoKey(MidiaService.getInstance().uploadObjectFromByteArray(ctx, tmidia, identificador, Base64.decodeBase64(dto.getBase64Imagem().split(",")[1])));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Override
    public List<ItemValidacaoDisponibilidadeDTO> findAllTipoValidacaoPlano(PaginadorDTO paginadorDTO, JSONObject filtro) throws ServiceException {
        List<ItemValidacaoDisponibilidadeDTO> planos = new ArrayList<>();
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                StringBuilder sql = new StringBuilder("select codigo, descricao from plano");
                if (filtro != null && isNotBlank(filtro.getString("quicksearchValue"))) {
                    sql.append(" where remove_acento_upper(descricao) like remove_acento_upper('%")
                            .append(filtro.getString("quicksearchValue").toUpperCase())
                            .append("%')");
                } else {
                    sql.append(" where 1=1");
                }
                sql.append(" and CURRENT_TIMESTAMP BETWEEN vigenciade AND vigenciaate");
                sql.append(" order by descricao asc");

                try (ResultSet result = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                    while (result.next()) {
                        ItemValidacaoDisponibilidadeDTO p = new ItemValidacaoDisponibilidadeDTO();
                        p.setPlano(result.getInt("codigo"));
                        p.setDescricao(result.getString("descricao"));
                        planos.add(p);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
        return planos;
    }

    @Override
    public List<ItemValidacaoDisponibilidadeDTO> findAllTipoValidacaoProduto(PaginadorDTO paginadorDTO, JSONObject filtro, String tipos) throws ServiceException {
        List<ItemValidacaoDisponibilidadeDTO> produtos = new ArrayList<>();
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            try (Connection conZW = conexaoZWService.conexaoZw(ctx)) {
                StringBuilder sql = new StringBuilder("select codigo, descricao, valorfinal from produto where 1=1 ");
                if(isNotBlank(tipos)){
                    sql.append(" and tipoProduto in ("+tipos+") ");
                }
                if (filtro != null && isNotBlank(filtro.getString("quicksearchValue"))) {
                    sql.append(" and remove_acento_upper(descricao) like remove_acento_upper('%").append(filtro.getString("quicksearchValue").toUpperCase()).append("%')");
                }
                sql.append(" and desativado is false order by descricao asc");
                try (ResultSet result = ConexaoZWServiceImpl.criarConsulta(sql.toString(), conZW)) {
                    while (result.next()) {
                        ItemValidacaoDisponibilidadeDTO p = new ItemValidacaoDisponibilidadeDTO();
                        p.setProduto(result.getInt("codigo"));
                        p.setDescricao(result.getString("descricao"));
                        p.setValorFinal(Uteis.arredondarForcando2CasasDecimais(result.getDouble("valorfinal")));
                        produtos.add(p);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
        return produtos;
    }

    private List<DisponibilidadeConfigDTO> obterDisponibilidades(String hql, Map<String,Object> params) throws Exception {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        configDisponibilidadeDao.prepare(ctx);
        List<ConfigDisponibilidade> configs = configDisponibilidadeDao.findByParam(ctx, hql, params);
        List<DisponibilidadeConfigDTO> disponibilidades = new ArrayList<DisponibilidadeConfigDTO>();
        for(ConfigDisponibilidade c : configs){
            disponibilidades.add(c.toDTO(SuperControle.independente(ctx)));
        }
        return disponibilidades;
    }

    @Override
    public List<DisponibilidadeConfigDTO> criarAlterarDisponibilidadeConfig(DisponibilidadeConfigGeradoraDTO disponilibilidadeConfig) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            List<DisponibilidadeConfigDTO> configDTOS = new ArrayList<>();
            ConfigDisponibilidade configDisponibilidade =
                    new ConfigDisponibilidade(disponilibilidadeConfig.getProfessorId(), disponilibilidadeConfig.getTipoAgendamentoId(), disponilibilidadeConfig);
            configDisponibilidade.setTipoEvento(tipoEventoService.obterPorId(ctx, disponilibilidadeConfig.getTipoAgendamentoId()));
            if (SuperControle.independente(ctx)) {
                configDisponibilidade.setProfessor(professorSinteticoService.obterPorId(ctx, disponilibilidadeConfig.getProfessorId()));
            } else {
                configDisponibilidade.setProfessor(professorSinteticoService.consultarPorCodigoColaborador(ctx, disponilibilidadeConfig.getProfessorId()));
            }
            int duracao = 0;
            if (!configDisponibilidade.getTipoEvento().getDuracao().name().equals("DURACAO_LIVRE")){
                duracao = configDisponibilidade.getTipoEvento().getDuracaoMinutosMin();
            }
            if (configDisponibilidade.getFim() - configDisponibilidade.getInicio() < duracao && duracao != 0){
                throw new ValidacaoException(String.valueOf(duracao));
            }
            if (UteisValidacao.emptyNumber(configDisponibilidade.getCodigo())) {
                configDisponibilidade = configDisponibilidadeDao.insert(ctx, configDisponibilidade);
            } else {
                configDisponibilidade = configDisponibilidadeDao.update(ctx, configDisponibilidade);
                deletarPorConfig(ctx, configDisponibilidade.getCodigo());
            }
            gerarDisponibilidades(ctx, configDisponibilidade, usuario);
            configDTOS.add(configDisponibilidade.toDTO(SuperControle.independente(ctx)));
            return configDTOS;
        } catch (Exception ex) {
            if (ex.toString().contains("ValidacaoException")) {
                throw new ServiceException(DisponibilidadeExcecoes.ERRO_SALVAR_CONFIG_DISPONIBILIDADE_HORARIO, ex);
            }
            throw new ServiceException(DisponibilidadeExcecoes.ERRO_SALVAR_CONFIG_DISPONIBILIDADE, ex);
        }
    }


    private void deletarPorConfig(String ctx, Integer cfg) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("DELETE FROM agendamento ag");
        sql.append(" WHERE ag.disponibilidade = true AND ag.nsu = ").append(cfg);

        agendamentoDao.executeNativeSQL(ctx, sql.toString());
    }

    @Override
    public Map<String, List<ServicoAgendamentoDisponibilidadeDTO>> disponibilidades(Integer empresaId, Date diaReferencia, PeriodoFiltrarEnum periodo, FiltrosAgendamentosDTO filtros, HttpServletRequest request) throws Exception{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        Map<String, List<ServicoAgendamentoDisponibilidadeDTO>> retorno = new HashMap<>();

        Date inicio = diaReferencia;
        Date fim = diaReferencia;
        if (periodo.equals(PeriodoFiltrarEnum.SEMANA)) {
            inicio = Calendario.inicioSemana(diaReferencia);
            fim = Calendario.fimSemana(diaReferencia);
        } else if (periodo.equals(PeriodoFiltrarEnum.MES)) {
            inicio = Calendario.inicioMes(diaReferencia);
            fim = Calendario.fimMes(diaReferencia);
        }
        for (Date diaPeriodo : Uteis.getDiasEntreDatas(inicio, fim)) {
            retorno.put(Calendario.getData(diaPeriodo, "yyyyMMdd"), new ArrayList<>());
        }
        String professoresSelecionados = "";
        String tiposServicoSelecionados = "";
        if (filtros != null) {
            if (!UteisValidacao.emptyList(filtros.getProfessoresId())) {
                for (Integer professorId : filtros.getProfessoresId()) {
                    professoresSelecionados += "," + professorId;
                }
                professoresSelecionados = professoresSelecionados.replaceFirst(",", "");
            }
            if (!UteisValidacao.emptyList(filtros.getTiposServicoId())) {
                for (Integer tipoServicoId : filtros.getTiposServicoId()) {
                    tiposServicoSelecionados += "," + tipoServicoId;
                }
                tiposServicoSelecionados = tiposServicoSelecionados.replaceFirst(",", "");
            }
        }

        List<Agendamento> agendamentos = agendamentoService.consultarPorData(ctx,
                Calendario.getDataComHoraZerada(inicio),
                Calendario.getDataComHora(fim, "23:59:59"),
                empresaId,
                professoresSelecionados,
                tiposServicoSelecionados,
                null,
                null,
                true,
                true,
                null,
                null,
                null, null);


        for(Agendamento disponibilidade : agendamentos){
            List<Agendamento> agendamentosConflitantes = agendamentoService.consultarPorData(ctx,
                    disponibilidade.getInicio(),
                    disponibilidade.getFim(),
                    empresaId,
                    (SuperControle.independente(ctx) ? disponibilidade.getProfessor().getCodigo().toString() : disponibilidade.getProfessor().getCodigoColaborador().toString()),
                    null,
                    null,
                    null,
                    false,
                    true,
                    null,
                    null,
                    null, null);
            disponibilidade.getProfessor().setUriImagem(UtilContext.getBean(FotoService.class).defineURLFotoPessoa(request, disponibilidade.getProfessor().getPessoa().getFotoKey(), disponibilidade.getProfessor().getCodigoPessoa(), false, ctx, false));
            if (retorno.get(Calendario.getData(disponibilidade.getInicio(), "yyyyMMdd")) != null) {
                retorno.get(Calendario.getData(disponibilidade.getInicio(), "yyyyMMdd")).add(new ServicoAgendamentoDisponibilidadeDTO(disponibilidade, agendamentosConflitantes, SuperControle.independente(ctx)));
            } else {
                List<ServicoAgendamentoDisponibilidadeDTO> disponibilidades = new ArrayList<>();
                disponibilidades.add(new ServicoAgendamentoDisponibilidadeDTO(disponibilidade, agendamentosConflitantes, SuperControle.independente(ctx)));
                retorno.put(Calendario.getData(disponibilidade.getInicio(), "yyyyMMdd"), disponibilidades);
            }
        }

        return retorno;
    }

    private List<Agendamento> processarValidarExcluirAgenDuplicados(Integer empresaId, String ctx, List<Agendamento> agendamentos) throws ServiceException {
        //Filtrar agendamentos titulares.
        List<Integer> agendamentosTitulares = new ArrayList<>();
        for(Agendamento disponibilidade : agendamentos) {
            List<Agendamento> duplicados = new ArrayList<>();

            if (disponibilidade.getTipoEvento() != null) {
                duplicados.addAll(agendamentoService.consultarPorData(ctx,
                        disponibilidade.getInicio(),
                        disponibilidade.getFim(),
                        empresaId,
                        (SuperControle.independente(ctx) ? disponibilidade.getProfessor().getCodigo().toString() : disponibilidade.getProfessor().getCodigoColaborador().toString()),
                        String.valueOf(disponibilidade.getTipoEvento().getCodigo()),
                        String.valueOf(disponibilidade.getStatusCod()),
                        null,
                        disponibilidade.getDisponibilidade(),
                        true,
                        null,
                        null,
                        null, null));
            } else {
                duplicados.addAll(agendamentoService.consultarPorData(ctx,
                        disponibilidade.getInicio(),
                        disponibilidade.getFim(),
                        empresaId,
                        (SuperControle.independente(ctx) ? disponibilidade.getProfessor().getCodigo().toString() : disponibilidade.getProfessor().getCodigoColaborador().toString()),
                        String.valueOf(disponibilidade.getHorarioDisponibilidade().getDisponibilidade().getCodigo()),
                        String.valueOf(disponibilidade.getStatusCod()),
                        null,
                        disponibilidade.getDisponibilidade(),
                        true,
                        null,
                        null,
                        null, false, null, true));
            }

            if(duplicados != null && duplicados.size() > 0){
                if(agendamentosTitulares.size() == 0) {
                    agendamentosTitulares.add(disponibilidade.getCodigo());
                }else if(podeAddTitularDuplicidade(duplicados, agendamentosTitulares)){
                    agendamentosTitulares.add(disponibilidade.getCodigo());
                }
            }
        }

        //Separar os titulares dos duplicados à exluir.
        List<Agendamento> agendamentosReais = new ArrayList<>();
        for(Agendamento disponibilidade : agendamentos){
            for(Integer codAgendamentoTitular : agendamentosTitulares){
                if(disponibilidade.getCodigo().equals(codAgendamentoTitular)){
                    agendamentosReais.add(disponibilidade);
                }
            }
        }

        //Excluir os duplicados.
        for(Agendamento disponibilidade : agendamentos){
            boolean excluir = true;
            for(Integer codAgendamentoTitular : agendamentosTitulares){
                if(disponibilidade.getCodigo().equals(codAgendamentoTitular)){
                    excluir = false;
                }
            }
            if(excluir) {
                agendamentoService.excluir(ctx, disponibilidade, false, false, false);
            }
        }
        return agendamentosReais;
    }

    public boolean podeAddTitularDuplicidade(List<Agendamento> duplicados, List<Integer> agendamentosComDuplicidade){
        for(Agendamento ag : duplicados){
            for(Integer ag2 : agendamentosComDuplicidade) {
                if (ag.getCodigo().equals(ag2)){
                    return false;
                }
            }
        }
        return true;
    }


    public void gerarDisponibilidades(String ctx, ConfigDisponibilidade config, Usuario usuario) throws Exception {
        Date inicio = Uteis.dataHoraZeradaUTC(config.getDia());
        if(UteisValidacao.emptyNumber(config.getDataLimite()) || UteisValidacao.emptyList(config.getListaDias())){
            Agendamento agendamento = montarAgendamento(inicio, config);
            agendamentoService.inserir(ctx, agendamento, usuario);
            return;
        }
        Date limite = Uteis.dataHoraZeradaUTC(config.getDataLimite());
        List<Date> diasEntreDatas = Uteis.getDiasEntreDatas(inicio, limite);
        for (Date dia : diasEntreDatas) {
            AgendaDiaSemana d = AgendaDiaSemana.getFromOrdinal(Calendario.getInstance(dia).get(Calendar.DAY_OF_WEEK));
            if(config.getListaDias().contains(d)){
                Agendamento agendamento = montarAgendamento(dia, config);
                agendamentoService.inserir(ctx, agendamento, usuario);
            }
        }
    }

    public Agendamento montarAgendamento(Date dia, ConfigDisponibilidade config) {
        Agendamento clone = new Agendamento();
        clone.setProfessor(config.getProfessor());
        clone.setCliente(null);
        clone.setDiaSemana(Calendario.getInstance(dia.getTime()).get(Calendar.DAY_OF_WEEK));
        clone.setInicio(Calendario.getDataComHora(dia, config.getDiaHoraInicio()));
        clone.setFim(Calendario.getDataComHora(dia, config.getDiaHoraFim()));
        clone.setTipoEvento(config.getTipoEvento());
        clone.setDisponibilidade(Boolean.TRUE);
        clone.setNsu(config.getCodigo());
        clone.setStatus(StatusAgendamentoEnum.AGUARDANDO_CONFIRMACAO);
        clone.setDataLancamento(Calendario.hoje());
        return clone;
    }

    public Map<String, List<Agendamento>> disponibilidadesAgendaV4(Integer empresaId,
                                                                   HttpServletRequest request,
                                                                   Date inicio,
                                                                   String ctx, Date fim) throws Exception {
        try {
            ctx = UteisValidacao.emptyString(ctx) ? sessaoService.getUsuarioAtual().getChave() : ctx;
            Map<String, List<Agendamento>> ret = new HashMap<>();

            List<Agendamento> agendamentos = agendamentoService.consultarPorData(ctx,
                    Calendario.getDataComHoraZerada(inicio),
                    Calendario.getDataComHora(fim, "23:59:59"),
                    empresaId,
                    null,
                    null,
                    null,
                    null,
                    true,
                    false,
                    null,
                    null,
                    null, null);

            agendamentos.addAll(agendamentoService.consultarPorData(ctx,
                    Calendario.getDataComHoraZerada(inicio),
                    Calendario.getDataComHora(fim, "23:59:59"),
                    empresaId,
                    null,
                    null,
                    null,
                    null,
                    true,
                    false,
                    null,
                    null,
                    null, false, null, true));
            Map<String, Agendamento> mapaAgrupar = new HashMap<>();
            for (Agendamento agendamento : agendamentos) {
                agendamento.getProfessor().setUriImagem((UtilContext.getBean(FotoService.class).defineURLFotoPessoa(request, agendamento.getProfessor().getPessoa().getFotoKey(), null, false, ctx, false)));

                String keyAgrupamento =
                        agendamento.getTipoEvento() != null
                        ? agendamento.getKeyAgrup() + agendamento.getTipoEvento().getCodigo()
                        : agendamento.getKeyAgrup() + agendamento.getHorarioDisponibilidade().getCodigo();

                if (mapaAgrupar.get(keyAgrupamento) == null) {
                    mapaAgrupar.put(keyAgrupamento, agendamento);
                    if (ret.get(Calendario.getData(agendamento.getInicio(), "yyyyMMdd")) == null) {
                        List<Agendamento> disponibilidades = new ArrayList<>();
                        disponibilidades.add(agendamento);
                        ret.put(Calendario.getData(agendamento.getInicio(), "yyyyMMdd"), disponibilidades);
                    } else {
                        ret.get(Calendario.getData(agendamento.getInicio(), "yyyyMMdd")).add(agendamento);
                    }
                }
            }

            return ret;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static String convertInputStreamToString(InputStream inputStream) throws IOException {
        StringBuilder resultStringBuilder = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
            String line;
            while ((line = br.readLine()) != null) {
                resultStringBuilder.append(line).append("\n");
            }
        }
        return resultStringBuilder.toString();
    }


    @Override
    public Map<String, List<AgendamentoDisponibilidadePersonalDTO>> disponibilidadesApp(
            String ctx,
            Integer empresaId,
            Date diaReferencia,
            PeriodoFiltrarEnum periodo,
            FiltrosAgendamentosDTO filtros,
            HttpServletRequest request,
            Integer sizeTipos, Boolean appTreino, Integer matricula) throws Exception{
        Map<String, List<AgendamentoDisponibilidadePersonalDTO>> retorno = new HashMap<>();

        Date inicio = diaReferencia;
        Date fim = diaReferencia;
        if (periodo.equals(PeriodoFiltrarEnum.SEMANA)) {
            inicio = Calendario.inicioSemana(diaReferencia);
            fim = Calendario.fimSemana(diaReferencia);
        } else if (periodo.equals(PeriodoFiltrarEnum.MES)) {
            inicio = Calendario.inicioMes(diaReferencia);
            fim = Calendario.fimMes(diaReferencia);
        }
        for (Date diaPeriodo : Uteis.getDiasEntreDatas(inicio, fim)) {
            retorno.put(Calendario.getData(diaPeriodo, "dd/MM/yyyy"), new ArrayList<>());
        }
        if (sizeTipos.equals(0)){
            return retorno;
        }
        String professoresSelecionados = "";
        String tiposServicoSelecionados = "";
        if (filtros != null) {
            if (!UteisValidacao.emptyList(filtros.getProfessoresId())) {
                for (Integer professorId : filtros.getProfessoresId()) {
                    professoresSelecionados += "," + professorId;
                }
                professoresSelecionados = professoresSelecionados.replaceFirst(",", "");
            }
            if (!UteisValidacao.emptyList(filtros.getTiposServicoId())) {
                for (Integer tipoServicoId : filtros.getTiposServicoId()) {
                    tiposServicoSelecionados += "," + tipoServicoId;
                }
                tiposServicoSelecionados = tiposServicoSelecionados.replaceFirst(",", "");
            }
        }

        if (appTreino) {
            retorno = new HashMap<>();
            List<AgendamentoDisponibilidadePersonalDTO> agendamentos = new ArrayList<>();
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");

            Calendar calInicio = Calendar.getInstance();
            Calendar calFim = Calendar.getInstance();
            Date dataAtual = Calendario.hoje();

            calInicio.setTime(inicio);
            calFim.setTime(Uteis.getDataHora2359(fim));

            if(Uteis.getDataHora2359(fim).before(dataAtual)){
                return retorno;
            }

            if(inicio.before(dataAtual)){
                calInicio.setTime(dataAtual);
            }

            if(!periodo.equals(PeriodoFiltrarEnum.DIA)){
                periodo = PeriodoFiltrarEnum.SEMANA;
                getAgendamentoDiaInicial(ctx, empresaId, filtros, request, retorno, agendamentos, calInicio);
            }

            // consulta não retornou as novas disponibilidades
            Map<String, List<Agendamento>> agendamentosDisponibilidade = disponibilidadesAgendaV4(
                        empresaId, request, calInicio.getTime(), ctx, fim);

            Calendar calDia = (Calendar) calInicio.clone();
            while (!calDia.getTime().after(calFim.getTime()) && calDia.getTime().before(Uteis.somarDias(fim, 1))) {
                String numeroDiaAtual = df.format(calDia.getTime());
                if (agendamentosDisponibilidade.containsKey(numeroDiaAtual)) {
                    agendamentos = new ArrayList<>();
                    for (Agendamento disponibilidade : agendamentosDisponibilidade.get(numeroDiaAtual)) {
                        if ((disponibilidade.getTipoEvento() != null && disponibilidade.getTipoEvento().getPermitirApp())
                        || (disponibilidade.getHorarioDisponibilidade() != null && disponibilidade.getHorarioDisponibilidade().getPermieAgendarAppTreino())) {
                            agendamentos.add(new AgendamentoDisponibilidadePersonalDTO(
                                    disponibilidade,
                                    SuperControle.independente(ctx)));
                        }
                    }
                    retorno.put(Calendario.getData(calDia.getTime(), "dd/MM/yyyy"), agendamentos);
                }
                calDia.add(Calendar.DAY_OF_MONTH, 1);
            }

            removeAgendamentosDeHorarioRetroativo(ctx, retorno);

            removeAgendamentosDoMesmoHorarioEProfessor(ctx, retorno, inicio, Calendario.getDataComUltimaHora(fim));

            retorno = agendamentosOrdenadosPorDia(retorno);

            if (matricula != null && matricula > 0) {
                removeDisponibilidadesDeProfessoresDeOutraCarteira(ctx, matricula, retorno);
            }

            return retorno;
        }

        List<Agendamento> agendamentos = agendamentoService.consultarPorData(ctx,
                Calendario.getDataComHoraZerada(inicio),
                Calendario.getDataComHora(fim, "23:59:59"),
                empresaId,
                professoresSelecionados,
                tiposServicoSelecionados,
                null,
                null,
                true,
                true,
                null,
                null,
                null, null);
        agendamentos.addAll(agendamentoService.consultarPorData(ctx,
                Calendario.getDataComHoraZerada(inicio),
                Calendario.getDataComHora(fim, "23:59:59"),
                empresaId,
                professoresSelecionados,
                tiposServicoSelecionados,
                null,
                null,
                true,
                true,
                null,
                null,
                null,false, null, true));

        List<Agendamento> agendamentosClientes = agendamentoService.consultarPorData(ctx,
                Calendario.getDataComHoraZerada(inicio),
                Calendario.getDataComHora(fim, "23:59:59"),
                empresaId,
                null,
                null,
                null,
                null,
                false,
                true,
                null,
                null,
                null, null);

        Map<String, List<Agendamento>> agendamentosAgrupados = new HashMap<>();
        for(Agendamento agendamento : agendamentosClientes){
            String key = Calendario.getData(agendamento.getInicio(), "dd/MM/yyyy") + "-"+ agendamento.getProfessor().getCodigo();
            if (agendamentosAgrupados.get(key) != null) {
                agendamentosAgrupados.get(key).add(agendamento);
            } else {
                List<Agendamento> agendamentosDia = new ArrayList<>();
                agendamentosDia.add(agendamento);
                agendamentosAgrupados.put(key, agendamentosDia);
            }
        }

        for(Agendamento disponibilidade : agendamentos){
            String key = Calendario.getData(disponibilidade.getInicio(), "dd/MM/yyyy") + "-"+ disponibilidade.getProfessor().getCodigo();
            List<Agendamento> agendamentosConflitantes = new ArrayList<>();
            if (agendamentosAgrupados.get(key) != null) {
                List<Agendamento> agendamentosProfessorDia = agendamentosAgrupados.get(key);
                for (Agendamento agendamento : agendamentosProfessorDia) {
                    if (verificaIntervaloDeHorasAgendamento(disponibilidade.getInicio(), disponibilidade.getFim(),
                            agendamento.getInicio(), agendamento.getFim())) {
                        agendamentosConflitantes.add(agendamento);
                    }
                }
            }
            if (!agendamentosConflitantes.isEmpty()){
                continue;
            }

            if (!UteisValidacao.dataMenorDataAtual(disponibilidade.getInicio())){
                disponibilidade.getProfessor().setUriImagem(UtilContext.getBean(FotoService.class).defineURLFotoPessoa(request, disponibilidade.getProfessor().getPessoa().getFotoKey(), disponibilidade.getProfessor().getCodigoPessoa(), false, ctx, false));
                if (retorno.get(Calendario.getData(disponibilidade.getInicio(), "dd/MM/yyyy")) != null) {
                    retorno.get(Calendario.getData(disponibilidade.getInicio(), "dd/MM/yyyy")).add(new AgendamentoDisponibilidadePersonalDTO(disponibilidade, SuperControle.independente(ctx)));
                } else {
                    List<AgendamentoDisponibilidadePersonalDTO> disponibilidades = new ArrayList<>();
                    disponibilidades.add(new AgendamentoDisponibilidadePersonalDTO(disponibilidade, SuperControle.independente(ctx)));
                    retorno.put(Calendario.getData(disponibilidade.getInicio(), "dd/MM/yyyy"), disponibilidades);
                }
            }
        }

        return retorno;
    }

    private void getAgendamentoDiaInicial(String ctx, Integer empresaId, FiltrosAgendamentosDTO filtros, HttpServletRequest request, Map<String, List<AgendamentoDisponibilidadePersonalDTO>> retorno, List<AgendamentoDisponibilidadePersonalDTO> agendamentos, Calendar calInicio) throws Exception {
        Map<String, List<AgendaDisponibilidadeDTO>> disponibilidadesAgendaV3 = disponibilidadesAgendaV3(
                empresaId, request, calInicio.getTime(), PeriodoFiltrarEnum.DIA, true, filtros, ctx);

        for(Map.Entry<String, List<AgendaDisponibilidadeDTO>> entry : disponibilidadesAgendaV3.entrySet()){
            for(AgendaDisponibilidadeDTO disponibilidade : entry.getValue()){
                agendamentos.add(new AgendamentoDisponibilidadePersonalDTO(
                        agendamentoDao.findById(ctx, disponibilidade.getId()),
                        SuperControle.independente(ctx),
                        disponibilidade.getHorarioInicial(),
                        disponibilidade.getHorarioFinal()
                ));
            }
            retorno.put(Calendario.getData(calInicio.getTime(), "dd/MM/yyyy"), agendamentos);
        }
    }

    private Map<String, List<AgendamentoDisponibilidadePersonalDTO>> agendamentosOrdenadosPorDia(Map<String, List<AgendamentoDisponibilidadePersonalDTO>> retorno) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        List<String> sortedKeys = new ArrayList<>(retorno.keySet());
        sortedKeys.sort((date1, date2) -> {
            Calendar cal1 = Calendar.getInstance();
            Calendar cal2 = Calendar.getInstance();
            try {
                cal1.setTime(dateFormat.parse(date1));
                cal2.setTime(dateFormat.parse(date2));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            return cal1.compareTo(cal2);
        });

        // Criar um novo map ordenado
        Map<String, List<AgendamentoDisponibilidadePersonalDTO>> sortedRetorno = new LinkedHashMap<>();
        for (String key : sortedKeys) {
            sortedRetorno.put(key, retorno.get(key));
        }


        return sortedRetorno;
    }

    private void removeDisponibilidadesDeProfessoresDeOutraCarteira(String ctx, Integer matricula, Map<String, List<AgendamentoDisponibilidadePersonalDTO>> retorno) throws Exception {
        if (matricula == null || matricula == 0) {
            throw new Exception("Matrícula não informada");
        }

        ClienteSintetico clienteSintetico = clienteSinteticoService.consultarPorMatricula(ctx, matricula + "");
        if (clienteSintetico == null) {
            throw new Exception("Matricula informada não encontrada!");
        }

        Integer codigoProfessoroCarteira = 0;
        Boolean removeTodosAgendamentos = false;
        if(clienteSintetico.getProfessorSintetico() == null){
            removeTodosAgendamentos = true;
        } else {
            codigoProfessoroCarteira = clienteSintetico.getProfessorSintetico().getCodigoColaborador();
        }

        for (Map.Entry<String, List<AgendamentoDisponibilidadePersonalDTO>> entry : retorno.entrySet()) {
            List<AgendamentoDisponibilidadePersonalDTO> agendamentosDia = entry.getValue();
            List<AgendamentoDisponibilidadePersonalDTO> agendamentosDeOutroProfessor = new ArrayList<>();
            for (AgendamentoDisponibilidadePersonalDTO agendamento : agendamentosDia) {
                if ((agendamento.getTipoAgendamento().getSomente_carteira_professor()
                        && !agendamento.getProfessor().getCodigoColaborador().equals(codigoProfessoroCarteira)) || (agendamento.getTipoAgendamento().getSomente_carteira_professor() && removeTodosAgendamentos)) {
                    agendamentosDeOutroProfessor.add(agendamento);
                }
            }
            for (AgendamentoDisponibilidadePersonalDTO agendamentoParaRemover : agendamentosDeOutroProfessor) {
                retorno.get(entry.getKey()).remove(agendamentoParaRemover);
            }
        }
    }

    private void removeAgendamentosDoMesmoHorarioEProfessor(String ctx, Map<String, List<AgendamentoDisponibilidadePersonalDTO>> retorno, Date dateInicio, Date dateFim) throws Exception {
        List<AgendamentoDisponibilidadePersonalDTO> agendamentosParaRemover = new ArrayList<>();

        List<Agendamento> agendamentosProfessor = agendamentoService.consultarAgendamentosDoDia(ctx,
                dateInicio, dateFim);

        for (Map.Entry<String, List<AgendamentoDisponibilidadePersonalDTO>> entry : retorno.entrySet()) {
            for (AgendamentoDisponibilidadePersonalDTO agendamento : entry.getValue()) {
                String dataInicio = agendamento.getDia() + " " + agendamento.getHorarioInicial();
                String dataFim = agendamento.getDia() + " " + agendamento.getHorarioFinal();

                dateInicio = Calendario.getDate("dd/MM/yyyy HH:mm", dataInicio);
                dateFim = Calendario.getDate("dd/MM/yyyy HH:mm", dataFim);

                for (Agendamento agendamentoProfessor : agendamentosProfessor) {

                    if (agendamentoProfessor.getProfessor().getCodigoColaborador().equals(agendamento.getProfessor().getCodigoColaborador())
                            && verificaIntervaloDeHorasAgendamento(dateInicio, dateFim, agendamentoProfessor.getInicio(), agendamentoProfessor.getFim())
                            && agendamento.getDia().equals(Uteis.getDataAplicandoFormatacao(agendamentoProfessor.getInicio(), "dd/MM/yyyy"))) {
                        agendamentosParaRemover.add(agendamento);
                    }


                }
            }
            for(AgendamentoDisponibilidadePersonalDTO agendamentoParaRemover : agendamentosParaRemover){
                retorno.get(entry.getKey()).remove(agendamentoParaRemover);
            }
        }
    }

    private boolean verificaIntervaloDeHorasAgendamento(Date dateInicio, Date dateFim, Date dateInicialAgendamentoMarcado, Date dateFinalAgendamentoMarcado) {
        Calendar cal = Calendar.getInstance();

        cal.setTime(dateInicio);
        int horaInicio = cal.get(Calendar.HOUR_OF_DAY);
        int minutoInicio = cal.get(Calendar.MINUTE);

        cal.setTime(dateFim);
        int horaFim = cal.get(Calendar.HOUR_OF_DAY);
        int minutoFim = cal.get(Calendar.MINUTE);

        cal.setTime(dateInicialAgendamentoMarcado);
        int horaInicioAgendamentoMarcado = cal.get(Calendar.HOUR_OF_DAY);
        int minutoInicioAgendamentoMarcado = cal.get(Calendar.MINUTE);

        cal.setTime(dateFinalAgendamentoMarcado);
        int horaFimAgendamentoMarcado = cal.get(Calendar.HOUR_OF_DAY);
        int minutoFimAgendamentoMarcado = cal.get(Calendar.MINUTE);

        int inicio = horaInicio * 60 + minutoInicio;
        int fim = horaFim * 60 + minutoFim;
        int inicioMarcado = horaInicioAgendamentoMarcado * 60 + minutoInicioAgendamentoMarcado;
        int fimMarcado = horaFimAgendamentoMarcado * 60 + minutoFimAgendamentoMarcado;

        return (inicio == inicioMarcado && fim == fimMarcado) ||
                (inicio == fimMarcado && fim == fimMarcado) ||
                (fim == inicioMarcado && fim == fimMarcado) ||
                (inicio == inicioMarcado && fim == fimMarcado) ||
                (inicio >= inicioMarcado && fim <= fimMarcado) ||
                (inicio <= inicioMarcado && fim >= fimMarcado) ||
                (inicio <= inicioMarcado && fim <= fimMarcado && fim > inicioMarcado) ||
                (inicio >= inicioMarcado && fim >= fimMarcado && inicio < fimMarcado);
    }

    private void removeAgendamentosDeHorarioRetroativo(String ctx, Map<String, List<AgendamentoDisponibilidadePersonalDTO>> retorno) throws Exception {
        Date dataAtual = Calendario.hoje();
        for (Map.Entry<String, List<AgendamentoDisponibilidadePersonalDTO>> entry : retorno.entrySet()) {
            List<AgendamentoDisponibilidadePersonalDTO> agendamentosDia = entry.getValue();
            List<AgendamentoDisponibilidadePersonalDTO> agendamentosDiaFuturos = new ArrayList<>();
            for (AgendamentoDisponibilidadePersonalDTO agendamento : agendamentosDia) {
                String dataFim = "";
                if (agendamento.getTipoAgendamento().getTipoEvento()) {
                    dataFim = agendamento.getDia() + " " + agendamento.getHorarioFinal();
                } else {
                    HorarioDisponibilidade hd = horarioDisponibilidadeDao.findById(ctx, agendamento.getHorarioDisponibilidadeCod());
                    dataFim = agendamento.getDia() + " " + hd.getHoraFinal();
                }
                Date datefim = Calendario.getDate("dd/MM/yyyy HH:mm", dataFim);
                if (datefim.after(dataAtual)) {
                    agendamentosDiaFuturos.add(agendamento);
                }
            }
            retorno.put(entry.getKey(), agendamentosDiaFuturos);
        }
    }

    public Map<String, List<AgendaDisponibilidadeDTO>> disponibilidadesAgendaV3(Integer empresaId,
                                                                                HttpServletRequest request,
                                                                                Date diaReferencia,
                                                                                PeriodoFiltrarEnum periodo,
                                                                                Boolean apenasDisponiveis,
                                                                                FiltrosAgendamentosDTO filtros, String ctx) throws ServiceException{
        try {
            ctx = UteisValidacao.emptyString(ctx) ? sessaoService.getUsuarioAtual().getChave() : ctx;
            Map<String, List<AgendaDisponibilidadeDTO>> ret = new HashMap<>();
            Date inicio = diaReferencia;
            Date fim = diaReferencia;
            String professoresSelecionados = "";
            String tiposServicoSelecionados = "";
            String statusAgendamentosSelecionados = "";
            if (periodo.equals(PeriodoFiltrarEnum.DIA)) {
                inicio = diaReferencia;
                fim = diaReferencia;
            } else if (periodo.equals(PeriodoFiltrarEnum.SEMANA)) {
                inicio = Uteis.somarDias(Calendario.inicioSemana(diaReferencia), 1);
                fim = Uteis.somarDias(Calendario.fimSemana(diaReferencia), 1);
            }
            for (Date diaPeriodo : Uteis.getDiasEntreDatas(inicio, fim)) {
                ret.put(Calendario.getData(diaPeriodo, "yyyyMMdd"), new ArrayList<>());
            }
            if (filtros != null) {
                if (!UteisValidacao.emptyList(filtros.getProfessoresId())) {
                    for (Integer professorId : filtros.getProfessoresId()) {
                        professoresSelecionados += "," + professorId;
                    }
                    professoresSelecionados = professoresSelecionados.replaceFirst(",", "");
                }
                if (!UteisValidacao.emptyList(filtros.getTiposServicoId())) {
                    for (Integer tipoServicoId : filtros.getTiposServicoId()) {
                        tiposServicoSelecionados += "," + tipoServicoId;
                    }
                    tiposServicoSelecionados = tiposServicoSelecionados.replaceFirst(",", "");
                }
                if (!UteisValidacao.emptyList(filtros.getStatusAgendamentos())) {
                    for (StatusAgendamentoEnum status : filtros.getStatusAgendamentos()) {
                        statusAgendamentosSelecionados += "," + status.ordinal();
                    }
                    statusAgendamentosSelecionados = statusAgendamentosSelecionados.replaceFirst(",", "");
                }
            }

            if(UteisValidacao.emptyString(tiposServicoSelecionados)){
                return ret;
            }

            List<Agendamento> agendamentos = agendamentoService.consultarPorData(ctx,
                    Calendario.getDataComHoraZerada(inicio),
                    Calendario.getDataComHora(fim, "23:59:59"),
                    empresaId,
                    professoresSelecionados,
                    tiposServicoSelecionados,
                    statusAgendamentosSelecionados,
                    null,
                    true,
                    false,
                    null,
                    null,
                    null, null);

            agendamentos.addAll(agendamentoService.consultarPorData(ctx,
                    Calendario.getDataComHoraZerada(inicio),
                    Calendario.getDataComHora(fim, "23:59:59"),
                    empresaId,
                    professoresSelecionados,
                    tiposServicoSelecionados,
                    statusAgendamentosSelecionados,
                    null,
                    true,
                    false,
                    null,
                    null,
                    null, false, null, true));
            Map<String, AgendaDisponibilidadeDTO> mapaAgrupar = new HashMap<>();
            for (Agendamento agendamento : agendamentos) {
                agendamento.getProfessor().setUriImagem((UtilContext.getBean(FotoService.class).defineURLFotoPessoa(request, agendamento.getProfessor().getPessoa().getFotoKey(), null, false, ctx, false)));
                String keyAgrupamento = agendamento.getKeyAgrup();
                if(mapaAgrupar.get(keyAgrupamento) == null){
                    AgendaDisponibilidadeDTO agendaDisponibilidadeDTO = new AgendaDisponibilidadeDTO(agendamento);
                    mapaAgrupar.put(keyAgrupamento, agendaDisponibilidadeDTO);
                    if (ret.get(Calendario.getData(agendamento.getInicio(), "yyyyMMdd")) == null) {
                        List<AgendaDisponibilidadeDTO> disponibilidades = new ArrayList<>();
                        disponibilidades.add(agendaDisponibilidadeDTO);
                        ret.put(Calendario.getData(agendamento.getInicio(), "yyyyMMdd"), disponibilidades);
                    } else {
                        ret.get(Calendario.getData(agendamento.getInicio(), "yyyyMMdd")).add(agendaDisponibilidadeDTO);
                    }
                } else if (
                        mapaAgrupar.get(keyAgrupamento) != null &&
                                mapaAgrupar.get(keyAgrupamento).getTiposAtividades() != null &&
                                agendamento.getTipoEvento() != null &&
                                !mapaAgrupar.get(keyAgrupamento)
                                        .getTiposAtividades()
                                        .stream()
                                        .anyMatch(item -> item.getId() == agendamento.getTipoEvento().getCodigo())
                ) {
                    mapaAgrupar.get(keyAgrupamento).getTiposAtividades().add(new TipoAtividadeDTO(agendamento.getTipoEvento()));
                }
            }
            if(apenasDisponiveis){
                Map<String, List<AgendaDisponibilidadeDTO>> retDisponiveis = new HashMap<>();
                //iterar nos dias
                for(String key : ret.keySet()){
                    retDisponiveis.put(key, new ArrayList<>());

                    for(AgendaDisponibilidadeDTO disponibilidadeOriginal : ret.get(key)){
                        List<AgendaDisponibilidadeDTO> disponibilidadeGeradas = new ArrayList(){{
                            add(disponibilidadeOriginal);
                        }};
                        //consultar agendamentos do professor no intervalo dessa disponibilidade
                        List<Agendamento> agendamentosProfessor = agendamentoService.consultarAgendamentoDataHoraProfessor(ctx,
                                Uteis.getDate(disponibilidadeOriginal.getDia(), "yyyyMMdd"),
                                disponibilidadeOriginal.getHorarioInicial(),
                                disponibilidadeOriginal.getHorarioFinal(),
                                disponibilidadeOriginal.getProfessor().getId());

                        for(Agendamento agendamento : agendamentosProfessor){
                            for(AgendaDisponibilidadeDTO disponibilidadeDTO : new ArrayList<>(disponibilidadeGeradas)){
                                //se o agendamento começa e termina na mesma hora que a disponibilidade, remove a disponibilidade
                                if(disponibilidadeDTO.getHorarioInicial().equals(agendamento.getHoraInicioApresentar())
                                        && disponibilidadeDTO.getHorarioFinal().equals(agendamento.getHoraFimApresentar())){
                                    disponibilidadeGeradas.remove(disponibilidadeDTO);
                                    continue;
                                }
                                //se o agendamento começa na mesma hora que a disponibilidade
                                if(disponibilidadeDTO.getHorarioInicial().equals(agendamento.getHoraInicioApresentar())
                                        && Calendario.maiorComHora(disponibilidadeDTO.getFimDate(), agendamento.getFim())){
                                    disponibilidadeDTO.setHorarioInicial(agendamento.getHoraFimApresentar());
                                    disponibilidadeDTO.setInicioDate(agendamento.getFim());
                                    continue;
                                }

                                //se o agendamento termina na mesma hora que a disponibilidade
                                if(disponibilidadeDTO.getHorarioFinal().equals(agendamento.getHoraFimApresentar())
                                        && Calendario.menorComHora(disponibilidadeDTO.getInicioDate(), agendamento.getInicio())){
                                    disponibilidadeDTO.setHorarioFinal(agendamento.getHoraInicioApresentar());
                                    disponibilidadeDTO.setFimDate(agendamento.getInicio());
                                    //sistema tem um comportamento de iniciar um evento 1 minuto depois de algum já criado para evitar conflito
                                    //isso gera problema de disponibilidade de 1 minuto
                                    //não exibir essa disponibilidade na agenda
                                    if(Uteis.minutosEntreDatas(disponibilidadeDTO.getInicioDate(), disponibilidadeDTO.getFimDate()) == 1){
                                        disponibilidadeGeradas.remove(disponibilidadeDTO);
                                    }

                                    continue;
                                }

                                //se o agendamento está dentro da disponibilidade, quebrar a disponibilidade
                                //se o agendamento termina na mesma hora que a disponibilidade
                                if(Calendario.menorComHora(disponibilidadeDTO.getInicioDate(), agendamento.getInicio()) &&
                                        Calendario.maiorComHora(disponibilidadeDTO.getFimDate(), agendamento.getFim())){
                                    //gerar outro
                                    AgendaDisponibilidadeDTO clone = disponibilidadeDTO.clone();
                                    disponibilidadeDTO.setHorarioFinal(agendamento.getHoraInicioApresentar());
                                    disponibilidadeDTO.setFimDate(agendamento.getInicio());
                                    clone.setHorarioInicial(agendamento.getHoraFimApresentar());
                                    clone.setInicioDate(agendamento.getFim());
                                    disponibilidadeGeradas.add(clone);
                                }

                            }
                        }
                        retDisponiveis.get(key).addAll(disponibilidadeGeradas);
                    }

                }

                return retDisponiveis;
            }
            return ret;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_LISTAR_AGENDAMENTOS, e);
        }

    }

    @Override
    public void criarDisponibilidadeV2(Integer empresaId, br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO disponibilidade) throws ServiceException {
        try {
            UsuarioSimplesDTO usuarioSimplesDTO = sessaoService.getUsuarioAtual();
            String ctx = usuarioSimplesDTO.getChave();
            Usuario usuario = usuarioService.obterPorId(ctx, usuarioSimplesDTO.getId());
            gravarDisponibilidadesV2(ctx, empresaId, usuario, disponibilidade, true);
            gravarDisponibilidadesV2(ctx, empresaId, usuario, disponibilidade, false);
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_SALVAR_AGENDAMENTO, e);
        }
    }

    @Override
    public void criarDisponibilidadeV2(Integer empresaId, br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO disponibilidade, String ctx, Integer usuarioId) throws ServiceException {
        try {
            Usuario usuario = usuarioService.obterPorId(ctx, usuarioId);
            gravarDisponibilidadesV2(ctx, empresaId, usuario, disponibilidade, true);
            gravarDisponibilidadesV2(ctx, empresaId, usuario, disponibilidade, false);
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_SALVAR_AGENDAMENTO, e);
        }
    }

    @Override
    public DisponibilidadeDTO criarDisponibilidade(HttpServletRequest request, Integer empresaId, DisponibilidadeDTO disponibilidade) throws ServiceException {
        try {
            UsuarioSimplesDTO usuarioSimplesDTO = sessaoService.getUsuarioAtual();
            String ctx = usuarioSimplesDTO.getChave();
            Usuario usuario = usuarioService.obterPorId(ctx, usuarioSimplesDTO.getId());

            List<GenericoTO> diasSemana = new ArrayList<>();
            for(AgendaDiaSemana ds : disponibilidade.getDiasSemana()){
                DiasSemana diaSemanaDisp = DiasSemana.getDiaSemanaDisp(ds);
                GenericoTO genericoTO = new GenericoTO(diaSemanaDisp.getNumeral(), diaSemanaDisp.getCodigo());
                genericoTO.setEscolhido(true);
                diasSemana.add(genericoTO);
            }
            gravarDisponibilidades(ctx, empresaId, usuario, diasSemana, disponibilidade, true);
            gravarDisponibilidades(ctx, empresaId, usuario, diasSemana, disponibilidade, false);

            return disponibilidade;
        } catch (Exception e) {
            throw new ServiceException(AgendaExcecoes.ERRO_SALVAR_AGENDAMENTO, e);
        }
    }

    public void gravarDisponibilidades(String ctx, Integer empresaId, Usuario usuario,
                                       List<GenericoTO> diasSemana, DisponibilidadeDTO disponibilidade, Boolean apenasValidar) throws Exception{
        for(Integer pKey : disponibilidade.getProfessores()){
            ProfessorSintetico professor;
            if (SuperControle.independente(ctx)) {
                professor = professorSinteticoService.obterPorId(ctx, pKey);
            } else {
                professor = professorSinteticoService.consultarPorCodigoColaborador(ctx, pKey);
            }
            Integer nsu = null;
            for(Integer tKey : disponibilidade.getTipos()){
                TipoEvento tipoEvento = tipoEventoService.obterPorId(ctx, tKey);
                Agendamento disponibilidadeBanco = new Agendamento();
                disponibilidadeBanco.setCliente(null);
                disponibilidadeBanco.setCodigo(null);
                disponibilidadeBanco.setDisponibilidade(true);
                String[] splitInicio = disponibilidade.getHorarioInicial().split(":");
                disponibilidadeBanco.setHoraInicio(splitInicio[0]);
                disponibilidadeBanco.setMinutoInicio(splitInicio[1]);
                String[] splitFim = disponibilidade.getHorarioFinal().split(":");
                disponibilidadeBanco.setHoraFim(splitFim[0]);
                disponibilidadeBanco.setMinutoFim(splitFim[1]);
                disponibilidadeBanco.setProfessor(professor);
                disponibilidadeBanco.setProfessorCod(professor.getCodigo());
                disponibilidadeBanco.setRepetir(true);
                disponibilidadeBanco.setDataAlterar(Calendario.getDate("yyyyMMdd", disponibilidade.getDataInicialFormatada()));
                disponibilidadeBanco.setTipoEventoCod(tKey);
                disponibilidadeBanco.setTipoEvento(tipoEvento);
                disponibilidadeBanco.setStatus(StatusAgendamentoEnum.CONFIRMADO);
                disponibilidadeBanco.setNsu(nsu);
                disponibilidadeBanco.atualizarInicioFim();

                if(apenasValidar){
                    agendamentoService.validaAgendamento(ctx, disponibilidadeBanco, usuario, empresaId);
                } else if (disponibilidade.getDataFinalFormatada().isEmpty() || disponibilidade.getDataFinalFormatada() == null) {
                    Agendamento agendamentoSalvo = agendamentoService.inserir(ctx, disponibilidadeBanco, usuario);
                    incluirLog(ctx,
                            agendamentoSalvo.getCodigo().toString(),
                            "",
                            "",
                            disponibilidadeBanco.getDescricaoParaLog(null) ,
                            "INCLUSÃO",
                            "INCLUSÃO DE DISPONIBILIDADE",
                            EntidadeLogEnum.DISPONIBILIDADE,
                            "Disponibilidade",
                            sessaoService,
                            logDao, null, null);
                } else {
                    new Thread() {
                        @Override
                        public void run() {
                            try {
                                disponibilidadeBanco.setRepetirAte(Calendario.getDate("yyyyMMdd", disponibilidade.getDataFinalFormatada()));
                                agendamentoService.repetir(ctx, disponibilidadeBanco, disponibilidadeBanco.getRepetirAte(),
                                        null, true, diasSemana, false, false, usuario);
                            } catch (Exception e) {
                                Uteis.logar(e, this.getClass());
                            }
                        }
                    }.start();
                    // dalay de 1s para dar tempo de salvar a primeira disponibilidade
                    // para que ela seja obtida ao montar a agenda;
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Uteis.logar(e, this.getClass());
                    }
                }
            }
        }
    }

    @Override
    public Disponibilidade obterPorId(String ctx, Integer id) throws ServiceException {
        try {
            return disponibilidadeDao.findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Disponibilidade obterPorIdHorario(String ctx, Integer id) throws ServiceException {
        try {
            StringBuilder hql = new StringBuilder();
            hql.append("select obj from Disponibilidade obj ");
            hql.append(" inner join obj.horarios h ");
            hql.append(" where h.codigo = ").append(id);
            List<Disponibilidade> disp = disponibilidadeDao.findByParam(ctx, hql.toString(), new HashMap<>(), 1, 0);
            if (!disp.isEmpty()) {
                return disp.get(0);
            }
            return null;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<TipoAgendamentoDTO> consultarDisponibilidadesHorarioTipoAgendamento(Date dia, Integer hora, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return disponibilidadeDao.consultarDisponibilidadesHorarioTipoAgendamento(ctx, dia, hora, empresaId);
    }

    @Override
    public TipoAgendamentoDuracaoDTO obterPorIdTipoAgendamentoDuracao(Integer id, Integer empresaId, Integer ambienteId, String inicio, String fim) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return disponibilidadeDao.obterPorIdTipoAgendamentoDuracao(ctx, id, empresaId, ambienteId, inicio, fim);
    }

    @Override
    public AgendaDisponibilidadeDTO detalheDisponibilidadeAgenda(Date dia, Integer hora, Integer tipo, Integer professor, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return disponibilidadeDao.detalheDisponibilidadeAgenda(ctx, dia, hora, tipo, professor, empresaId);
    }

    @Override
    public Boolean existeAgendamentoAlunoHorarioDisponibilidade(Integer idHorario, List<HorarioDisponibilidadeDTO> horarios) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if (idHorario != null && idHorario == 0 && horarios != null && !horarios.isEmpty()) {
            boolean possui = false;
            for (HorarioDisponibilidadeDTO horario : horarios) {
                if (horario.getCodigo() != null && horario.getCodigo() > 0) {
                    if (disponibilidadeDao.existeAgendamentoAlunoHorarioDisponibilidade(ctx, horario.getCodigo())) {
                        possui = true;
                        break;
                    }
                }
            }
            return possui;
        } else {
            if (idHorario == null || idHorario == 0) {
                return false;
            }
            return disponibilidadeDao.existeAgendamentoAlunoHorarioDisponibilidade(ctx, idHorario);
        }
    }

    @Override
    public List<ColaboradorSimplesTO> consultarDisponibilidadesHorarioProfessorTipoAgendamento(Date dia, Integer hora, Integer tipo, Integer empresaId) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return disponibilidadeDao.consultarDisponibilidadesHorarioProfessorTipoAgendamento(ctx, dia, hora, tipo, empresaId);
    }

    public void gravarDisponibilidadesV2(String ctx, Integer empresaId, Usuario usuario,
                                         br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO disponibilidade, Boolean apenasValidar) throws Exception{
        for (HorarioDisponibilidadeDTO hD : disponibilidade.getHorarios()) {
            if (!hD.getAtivo()) {
                continue;
            }
            ProfessorSintetico professor = professorSinteticoService.obterPorId(ctx, hD.getProfessor().getId());
            Disponibilidade disponibilidadeConfig = disponibilidadeDao.findById(ctx, disponibilidade.getCodigo());
            Agendamento disponibilidadeBanco = new Agendamento();
            disponibilidadeBanco.setCliente(null);
            disponibilidadeBanco.setCodigo(null);
            disponibilidadeBanco.setDisponibilidade(true);
            String[] splitInicio = hD.getHoraInicial().split(":");
            disponibilidadeBanco.setHoraInicio(splitInicio[0]);
            disponibilidadeBanco.setMinutoInicio(splitInicio[1]);
            String[] splitFim = hD.getHoraFinal().split(":");
            disponibilidadeBanco.setHoraFim(splitFim[0]);
            disponibilidadeBanco.setMinutoFim(splitFim[1]);
            disponibilidadeBanco.setProfessor(professor);
            disponibilidadeBanco.setProfessorCod(professor.getCodigo());
            disponibilidadeBanco.setRepetir(true);
            disponibilidadeBanco.setDataAlterar(Calendario.hoje());
            disponibilidadeBanco.setDisponibilidadeConfigCod(disponibilidadeConfig.getCodigo());
            disponibilidadeBanco.setHorarioDisponibilidade(disponibilidadeConfig.getHorarios().stream().filter(h -> h.getCodigo().equals(hD.getCodigo()))
                    .findFirst().orElse(null));
            if(disponibilidadeBanco.getHorarioDisponibilidade() == null) {
                continue;
            }
            disponibilidadeBanco.setStatus(StatusAgendamentoEnum.CONFIRMADO);
            disponibilidadeBanco.setNsu(null);

            disponibilidadeBanco.setStatus(StatusAgendamentoEnum.CONFIRMADO);
            disponibilidadeBanco.setNsu(null);
            disponibilidadeBanco.setInicio(disponibilidadeConfig.getInicio());
            disponibilidadeBanco.setFim(disponibilidadeConfig.getFim());

            if (apenasValidar) {
                agendamentoService.validaAgendamentoV2(ctx, disponibilidadeBanco, usuario, empresaId);
            } else if (disponibilidade.getDataFinal() == null) {
                Agendamento agendamentoSalvo = agendamentoService.inserirV2(ctx, disponibilidadeBanco, usuario, null);
                incluirLog(ctx,
                        agendamentoSalvo.getCodigo().toString(),
                        "",
                        "",
                        disponibilidadeBanco.getDescricaoParaLog(null),
                        "INCLUSÃO",
                        "INCLUSÃO DE DISPONIBILIDADE",
                        EntidadeLogEnum.DISPONIBILIDADE,
                        "Disponibilidade",
                        sessaoService,
                        logDao, null, null);
            } else {
                new Thread() {
                    @Override
                    public void run() {
                        try {
                            disponibilidadeBanco.setRepetirAte(disponibilidade.getDataFinal());
                            agendamentoService.repetirV2(ctx, disponibilidadeBanco, disponibilidadeBanco.getRepetirAte(),
                                    null, true, hD.getDiaSemana(), false, false, usuario);
                        } catch (Exception e) {
                            Uteis.logar(e, this.getClass());
                        }
                    }
                }.start();
                // dalay de 1s para dar tempo de salvar a primeira disponibilidade
                // para que ela seja obtida ao montar a agenda;
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Uteis.logar(e, this.getClass());
                }
            }
        }
    }

    public boolean configDisponibilidade(Integer id) throws Exception{
        UsuarioSimplesDTO usuarioSimplesDTO = sessaoService.getUsuarioAtual();
        String ctx = usuarioSimplesDTO.getChave();

        Agendamento byId = agendamentoDao.findById(ctx, id);
        return byId == null ? false : possuiAgendamentoAssociado(ctx, byId.getNsu(), byId.getInicio());
    }


    public void alterarDisponibilidade(Integer id, DisponibilidadeEditDTO edit) throws ServiceException{
        UsuarioSimplesDTO usuarioSimplesDTO = sessaoService.getUsuarioAtual();
        String ctx = usuarioSimplesDTO.getChave();
        try {
            Agendamento agendamento = agendamentoDao.findById(ctx, id);
            Integer professor = agendamento.getProfessor().getCodigo();
            Date dia = new Date(agendamento.getInicio().getTime());
            HorasInteirosDisponibilidade horarios = new HorasInteirosDisponibilidade(Uteis.getDataAplicandoFormatacao(agendamento.getInicio(), "HH:mm"),
                    Uteis.getDataAplicandoFormatacao(agendamento.getFim(), "HH:mm"));
            Agendamento antesAlteracao = UtilReflection.copy(agendamento);
            agendamento.setInicio(Calendario.getDataComHora(agendamento.getInicio(), edit.getHorarioInicial()));
            agendamento.setFim(Calendario.getDataComHora(agendamento.getFim(), edit.getHorarioFinal()));
            agendamentoDao.update(ctx, agendamento);
            Usuario usuario = usuarioService.obterPorId(ctx, usuarioSimplesDTO.getId());
            aplicarAlteracoesDisponibilidadesFuturas(ctx,
                    dia, professor, horarios, edit.getHorarioInicial(),
                    edit.getHorarioFinal(), edit.getRepetir(), usuario);
            incluirLog(ctx, agendamento.getCodigo().toString(), "", antesAlteracao.getDescricaoParaLog(agendamento), agendamento.getDescricaoParaLog(antesAlteracao),
                    "ALTERAÇÃO", "ALTERAÇÃO DE DISPONIBILIDADE", EntidadeLogEnum.DISPONIBILIDADE, "Disponibilidade", sessaoService, logDao, null, null);

        }catch (Exception e){
            throw new ServiceException(e.getMessage());
        }
    }

    public boolean possuiAgendamentoAssociado(String ctx, Integer nsu, Date dia) throws ServiceException {
        try {
            String hql = "SELECT obj FROM Agendamento obj "
                    + "WHERE nsu = :nsu and inicio > :dia";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("nsu", nsu);
            params.put("dia", dia);
            List<Agendamento> agendamentos = agendamentoDao.findByParam(ctx, hql, params, 1, 0);
            return agendamentos != null && !agendamentos.isEmpty();
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }

    class HorasInteirosDisponibilidade{
        int horaInicio;
        int horaFim;
        int minutoInicio;
        int minutoFim;

        public HorasInteirosDisponibilidade(String inicio, String fim) {
            String[] splitInicio = inicio.split(":");
            this.horaInicio = Integer.valueOf(splitInicio[0]);
            this.minutoInicio = Integer.valueOf(splitInicio[1]);;
            String[] splitFim = fim.split(":");
            this.horaFim = Integer.valueOf(splitFim[0]);
            this.minutoFim = Integer.valueOf(splitFim[1]);
        }
    }

    @Override
    public void removerDisponibilidade(Integer empresaId, Integer id, Boolean posteriores) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Agendamento disponibilidade = agendamentoService.obterPorId(ctx, id);
            Integer professor = disponibilidade.getProfessor().getCodigo();
            HorasInteirosDisponibilidade horarios = new HorasInteirosDisponibilidade(Uteis.getDataAplicandoFormatacao(disponibilidade.getInicio(), "HH:mm"),
                    Uteis.getDataAplicandoFormatacao(disponibilidade.getFim(), "HH:mm"));
            disponibilidade.setPosteriores(posteriores);
            aplicarExcluirDisponibilidadesFuturas(ctx, new Date(disponibilidade.getInicio().getTime()),
                    professor, horarios, posteriores, disponibilidade);
            incluirLog(ctx, disponibilidade.getCodigo().toString(), "", disponibilidade.getDescricaoParaLog(null), "", "EXCLUSÃO", "EXCLUSÃO DE DISPONIBILIDADE", EntidadeLogEnum.DISPONIBILIDADE, "Disponibilidade", sessaoService, logDao, null, null);

        } catch (Exception ex) {
            throw new ServiceException(DisponibilidadeExcecoes.ERRO_REMOVER_DISPONIBILIDADE_CONFIG, ex);
        }
    }

    public void aplicarExcluirDisponibilidadesFuturas(final String ctx,
                                                      Date dia,
                                                      Integer professor,
                                                      HorasInteirosDisponibilidade horarios,
                                                      boolean diasFuturos,
                                                      Agendamento disponibilidade
    ) throws ServiceException {
        try {
            StringBuilder sqlWhere = new StringBuilder();
            sqlWhere.append(" where disponibilidade and professor_codigo = ").append(professor).append("\n");
            if(diasFuturos){
                sqlWhere.append(" and inicio > '").append(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append(" 00:00:00'\n");
            }else{
                sqlWhere.append(" and inicio::date = '").append(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("' \n");
            }
            sqlWhere.append(" and extract(hour from inicio) = ").append(horarios.horaInicio).append("\n");
            sqlWhere.append(" and extract(minute from inicio) = ").append(horarios.minutoInicio).append("\n");
            sqlWhere.append(" and extract(hour from fim) = ").append(horarios.horaFim).append("\n");
            sqlWhere.append(" and extract(minute from fim) = ").append(horarios.minutoFim).append("\n");
            persistirOperacaoEmMassa(ctx, sqlWhere.toString(), disponibilidade);
            agendamentoDao.executeNative(ctx, "delete from agendamento " +sqlWhere);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private void persistirOperacaoEmMassa(String ctx, String where, Agendamento disponibilidade) throws Exception{
        try (ResultSet maxRs = agendamentoDao.createStatement(ctx, "select max(fim) as fim from agendamento " + where)) {
            if (maxRs.next()) {
                disponibilidade.setFimExclusao(maxRs.getDate("fim"));
            }
        }
        disponibilidade.setOperacaoEmMassa(TipoRevisaoEnum.DELETE.getId());
        agendamentoDao.update(ctx, disponibilidade);
    }

    public void aplicarAlteracoesDisponibilidadesFuturas(final String ctx,
                                                         Date dia,
                                                         Integer professor,
                                                         HorasInteirosDisponibilidade horarios,
                                                         String inicio,
                                                         String fim,
                                                         boolean diasFuturos, Usuario usuario) throws ServiceException {
        try {
            StringBuilder sqlUpdate = new StringBuilder();
            sqlUpdate.append(" update agendamento set \n");
            sqlUpdate.append("         usuarioultalteracao_codigo = ").append(usuario.getCodigo()).append(", \n");
            sqlUpdate.append("         ultimaalteracao = '");
            sqlUpdate.append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd HH:mm:ss"));
            sqlUpdate.append("'").append(", \n");
            sqlUpdate.append("         inicio = date_trunc('day', inicio) + '").append(inicio).append(":00', \n");
            sqlUpdate.append("         fim = date_trunc('day', fim) + '").append(fim).append(":00' \n");
            sqlUpdate.append(" where disponibilidade and professor_codigo = ").append(professor).append("\n");
            if(diasFuturos){
                sqlUpdate.append(" and inicio > '").append(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append(" 00:00:00'\n");
            }else{
                sqlUpdate.append(" and inicio::date = '").append(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("' \n");
            }
            sqlUpdate.append(" and extract(hour from inicio) = ").append(horarios.horaInicio).append("\n");
            sqlUpdate.append(" and extract(minute from inicio) = ").append(horarios.minutoInicio).append("\n");
            sqlUpdate.append(" and extract(hour from fim) = ").append(horarios.horaFim).append("\n");
            sqlUpdate.append(" and extract(minute from fim) = ").append(horarios.minutoFim).append("\n");

            agendamentoDao.executeNative(ctx, sqlUpdate.toString());
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }

    public List<TipoAgendamentoDTO> consultarDisponibilidadesHorario(Date dia, Integer hora, Integer empresaId) throws ServiceException{
        try {
            Date inicio = Calendario.getDataComHora(dia, (hora > 9 ? hora.toString() : ("0".concat(hora.toString()))).concat(":01"));
            Date fim = Uteis.somarCampoData(inicio, Calendar.MINUTE, 59);
            String ctx = sessaoService.getUsuarioAtual().getChave();
            StringBuilder query = new StringBuilder();
            HashMap<String, Object> p = new HashMap<String, Object>();
            query.append(" select distinct obj.tipoEvento from Agendamento obj ");
            query.append(" where obj.tipoEvento.ativo is true and (:inicio between obj.inicio and obj.fim OR obj.inicio between :inicio and :fim) and obj.disponibilidade IS true  ");
            query.append(" and obj.professor.empresa.codZW = ").append(empresaId);
            p.put("inicio", inicio);
            p.put("fim", fim);
            List<TipoEvento> tipoEventos = tipoEventoDao.findByParam(ctx, query.toString(), p);
            return new ArrayList(){{
                for(TipoEvento te : tipoEventos){
                    add(new TipoAgendamentoDTO(te));
                }
            }};
        }catch (Exception e){
            throw new ServiceException(e.getMessage());
        }
    }

    public List<TipoAgendamentoDTO> consultarDisponibilidadesHorario(Date inicio, Date fim,
                                                                     Integer empresaId,
                                                                     TipoEvento tipoEvento,
                                                                     String nomeProfessor,
                                                                     String codProfessores) throws ServiceException{
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            StringBuilder query = new StringBuilder();
            HashMap<String, Object> p = new HashMap<String, Object>();
            query.append(" select obj from Agendamento obj ");
            query.append(" where 1 = 1 ");
            if (tipoEvento != null) {
                query.append(" obj.tipoEvento.ativo is true ");
            }
            query.append(" and obj.professor.ativo is true");
            query.append(" and (:inicio between obj.inicio and obj.fim ");
            query.append(" OR obj.inicio between :inicio and :fim) and obj.disponibilidade IS true  ");
            query.append(" and obj.professor.empresa.codZW = ").append(empresaId);
            if(!UteisValidacao.emptyString(nomeProfessor)){
                query.append(" and upper(obj.professor.nome) like '%");
                query.append(nomeProfessor.toUpperCase()).append("%'");
            }
            if(!UteisValidacao.emptyString(codProfessores)){
                query.append(" and obj.professor.codigoColaborador IN (").append(codProfessores).append(") ");
            }
            if(tipoEvento != null){
                query.append(" and obj.tipoEvento.codigo = ").append(tipoEvento.getCodigo());
            }
            p.put("inicio", inicio);
            p.put("fim", fim);
            List<Agendamento> disponibilidades = agendamentoDao.findByParam(ctx, query.toString(), p);
            List<TipoAgendamentoDTO> dtos = new ArrayList<>();
            for(Agendamento a : disponibilidades){
                ProfessorResponseTO prof = new ProfessorResponseTO(a.getProfessor().getCodigo(),
                        a.getProfessor().getNome());
                TipoAgendamentoDTO tipoAgendamentoDTO;
                if (a.getTipoEvento() != null && a.getTipoEvento().getCodigo() != null && a.getTipoEvento().getCodigo() > 0) {
                    tipoAgendamentoDTO = new TipoAgendamentoDTO(a.getTipoEvento(), prof);
                } else {
                    tipoAgendamentoDTO = new TipoAgendamentoDTO(a.getHorarioDisponibilidade().getDisponibilidade(), prof);
                }
                a.setarHoras();
                tipoAgendamentoDTO.setInicio(Calendario.getHora(a.getInicio(), "HHmm"));
                tipoAgendamentoDTO.setFim(Calendario.getHora(a.getFim(), "HHmm"));
                dtos.add(tipoAgendamentoDTO);
            }
            return dtos;
        }catch (Exception e){
            throw new ServiceException(e.getMessage());
        }
    }

    public static Map<String, List<TipoAgendamentoDTO>> agruparPorComportamento(List<TipoAgendamentoDTO> agendamentos) {
        Map<String, List<TipoAgendamentoDTO>> agendamentosAgrupados = new HashMap<>();

        for (TipoAgendamentoDTO agendamento : agendamentos) {
            String comportamentoEnum = agendamento.getComportamentoEnum().toString();

            if (!agendamentosAgrupados.containsKey(comportamentoEnum)) {
                agendamentosAgrupados.put(comportamentoEnum, new ArrayList<>());
            }
            agendamentosAgrupados.get(comportamentoEnum).add(agendamento);
        }

        return agendamentosAgrupados;
    }

    @Override
    public Map<String, Map<TipoAgendamentoEnum, List<AgendamentoDisponibilidadePersonalDTO>>> disponibilidadesPorComportamentoApp(String ctx, Integer empresaId, Date dia, PeriodoFiltrarEnum periodo, FiltrosAgendamentosDTO filtros, HttpServletRequest request, Integer sizeTipos) throws Exception {
       Map<String, List<AgendamentoDisponibilidadePersonalDTO>> disponibilidadesApp = disponibilidadesApp(ctx, empresaId, dia, periodo, filtros, request, sizeTipos, false, 0);
        Map<String, Map<TipoAgendamentoEnum, List<AgendamentoDisponibilidadePersonalDTO>>> mapaAgrupado = disponibilidadesApp.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .collect(Collectors.groupingBy(agendamento -> agendamento.getTipoAgendamento().getComportamentoEnum()))
                ));

        return mapaAgrupado;
    }

    public List<ColaboradorSimplesTO> consultarDisponibilidadesHorarioProfessor(Date dia, Integer hora, Integer tipo, Integer empresaId) throws ServiceException{
        try {
            Date inicio = Calendario.getDataComHora(dia, (hora > 9 ? hora.toString() : ("0".concat(hora.toString()))).concat(":01"));
            Date fim = Uteis.somarCampoData(inicio, Calendar.MINUTE, 59);
            String ctx = sessaoService.getUsuarioAtual().getChave();
            StringBuilder query = new StringBuilder();
            HashMap<String, Object> p = new HashMap<String, Object>();
            query.append(" select distinct obj.professor from Agendamento obj ");
            query.append(" where obj.tipoEvento.ativo is true and (:inicio between obj.inicio and obj.fim OR obj.inicio between :inicio and :fim) and obj.disponibilidade IS true  ");
            query.append(" and obj.professor.ativo is true ");
            query.append(" and obj.professor.empresa.codZW = ").append(empresaId);
            query.append(" and obj.tipoEvento.codigo = ").append(tipo);
            p.put("inicio", inicio);
            p.put("fim", fim);
            List<ProfessorSintetico> professorSinteticos = professorsinteticoDao.findByParam(ctx, query.toString(), p);
            return new ArrayList(){{
               for(ProfessorSintetico p : professorSinteticos){
                   add(new ColaboradorSimplesTO(p, SuperControle.independente(ctx)));
               }
            }};
        }catch (Exception e){
            throw new ServiceException(e.getMessage());
        }
    }

    public AgendaDisponibilidadeDTO detalheDisponibilidade(Date dia, Integer hora, Integer tipo, Integer professor, Integer empresaId) throws ServiceException{
        try {
            Date inicio = Calendario.getDataComHora(dia, (hora > 9 ? hora.toString() : ("0".concat(hora.toString()))).concat(":01"));
            Date fim = Uteis.somarCampoData(inicio, Calendar.MINUTE, 59);
            String ctx = sessaoService.getUsuarioAtual().getChave();
            StringBuilder query = new StringBuilder();
            HashMap<String, Object> p = new HashMap<String, Object>();
            query.append(" select obj from Agendamento obj ");
            query.append(" where obj.tipoEvento.ativo is true and (:inicio between obj.inicio and obj.fim OR obj.inicio between :inicio and :fim) and obj.disponibilidade IS true  ");
            query.append(" and obj.professor.empresa.codZW = ").append(empresaId);
            query.append(" and obj.tipoEvento.codigo = ").append(tipo);
            query.append(" and obj.professor.").append(SuperControle.independente(ctx) ? "codigo = " : "codigoColaborador = ").append(professor);
            p.put("inicio", inicio);
            p.put("fim", fim);
            List<Agendamento> agendamentos = agendamentoDao.findByParam(ctx, query.toString(), p);
            if(agendamentos == null || agendamentos.isEmpty()){
                return new AgendaDisponibilidadeDTO();
            }
            return new AgendaDisponibilidadeDTO(agendamentos.get(0));
        }catch (Exception e){
            throw new ServiceException(e.getMessage());
        }
    }

    public String migrarDadosAgendamentoModeloNovoDisponibilidade() throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            List<TipoEvento> disponibilidades = tipoEventoDao.findAll(ctx);
            List<Disponibilidade> disponibilidadesGeradas = new ArrayList<>();
            List<Agendamento> agendamentosMigrados = new ArrayList<>();
            for (TipoEvento disp : disponibilidades) {
                Disponibilidade disponibilidade = new Disponibilidade();
                disponibilidade.setNome(disp.getNome());
                disponibilidade.setCor(disp.getCor().getCor());
                disponibilidade.setComportamento(disp.getComportamento().getId());
                disponibilidade.setIntervaloDiasEntreAgendamentos(disp.getDias());
                disponibilidade.setIntervaloMinimoDiasCasoFalta(disp.getIntervaloMinimoFalta());
                disponibilidade.setTipoValidacao(0);
                disponibilidade.setTipoHorario(disp.getDuracaoPreDefinida() ? 1 : (disp.getDuracaoIntervalo() ? 2 : 0));
                if (disponibilidade.getTipoHorario() != 0) {
                    if (disponibilidade.getTipoHorario() == 1) {
                        disponibilidade.setDuracao(disp.getDuracaoMinutosMin());
                    }
                    if (disponibilidade.getTipoHorario() == 2) {
                        disponibilidade.setDuracaoMinima(disp.getDuracaoMinutosMin());
                        disponibilidade.setDuracaoMaxima(disp.getDuracaoMinutosMax());
                    }
                }

                disponibilidade.setHorarios(new ArrayList<>());
                HashMap<String, String> controleSemanal = new HashMap<>();
                Set<Date> controleDisponibilidadeInicio = new HashSet<>();
                Set<Date> controleDisponibilidadeFim = new HashSet<>();
                List<Agendamento> agendamentos = agendamentoDao.findByParam(ctx,
                        "select obj from Agendamento obj where (obj.migradoDisponibilidade = null or obj.migradoDisponibilidade = false)" +
                                " and obj.disponibilidade = true and obj.horarioDisponibilidade = null and tipoEvento.codigo = " + disp.getCodigo(), new HashMap<>());
                agendamentosMigrados.addAll(agendamentos);
                for (Agendamento agDisp : agendamentos) {

                    HorarioDisponibilidade horarioDisponibilidadePai = new HorarioDisponibilidade();
                    horarioDisponibilidadePai.setDisponibilidade(disponibilidade);
                    horarioDisponibilidadePai.setCodigoAgendamentoOriginou(agDisp.getCodigo());
                    controleDisponibilidadeInicio.add(agDisp.getInicio());
                    controleDisponibilidadeFim.add(agDisp.getFim());
                    horarioDisponibilidadePai.setDisponibilidade(disponibilidade);
                    horarioDisponibilidadePai.setAmbiente(null);
                    horarioDisponibilidadePai.setProfessor(agDisp.getProfessor());
                    horarioDisponibilidadePai.setAtivo(disp.getAtivo());
                    horarioDisponibilidadePai.setDiaSemana(agDisp.getCodigoDiaSemana());
                    horarioDisponibilidadePai.setHoraInicial(Calendario.getHora(agDisp.getInicio(), "HH:mm"));
                    horarioDisponibilidadePai.setHoraFinal(Calendario.getHora(agDisp.getFim(), "HH:mm"));
                    horarioDisponibilidadePai.setPermieAgendarAppTreino(disp.getPermitirApp());
                    horarioDisponibilidadePai.setApenasAlunosCarteira(disp.getApenasAlunosCarteira());
                    String itemControladorPai = horarioDisponibilidadePai.getProfessor().getCodigo()
                            + ";" + horarioDisponibilidadePai.getHoraInicial()
                            + ";" + horarioDisponibilidadePai.getHoraFinal()
                            + ";" + horarioDisponibilidadePai.getDiaSemana();
                    if (controleSemanal.isEmpty() ||
                            controleSemanal.get(itemControladorPai) == null ||
                            !controleSemanal.get(itemControladorPai).equals(horarioDisponibilidadePai.getDiaSemana())) {
                        controleSemanal.put(itemControladorPai, horarioDisponibilidadePai.getDiaSemana());
                        disponibilidade.getHorarios().add(horarioDisponibilidadePai);
                    }
                    agDisp.setMigradoDisponibilidade(true);
                }
                Set<String> diasSemanaUnico = new HashSet<>();
                controleSemanal.keySet().forEach(key -> {
                    diasSemanaUnico.add(controleSemanal.get(key));
                });
                AtomicReference<String> diasSemana = new AtomicReference<>("");
                diasSemanaUnico.forEach(dia -> {
                    diasSemana.set(diasSemana + "," + dia);
                });
                disponibilidade.setDiasSemana(diasSemana.get().replaceFirst(",", ""));

                if (!disponibilidade.getHorarios().isEmpty()) {
                    disponibilidade.setInicio(controleDisponibilidadeInicio.stream().sorted().iterator().next());
                    disponibilidade.setFim(controleDisponibilidadeFim.stream().sorted().skip(controleDisponibilidadeFim.size() - 1).iterator().next());
                    disponibilidadeDao.insert(ctx, disponibilidade);
                    disponibilidadesGeradas.add(disponibilidade);
                }
            }

            AtomicReference<String> codigoAgendamentos = new AtomicReference<>("");
            agendamentosMigrados.forEach(a ->{
                codigoAgendamentos.set(codigoAgendamentos + "," + a.getCodigo());
            });
            agendamentoDao.executeNativeSQL(ctx, "update agendamento set migradoDisponibilidade = true where codigo in ("+codigoAgendamentos.get().replaceFirst(",", "")+")");

            for (Disponibilidade d : disponibilidadesGeradas) {
                for (HorarioDisponibilidade h : d.getHorarios()) {
                    agendamentoDao.executeNativeSQL(ctx,
                            "update agendamento set tipoEvento_codigo = null, horarioDisponibilidade_codigo = "
                                    + h.getCodigo() + " where (codigo = " + h.getCodigoAgendamentoOriginou() + " or nsu = " + h.getCodigoAgendamentoOriginou() + ")");
                }
            }
            return "OK";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex.getMessage());
        }
    }
}
