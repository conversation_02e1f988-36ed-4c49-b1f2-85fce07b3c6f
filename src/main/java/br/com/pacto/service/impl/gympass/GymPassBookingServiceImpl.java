/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.gympass;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.aula.TipoAulaCheiaOrigemEnum;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.configuracoes.TimeZoneEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gympass.ConfigGymPass;
import br.com.pacto.bean.gympass.HorarioGymPass;
import br.com.pacto.controller.json.agendamento.FiltroTurmaDTO;
import br.com.pacto.dao.intf.gympass.LogGymPassDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.gympass.dto.*;
import br.com.pacto.service.impl.gympass.json.GymPassBookingZWJSON;
import br.com.pacto.service.impl.gympass.json.HorarioTurmaGymPassJSON;
import br.com.pacto.service.impl.gympass.json.SlotDiaDTO;
import br.com.pacto.service.impl.gympass.json.TurmaGymPassJSON;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gympass.ConfigGymPassService;
import br.com.pacto.service.intf.gympass.GymPassBookingService;
import br.com.pacto.service.intf.gympass.HorarioGymPassService;
import br.com.pacto.service.intf.gympass.LogGymPassService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.json.TurmaAulaCheiaJSON;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.*;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 22/03/2020
 */
@Service
@Qualifier(value = "gymPassBookingService")
public class GymPassBookingServiceImpl implements GymPassBookingService {

    @Autowired
    private ConfigGymPassService configGymPassService;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private HorarioGymPassService horarioGymPassService;
    @Autowired
    private LogGymPassService logGymPassService;
    @Autowired
    private LogGymPassDao logGymPassDao;
    @Autowired
    private AgendaService agendaService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private SessaoService sessaoService;

    public TurmaGymPassJSON sincronizarTurma(String ctx, Empresa empresa, TurmaGymPassJSON turmaGymPassJSON) throws ServiceException {
        IntegracaoGymPassBooking gymPassService = null;
        String msgRetorno = "";
        try {

            ConfigGymPass configGymPass = configGymPassService.obterPorEmpresa(ctx, empresa);
            configGymPassService.validarConfiguracaoGympass(configGymPass);

            if (UteisValidacao.emptyNumber(turmaGymPassJSON.getProdutoGymPass())) {
                throw new Exception("Produto Gympass não informado.");
            }

            ClassesDTO classesDTO = new ClassesDTO();
            classesDTO.setName(turmaGymPassJSON.getNome());
            classesDTO.setDescription(turmaGymPassJSON.getDescricao());
            classesDTO.setNotes(turmaGymPassJSON.getObservacao());
            classesDTO.setBookable(turmaGymPassJSON.isAtivo());
            classesDTO.setVisible(turmaGymPassJSON.isAtivo());

            //Enviar o "ZW-B-" no inicio para servir como identificador que foi enviado pelo ZIllyonWeb
            classesDTO.setReference("ZW-B-" + turmaGymPassJSON.getCodigo().toString());
            classesDTO.setProduct_id(turmaGymPassJSON.getProdutoGymPass());

            if (!UteisValidacao.emptyNumber(turmaGymPassJSON.getIdClasseGymPass())) {
                classesDTO.setId(turmaGymPassJSON.getIdClasseGymPass());
            }

            gymPassService = new IntegracaoGymPassBooking(configGymPass);

            if (UteisValidacao.emptyNumber(classesDTO.getId())) {
                gymPassService.createClasse(classesDTO);
            } else {
                try {
                    gymPassService.updateClasse(classesDTO);
                } catch (Exception e){
                    if(e.getMessage().contains("gymclass.not.found")){
                        classesDTO.setId(0);
                        gymPassService.createClasse(classesDTO);
                    } else{
                        throw e;
                    }
                }

            }

            turmaGymPassJSON.setIdClasseGymPass(classesDTO.getId());

            msgRetorno = ("Turma " + turmaGymPassJSON.getCodigo() + " | idClass " + turmaGymPassJSON.getIdClasseGymPass());
            return turmaGymPassJSON;
        } catch (Exception ex) {
            msgRetorno = ex.getMessage();
            ex.printStackTrace();
            throw new ServiceException("erroSincronizarGymPass", ex);
        } finally {
            gymPassService = null;
            logGymPassService.incluir(ctx, turmaGymPassJSON.getCodigo().toString(), msgRetorno, turmaGymPassJSON.toJSON());
        }
    }

    public void sincronizarHorarios(String ctx, Date inicioSinc, Empresa empresa, TurmaGymPassJSON turmaGymPassJSON) throws ServiceException {
        IntegracaoGymPassBooking gymPassService = null;
        try {

            ConfigGymPass configGymPass = configGymPassService.obterPorEmpresa(ctx, empresa);
            configGymPassService.validarConfiguracaoGympass(configGymPass);

            if (UteisValidacao.emptyNumber(turmaGymPassJSON.getProdutoGymPass())) {
                throw new Exception("Produto Gympass não informado.");
            }

            ConfiguracaoSistema cfgLimiteMarcar = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.MINUTOS_AGENDAR_COM_ANTECEDENCIA);
            ConfiguracaoSistema cfgLimiteDesmarcar = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.MINUTOS_DESMARCAR_COM_ANTECEDENCIA);
            int minutosAgendarAntecendencia = cfgLimiteMarcar.getValorAsInteger();
            int minutosDesmarcar = cfgLimiteDesmarcar.getValorAsInteger();

            gymPassService = new IntegracaoGymPassBooking(configGymPass);

            boolean houveErro = false;
            StringBuilder msgErro = new StringBuilder();

            for (HorarioTurmaGymPassJSON horarioJSON : turmaGymPassJSON.getHorarios()) {
                try {

                    //não alterar os que já passaram
                    if (Calendario.menorComHora(horarioJSON.getInicioDate(), Calendario.hoje())) {
                        Uteis.logarDebug( "Horário já passou.. não alterar.. ");
                        continue;
                    }

                    SlotsDTO slotsDTO = new SlotsDTO();
                    slotsDTO.setOccur_date(obterDataFormatoIntegracao(empresa, horarioJSON.getInicioDate())); //horario da aula
                    slotsDTO.setRoom(horarioJSON.getAmbiente());
                    slotsDTO.setStatus(horarioJSON.isAtivo() ? 1 : 0);
                    slotsDTO.setLength_in_minutes(horarioJSON.getDuracaoMinutos());
                    slotsDTO.setTotal_capacity(horarioJSON.getQtdTotal());
                    slotsDTO.setTotal_booked(horarioJSON.getQtdReservado());

                    BookingWindowDTO bookingWindowDTO = new BookingWindowDTO();

                    if (!UteisValidacao.emptyNumber(minutosAgendarAntecendencia) && minutosAgendarAntecendencia > 0) {
                        bookingWindowDTO.setOpens_at(obterDataFormatoIntegracao(empresa, Uteis.somarCampoData(horarioJSON.getInicioDate(), Calendar.MINUTE, -minutosAgendarAntecendencia))); //horário disponível para marcar
                    }

//                    bookingWindowDTO.setCloses_at(obterDataFormatoIntegracao(empresa, Uteis.somarCampoData(horarioJSON.getInicioDate(), Calendar.MINUTE, -minutosDesmarcar))); //horário disponível para desmarcar
                    slotsDTO.setBooking_window(bookingWindowDTO);

                    slotsDTO.setCancellable_until(obterDataFormatoIntegracao(empresa, Uteis.somarCampoData(horarioJSON.getInicioDate(), Calendar.MINUTE, -minutosDesmarcar))); //horário disponível para desmarcar

                    slotsDTO.setInstructors(new ArrayList<>());

                    InstructorsDTO professor = new InstructorsDTO();
                    professor.setName(horarioJSON.getProfessor());
                    professor.setSubstitute(false);
                    slotsDTO.getInstructors().add(professor);
                    if (!UteisValidacao.emptyString(horarioJSON.getProfessorSubstituto())) {
                        InstructorsDTO professorSubstituto = new InstructorsDTO();
                        professorSubstituto.setName(horarioJSON.getProfessor());
                        professorSubstituto.setSubstitute(true);
                        slotsDTO.getInstructors().add(professorSubstituto);
                    }

                    slotsDTO.setRating(null);
                    slotsDTO.setVirtual(turmaGymPassJSON.getUrlTurmaVirtual() != null && turmaGymPassJSON.getUrlTurmaVirtual().startsWith("http"));
                    slotsDTO.setProduct_id(turmaGymPassJSON.getProdutoGymPass());

                    if (UteisValidacao.emptyNumber(slotsDTO.getId())) {
                        slotsDTO = gymPassService.createSlots(turmaGymPassJSON.getIdClasseGymPass(), slotsDTO);
                    } else {
                        slotsDTO = gymPassService.updateSlotsPatch(turmaGymPassJSON.getIdClasseGymPass(), slotsDTO);
                    }

                    horarioJSON.setIdSlotGymPass(slotsDTO.getId());
                    salvarHorarioTurmaGympass(ctx, turmaGymPassJSON.getCodigo(), turmaGymPassJSON.getIdClasseGymPass(), horarioJSON);

                } catch (Exception ex) {
                    String msg = ("Erro Horário:" + horarioJSON.getIdReferencia() + " | " + ex.getMessage());
                    logGymPassService.incluir(ctx, turmaGymPassJSON.getCodigo().toString(), msg, horarioJSON.toJSON());
                    houveErro = true;
                    msgErro.append(msg).append(" \n");
                }
            }

            verificarExcluidos(ctx, inicioSinc, empresa, turmaGymPassJSON, gymPassService);

            if (houveErro) {
                throw new Exception(msgErro.toString());
            }
        } catch (Exception ex) {
            logGymPassService.incluir(ctx, turmaGymPassJSON.getCodigo().toString(), "Sincronizar Horario | Erro: " + ex.getMessage(), turmaGymPassJSON.toJSON());
            ex.printStackTrace();
        } finally {
            gymPassService = null;
        }
    }

    private void verificarExcluidos(String ctx, Date inicioSinc, Empresa empresa, TurmaGymPassJSON turmaGymPassJSON, IntegracaoGymPassBooking gymPassService) {
        try {

            String inicio = obterDataFormatoIntegracao(empresa, inicioSinc);
            String fim = obterDataFormatoIntegracao(empresa, Uteis.somarDias(inicioSinc, 15));

            List<SlotsDTO> slotsDTOS = gymPassService.obterSlots(turmaGymPassJSON.getIdClasseGymPass(), inicio, fim);

            List<SlotsDTO> slotsExcluir = new ArrayList<>();

            for (SlotsDTO slotsGymPass : slotsDTOS) {
                boolean excluir = true;
                for (HorarioTurmaGymPassJSON atual : turmaGymPassJSON.getHorarios()) {
                    if (atual.getIdSlotGymPass().equals(slotsGymPass.getId())) {
                        excluir = false;
                        break;
                    }
                }

                if (excluir) {
                    slotsExcluir.add(slotsGymPass);
                }
            }

            if (!UteisValidacao.emptyList(slotsExcluir)) {
                for (SlotsDTO slotsDTO : slotsExcluir) {
                    excluirSlotGymPass(ctx, turmaGymPassJSON.getIdClasseGymPass(), turmaGymPassJSON.getCodigo().toString(), slotsDTO, gymPassService);
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void excluirSlotGymPass(String ctx, Integer classID, String turmaId, SlotsDTO slotsDTO, IntegracaoGymPassBooking gymPassService) throws ServiceException {

        String msg = "Excluir... ClassId " + classID + " | TurmaId " + turmaId + " | SlotId " + slotsDTO.getId() + " | Horario " + slotsDTO.getOccur_date();
        try {
            Uteis.logarDebug( msg);
            gymPassService.deleteSlots(classID, slotsDTO);
            logGymPassDao.executeNative(ctx, "update horariogympass set ativo = false where idslotgympass = "+ slotsDTO.getId()+" and idclassgympass = "+ classID);
            msg += " | Sucesso!";
        } catch (Exception ex) {
            msg += " | Erro " + ex.getMessage();
            ex.getMessage();
        } finally {
            logGymPassService.incluir(ctx, turmaId, msg, "");
        }
    }

    private void salvarHorarioTurmaGympass(String ctx, Integer turmaId, Integer IdClasseGymPass, HorarioTurmaGymPassJSON horarioJSON) throws Exception {

//        HorarioGymPass horarioGymPass = horarioGymPassService.obterPorIdReferencia(ctx, turmaId, horarioJSON.getIdReferencia());

        HorarioGymPass horarioGymPass = new HorarioGymPass();
        horarioGymPass.setAtivo(true);
        horarioGymPass.setIdTurma(turmaId);
        horarioGymPass.setIdClassGymPass(IdClasseGymPass);
        horarioGymPass.setCodigoHorario(horarioJSON.getCodigo());
        horarioGymPass.setDia(horarioJSON.getDia());
        horarioGymPass.setInicioHorario(horarioJSON.getInicioDate());
        horarioGymPass.setIdReferencia(horarioJSON.getIdReferencia());
        horarioGymPass.setIdSlotGymPass(horarioJSON.getIdSlotGymPass());
        horarioGymPassService.incluir(ctx, horarioGymPass);

//        if (UteisValidacao.emptyNumber(horarioGymPass.getCodigo())) {
//            horarioGymPassService.incluir(ctx, horarioGymPass);
//        } else {
//            horarioGymPassService.alterar(ctx, horarioGymPass);
//        }
    }

    private String obterDataFormatoIntegracao(Empresa empresa, Date dataConverter) {
        TimeZoneEnum timeZoneEnum = TimeZoneEnum.obterPorId(empresa.getTimeZoneDefault());
        String gmt = timeZoneEnum.getTimeZoneGmt().replace("GMT-", "");
        String retorno = Uteis.getDataAplicandoFormatacao(dataConverter, "yyyy-MM-dd'T'HH:mm:ss");
        retorno += "-0" + gmt + ":00";
        return retorno;
    }

    public String healthGympass(String ctx, Integer empresaTR) throws ServiceException {
        IntegracaoGymPassBooking gymPassService = null;
        try {
            ConfigGymPass configGymPass = configGymPassService.obterPorEmpresaTR(ctx, empresaTR);
            configGymPassService.validarConfiguracaoGympass(configGymPass);
            gymPassService = new IntegracaoGymPassBooking(configGymPass);
            return gymPassService.healthCheck();
        } catch (Exception ex) {
            throw new ServiceException("healthGympass", ex);
        } finally {
            gymPassService = null;
        }
    }

    public void enviarBooking(BookingsDTO bookingsDTO, String booking_number, ConfigGymPass configGymPass) throws Exception {
        IntegracaoGymPassBooking gymPassService = null;
        try {
            gymPassService = new IntegracaoGymPassBooking(configGymPass);
            gymPassService.booking(bookingsDTO, booking_number);

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            gymPassService = null;
        }
    }

    public String excluirHorarios(String ctx, Empresa empresa, boolean excluirTodas, TurmaGymPassJSON turmaGymPassJSON) throws ServiceException {
        IntegracaoGymPassBooking gymPassService = null;
        StringBuilder retorno = new StringBuilder();
        try {

            ConfigGymPass configGymPass = configGymPassService.obterPorEmpresa(ctx, empresa);
            configGymPassService.validarConfiguracaoGympass(configGymPass);
            gymPassService = new IntegracaoGymPassBooking(configGymPass);

            List<ClassesDTO> classesDTOS = new ArrayList<>();
            if (excluirTodas) {
                classesDTOS = gymPassService.obterClasse();
            } else if (turmaGymPassJSON != null && !UteisValidacao.emptyNumber(turmaGymPassJSON.getIdClasseGymPass())) {
                ClassesDTO classesDTO = gymPassService.obterClasse(turmaGymPassJSON.getIdClasseGymPass());
                classesDTOS.add(classesDTO);
            }

            for (ClassesDTO classesDTO : classesDTOS) {
                try {

                    if (!classesDTO.getReference().startsWith("ZW-B-")) {
                        Uteis.logarDebug( "Vou ignorar não é do ZW | ClassId " + classesDTO.getId() + " | Referente " + classesDTO.getReference());
                        continue;
                    }

                    String inicio = obterDataFormatoIntegracao(empresa, Calendario.hoje());
                    String fim = obterDataFormatoIntegracao(empresa, Uteis.somarDias(Calendario.hoje(), 15));

                    List<SlotsDTO> slotsDTOS = gymPassService.obterSlots(classesDTO.getId(), inicio, fim);
                    for (SlotsDTO slotsDTO : slotsDTOS) {
                        excluirSlotGymPass(ctx, classesDTO.getId(), classesDTO.getReference(), slotsDTO, gymPassService);
                    }
                } catch (Exception ex) {
                    retorno.append("Class | ").append(classesDTO.getId()).append(" | Erro | ").append(ex.getMessage());
                    ex.getMessage();
                }
            }
        } catch (Exception ex) {
            logGymPassService.incluir(ctx, null, "Excluir Todos | " + ex.getMessage(), turmaGymPassJSON.toJSON());
            throw new ServiceException("excluirHorarios", ex);
        } finally {
            gymPassService = null;
        }

        if (UteisValidacao.emptyString(retorno.toString())) {
            retorno.append("ok");
        }
        return retorno.toString();
    }

    public String excluirHorarioEspecifico(String ctx, Empresa empresa, TurmaGymPassJSON turmaGymPassJSON, Integer codigoHorarioTurmaExcluir, Integer codigoTurma) throws ServiceException {
        IntegracaoGymPassBooking gymPassService = null;
        StringBuilder retorno = new StringBuilder();
        try {
            ConfigGymPass configGymPass = configGymPassService.obterPorEmpresa(ctx, empresa);
            configGymPassService.validarConfiguracaoGympass(configGymPass);
            gymPassService = new IntegracaoGymPassBooking(configGymPass);

            ClassesDTO classesDTO = gymPassService.obterClasse(turmaGymPassJSON.getIdClasseGymPass());

            List<Integer> idSlots = consultarIdSlotsGympassFuturosPorCodHorario(ctx, codigoHorarioTurmaExcluir, codigoTurma);

            if (!idSlots.isEmpty()) {
                try {
                    String inicio = obterDataFormatoIntegracao(empresa, Calendario.hoje());
                    String fim = obterDataFormatoIntegracao(empresa, Uteis.somarDias(Calendario.hoje(), 15));

                    List<SlotsDTO> slotsDTOS = gymPassService.obterSlots(classesDTO.getId(), inicio, fim);
                    for (SlotsDTO slotsDTO : slotsDTOS) {
                        if (idSlots.contains(slotsDTO.getId())) {
                            excluirSlotGymPass(ctx, classesDTO.getId(), classesDTO.getReference(), slotsDTO, gymPassService);
                        }
                    }
                } catch (Exception ex) {
                    retorno.append("Class | ").append(classesDTO.getId()).append(" | Erro | ").append(ex.getMessage());
                    ex.getMessage();
                }
            }
        } catch (Exception ex) {
            logGymPassService.incluir(ctx, null, "Excluir Todos | " + ex.getMessage(), turmaGymPassJSON.toJSON());
            throw new ServiceException("excluirHorarios", ex);
        } finally {
            gymPassService = null;
        }

        if ( UteisValidacao.emptyString(retorno.toString()) ) {
            retorno.append("ok");
        }

        return retorno.toString();

    }


    public List<ClassesShowDTO> aulas(String ctx, Integer empresaZW) throws Exception {

        Empresa empresa = empresaService.obterPorIdZW(ctx, empresaZW);

        IntegracaoGymPassBooking gymPassService = null;
        StringBuilder retorno = new StringBuilder();

        ConfigGymPass configGymPass = configGymPassService.obterPorEmpresa(ctx, empresa);
        configGymPassService.validarConfiguracaoGympass(configGymPass);
        gymPassService = new IntegracaoGymPassBooking(configGymPass);
        List<ClassesDTO> classesDTOS = gymPassService.obterClasse();
        return new ArrayList(){{
            for(ClassesDTO c : classesDTOS){
                if(!c.getReference().toUpperCase().contains("INATIVA")){
                    add(new ClassesShowDTO(c));
                }

            }
        }};
    }

    public Map<String, List<SlotsDTO>> horarios(String ctx, Integer empresaZW, String dia, Integer idClasse) throws Exception {
        Map<String, List<SlotsDTO>> slots = new HashMap<>();

        Empresa empresa = empresaService.obterPorIdZW(ctx, empresaZW);

        IntegracaoGymPassBooking gymPassService = null;

        ConfigGymPass configGymPass = configGymPassService.obterPorEmpresa(ctx, empresa);
        configGymPassService.validarConfiguracaoGympass(configGymPass);
        gymPassService = new IntegracaoGymPassBooking(configGymPass);
        String diaparam = obterDataFormatoIntegracao(empresa, Uteis.getDate(dia, "ddMMyyyy"));
        String diafimparam = obterDataFormatoIntegracao(empresa, Uteis.somarDias(Uteis.getDate(dia, "ddMMyyyy"), 1));
        List<ClassesDTO> classesDTOS = gymPassService.obterClasse();

        for(ClassesDTO classesDTO : classesDTOS){
            if(idClasse == null || classesDTO.getReference().contains(idClasse.toString())){
                List<SlotsDTO> slotsDTOS = gymPassService.obterSlots(classesDTO.getId(), diaparam, diafimparam);
                if(!UteisValidacao.emptyList(slotsDTOS)){

                    for(SlotsDTO slot : slotsDTOS){
                        Date ocorrencia = Uteis.getDate(slot.getOccur_date().replace("T", " ").replace("Z[UTC]", ""),
                                "yyyy-MM-dd HH:mm:ss");
                        slot.setOccur_date(Uteis.getDataAplicandoFormatacao(Uteis.somarCampoData(ocorrencia,
                                Calendar.HOUR,
                                -3),
                                "dd/MM/yyyy HH:mm"));
                    }

                    slots.put(classesDTO.getName()+"_"+classesDTO.getSlug()+"_"+classesDTO.getReference() + ("(id:"+classesDTO.getId()+")"), slotsDTOS);
                }
            }
        }

        return slots;
    }


    public List<ClassesShowDTO> inativarUmaAula(String ctx, Integer empresacod, String reference, Integer exceto) throws Exception{
        Empresa empresa = empresaService.obterPorIdZW(ctx, empresacod);
        IntegracaoGymPassBooking gymPassService = null;
        ConfigGymPass configGymPass = configGymPassService.obterPorEmpresa(ctx, empresa);
        configGymPassService.validarConfiguracaoGympass(configGymPass);
        gymPassService = new IntegracaoGymPassBooking(configGymPass);

        String inicio = obterDataFormatoIntegracao(empresa, Calendario.hoje());
        String fim = obterDataFormatoIntegracao(empresa, Uteis.somarDias(Calendario.hoje(), 15));

        List<ClassesDTO> classesDTOS = gymPassService.obterClasse();

        for(ClassesDTO classesDTO : classesDTOS){
            if(classesDTO.getReference().equals(reference) && classesDTO.getId() != exceto){
                List<SlotsDTO> slotsDTOS = gymPassService.obterSlots(classesDTO.getId(), inicio, fim);
                for (SlotsDTO slotsDTO : slotsDTOS) {
                    excluirSlotGymPass(ctx, classesDTO.getId(), classesDTO.getReference(), slotsDTO, gymPassService);
                }
                classesDTO.setReference(classesDTO.getReference().concat("-INATIVA"));
                classesDTO.setBookable(false);
                classesDTO.setVisible(false);
                gymPassService.updateClasse(classesDTO);
            }
        }
        List<ClassesDTO> classesDTOSPosEDicao = gymPassService.obterClasse();
        return new ArrayList(){{
            for(ClassesDTO c : classesDTOSPosEDicao){
                add(new ClassesShowDTO(c));
            }
        }};
    }

    public void normalizar(String ctx, Integer empresacod) throws Exception{
        Empresa empresa = empresaService.obterPorIdZW(ctx, empresacod);
        IntegracaoGymPassBooking gymPassService = null;
        ConfigGymPass configGymPass = configGymPassService.obterPorEmpresa(ctx, empresa);
        configGymPassService.validarConfiguracaoGympass(configGymPass);
        gymPassService = new IntegracaoGymPassBooking(configGymPass);

        GymPassBookingZWJSON jsonEnvio = new GymPassBookingZWJSON();
        jsonEnvio.setOperacao("turmas-id-classes");
        jsonEnvio.setChave(ctx);
        jsonEnvio.setEmpresaZw(configGymPass.getEmpresa().getCodZW());

        final String urlZW = Aplicacao.getPropOAMD(ctx, Aplicacao.urlZillyonWeb);
        StringEntity entity = new StringEntity(new JSONObject(jsonEnvio).toString(), "UTF-8");
        HttpPost httpPost = new HttpPost(urlZW + "/prest/gympassbooking");
        httpPost.setEntity(entity);
        httpPost.setHeader("Content-Type", "application/json");
        HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpResponse response = client.execute(httpPost);

        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = EntityUtils.toString(response.getEntity());

        JSONObject jsonRetorno = new JSONObject(responseBody);
        JSONArray jsonArray = new JSONArray(jsonRetorno.getString("msg"));
        Map<Integer, ClassesDTO> mapaAulasGympass = new HashMap<>();
        List<ClassesDTO> classesDTOS = gymPassService.obterClasse();
        AulaService aulaService = UtilContext.getBean(AulaService.class);
        for(int i = 0; i < jsonArray.length(); i++){
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String reference = "ZW-B-" + jsonObject.getInt("aula");
            Integer exceto = jsonObject.getInt("idclasse");

            for(ClassesDTO classesDTO : classesDTOS){

                if(classesDTO.getReference().equals(reference) && classesDTO.getId() == exceto){
                    mapaAulasGympass.put(jsonObject.getInt("aula"), classesDTO);
                }

                if(classesDTO.getReference().equals(reference) && classesDTO.getId() != exceto){
                    classesDTO.setReference(classesDTO.getReference().concat("-INATIVA"));
                    classesDTO.setBookable(false);
                    classesDTO.setVisible(false);
                    gymPassService.updateClasse(classesDTO);
                }
            }
        }

        JSONObject filtros = new JSONObject().put("vigencia", new String[]{"VIGENTE", "NAO_VIGENTE"});
        List<TurmaAulaCheiaJSON> turmaAulaCheiaJSONS = aulaService.obterAulasColetivas(ctx, null, empresacod, null, filtros);
        for(TurmaAulaCheiaJSON turma : turmaAulaCheiaJSONS){
            ClassesDTO classesDTO = mapaAulasGympass.get(turma.getCodigo());
            if(classesDTO == null){
                continue;
            }
            classesDTO.setDescription(turma.getNome());
            if(Calendario.igual(Calendario.hoje(), turma.getDataInicio()) ||
                    Calendario.igual(Calendario.hoje(), turma.getDataFim()) ||
                    Calendario.entre(Calendario.hoje(), turma.getDataInicio(), turma.getDataFim())){
                classesDTO.setBookable(true);
                classesDTO.setVisible(true);
            }else{
                classesDTO.setBookable(false);
                classesDTO.setVisible(false);
            }
            gymPassService.updateClasse(classesDTO);
        }
    }

    public void excluirSlotsDeUmDia(String ctx, Integer empresaid, Date dia, Integer turmaId) {
        try {
            Empresa empresa = null;
            if (!UteisValidacao.emptyNumber(empresaid)) {
                empresa = empresaService.obterPorIdZW(ctx, empresaid);
            }

            if (empresa == null) {
                throw new Exception("Não foi possível obter empresa");
            }
            FiltroTurmaDTO filtro = new FiltroTurmaDTO(null);
            filtro.setTipo(TipoAulaCheiaOrigemEnum.TODAS);
            filtro.setTurmaId(turmaId);

            TurmaGymPassJSON turmaGymPassJSON = agendaService.obterDadosTurmaTurmaGymPassJSON(ctx, filtro.getTurmaId(), null);
            if (turmaGymPassJSON == null) {
                throw new Exception("Turma não encontrada");
            }

            String inicio = obterDataFormatoIntegracao(empresa, dia);
            String fim = obterDataFormatoIntegracao(empresa, Calendario.getDataComHora(dia, "23:59:59"));
            ConfigGymPassService configGymPassService = UtilContext.getBean(ConfigGymPassService.class);
            ConfigGymPass configGymPass = configGymPassService.obterPorEmpresaZW(ctx, empresaid);
            IntegracaoGymPassBooking gymPassService = new IntegracaoGymPassBooking(configGymPass);
            List<SlotsDTO> slotsDTOS = gymPassService.obterSlots(turmaGymPassJSON.getIdClasseGymPass(), inicio, fim);
            for (SlotsDTO slotsGymPass : slotsDTOS) {
                excluirSlotGymPass(ctx, turmaGymPassJSON.getIdClasseGymPass(), turmaGymPassJSON.getCodigo().toString(), slotsGymPass, gymPassService);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
    public void excluirSlots(String ctx, Integer empresaid, Integer slot, Integer idClass) {
        try {
            Empresa empresa = null;
            if (!UteisValidacao.emptyNumber(empresaid)) {
                empresa = empresaService.obterPorIdZW(ctx, empresaid);
            }

            if (empresa == null) {
                throw new Exception("Não foi possível obter empresa");
            }
            ConfigGymPassService configGymPassService = UtilContext.getBean(ConfigGymPassService.class);
            ConfigGymPass configGymPass = configGymPassService.obterPorEmpresaZW(ctx, empresaid);
            IntegracaoGymPassBooking gymPassService = new IntegracaoGymPassBooking(configGymPass);
            gymPassService.deleteSlotID(idClass, slot);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public List<ProductDTO> listarProdutos(Integer empresaId) throws Exception {
        List<ProductDTO> products = new ArrayList<>();
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            if (!Uteis.valorVazioString(ctx)) {
                Empresa empresa = empresaService.obterPorIdZW(ctx, empresaId);
                IntegracaoGymPassBooking gymPassService = null;
                ConfigGymPass configGymPass = configGymPassService.obterPorEmpresa(ctx, empresa);
                configGymPassService.validarConfiguracaoGympass(configGymPass);

                gymPassService = new IntegracaoGymPassBooking(configGymPass);
                products = gymPassService.listarProdutos();
            }

            return products;
        } catch (Exception ex) {
            Uteis.logar(ex, GymPassBookingServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
    }

    public List<SlotDiaDTO> consultarSlotsGympass(String chave, Date inicio, Date fim) throws Exception {
        List<SlotDiaDTO> slots = new ArrayList<>();
        try {
            try (ResultSet rs = logGymPassDao.createStatement(chave, "select codigohorario, " +
                    "idclassgympass, dia, idreferencia, idslotgympass, idturma, idslotgympass, iniciohorario, ativo" +
                    " from horariogympass h where dia between '"
                    + Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd") + "' and '"
                    + Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd") + "'")) {
                while (rs.next()) {
                    SlotDiaDTO slot = new SlotDiaDTO();
                    slot.setSlot(rs.getInt("idslotgympass"));
                    slot.setClasse(rs.getInt("idclassgympass"));
                    slot.setTurma(rs.getInt("idturma"));
                    slot.setHorario(rs.getInt("codigohorario"));
                    slot.setDia(Uteis.getDataAplicandoFormatacao(rs.getDate("dia"), "dd/MM/yyyy"));
                    slot.setInicio(Uteis.getDataAplicandoFormatacao(rs.getTime("iniciohorario"), "HH:mm"));
                    slot.setAtivo(rs.getBoolean("ativo"));
                    slot.setReferencia(rs.getString("idreferencia"));
                    slots.add(slot);
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, GymPassBookingServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }
        return slots;
    }

    public List<Integer> consultarIdSlotsGympassFuturosPorCodHorario(String chave, Integer codigoHorarioTurma, Integer codigoTurma) throws Exception {
        List<Integer> idSlots = new ArrayList<>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select distinct(h.idslotgympass) \n");
            sql.append("from horariogympass h  \n");
            sql.append("where h.codigohorario = ").append(codigoHorarioTurma).append(" \n");
            sql.append("and h.idturma = ").append(codigoTurma).append(" \n");
            sql.append("and h.iniciohorario > current_date \n");

            try (ResultSet rs = logGymPassDao.createStatement(chave, sql.toString())) {
                while (rs.next()) {
                    idSlots.add(rs.getInt("idslotgympass"));
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, GymPassBookingServiceImpl.class);
            throw new ServiceException(ex.getMessage());
        }

        return idSlots;
    }


}
