package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.objeto.Uteis;

import java.math.BigDecimal;

/**
 * Created by Glauco
 */
public class ProtocoloYuhasz {

    public static AvaliacaoFisica calculaDados(AvaliacaoFisica avaliacao) {
        if (!isCalcularDados(avaliacao)) {
            return avaliacao;
        }

        avaliacao.setTotalDobras(Uteis.forcarCasasDecimais(2, new BigDecimal(somatorio(avaliacao.getSubescapular(), avaliacao.getTriceps(), avaliacao.getCoxaMedial(), avaliacao.getSupraIliaca(), avaliacao.getAbdominal(), avaliacao.getPeitoral()))));

        Double percGordura = percentualGorduraYuhasz(avaliacao.getTotalDobras());
        percGordura = percGordura < 0.0 ? 0.0 : percGordura;
        Double massaGorda = pesoGorduraYuhasz(avaliacao.getPeso(), percGordura);
        Double massaMagra = massaCorporalMagraYuhasz(avaliacao.getPeso(), massaGorda);


        avaliacao.setPercentualGordura(Uteis.forcarCasasDecimais(2, new BigDecimal(percGordura)));
        avaliacao.setMassaGorda(Uteis.forcarCasasDecimais(2, new BigDecimal(massaGorda)));
        avaliacao.setMassaMagra(Uteis.forcarCasasDecimais(2, new BigDecimal(massaMagra)));


        return avaliacao;
    }

    public static Double somatorio(double subescapular, double triceps, double coxaMedial, double supraIliaca, double abdominal, double peitoral) {
        return subescapular + triceps + coxaMedial + supraIliaca + abdominal + peitoral;
    }


    /**
     * Método para calcular o percentual de gordura Yuhasz
     *
     * @return Percentual de gordura
     */
    public static Double percentualGorduraYuhasz(double totalDobras) {
        if (totalDobras > 0) {
            // Percentual de gordura = (S6) x 0,095 + 3,64
            return (totalDobras) * 0.095 + 3.64;
        }
        return 0.0;
    }

    /**
     * Método para calcular o peso gordura Yuhasz
     *
     * @param peso              - Dado equivalente ao peso
     * @param percentualGordura - Dado equivalente ao Percentual de gordura
     * @return Peso gordura
     */
    public static Double pesoGorduraYuhasz(double peso, Double percentualGordura) {
        // Se peso maior que 0 e percentual de gordura possuir alguma coisa
        if ((peso > 0) && (percentualGordura != 0.0)) {
            return Uteis.calculaPesoGordura(peso, percentualGordura);
        }
        return 0.0;
    }

    /**
     * Método para calcular a Massa corporal magra Yuhasz
     *
     * @param peso        - Dado equivalente ao peso
     * @param pesoGordura - Dado equivalente ao peso da gordura
     * @return Massa Corporal Magra
     */
    public static Double massaCorporalMagraYuhasz(double peso, Double pesoGordura) {
        // Se peso maior que 0 e peso de gordura possui alguma coisa
        if ((peso > 0) && (pesoGordura != null)) {
            return Uteis.calculaMassaMagraCorporal(peso, pesoGordura);
        }
        return 0.0;
    }

    /**
     * Método para calcular o Peso Muscular Yuhasz
     *
     * @param peso         - Dado equivalente ao peso
     * @param pesoResidual - Dado equivalente ao peso residual
     * @param pesoOsseo    - Dado equivalente ao peso ósseo
     * @param pesoGordura  - Dado equivalente ao peso de gordura
     * @return Peso Muscular
     */
    public static Double pesoMuscularYuhasz(double peso, Double pesoResidual, Double pesoOsseo, Double pesoGordura) {
        // Se peso for maior que 0 e peso residual, peso ósseo e peso de gordura tiver alguma coisa
        if ((peso > 0) && (pesoResidual != null) && (pesoOsseo != null) && (pesoGordura != null)) {
            return Uteis.calculaPesoMuscular(peso, pesoGordura, pesoOsseo, pesoResidual);
        }
        return 0.0;
    }

    public static boolean isCalcularDados(AvaliacaoFisica a) {
        return a.getSubescapular() > 0.0 && a.getTriceps() > 0.0 && a.getCoxaMedial() > 0.0
                && a.getSupraIliaca() > 0.0 && a.getAbdominal() > 0.0 && a.getPeitoral() > 0.0;
    }
}
