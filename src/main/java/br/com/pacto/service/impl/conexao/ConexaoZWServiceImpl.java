package br.com.pacto.service.impl.conexao;

import br.com.pacto.base.oamd.ManyDataBasesException;
import br.com.pacto.base.oamd.OAMD;
import br.com.pacto.base.oamd.ZWDatabaseNotFoundException;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.intf.conexao.ConexaoZWService;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

@Service
public class ConexaoZWServiceImpl implements ConexaoZWService {

    private static Map<String, Map<String, Object>> empresas = new HashMap<>();
    private static Map<String, HikariDataSource> dataSourceMap = new HashMap<>();

    public ConexaoZWServiceImpl() {
        init();
    }

    public void init() {
        try {
            empresas = OAMD.buscarListaEmpresasMapeado();
        } catch (Exception e) {
            Uteis.logar(e, ConexaoZWServiceImpl.class);
        }
    }

    public static ResultSet criarConsulta(final String sql, Connection con) throws Exception {
        try (Statement stm = con.createStatement()) {
            return stm.executeQuery(sql);
        }
    }

    public static boolean existe(final String sql, Connection con) throws Exception {
        try (ResultSet tabelaResultado = ConexaoZWServiceImpl.criarConsulta(
                "select exists(" + sql + ")", con)) {
            tabelaResultado.next();
            return tabelaResultado.getBoolean(1);
        }
    }

    public static Boolean executarConsulta(final String sql, Connection con) throws Exception {
        try (Statement stm = con.createStatement()) {
            return stm.execute(sql);
        }
    }

    @Override
    public Connection conexaoZw(final String key) throws Exception {
        DataSource ds = getDataSource(key);
        return ds.getConnection();
    }

    public void closeDatasource(final String key) {
        HikariDataSource ds = dataSourceMap.remove(key);
        if (ds != null) {
            ds.close();
        }
    }

    public void limparMapaPools() {
        dataSourceMap = new HashMap<>();
    }

    public HikariDataSource createDataSource(String key) throws Exception {
        Map<String, Object> params = empresas.get(key);
        if (params == null) {
            throw new ManyDataBasesException("Parâmetros não definidos para chave: " + key);
        }

        if (SuperControle.independente(key)) {
            throw new ZWDatabaseNotFoundException(String.format("A chave %s é de um Treino Independente", key));
        }


        String nomeBD = params.get("nomeBD").toString().trim().replace("bdmusc", "bdzillyon");
        String userBD = params.get("userBD").toString().trim();
        String passwordBD = params.get("passwordBD").toString().trim();
        String url = "jdbc:postgresql://" + params.get("hostBD") + ":"
                + params.get("porta") + "/" + nomeBD + "?ApplicationName=tr_zw_pool";
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(url);
        config.setUsername(userBD);
        config.setPassword(passwordBD);
        config.setMinimumIdle(0);
        config.setConnectionTimeout(60000);
        config.setIdleTimeout(Integer.parseInt(Aplicacao.getProp(Aplicacao.tempoOciosoPool)));
        config.setMaximumPoolSize(Integer.parseInt(Aplicacao.getProp(Aplicacao.maximumPoolSize)));
        config.setMaxLifetime(60000);
        HikariDataSource dataSource = new HikariDataSource(config);
        dataSourceMap.put(key, dataSource);
        return dataSource;
    }

    public HikariDataSource getDataSource(String key) throws Exception {
        HikariDataSource dataSource = dataSourceMap.get(key);
        if (dataSource == null) {
            dataSource = createDataSource(key);
        }
        return dataSource;
    }

    public Connection conexaoDiretaZw(final String key) throws Exception {
        Map<String, Object> params = empresas.get(key);
        if (params == null) {
            throw new ManyDataBasesException("Parâmetros não definidos para chave: " + key);
        }
        String nomeBD = params.get("nomeBD").toString().trim().replace("bdmusc", "bdzillyon");
        String userBD = params.get("userBD").toString().trim();
        String passwordBD = params.get("passwordBD").toString().trim();
        String url = "jdbc:postgresql://" + params.get("hostBD") + ":"
                + params.get("porta") + "/" + nomeBD + "?ApplicationName=tr_zw";
        return DriverManager.getConnection(url, userBD, passwordBD);
    }
}
