package br.com.pacto.service.impl.benchmark;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.bean.benchmark.OrigemTipoBenchmark;
import br.com.pacto.bean.benchmark.TipoBenchmark;
import br.com.pacto.bean.benchmark.TipoBenchmarkResponseTO;
import br.com.pacto.bean.benchmark.TipoBenchmarkTO;
import br.com.pacto.controller.json.crossfit.FiltroTipoBenchmarkJSON;
import br.com.pacto.dao.intf.benchmark.TipoBenchmarkDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.notificacao.excecao.TipoBenchmarkExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.benchmark.TipoBenchmarkService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.JSFUtilities;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Rafael on 12/07/2016.
 */
@Service
public class TipoBenchmarkServiceImpl implements TipoBenchmarkService{

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private TipoBenchmarkDao tipoBenchmarkDao;
    @Autowired
    private SessaoService sessaoService;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public TipoBenchmarkDao getTipoBenchmarkDao() {
        return tipoBenchmarkDao;
    }

    public void setTipoBenchmarkDao(TipoBenchmarkDao tipoBenchmarkDao) {
        this.tipoBenchmarkDao = tipoBenchmarkDao;
    }

    public TipoBenchmark alterar(final String ctx, TipoBenchmark object) throws ServiceException {
        try {
            return getTipoBenchmarkDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, TipoBenchmark object) throws ServiceException {
        try {
            getTipoBenchmarkDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public TipoBenchmark inserir(final String ctx, TipoBenchmark object) throws ServiceException {
        try {
            return getTipoBenchmarkDao().insert(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public TipoBenchmark obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getTipoBenchmarkDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public TipoBenchmark obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getTipoBenchmarkDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<TipoBenchmark> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getTipoBenchmarkDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<TipoBenchmark> obterPorParam(final String ctx, String query,
                                         Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getTipoBenchmarkDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }
    public List<TipoBenchmark> obterTodos(final String ctx, OrigemTipoBenchmark origem) throws ServiceException {
        try {
            HashMap<String,Object> params = new HashMap<String, Object>() ;
            StringBuilder sql = new StringBuilder();


            sql.append("Select obj FROM TipoBenchmark obj\n");
            if(origem != null){
                sql.append("Where obj.origem = :origem ");
                params.put("origem",origem);
            }
            return getTipoBenchmarkDao().findByParam(ctx, sql.toString(),params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<TipoBenchmarkResponseTO> consultarTipoBenchmark(FiltroTipoBenchmarkJSON filtros, PaginadorDTO paginadorDTO)throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if (!StringUtils.isBlank(paginadorDTO.getSort())) {
            paginadorDTO.setSort(paginadorDTO.getSort());
        } else {
            paginadorDTO.setSort("nome,ASC");        }

        List<TipoBenchmarkResponseTO> lista = getTipoBenchmarkDao().consultarTodos(ctx, filtros, paginadorDTO);
        return lista;
    }

    public List<TipoBenchmarkResponseTO> consultarTodos() throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            List<TipoBenchmark> tipoBenchmarks = getTipoBenchmarkDao().findAll(ctx);
            List<TipoBenchmarkResponseTO> listReturn = new ArrayList<>();
            for (TipoBenchmark tipoBenchmark : tipoBenchmarks) {
                listReturn.add(new TipoBenchmarkResponseTO(tipoBenchmark));
            }
            return listReturn;
        } catch (Exception e) {
            throw new ServiceException(TipoBenchmarkExcecoes.ERRO_BUSCAR_TIPOBENCHMARK);
        }
    }

    public TipoBenchmarkResponseTO inserir(TipoBenchmarkTO tipoBenchmarkTO)throws ServiceException{
        try{
            String ctx = sessaoService.getUsuarioAtual().getChave();
            TipoBenchmark tipoBenchmark = new TipoBenchmark();
            tipoBenchmark.setNome(tipoBenchmarkTO.getNome());
            tipoBenchmark.setOrigem(OrigemTipoBenchmark.BOX);
            validarDados(ctx,tipoBenchmark);

            tipoBenchmark = inserir(ctx,tipoBenchmark);

            return new TipoBenchmarkResponseTO(tipoBenchmark);
        }catch (Exception e){
            throw new ServiceException(TipoBenchmarkExcecoes.ERRO_INCLUIR_TIPOBENCHMARK, e);
        }
    }

    public TipoBenchmarkResponseTO consultar(Integer id)throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        prepare(ctx);
        TipoBenchmark tipoBenchmark = obterPorId(ctx, id);
        if ((tipoBenchmark == null) || (tipoBenchmark.getCodigo() == null) || (tipoBenchmark.getCodigo() <= 0)){
            throw new ServiceException(TipoBenchmarkExcecoes.TIPOBENCHMARK_NAO_ENCONTRADO);
        }
        return new TipoBenchmarkResponseTO(tipoBenchmark);
    }

    public TipoBenchmarkResponseTO alterar(TipoBenchmarkTO tipoBenchmarkTO)throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        TipoBenchmark tipoBenchmark = null;
        try{
            tipoBenchmark = obterPorId(ctx, tipoBenchmarkTO.getId());
            if ((tipoBenchmark == null) || (tipoBenchmark.getCodigo() == null) || (tipoBenchmark.getCodigo() <= 0)){
                throw new ServiceException(TipoBenchmarkExcecoes.TIPOBENCHMARK_NAO_ENCONTRADO);
            }
            tipoBenchmark.setNome(tipoBenchmarkTO.getNome());
            validarDados(ctx,tipoBenchmark);
            tipoBenchmark = getTipoBenchmarkDao().update(ctx, tipoBenchmark);

            return new TipoBenchmarkResponseTO(tipoBenchmark);
        }catch (Exception e){
            throw new ServiceException(TipoBenchmarkExcecoes.ERRO_ALTERAR_TIPOBENCHMARK, e);
        }
    }

    public void excluir(Integer id)throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        TipoBenchmark tipoBenchmark = null;
        try{
            tipoBenchmark = obterPorId(ctx, id);
        }catch (Exception e){
            throw new ServiceException(TipoBenchmarkExcecoes.ERRO_BUSCAR_TIPOBENCHMARK, e);
        }
        if ((tipoBenchmark == null) || (tipoBenchmark.getCodigo() == null) || (tipoBenchmark.getCodigo() <= 0)){
            throw new ServiceException(TipoBenchmarkExcecoes.TIPOBENCHMARK_NAO_ENCONTRADO);
        }
        try{
            getTipoBenchmarkDao().delete(ctx, tipoBenchmark);
        }catch (Exception e){
            throw new ServiceException(TipoBenchmarkExcecoes.ERRO_EXCLUIR_TIPOBENCHMARK, e);
        }

    }

    public void validarDados(final String ctx, TipoBenchmark object) throws ServiceException {
        if (object.getNome() == null || object.getNome().trim().isEmpty()) {
            if (!JSFUtilities.isJSFContext()){
                throw new ServiceException(TipoBenchmarkExcecoes.VALIDACAO_NOME_TIPOBENCHMARK);
            }
            throw new ValidacaoException("validacao.nome");
        }
        if (object.getOrigem() == null) {
            if (!JSFUtilities.isJSFContext()){
                throw new ServiceException(TipoBenchmarkExcecoes.VALIDACAO_ORIGEM_TIPOBENCHMARK);
            }
        }
        object.setNome(object.getNome().trim());
        if (getTipoBenchmarkDao().exists(ctx, object, "nome")) {
            if (!JSFUtilities.isJSFContext()){
                throw new ServiceException(TipoBenchmarkExcecoes.VALIDACAO_TIPOBENCHMARK_JA_EXISTE);
            }
            throw new ValidacaoException("cadastros.existente");
        }
    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }




}
