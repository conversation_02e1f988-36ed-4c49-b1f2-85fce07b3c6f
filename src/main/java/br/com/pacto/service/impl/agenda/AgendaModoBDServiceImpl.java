package br.com.pacto.service.impl.agenda;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.controller.json.aulaDia.AulaDiaJSON;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.enumerador.agenda.TipoToleranciaAulaEnum;
import br.com.pacto.enumerador.cliente.SituacaoClienteEnum;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.agendatotal.AgendaTotalServiceImpl;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.agenda.AgendaAulasService;
import br.com.pacto.service.intf.empresa.CidadeVO;
import br.com.pacto.service.intf.empresa.EmpresaVO;
import br.com.pacto.service.intf.empresa.EstadoVO;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.bean.AgendaTotalTO;
import br.com.pacto.util.bean.AgendadoTO;
import br.com.pacto.util.bean.AgendamentoConfirmadoTO;
import br.com.pacto.util.bean.AgendamentoDesmarcadoTO;
import br.com.pacto.util.enumeradores.DiasSemana;
import br.com.pacto.util.enumeradores.TipoHorarioCreditoTreinoEnum;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.impl.UtilReflection;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.AgendadoJSON;
import org.apache.commons.lang.SerializationUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONArray;
import org.json.JSONObject;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class AgendaModoBDServiceImpl implements AgendaAulasService, AutoCloseable {

    private Connection con;
    private String key;
    private Map<Integer, List<Date>> mapaAulasExcluidas;

    public AgendaModoBDServiceImpl(Connection con, String key) {
        this.key = key;
        this.con = con;
    }

    public List<AulaDiaJSON> consultarAgendamentosModalidadesAlunoApp(final String key, final Date inicio,
                                                                      final Date fim, final Integer matricula, final Integer empresa,
                                                                      final Integer contrato) throws Exception{
        List<AgendaTotalJSON> listaJson = consultarAgendamentosModalidadeAluno( key, inicio, fim, matricula, empresa);
        List<Integer> modalidadeFiltrar = new ArrayList<>();
        if(!UteisValidacao.emptyNumber(contrato)){
            modalidadeFiltrar = modalidadesContrato(key, contrato, matricula);
        }
        List<AgendaTotalTO> listaTO = new ArrayList<>();
        for(AgendaTotalJSON a : listaJson){
            if(!UteisValidacao.emptyString(a.getSituacao()) && a.getSituacao().equals("IN")){
                continue;
            }
            if(!UteisValidacao.emptyNumber(contrato) && !modalidadeFiltrar.contains(a.getCodigoTipo())){
                continue;
            }
            AgendaTotalTO agenda = new AgendaTotalTO(a);
            List<Date> datas = mapaAulasExcluidas.get(Integer.valueOf(agenda.getId()));
            if(datas != null && datas.contains(Calendario.getDataComHoraZerada(agenda.getStartDate())) || a.getAulaCheia()){
                continue;
            }
            listaTO.add(agenda);
        }
        montarMapaAgendados(key, inicio, fim, empresa, listaTO, false, null);
        List<AulaDiaJSON> adjsons = new ArrayList<AulaDiaJSON>();
        for(AgendaTotalTO ato : listaTO){
            adjsons.add(new AulaDiaJSON(ato));
        }
        return adjsons;
    }



    public Map<String, List<AgendadoTO>> montarMapaAgendados(String ctx, Date inicio,
                                                             Date fim, Integer empresa,
                                                             List<AgendaTotalTO> agendamentos,
                                                             boolean armazenarTurma, Integer horarioTurma) throws Exception {

        List<AgendadoTO> agendados = consultarAgendadosParaAgenda( inicio, fim, empresa, horarioTurma);
        List<AgendadoTO> reposicoes = consultarReposicoesParaAgenda(inicio, fim, empresa, null, false, null, null);
        Map<Integer, Map<String, AgendamentoDesmarcadoTO>> desmarcados = consultarDesmarcados( ctx, inicio, fim, empresa);
        Map<Integer, Map<String, AgendamentoConfirmadoTO>> confirmados = consultarConfirmados( ctx, inicio, fim);

        Map<String, List<AgendadoTO>> mapa = new HashMap<String, List<AgendadoTO>>();
        for (AgendaTotalTO agenda : agendamentos) {
            List<AgendadoTO> lista = new ArrayList<AgendadoTO>();
            int vagasPreenchidas = 0;
            int nrdesmarcados = 0;
            int nrreposicoes = 0;
            int nrConfirmados = 0;
            int marcacoes = 0;
            int experimentais = 0;
            int diaristas = 0;
            int desafios = 0;
            int esperas = 0;

            for (AgendadoTO ag : agendados) {

                AgendadoTO agendado = ag.cloneTO();
                agendado.setAgendamento(agenda);
                if (agenda.getId().equals(agendado.getIdAgendamento())
                        && Calendario.menorOuIgual(agendado.getInicio(), agenda.getStartDate())
                        && Calendario.maiorOuIgual(agendado.getFim(), agenda.getStartDate())) {
                    Map<String, AgendamentoDesmarcadoTO> mapaDesmarcados = desmarcados.get(agendado.getCodigoCliente());
                    Map<String, AgendamentoConfirmadoTO> mapaConfirmados = confirmados.get(agendado.getCodigoCliente());
                    if (mapaDesmarcados != null && mapaDesmarcados.get(agenda.getIdentificador()) != null &&
                            mapaDesmarcados.get(agenda.getIdentificador()).getCodigoContrato().equals(ag.getCodigoContrato())) {
                        if(mapaDesmarcados.get(agenda.getIdentificador()).getJustificativa() != null) {
                            agendado.setJustificativa(mapaDesmarcados.get(agenda.getIdentificador()).getJustificativa());
                        }
                        nrdesmarcados++;
                        agendado.setDesmarcado(true);
                    } else  {
                        if(agenda.getAulaColetiva() && mapaConfirmados != null && mapaConfirmados.get(agenda.getIdentificador()) != null){
                            nrConfirmados ++;
                            agendado.setConfirmado(true);
                            agenda.getAlunosConfirmados().put(agendado.getNomeAbreviado(), agendado.getMatricula());
                        }
                        if(agendado.isDiaria()) {
                            diaristas++;
                        }
                        if (agendado.getDesafio()) {
                            desafios++;
                        }
                        if(agendado.isEspera()){
                            esperas++;
                        }
                        vagasPreenchidas++;
                        agenda.getAlunos().put(agendado.getNomeAbreviado(), agendado.getMatricula());
                    }

                    if (armazenarTurma) {
                        agendado.setAgendamento(agenda);
                    }
                    lista.add(agendado);
                }
            }
            for (AgendadoTO agendado : reposicoes) {
                if (agenda.getId().equals(agendado.getIdAgendamento())
                        && Calendario.menorOuIgual(agendado.getInicio(), agenda.getStartDate())
                        && Calendario.maiorOuIgual(agendado.getFim(), agenda.getStartDate())) {
                    vagasPreenchidas++;
                    if(agendado.isDiaria()){
                        diaristas++;
                    }else {
                        agendado.setReposicao(true);
                        if (agendado.getDesafio()) {
                            desafios++;
                        } else if (agendado.isExperimental()) {
                            experimentais++;
                        } else if (agendado.getUsaSaldo()) {
                            marcacoes++;
                        } else {
                            nrreposicoes++;
                        }
                    }
                    Map<String, AgendamentoConfirmadoTO> mapaConfirmados = confirmados.get(agendado.getCodigoCliente());
                    if(agenda.getAulaColetiva() && mapaConfirmados != null && mapaConfirmados.get(agenda.getIdentificador()) != null){
                        nrConfirmados++;
                        agendado.setConfirmado(true);
                        agenda.getAlunosConfirmados().put(agendado.getNomeAbreviado(), agendado.getMatricula());
                    }
                    //criado validação para verificar se já tem algum cliente que está ativo nesta aula e não adicionando dados duplicados. #21863
                    boolean adicionaReposicao = true;
                    for (AgendadoTO agendadosAdicionados : lista) {
                        if (agendado.getMatricula().equals(agendadosAdicionados.getMatricula()) && !(agendadosAdicionados.isDesmarcado())) {
                            vagasPreenchidas--;
                            if(agendado.isDiaria()){
                                diaristas--;
                            }else {
                                agendado.setReposicao(true);
                                if (agendado.getDesafio()) {
                                    desafios--;
                                } else if (agendado.isExperimental()) {
                                    experimentais--;
                                } else if (agendado.getUsaSaldo()) {
                                    marcacoes--;
                                } else {
                                    nrreposicoes--;
                                }
                            }
                            adicionaReposicao = false;
                            break;
                        }
                    }
                    if (adicionaReposicao){
                        agendado.setAgendamento(agenda);
                        lista.add(agendado);
                    }
                    agenda.getAlunos().put(agendado.getNomeAbreviado(), agendado.getMatricula());
                }

            }

            agenda.setExperimentais(experimentais);
            agenda.setMarcacoes(marcacoes);
            agenda.setConfirmacoes(nrConfirmados);
            agenda.setReposicoes(nrreposicoes);
            agenda.setNrVagasPreenchidas(vagasPreenchidas);
            agenda.setDesmarcados(nrdesmarcados);
            agenda.setDiaristas(diaristas);
            agenda.setDesafios(desafios);
            mapa.put(agenda.getIdentificador(), lista);

        }
        return mapa;
    }

    public List<Integer> modalidadesContrato(String ctx, Integer contrato, Integer matricula){
        List<Integer> modalidadesContrato = new ArrayList<>();
        try {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("select modalidade from contratomodalidade c \n" +
                    "inner join contrato con on con.codigo = c.contrato \n" +
                    "inner join cliente cli on cli.pessoa = con.pessoa \n" +
                    "where c.contrato =  " + contrato + " and cli.codigomatricula = " + matricula, con)) {
                while (rs.next()) {
                    modalidadesContrato.add(rs.getInt("modalidade"));
                }
            }
        } catch (Exception e){
            Uteis.logar(e, AgendaTotalServiceImpl.class);
        }
        return modalidadesContrato;
    }

    public List<Integer> dependentes(List<AgendadoTO> agendados) {
        if(agendados == null || agendados.isEmpty()){
            return new ArrayList<>();
        }

        List<Integer> dependentes = new ArrayList<>();
        try {
            String codigos = "";
            for (AgendadoTO agendado : agendados) {
                codigos += "," + agendado.getCodigoCliente();
            }
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("select codigo  from cliente \n" +
                    "where titularplanocompartilhado is not null\n" +
                    "and codigo in (" + codigos.replaceFirst(",", "") + ")", con)) {
                while (rs.next()) {
                    dependentes.add(rs.getInt("codigo"));
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, AgendaModoBDServiceImpl.class);
        }
        return dependentes;
    }

    @Override
    public List<AgendaTotalJSON> consultarAgendamentosModalidadeAluno(String key, Date inicio, Date fim, Integer matricula, Integer empresa) throws Exception {
        verificarSituacaoAluno(matricula, null);

        Set<Integer> modalidades = new HashSet<>();
        Date agora;
        TimeZone tz = TimeZone.getTimeZone(obterTimeZoneDefault(empresa));
        agora = Calendario.hojeCalendar(tz).getTime();

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("select tipohorario, modalidade, cli.empresa from contratomodalidade cm\n"
                + " INNER JOIN contrato c ON c.codigo = cm.contrato AND c.situacao = 'AT'\n"
                + " INNER JOIN cliente cli ON cli.pessoa = c.pessoa AND cli.codigomatricula = " + matricula
                + " INNER JOIN contratoduracao cd ON cd.contrato = c.codigo \n"
                + " LEFT JOIN contratoduracaocreditotreino cdc ON cd.codigo = cdc.contratoduracao \n"
                + "inner join modalidade m on m.codigo = cm.modalidade \n"
                + "where m.utilizarturma is true ", con)) {
            while (rs.next()) {
                modalidades.add(rs.getInt("modalidade"));
            }
        }

        try (ResultSet rsModaDes = ConexaoZWServiceImpl.criarConsulta("SELECT turma.modalidade FROM auladesmarcada a \n"
                + " INNER JOIN contrato con ON a.contrato = con.codigo AND con.situacao = 'AT'\n"
                + " INNER JOIN cliente cli ON con.pessoa = cli.pessoa\n"
                + " INNER JOIN turma ON turma.codigo = a.turma"
                + " inner join modalidade m on m.codigo = turma.modalidade \n"
                + " WHERE cli.codigomatricula = " + matricula
                + " and datareposicao is null and  permiteReporAulaDesmarcada = true "
                + "and m.utilizarturma is true "
                + "and con.datarematricularealizada is not null and con.datarematricularealizada >= CURRENT_DATE "
                + "order by dataorigem ", con)) {
            while (rsModaDes.next()) {
                modalidades.add(rsModaDes.getInt("modalidade"));
            }
        }

        Map<String, Date> mapaMatriculas = obterMapaMatriculas(matricula);
        Map<String, List<Date>> mapaReposicoes = obterMapaReposicoes(matricula, null, Calendario.hoje());
        List<AgendaTotalJSON> listaAgenda = consultarParaAgenda(inicio, fim, null, new ArrayList<>(modalidades), empresa, false, agora,
                mapaMatriculas, mapaReposicoes, matricula, true, null, null);
        List<AgendaTotalJSON> aulasDesmarcadasSemReposicao = consultarAulasDesmarcadasSemReposicao(matricula, inicio, fim, null);
        for (AgendaTotalJSON aulaDesmarcadaSemReposicao : aulasDesmarcadasSemReposicao) {
            boolean utilizarTurma = verificarModalidade(aulaDesmarcadaSemReposicao.getCodigoTipo());
            boolean existe = false;
            for (AgendaTotalJSON aulaMatriculada : listaAgenda) {
                if (aulaMatriculada.getCodDia().equals(aulaDesmarcadaSemReposicao.getCodDia())) {
                    existe = true;
                }
            }
            if (!existe && utilizarTurma) {
                listaAgenda.add(aulaDesmarcadaSemReposicao);
            }
        }
        for(int i = 0 ; i < listaAgenda.size(); i ++) {
            try (ResultSet rsVideo = ConexaoZWServiceImpl.criarConsulta("select codigo , turma_codigo , linkvideo , professor  from turmavideo where turma_codigo = "+listaAgenda.get(i).getCodigoTurma(), con)){
                List<TurmaVideoDTO> linkVideos = new ArrayList<>();
                while (rsVideo.next()) {
                    TurmaVideoDTO turmaVideoDTO = new TurmaVideoDTO();
                    turmaVideoDTO.setId(rsVideo.getInt("codigo"));
                    turmaVideoDTO.setLinkVideo(rsVideo.getString("linkvideo"));
                    turmaVideoDTO.setProfessor(rsVideo.getBoolean("professor"));
                    linkVideos.add(turmaVideoDTO);
                }
                listaAgenda.get(i).setLinkVideos(linkVideos);
            }
        }
        return listaAgenda;
    }

    private Map<String, List<Date>> obterMapaReposicoes(Integer matricula, Integer codigoCliente, Date data) throws Exception {
        Map<String, List<Date>> mapa = new HashMap<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select rp.horarioturma, rp.datareposicao, h.horainicial ,h.horafinal from reposicao rp\n");
        sql.append("INNER JOIN situacaoclientesinteticodw sw ON sw.codigocontrato = rp.contrato \n");
        sql.append("inner join horarioturma h on h.codigo = rp.horarioturma \n");
        if (data != null) {
            sql.append(" AND datareposicao >= '").append(Uteis.getDataAplicandoFormatacao(data, "yyyy-MM-dd")).append("'\n");
        }
        if (codigoCliente == null) {
            sql.append(" AND sw.matricula = ").append(matricula);
        } else {
            sql.append(" AND sw.codigoCliente = ").append(codigoCliente);
        }

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                List<Date> get = mapa.get(rs.getString("horarioturma"));
                if (get == null) {
                    get = new ArrayList<Date>();
                    mapa.put(rs.getString("horarioturma"), get);
                }
                Date dataReposicao = rs.getDate("datareposicao");
                String horarioInicial = rs.getString("horainicial");
                String[] inicialHoraMinuto = horarioInicial.split(":");
                String horaInicial = inicialHoraMinuto[0];
                String minutiInicial = inicialHoraMinuto[1];
                dataReposicao = Calendario.getDataComHoraZerada(dataReposicao);
                dataReposicao.setHours(Integer.parseInt(horaInicial));
                dataReposicao.setMinutes(Integer.parseInt(minutiInicial));
                get.add(dataReposicao);
            }
        }
        return mapa;
    }

    private void verificarSituacaoAluno(Integer matricula, Integer contrato) throws Exception {
        try (ResultSet sit = ConexaoZWServiceImpl.criarConsulta("select codigocliente, situacaocontrato from situacaoclientesinteticodw where matricula = " + matricula, con)) {
            if (sit.next()) {
                String situacaoContrato = sit.getString("situacaocontrato");
                Integer codigocliente = sit.getInt("codigocliente");

                boolean contratoConcomitanteAlunoAtivo = contratoConcomitanteAlunoAtivo(codigocliente, Calendario.hoje());
                boolean existeOperacao = false;
                if (contratoConcomitanteAlunoAtivo && contrato != null) {
                    boolean existeOperacaoParaEstaData = existeOperacaoParaEstaData("CR", new Date(), contrato);
                    if (existeOperacaoParaEstaData) {
                        existeOperacao = true;
                        situacaoContrato = "CR";
                    }
                }

                if ((!contratoConcomitanteAlunoAtivo || existeOperacao) && situacaoContrato != null && !situacaoContrato.isEmpty()) {
                    switch (situacaoContrato) {
                        case "TR":
                            throw new Exception("Aluno está Trancado");
                        case "AE":
                            throw new Exception("Aluno em Atestado");
                        case "CR":
                            throw new Exception("Aluno em Férias");
                    }
                }
            }
        }

    }

    public boolean existeOperacaoParaEstaData(String tipoOperacao, Date dataBase, int codigoContrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT codigo FROM contratoOperacao op \n");
        sql.append("WHERE tipoOperacao = '").append(tipoOperacao).append("' \n");
        sql.append("AND '").append(dataBase).append("' BETWEEN CAST(dataInicioEfetivacaoOperacao AS date)\n");
        sql.append("AND CAST(dataFimEfetivacaoOperacao AS date)\n");
        sql.append("AND contrato = ").append(codigoContrato);

        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                return rs.next();
            }
        }
    }

    private boolean contratoConcomitanteAlunoAtivo(int codigocliente, Date data) throws Exception {
        try {
            try (ResultSet rsAuno = ConexaoZWServiceImpl.criarConsulta(
                    "SELECT situacao, pessoa from cliente where codigo = " + codigocliente, con)) {
                if (rsAuno.next()) {
                    int pessoa = rsAuno.getInt("pessoa");
                    String situacao = rsAuno.getString("situacao");
                    if (situacao.equals(SituacaoClienteEnum.ATIVO.getCodigo())) {
                        return existeContratoConcomitantePessoaNaData(pessoa, data);
                    }

                }
            }
        } catch (Exception e) {
            Uteis.logar(e, AgendaModoBDServiceImpl.class);
        }
        return false;
    }

    public boolean existeContratoConcomitantePessoaNaData(Integer pessoa, Date dataPesquisa) throws Exception {
        String sqlStr = "select exists (select pessoa,count(pessoa) from contrato where pessoa = " + pessoa + " and   '" + Uteis.getDataFormatoBD(dataPesquisa) + "' between  vigenciade and  vigenciaateajustada group by pessoa having count(pessoa) > 1);";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return false;
                }
                return tabelaResultado.getBoolean(1);
            }
        }
    }

    public String obterTimeZoneDefault(Integer empresa) throws Exception {
        String sqlStr = "SELECT timeZoneDefault FROM Empresa ";
        if (empresa == null) {
            sqlStr += "LIMIT 1";
        } else {
            sqlStr += "WHERE codigo = " + empresa;
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                tabelaResultado.next();
                return tabelaResultado.getString("timeZoneDefault");
            }
        }
    }

    private Map<String, Date> obterMapaMatriculas(Integer matricula) throws Exception {
        Map<String, Date> mapa = new HashMap<String, Date>();
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("SELECT rp.horarioturma, datafim FROM matriculaalunohorarioturma rp\n" +
                "INNER JOIN contrato con ON rp.contrato = con.codigo AND con.situacao = 'AT'\n" +
                "INNER JOIN cliente cli ON con.pessoa = cli.pessoa \n" +
                "WHERE cli.codigomatricula = " + matricula + "\n" +
                "ORDER BY datafim ", con)) {
            while (rs.next()) {
                mapa.put(rs.getString("horarioturma"), rs.getDate("datafim"));
            }
        }
        return mapa;
    }

    @Override
    public void close() throws Exception {
        if (con != null && !con.isClosed()) {
            con.close();
        }
    }

    public List<AgendaTotalJSON> consultarParaAgenda(Date inicio, Date fim, Integer turma,
                                                     List<Integer> modalidades, Integer empresa, boolean aulasColetivas, String search, String situacaoHorario) throws Exception {
        return consultarParaAgenda(inicio, fim, turma, modalidades, empresa, aulasColetivas, null, null, null, null, false, search, situacaoHorario);
    }

    @Override
    public List<AgendaTotalJSON> consultarAulasAluno(Date inicio, Date fim, Integer matricula) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select dia, identificador, horainicial, horafinal, m.nome as modalidade, colp.nome as professor from alunohorarioturma aht ");
        sql.append(" inner join horarioturma ht on ht.codigo = aht.horarioturma ");
        sql.append(" inner join turma t on t.codigo = ht.turma ");
        sql.append(" inner join cliente cli on cli.codigo = aht.cliente ");
        sql.append(" inner join modalidade m on m.codigo = t.modalidade ");
        sql.append(" inner join colaborador col on col.codigo = ht.professor ");
        sql.append(" inner join pessoa colp on col.pessoa = colp.codigo ");
        sql.append(" where cli.codigomatricula = ?  ");
        sql.append(" and dia between  ? and ? ");
        sql.append(" and (t.datafinalvigencia is null or t.datafinalvigencia >  aht.dia) ");
        sql.append(" order by aht.dia ");

        List<AgendaTotalJSON> lista;
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.setInt(1, matricula);
            stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
            stm.setTimestamp(3, Uteis.getDataJDBCTimestamp(Calendario.getDataComUltimaHora(fim)));
            lista = new ArrayList<AgendaTotalJSON>();
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    Date data = rs.getDate("dia");
                    AgendaTotalJSON item = new AgendaTotalJSON();
                    item.setInicioVigencia(data);
                    item.setInicio(rs.getString("horainicial"));
                    item.setFim(rs.getString("horafinal"));
                    item.setInicio(Uteis.getData(data) + " " + item.getInicio());
                    item.setFim(Uteis.getData(data) + " " + item.getFim());
                    item.setTitulo(rs.getString("identificador"));
                    item.setTipo(rs.getString("modalidade"));
                    item.setResponsavel(rs.getString("professor"));
                    lista.add(item);
                }
            }
        }
        return lista;
    }

    @Override
    public List<AgendaTotalJSON> consultarParaAgenda(Date inicio, Date fim, Integer turma,
                                                     List<Integer> modalidades, Integer empresa, boolean aulasColetivas,
                                                     Date agora, Map<String, Date> mapaMatriculas,
                                                     Map<String, List<Date>> mapaReposicoes, Integer matricula,
                                                     boolean isApp, String search, String situacaoHorario) throws Exception {

        return consultarParaAgenda(inicio, fim, turma, modalidades, empresa, aulasColetivas, agora, mapaMatriculas,
                mapaReposicoes, matricula, isApp, false, search, situacaoHorario);
    }

    @Override
    public List<AgendaTotalJSON> consultarParaAgenda(Date inicio, Date fim, Integer turma,
                                                     List<Integer> modalidades, Integer empresa, boolean aulasColetivas,
                                                     Date agora, Map<String, Date> mapaMatriculas,
                                                     Map<String, List<Date>> mapaReposicoes, Integer matricula,
                                                     boolean isApp, boolean isAgendaOnline, String search, String situacaoHorario) throws Exception {
        ParamFeriadoAulaCheiaTO paramFeriadoAulaCheiaTO;
        List<ParamFeriadoAulaCheiaTO> listParamFeriadoAulaCheiaTO = new ArrayList<ParamFeriadoAulaCheiaTO>();
        boolean verificaEmpresaTrabalhaFeriado = true;
        List<Integer> todasEmpresas;
        List<AgendaTotalJSON> lista = new ArrayList<AgendaTotalJSON>();

        Map<String, List<Date>> mapaDesmarcacoes = obterMapaDesmarcacoes(null, matricula, inicio, fim);

        List<Date> diasEntreDatas = Uteis.getDiasEntreDatas(inicio, fim);
        EmpresaVO empresaVO = consultarEmpresa(empresa);
        Set<Date> diasFeriados = consultarFeriadosPorPeriodoEmpresaAulaCheia(inicio, fim, empresaVO);
        if (null != diasFeriados && !diasFeriados.isEmpty()) {
            Iterator<Date> it = diasEntreDatas.iterator();
            if (!empresaVO.isPermMarcarAulaFeriado()) {
                while (it.hasNext()) {
                    Date dataRemove = it.next();
                    for (Date diaFeriado : diasFeriados) {
                        if (Calendario.dataNoMesmoDiaMes(diaFeriado, dataRemove)) {
                            it.remove();
                            break;
                        }
                    }
                }
            }
        }

        Map<Integer, List<HistoricoProfessorTurmaVO>> mapaHistoricoProfessores = montarHistoricoProfessor(inicio);
        Map<String, List<AgendaTotalJSON>> turmasParaAgenda = consultarHorariosTurmaParaAgenda(inicio, fim,
                empresa, modalidades, aulasColetivas, null, matricula, isApp, isAgendaOnline, search, situacaoHorario);

        if(search!=null && !search.equals("")){
            Map<String, List<AgendaTotalJSON>> turmaReposicaoParaAgenda = consultarHorariosTurmaReposicaoParaAgenda(inicio, fim,
                    empresa, modalidades, aulasColetivas, null, matricula, isApp, isAgendaOnline, search, situacaoHorario);
            if(turmaReposicaoParaAgenda!=null && turmaReposicaoParaAgenda.size() > 0){
                if(turmasParaAgenda != null && turmasParaAgenda.size() > 0){
                    for (Map.Entry<String, List<AgendaTotalJSON>> entry : turmasParaAgenda.entrySet()) {
                        if(turmaReposicaoParaAgenda.get(entry.getKey())!=null){
                            turmasParaAgenda.get(entry.getKey()).addAll(turmaReposicaoParaAgenda.get(entry.getKey()));
                        }
                    };
                }else{
                    for (Map.Entry<String, List<AgendaTotalJSON>> entry : turmaReposicaoParaAgenda.entrySet()) {
                        turmasParaAgenda.put(entry.getKey(), entry.getValue());
                    }
                }
            }
        }

        Map<Integer, ColaboradorVO> colaboradorResponsavel = colaboradorResponsavel(turmasParaAgenda);
        Map<String, Integer> mapaOcupacaoPorId = new HashMap<String, Integer>();
        Map<String, Integer> mapaQuantidadeAlunosAulaNaoColetiva = new HashMap<String, Integer>();
        Map<String, Integer> mapaCountRemove = new HashMap<String, Integer>();

        for (Date d : diasEntreDatas) {
            String diaDaSemana = Uteis.obterDiaSemanaData(d);
            List<AgendaTotalJSON> turmas = turmasParaAgenda.get(diaDaSemana);
            if (turmas == null) {
                continue;
            }
            AGENDAMENTOS:
            for (AgendaTotalJSON a : turmas) {

                //CONSULTAR FOTO PARA APLICATIVO
                if (!UteisValidacao.emptyNumber(a.getCodigoResponsavel())) {
                    ColaboradorVO colaboradorVO = colaboradorResponsavel.get(a.getCodigoResponsavel());
                    if (colaboradorVO == null) {
                        colaboradorVO = consultarColaborador(a.getCodigoResponsavel());
                        colaboradorResponsavel.put(a.getCodigoResponsavel(), colaboradorVO);
                    }
                    a.setFotoProfessor(colaboradorVO.getPessoa().getUrlFoto());
                }

                if (mapaMatriculas != null
                        && mapaMatriculas.get(a.getId()) != null
                        && Calendario.igual(mapaMatriculas.get(a.getId()), d)
                        && !(mapaDesmarcacoes.get(a.getId()) != null && mapaDesmarcacoes.get(a.getId()).contains(d))
                ) {
                    continue;
                }
                if (mapaReposicoes != null
                        && mapaReposicoes.get(a.getId()) != null) {
                    List<Date> dias = mapaReposicoes.get(a.getId());
                    for (Date dia : dias) {
                        if (Calendario.igual(dia, d)) {
                            continue AGENDAMENTOS;
                        }
                    }
                }
                Date agDia = Calendario.getDataComHora(d, a.getInicio());
                if (!UteisValidacao.emptyNumber(a.getToleranciaApresentarApp())) {
                    agDia = Uteis.somarCampoData(agDia, Calendar.MINUTE, a.getToleranciaApresentarApp());
                }
                if ((agora == null || agDia.after(agora))
                        && Calendario.maiorOuIgual(d, a.getInicioVigencia())
                        && Calendario.menorOuIgual(d, a.getFimVigencia())) {
                    AgendaTotalJSON agendaTotalJSON = new AgendaTotalJSON(d, a, a.getAulaCheia() ? null :
                            professorDaEpoca(d, Integer.parseInt(a.getId()), mapaHistoricoProfessores));

                    agendaTotalJSON.setValorProduto(a.getValorProduto());
                    agendaTotalJSON.setCodigoProduto(a.getCodigoProduto());
                    agendaTotalJSON.setDiaSemana(diaDaSemana);
                    if (agendaTotalJSON.getAulaCheia()) {
                        Integer ocupacao = mapaOcupacaoPorId.get(agendaTotalJSON.getId() + Uteis.getData(d, "yyyy-MM-dd"));
                        if (ocupacao == null) {
                            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(
                                    "SELECT COUNT(*) as ocupacao FROM alunohorarioturma WHERE horarioturma = " + agendaTotalJSON.getId()
                                            + " AND dia = '" + Uteis.getData(d, "yyyy-MM-dd") + "'", con)) {
                                ocupacao = rs.next() ? rs.getInt("ocupacao") : 0;
                            }
                            mapaOcupacaoPorId.put(agendaTotalJSON.getId() + Uteis.getData(d, "yyyy-MM-dd"), ocupacao);
                        }
                        agendaTotalJSON.setNrVagasPreenchidas(ocupacao);
                    } else {
                        Integer quantidadeAlunosAulaNaoColetiva = mapaQuantidadeAlunosAulaNaoColetiva.get(agendaTotalJSON.getId());
                        if (quantidadeAlunosAulaNaoColetiva == null) {
                            quantidadeAlunosAulaNaoColetiva = contarAulasAlunosNaoColetivas(d, Integer.valueOf(agendaTotalJSON.getId()), a.getNrVagas());
                            mapaQuantidadeAlunosAulaNaoColetiva.put(agendaTotalJSON.getId(), quantidadeAlunosAulaNaoColetiva);
                        }

                        agendaTotalJSON.setNrVagasPreenchidas(quantidadeAlunosAulaNaoColetiva);
                    }
                    if (UteisValidacao.notEmptyNumber(empresa)) {
                        if (empresaVO == null) {
                            empresaVO = consultarEmpresa(empresa);
                        }
                        agendaTotalJSON.setEmpresa(empresaVO.getCodigo());
                        if (isAulaFeriado(empresaVO, diasFeriados, d)) {
                            final Long horaAbertura = Calendario.pegaHoraEmMilisegundos(empresaVO.getHoraAberturaFeriado());
                            final Long horaFechamento = Calendario.pegaHoraEmMilisegundos(empresaVO.getHoraFechamentoFeriado());
                            final Long horaInicio = Calendario.pegaHoraEmMilisegundos(a.getInicio());
                            final Long horaFim = Calendario.pegaHoraEmMilisegundos(a.getFim());

                            if (isHorarioIntervaloNaoPermitido(diasFeriados, Calendario.getDataComHoraZerada(agDia), horaAbertura, horaFechamento, horaInicio, horaFim)) {
                                continue;
                            }
                        }
                    } else if (listParamFeriadoAulaCheiaTO.size() > 0) {
                        for (ParamFeriadoAulaCheiaTO param : listParamFeriadoAulaCheiaTO) {
                            empresaVO = consultarEmpresa(empresa);
                            if (!empresaVO.isPermMarcarAulaFeriado()) {
                                continue AGENDAMENTOS;
                            }
                            if (a.getEmpresa() == param.getEmpresa()) {
                                final Long horaAbertura = Calendario.pegaHoraEmMilisegundos(param.getHorarioInicial());
                                final Long horaFechamento = Calendario.pegaHoraEmMilisegundos(param.getHorarioFinal());
                                final long horaInicio = Calendario.pegaHoraEmMilisegundos(a.getInicio());
                                final long horaFim = Calendario.pegaHoraEmMilisegundos(a.getFim());

                                if (isHorarioIntervaloNaoPermitido(diasFeriados, inicio, horaAbertura, horaFechamento, horaInicio, horaFim)) {
                                    continue AGENDAMENTOS;
                                }
                            } else {
                                continue;
                            }
                        }
                    }
                    lista.add(agendaTotalJSON);
                }
            }
        }
        return lista;
    }

    private boolean isAulaFeriado(EmpresaVO empresaVO, Set<Date> diasFeriados, Date d) {
        if (empresaVO.isPermMarcarAulaFeriado() && diasFeriados != null) {
            for (Date diaFeriado : diasFeriados) {
                if (Calendario.dataNoMesmoDiaMes(diaFeriado, d)) {
                    return true;
                }
            }
        }
        return false;
    }

    public List<AgendaTotalJSON> consultarAulasDesmarcadasSemReposicao(Integer matricula, Date inicio, Date fim, Integer modalidade) throws Exception {
        String validaIdade = "";
        List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();
        Set<Date> diasFeriados = null;
        EmpresaVO empresaVO = null;
        Integer empresa;
        Integer pessoa = 0;
        try (ResultSet codigoEmpresa = ConexaoZWServiceImpl.criarConsulta("select p.codigo as codigoPessoa, p.datanasc, c.empresa " +
                "from pessoa as p inner join cliente as c on c.pessoa = p.codigo " +
                "where c.codigomatricula = " + matricula, con)) {
            empresa = 0;
            while (codigoEmpresa.next()) {
                try {
                    pessoa = codigoEmpresa.getInt("codigoPessoa");
                    empresa = codigoEmpresa.getInt("empresa");
                    validaIdade = Uteis.calculaIdadeComMeses(codigoEmpresa.getDate("datanasc").toString());
                } catch (Exception e) {
                    Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
                }
            }
            if (UteisValidacao.emptyNumber(pessoa)) {
                throw new Exception("Nenhum cliente encontrado.");
            }
        }
        empresaVO = consultarEmpresa(empresa);
        if (inicio != null) {
            diasFeriados = consultarFeriadosPorPeriodoEmpresaAulaCheia(inicio, fim, empresaVO);
        }
        StringBuilder consulta = new StringBuilder(sqlConsultaAulasDesmarcadasSemReposicao(matricula, empresaVO));
        if (UteisValidacao.notEmptyNumber(modalidade)) {
            ModalidadeVO modalidadeVO = consultarModalidade(modalidade);
            if (UtilReflection.objetoMaiorQueZero(modalidadeVO, "getCodigo()")) {
                if (UteisValidacao.notEmptyNumber(modalidadeVO.getTipo())) {
                    consulta.append(" \n and m.tipo = ").append(modalidadeVO.getTipo());
                } else {
                    consulta.append(" \n and m.codigo = ").append(modalidadeVO.getCodigo());
                }
            }
        }
        consulta.append(" \n order by ad.dataorigem asc \n");
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(consulta.toString(), con)) {
            Date dataLimiteAulasRepor = null;
            if (UteisValidacao.notEmptyNumber(empresaVO.getTempoAposFaltaReposicao())) {
                dataLimiteAulasRepor = Uteis.obterDataAnterior(Calendario.hoje(), empresaVO.getTempoAposFaltaReposicao());
            }
            while (rs.next()) {
                String[] split = validaIdade.split("-");
                int idadeAlunoAno = 0;
                int idadeAlunoMes = 0;
                if (isNotBlank(split[0])) {
                    idadeAlunoAno = Integer.parseInt(split[0]);
                    idadeAlunoMes = Integer.parseInt(split[1]);
                }
                boolean isIdadeAlunoValida = validaIdadeAluno(
                        idadeAlunoAno,
                        idadeAlunoMes,
                        rs.getInt("idademinima"),
                        rs.getInt("idademinimameses"),
                        rs.getInt("idademaxima"),
                        rs.getInt("idademaximameses")
                );
                if (isIdadeAlunoValida) {

                    Date date = rs.getDate("dataorigem");

                    if (dataLimiteAulasRepor == null
                            || Calendario.maiorOuIgual(date, dataLimiteAulasRepor)) {
                        if (inicio == null || fim == null
                                || Calendario.igual(date, inicio)
                                || Calendario.igual(date, fim)
                                || Calendario.entre(date, inicio, fim)) {
                            if (diasFeriados != null) {
                                if (empresaVO.isPermMarcarAulaFeriado()) {
                                    final Long horaAbertura = Calendario.pegaHoraEmMilisegundos(empresaVO.getHoraAberturaFeriado());
                                    final Long horaFechamento = Calendario.pegaHoraEmMilisegundos(empresaVO.getHoraFechamentoFeriado());
                                    final Long horaInicio = Calendario.pegaHoraEmMilisegundos(rs.getString("horainicial"));
                                    final Long horaFim = Calendario.pegaHoraEmMilisegundos(rs.getString("horafinal"));

                                    if (!isHorarioIntervaloNaoPermitido(horaAbertura, horaFechamento, horaInicio, horaFim)) {
                                        aulas.add(new AgendaTotalJSON(date, montarDadosAgenda(rs)));
                                    }
                                }
                            } else {
                                aulas.add(new AgendaTotalJSON(date, montarDadosAgenda(rs)));
                            }
                        }
                    }
                }
            }
        }
        return aulas;
    }

    private boolean isHorarioIntervaloNaoPermitido(final Long horaAbertura, final Long horaFechamento,
                                                   final Long horaInicio, final Long horaFim) {
        return (horaAbertura != null && horaFechamento != null && horaInicio != null && horaFim != null)
                && (horaInicio < horaAbertura || horaInicio  >= horaFechamento || horaFim > horaFechamento);
    }

    private boolean validaIdadeAluno(int idadeAnoAluno, int idadeMesAluno, int idadeAnoMinima,
                                     int idadeMesMinima, int idadeAnoMaxima, int idadeMesMaxima) throws SQLException {
        if ((idadeAnoAluno < idadeAnoMinima) || (idadeAnoMinima == idadeAnoAluno && idadeMesAluno < idadeMesMinima)) {
            return false;
        } else if ((idadeAnoAluno > idadeAnoMaxima) || idadeAnoAluno == idadeAnoMaxima && idadeMesAluno > idadeMesMaxima){
            return false;
        }
        return true;
    }

    private boolean isHorarioIntervaloNaoPermitido(final Set<Date> diasFeriados, final Date inicio, final Long horaAbertura, final Long horaFechamento,
                                                   final Long horaInicio, final Long horaFim) {
        try {
            boolean naoPermitido = false;

            for (Date diaFeriado : diasFeriados) {
                if (Calendario.dataNoMesmoDiaMes(diaFeriado, inicio) && (!(Calendario.getAno(diaFeriado) < Calendario.getAno(inicio)) ||
                        isFeriadoRecorrente(diaFeriado))) {
                    naoPermitido = true;
                    break;
                }
            }
            return (horaAbertura != null && horaFechamento != null && horaInicio != null && horaFim != null)
                    && naoPermitido && (horaInicio < horaAbertura || horaInicio >= horaFechamento || horaFim > horaFechamento);
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isFeriadoRecorrente(Date diaFeriado) {
        try {
            List<FeriadoVO> feriados = consultarFeriadoPorDiaAndMes(diaFeriado);
            for (FeriadoVO feriado : feriados) {
                if (feriado.getNaoRecorrente() != null && !feriado.getNaoRecorrente()) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    private Map<String, List<Date>> obterMapaDesmarcacoes(Integer codigoCliente, Integer matricula, Date inicio, Date fim) throws Exception {
        Map<String, List<Date>> mapa = new HashMap<String, List<Date>>();
        StringBuilder sql = new StringBuilder();
        sql.append("select horarioturma, dataorigem from auladesmarcada a\n");
        sql.append("INNER JOIN situacaoclientesinteticodw sw ON sw.codigocontrato = a.contrato \n");
        if (matricula == null) {
            sql.append("AND sw.codigoCliente = ").append(codigoCliente);
        } else {
            sql.append("AND sw.matricula = ").append(matricula);
            sql.append("AND a.dataorigem BETWEEN '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd")).append("' AND '");
            sql.append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd")).append("' ");
        }
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                List<Date> get = mapa.get(rs.getString("horarioturma"));
                if (get == null) {
                    get = new ArrayList<Date>();
                    mapa.put(rs.getString("horarioturma"), get);
                }
                get.add(rs.getDate("dataorigem"));
            }
        }
        return mapa;
    }

    private Boolean verificarModalidade(Integer codigoModalidade) {
        Boolean utilizarTurma = false;
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT utilizarTurma FROM Modalidade WHERE codigo = ").append(codigoModalidade);
        return utilizarTurma;
    }

    public Integer contarAulasAlunosNaoColetivas(Date data, Integer codigoHorarioTurma, Integer nrVagas) throws Exception {
        final ResultSet rsAlunosTurma = getResultSetConsultarAlunosAulasNaoColetivas(data, codigoHorarioTurma, true);
        final ResultSet rsAlunosRealocadosParaEstaTurma = getResultSetConsultarAlunosRealocados(data, codigoHorarioTurma, true);
        final ResultSet rsAlunosRealocadosParaOutraTurma = getResultSetConsultarAlunosRealocadosParaOutraTurma(data, codigoHorarioTurma, true);
        final ResultSet rsAlunosComAulaDesmarcada = getResultSetConsultarComAulasDesmarcadas(data, codigoHorarioTurma, true);

        final Integer total = (rsAlunosTurma.next() ? rsAlunosTurma.getInt("total") : 0);
        Integer alunosRealocadosParaEstaTurma = (rsAlunosRealocadosParaEstaTurma.next() ? rsAlunosRealocadosParaEstaTurma.getInt("total") : 0);
        Integer alunosRealocadosParaOutraTurma = (rsAlunosRealocadosParaOutraTurma.next() ? rsAlunosRealocadosParaOutraTurma.getInt("total") : 0);
        Integer alunosComAulasDesmarcadas = (rsAlunosComAulaDesmarcada.next() ? rsAlunosComAulaDesmarcada.getInt("total") : 0);

        if (alunosComAulasDesmarcadas > 0 || (alunosRealocadosParaEstaTurma == alunosRealocadosParaOutraTurma)) {
            final ResultSet rsAlunosTurmaDados = getResultSetConsultarAlunosAulasNaoColetivas(data, codigoHorarioTurma, false);
            final List<AgendadoJSON> alunosNaAula = popularAlunosTurma(rsAlunosTurmaDados);
            final ResultSet rsAlunosComAulaDesmarcadaDados = getResultSetConsultarComAulasDesmarcadas(data, codigoHorarioTurma, false);
            final List<AgendamentoDesmarcadoJSON> agendamentosDesmarcado = popularAgendamentoDesmarcado(rsAlunosComAulaDesmarcadaDados);
            final ResultSet rsAlunosRealocadosParaEstaTurmaDados = getResultSetConsultarAlunosRealocados(data, codigoHorarioTurma, false);
            final List<AgendaReposicaoJSON> realocados = popularAgendamentoRealocado(rsAlunosRealocadosParaEstaTurmaDados);
            final ResultSet rsAlunosRealocadosParaOutraTurmaDados = getResultSetConsultarAlunosRealocadosParaOutraTurma(data, codigoHorarioTurma, false);
            final List<AgendaReposicaoJSON> reposicoes = popularAgendamentoRealocado(rsAlunosRealocadosParaOutraTurmaDados);

            if (!UteisValidacao.emptyList(alunosNaAula)) {
                ArrayList<Integer> clienteDesmarcado = new ArrayList<Integer>();
                ArrayList<Integer> clienteRegistradoDesmarcado = new ArrayList<Integer>();
                for (AgendadoJSON a : alunosNaAula) {
                    if (!clienteDesmarcado.contains(a.getCodigoCliente())) {
                        clienteDesmarcado.add(a.getCodigoCliente());
                    } else {
                        clienteRegistradoDesmarcado.add(a.getCodigoCliente());
                    }
                }
                for (AgendadoJSON a : alunosNaAula) {
                    if (!UteisValidacao.emptyNumber(a.getCodigoContrato())) {
                        if (!UteisValidacao.emptyList(agendamentosDesmarcado)) {
                            for (AgendamentoDesmarcadoJSON ad : agendamentosDesmarcado) {
                                if (a.getCodigoCliente().equals(ad.getCodigoCliente())) {
                                    if (!clienteRegistradoDesmarcado.contains(ad.getCodigoCliente())) {
                                        if (!a.getCodigoContrato().equals(ad.getCodigoContrato())) {
                                            alunosComAulasDesmarcadas--;
                                        }
                                    }
                                }
                            }
                        }
                        if (!UteisValidacao.emptyList(realocados)) {
                            for (AgendaReposicaoJSON re : realocados) {
                                if (a.getCodigoContrato().equals(re.getCodigoContrato())) {
                                    alunosRealocadosParaEstaTurma--;
                                }
                            }
                        }
                    }
                }
            }
            if (!UteisValidacao.emptyList(reposicoes)) {
                for (AgendaReposicaoJSON ar : reposicoes) {
                    boolean desmarcouPoremSofreuManutencao = true;
                    if (!UteisValidacao.emptyList(agendamentosDesmarcado)) {
                        for (AgendamentoDesmarcadoJSON ad : agendamentosDesmarcado) {
                            if (ad.getCodigoCliente().equals(ar.getCodigoCliente())) {
                                desmarcouPoremSofreuManutencao = false;
                                alunosRealocadosParaOutraTurma--;
                            }
                        }
                    }
                    if (desmarcouPoremSofreuManutencao && alunosRealocadosParaOutraTurma > 0) {
                        alunosRealocadosParaOutraTurma--;
                    }
                }
            }
            fecharResultSetQuietly(rsAlunosTurmaDados);
            fecharResultSetQuietly(rsAlunosComAulaDesmarcadaDados);
            fecharResultSetQuietly(rsAlunosRealocadosParaOutraTurmaDados);
        }

        Integer totalAlunosNaAula = 0;
        if (alunosRealocadosParaOutraTurma != 0 && (alunosRealocadosParaOutraTurma + alunosComAulasDesmarcadas == nrVagas)) {
            totalAlunosNaAula = alunosRealocadosParaEstaTurma;
        } else if (total - alunosComAulasDesmarcadas == 0) {
            totalAlunosNaAula = alunosRealocadosParaEstaTurma;
        } else if (alunosRealocadosParaEstaTurma == 0 && alunosRealocadosParaOutraTurma == 0) {
            if (alunosComAulasDesmarcadas == 0) {
                totalAlunosNaAula = total;
            } else {
                totalAlunosNaAula = total - alunosComAulasDesmarcadas;
            }
        } else {
            totalAlunosNaAula = total + alunosRealocadosParaEstaTurma - alunosComAulasDesmarcadas;
        }
        if (totalAlunosNaAula<0){
            totalAlunosNaAula = 0;
        }

        fecharResultSetQuietly(rsAlunosTurma);
        fecharResultSetQuietly(rsAlunosRealocadosParaEstaTurma);
        fecharResultSetQuietly(rsAlunosRealocadosParaOutraTurma);

        return totalAlunosNaAula;
    }

    private List<AgendamentoDesmarcadoJSON> popularAgendamentoDesmarcado(ResultSet rsAlunosComAulaDesmarcadaDados) throws SQLException {
        final List<AgendamentoDesmarcadoJSON> agentamentos = new ArrayList<AgendamentoDesmarcadoJSON>();
        while (rsAlunosComAulaDesmarcadaDados.next()) {
            AgendamentoDesmarcadoJSON agendamentoDesmarcado = new AgendamentoDesmarcadoJSON();
            agendamentoDesmarcado.setCodigoCliente(rsAlunosComAulaDesmarcadaDados.getInt("cliente"));
            agendamentoDesmarcado.setReposto(rsAlunosComAulaDesmarcadaDados.getDate("datareposicao") != null);
            agendamentoDesmarcado.setCodigoContrato(rsAlunosComAulaDesmarcadaDados.getInt("contrato"));

            agentamentos.add(agendamentoDesmarcado);
        }

        return agentamentos;
    }

    private List<AgendaReposicaoJSON> popularAgendamentoRealocado(ResultSet rsAlunosRealocadosParaOutraTurmaDados) throws SQLException {
        final List<AgendaReposicaoJSON> agentamentos = new ArrayList<AgendaReposicaoJSON>();
        while (rsAlunosRealocadosParaOutraTurmaDados.next()) {
            AgendaReposicaoJSON agendaReposicao = new AgendaReposicaoJSON();
            agendaReposicao.setCodigoCliente(rsAlunosRealocadosParaOutraTurmaDados.getInt("codigocliente"));
            agendaReposicao.setCodigoPessoa(rsAlunosRealocadosParaOutraTurmaDados.getInt("codigopessoa"));
            agendaReposicao.setCodigoContrato(rsAlunosRealocadosParaOutraTurmaDados.getInt("codigocontrato"));
            agendaReposicao.setConfirmadoReposicao(rsAlunosRealocadosParaOutraTurmaDados.getBoolean("confirmadoReposicao"));
            agendaReposicao.setMatricula(rsAlunosRealocadosParaOutraTurmaDados.getString("matricula"));

            agentamentos.add(agendaReposicao);
        }

        return agentamentos;
    }

    private ResultSet getResultSetConsultarComAulasDesmarcadas(Date data, Integer codigoHorarioTurma, Boolean contar) throws Exception {
        final StringBuilder sql = new StringBuilder("SELECT ");

        if (contar) {
            sql.append("COUNT(distinct a.cliente) as total");
            sql.append(" FROM auladesmarcada a \n");
            sql.append("INNER JOIN contrato c ON c.codigo = a.contrato  \n");
            sql.append("INNER JOIN horarioturma ht ON ht.codigo = a.horarioturma AND ht.codigo = ? \n");
            sql.append("WHERE a.dataorigem BETWEEN ? AND ?  \n");
            sql.append(" and c.codigo in (select contrato from matriculaalunohorarioturma m\n");
            sql.append(" where a.horarioturma = m.horarioturma and a.dataorigem between m.datainicio and m.datafim)");
        } else {
            sql.append(" distinct a.cliente, a.contrato, a.datareposicao ");
            sql.append(" FROM auladesmarcada a \n");
            sql.append("INNER JOIN contrato c ON c.codigo = a.contrato  \n");
            sql.append("INNER JOIN horarioturma ht ON ht.codigo = a.horarioturma AND ht.codigo = ? \n");
            sql.append("WHERE a.dataorigem BETWEEN ? AND ?  \n");
            sql.append(" and c.codigo in (select contrato from matriculaalunohorarioturma m\n");
            sql.append(" where a.horarioturma = m.horarioturma and a.dataorigem between m.datainicio and m.datafim) group by a.cliente, a.cliente, a.contrato, a.datareposicao");
        }

        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setInt(1, codigoHorarioTurma);
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(data)));
        stm.setTimestamp(3, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(data, "23:59:59")));
        return stm.executeQuery();
    }

    private ResultSet getResultSetConsultarAlunosRealocadosParaOutraTurma(Date data, Integer codigoHorarioTurma, Boolean contar) throws Exception {
        final StringBuilder sql = new StringBuilder("SELECT ");

        if (contar) {
            sql.append("COUNT(1) as total");
        } else {
            sql
                    .append("r.contrato as codigocontrato, p.fotokey, p.codigo as codigopessoa, c.codigo as codigocliente,c.matricula,")
                    .append("ht.horainicial, ht.horafinal, p.nome, r.datapresenca IS NOT NULL AS confirmadoreposicao,")
                    .append("ARRAY_TO_STRING(ARRAY(select numero from telefone where pessoa = p.codigo), ';') as telefones");
        }

        sql
                .append(" FROM reposicao r")

                .append(" INNER JOIN cliente c ON c.codigo = r.cliente")
                .append(" INNER JOIN pessoa p ON p.codigo = c.pessoa")
                .append(" INNER JOIN horarioturma ht ON ht.codigo = r.horarioturmaorigem AND ht.codigo = ?")

                .append(" WHERE r.dataorigem = ? AND r.horarioturma != r.horarioturmaorigem");

        if (!contar) {
            sql.append(" ORDER BY p.nome");
        }

        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setInt(1, codigoHorarioTurma);
        stm.setDate(2, Uteis.getDataJDBC(data));
        return stm.executeQuery();
    }

    private List<AgendadoJSON> popularAlunosTurma(ResultSet rsAlunosTurma) throws SQLException {
        final List<AgendadoJSON> alunos = new ArrayList<AgendadoJSON>();
        while (rsAlunosTurma.next()) {
            AgendadoJSON aluno = new AgendadoJSON();
            aluno.setUrlFoto(Uteis.getPaintFotoDaNuvem(rsAlunosTurma.getString("fotokey")));
            aluno.setNome(rsAlunosTurma.getString("nome"));
            aluno.setMatricula(rsAlunosTurma.getString("matricula"));
            aluno.setTelefones(rsAlunosTurma.getString("telefones"));
            aluno.setFim(rsAlunosTurma.getString("horafinal"));
            aluno.setInicio(rsAlunosTurma.getString("horainicial"));

            try {
                // quando existe presenca
                aluno.setConfirmado(rsAlunosTurma.getInt("quantidadePresenca") > 0);
            } catch (Exception e) {
                try {
                    // quando eh reposicao
                    aluno.setConfirmado(rsAlunosTurma.getBoolean("confirmadoreposicao"));
                } catch (Exception e1) {
                    // ignora...
                }
            }

            aluno.setCodigoCliente(rsAlunosTurma.getInt("codigocliente"));
            aluno.setCodigoPessoa(rsAlunosTurma.getInt("codigopessoa"));
            aluno.setCodigoContrato(rsAlunosTurma.getInt("contrato"));
            alunos.add(aluno);
        }

        return alunos;
    }

    private ResultSet getResultSetConsultarAlunosAulasNaoColetivas(Date data, Integer codigoHorarioTurma, Boolean contar) throws Exception {
        final StringBuilder sql = new StringBuilder("SELECT ");

        if (contar) {
            sql.append("COUNT(1) as total");
        } else {
            sql
                    .append("p.fotokey, p.codigo as codigopessoa, c.codigo as codigocliente,c.matricula,")
                    .append("m.codigo as codigoagendamento, ht.horainicial, ht.horafinal, p.nome, m.contrato,")
                    .append("ARRAY_TO_STRING(ARRAY(select numero from telefone where pessoa = p.codigo), ';') as telefones,")
                    .append("(SELECT COUNT(1) FROM presenca pre WHERE pre.dadosturma = m.codigo AND pre.datapresenca = ?) as quantidadePresenca");
        }

        sql
                .append(" FROM matriculaalunohorarioturma m")

                .append(" INNER JOIN horarioturma ht ON ht.codigo = m.horarioturma AND ht.codigo = ?")
                .append(" INNER JOIN pessoa p ON p.codigo = m.pessoa")
                .append(" INNER JOIN cliente c ON c.pessoa = p.codigo")

                .append(" WHERE ? BETWEEN m.datainicio AND m.datafim");

        if (!contar) {
            sql.append(" ORDER BY p.nome");
        }

        int index = 1;

        PreparedStatement stm = con.prepareStatement(sql.toString());
        if (!contar) {
            stm.setDate(index++, Uteis.getDataJDBC(data));
        }

        stm.setInt(index++, codigoHorarioTurma);
        stm.setDate(index++, Uteis.getDataJDBC(data));
        return stm.executeQuery();
    }

    private void fecharResultSetQuietly(ResultSet resultSet) {
        try {
            if (resultSet != null && !resultSet.isClosed()) {
                resultSet.close();
            }
        } catch (SQLException e) {
            // se nao for possivel fechar alguma stream entao apenas ignora a mesma
            Uteis.logar(e, AgendaModoBDServiceImpl.class);
        }
    }

    private ResultSet getResultSetConsultarAlunosRealocados(Date data, Integer codigoHorarioTurma, Boolean contar) throws Exception {
        final StringBuilder sql = new StringBuilder("SELECT ");

        if (contar) {
            sql.append("COUNT(1) as total");
        } else {
            sql
                    .append("m2.contrato as codigocontrato, p.fotokey, p.codigo as codigopessoa, c.codigo as codigocliente,c.matricula,")
                    .append("ht.horainicial, ht.horafinal, p.nome, r.datapresenca IS NOT NULL AS confirmadoreposicao,")
                    .append("ARRAY_TO_STRING(ARRAY(select numero from telefone where pessoa = p.codigo), ';') as telefones");
        }

        sql.append(" FROM reposicao r");
        sql.append(" INNER JOIN cliente c ON c.codigo = r.cliente");
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = r.horarioturma AND ht.codigo = ?");
        if (!contar) {
            sql.append(" INNER JOIN matriculaalunohorarioturma m2 ON ht.codigo = m2.horarioturma AND p.codigo = m2.pessoa ");
        }
        sql.append(" WHERE r.datareposicao = ?");
        sql.append(" and (c.pessoa not in ( ");
        sql.append(" select pessoa from matriculaalunohorarioturma m ");
        sql.append(" where m.horarioturma = r.horarioturma ");
        sql.append(" and r.datareposicao between m.datainicio and m.datafim) ");
        sql.append(" or exists (select codigo from auladesmarcada a where cliente = c.codigo and horarioturma = ht.codigo and a.dataorigem = r.datareposicao))");
        if (!contar) {
            sql.append(" AND (m2.contrato NOT IN (SELECT a2.contrato FROM auladesmarcada a2 WHERE cliente = c.codigo AND horarioturma = ht.codigo AND a2.dataorigem = r.datareposicao)) ");
            sql.append(" ORDER BY p.nome");
        }

        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setInt(1, codigoHorarioTurma);
        stm.setDate(2, Uteis.getDataJDBC(data));
        return stm.executeQuery();
    }

    public static AgendaTotalJSON montarDadosAgenda(ResultSet rs) throws Exception{
        AgendaTotalJSON item = new AgendaTotalJSON();
        item.setEmpresa(rs.getInt("empresa"));
        item.setCodigoTurma(rs.getInt("turma"));
        item.setAulaCheia(rs.getBoolean("aulaColetiva"));
        try{
            item.setNivel(rs.getString("descricao"));
        }catch(Exception ignored){

        }
        item.setInicio(rs.getString("horainicial"));
        item.setFim(rs.getString("horafinal"));
        item.setTitulo(rs.getString("identificador"));
        item.setTipo(rs.getString("nomemodalidade"));
        item.setCodigoTipo(rs.getInt("modalidade"));
        item.setCodigoLocal(rs.getInt("ambiente"));
        item.setNrVagas(rs.getInt("nrmaximoaluno"));
        item.setResponsavel(rs.getString("nomeprofessor"));
        item.setCodigoResponsavel(rs.getInt("professor"));
        item.setId(String.valueOf(rs.getInt("horarioturma")));
        item.setFimVigencia(rs.getDate("datafim"));
        item.setInicioVigencia(rs.getDate("datainicio"));
        item.setDiaSemana(rs.getString("diasemana"));
        item.setCodigoContrato(rs.getInt("contrato"));
        try {
            String imageUrl = !UteisValidacao.emptyString(rs.getString("fotokey"))
                    ? Aplicacao.obterUrlFotoDaNuvem(rs.getString("fotokey"))
                    : null;
            item.setImageUrl(imageUrl);
        }catch (Exception e){
        }
        try{
            item.setUrlVideoYoutube(rs.getString("urlVideoYoutube"));
        }catch (Exception e){
        }

        try {
            item.setJaMarcouEuQuero(rs.getBoolean("jaMarcouEuQuero"));
        } catch (Exception e) {
        }
        try {
            item.setOcupacao(rs.getInt("ocupacao"));
        } catch (Exception e) {
            item.setOcupacao(0);
        }
        try {
            item.setLocal(rs.getString("nomeambiente"));
        } catch (Exception e) {
        }
        try {
            final String fotoKey = rs.getString("fotoProfessor");
            if (!UteisValidacao.emptyString(fotoKey)) {
                item.setFotoProfessor(Aplicacao.obterUrlFotoDaNuvem(fotoKey));
            }
            final String fotoModalidade = rs.getString("fotoModalidade");
            if (!UteisValidacao.emptyString(fotoModalidade)) {
                item.setFotoModalidade(String.format("%s/%s", new Object[]{
                        Aplicacao.getProp(Aplicacao.urlFotosNuvem), fotoModalidade}));
            }
        } catch (Exception e) {
        }
        return item;
    }

    private String sqlConsultaAulasDesmarcadasSemReposicao(Integer matricula, EmpresaVO empresaVO) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT t.codigo as turma, t.empresa, t.idademinima, t.idademinimameses, t.idademaxima, t.idademaximameses, \n");
        sql.append("t.identificador,t.aulaColetiva, m.codigo as modalidade, m.nome as nomemodalidade,  \n");
        sql.append("nt.descricao, CASE WHEN (con.vigenciade >= '"+Uteis.getDataJDBC(Calendario.hoje())+"' and ad.contratoanterior is not null) THEN ad.contratoanterior ELSE ad.contrato END as contrato, \n");
        sql.append("ht.diasemana,ht.professor,p.nome as nomeprofessor, ht.ambiente, a.descricao as nomeambiente, con.vigenciaateajustada, \n");
        sql.append(" ht.horainicial, ht.horafinal, ht.nrmaximoaluno, ht.codigo as horarioturma,null as datainicio,null as datafim, con.vigenciade,null as codigomatriculaalunohorarioturma, \n");
        sql.append("p.fotokey as fotoProfessor, \n");
        sql.append("exists(select codigo from demandahorarioturma dht where dht.cliente = sc.codigocliente AND dht.horarioturma = ht.codigo) as jaMarcouEuQuero, \n");
        sql.append("(select count(*) from matriculaalunohorarioturma mat2 where mat2.horarioturma = ht.codigo and current_date between mat2.datainicio and mat2.datafim) as ocupacao \n");
        sql.append(",ad.dataorigem \n");
        sql.append("FROM auladesmarcada ad \n");
        sql.append("INNER JOIN situacaoclientesinteticodw sc ON ad.cliente= sc.codigocliente \n");
        sql.append("INNER JOIN horarioturma ht ON ht.codigo = ad.horarioturma \n");
        sql.append("INNER JOIN colaborador c ON c.codigo = ht.professor \n");
        sql.append("INNER JOIN pessoa p ON p.codigo = c.pessoa \n");
        sql.append("INNER JOIN turma t ON t.codigo = ht.turma \n");
        sql.append("INNER JOIN nivelturma nt ON nt.codigo = ht.nivelturma \n");
        sql.append("INNER JOIN modalidade m ON t.modalidade = m.codigo \n");
        sql.append("INNER JOIN contrato con ON con.codigo = ad.contrato and con.situacao = 'AT' \n");
        sql.append("INNER JOIN ambiente a ON a.codigo = ht.ambiente  \n");
        sql.append("WHERE sc.matricula = ").append(matricula).append(" AND (con.situacao = 'AT' or\n");
        sql.append("ad.contrato = sc.codigocontrato");
        if (empresaVO.isAdicionarAulasDesmarcadasContratoAnterior()) {
            sql.append(" or ad.contratoanterior = sc.codigocontrato\n");
        }
        sql.append(") and ad.datareposicao IS null and permiteReporAulaDesmarcada = true \n");
        sql.append("and m.utilizarturma = true \n");

        return sql.toString();
    }

    private EmpresaVO consultarEmpresa(Integer empresa) throws Exception{
        EmpresaVO empresaVO = new EmpresaVO();
        StringBuilder sql = new StringBuilder("select em.codigo, tempoAposFaltaReposicao, permMarcarAulaFeriado, em.nome, horaAberturaFeriado,\n");
        sql.append(" adicionarAulasDesmarcadasContratoAnterior, horaFechamentoFeriado, ");
        sql.append(" e.codigo as estado, c.codigo as cidade ");
        sql.append(" from empresa em ");
        sql.append(" left join estado e on e.codigo = em.estado ");
        sql.append(" left join cidade c on c.codigo = em.cidade ");
        sql.append(" where em.codigo = ").append(empresa);
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)){
            while(rs.next()){
                empresaVO.setCodigo(rs.getInt("codigo"));
                empresaVO.setTempoAposFaltaReposicao(rs.getInt("tempoAposFaltaReposicao"));
                empresaVO.setPermMarcarAulaFeriado(rs.getBoolean("permMarcarAulaFeriado"));
                empresaVO.setAdicionarAulasDesmarcadasContratoAnterior(rs.getBoolean("adicionarAulasDesmarcadasContratoAnterior"));
                empresaVO.setNome(rs.getString("nome"));
                empresaVO.setHoraAberturaFeriado(rs.getString("horaAberturaFeriado"));
                empresaVO.setHoraFechamentoFeriado(rs.getString("horaFechamentoFeriado"));
                empresaVO.setCidade(new CidadeVO());
                empresaVO.getCidade().setCodigo(rs.getInt("cidade"));
                empresaVO.setEstado(new EstadoVO());
                empresaVO.getEstado().setCodigo(rs.getInt("estado"));
            }
        }
        return empresaVO;
    }

    public HistoricoProfessorTurmaVO professorDaEpoca(Date dia, Integer codigoHorarioTurma,
                                                      Map<Integer, List<HistoricoProfessorTurmaVO>> mapaHistoricoProfessores) {

        //se a aula ainda vai acontecer, não faz sentido usar o historico
        if (Calendario.maiorOuIgual(dia, Calendario.hoje())
                || UteisValidacao.emptyList(mapaHistoricoProfessores.get(codigoHorarioTurma))) {
            return null;
        }
        List<HistoricoProfessorTurmaVO> historico = Ordenacao.ordenarLista(mapaHistoricoProfessores.get(codigoHorarioTurma), "inicio");
        Collections.reverse(historico);
        for (HistoricoProfessorTurmaVO hst : historico) {
            if ((hst.getFim() == null && Calendario.menorOuIgual(hst.getInicio(), dia))
                    || (Calendario.menorOuIgual(hst.getInicio(), dia) && Calendario.maiorOuIgual(hst.getFim(), dia))) {
                return hst;
            }
        }
        return null;
    }



    public Map<String, List<AgendaTotalJSON>> consultarHorariosTurmaParaAgenda(Date inicio, Date fim,
                                                                               Integer empresa, List<Integer> modalidades,
                                                                               boolean somenteColetivas, Integer codigoHorario,
                                                                               Integer matricula, boolean isApp,
                                                                               boolean isAgendaOnline, String search, String situacaoHorario) throws Exception{

        Map<String,List<AgendaTotalJSON>> mapa = new HashMap<String,List<AgendaTotalJSON>>();
        StringBuilder sql = new StringBuilder();
        String validaIdade = "";
        sql.append(" SELECT t.permitirAulaExperimental, t.permiteAlunoOutraEmpresa, t.naovalidarmodalidadecontrato, ");
        sql.append(" (SELECT COUNT(*) FROM alunohorarioturma WHERE horarioturma = ht.codigo AND dia = '").append(Uteis.getDataJDBC(inicio)).append("' AND aulaexperimental is true) AS ocupacaoExperimental, ");
        if (somenteColetivas) {
            sql.append(" (SELECT COUNT(*) FROM alunohorarioturma WHERE horarioturma = ht.codigo AND dia = '").append(Uteis.getDataJDBC(inicio)).append("') AS ocupacao, ");
        } else {
            sql.append(" t.ocupacao, ");
        }
        sql.append("t.codigo as turma, t.empresa,t.datainicialvigencia, t.datafinalvigencia,t.mensagem,t.meta,t.bonificacao,t.pontosbonus, t.bloquearmatriculasacimalimite, \n");
        sql.append(" t.idademinima as idademinima, t.idademinimameses as idademinimameses, t.idademaxima as idademaxima, t.idademaximameses as idademaximameses, \n");
        sql.append(" t.tipotolerancia, t.identificador,t.aulaColetiva, t.integracaospivi, t.urlVideoYoutube,m.codigo as modalidade, m.nome as nomemodalidade, p.fotokey as professorfotokey, m.fotokey as fotoModalidade, p.codigo as professorCodigoPessoa,\n");
        sql.append(" ht.diasemana, ht.toleranciaentradaminutos,ht.professor,p.nome as nomeprofessor, ht.ambiente,a.descricao as nomeambiente,  \n");
        sql.append(" ht.situacao, ht.dataentrouturma, ht.datasaiuturma, t.cor, \n");
        sql.append(" ht.horainicial, ht.horafinal, ht.nrmaximoaluno, ht.codigo as horarioturma, n.codigo as nivel, n.descricao as nomenivel \n");
        sql.append(",exists(select dht.codigo from demandahorarioturma dht inner join cliente cli on cli.codigo = dht.cliente where cli.codigomatricula = ");
        sql.append(matricula);
        sql.append(" AND dht.horarioturma = ht.codigo) as jaMarcouEuQuero, t.validarrestricoesmarcacao, t.minutosAposInicioApp, t.fotokey, t.visualizarProdutosGympass, t.visualizarProdutosTotalPass, t.tiporeservaequipamento, t.mapaequipamentos, ");

        if (isAgendaOnline) {
            sql.append("\n, prod.valorfinal as valorproduto, prod.codigo as codigoProduto, ");
        }
        sql.append(" ht.qtdemaximaalunoexperimental as qtdemaximaalunoexperimental  ");
        sql.append(" FROM turma t \n");
        sql.append(" INNER JOIN empresa emp ON t.empresa = emp.codigo \n");
        sql.append(" INNER JOIN modalidade m ON m.codigo = t.modalidade \n");
        if(modalidades != null && !modalidades.isEmpty()){
            sql.append(" AND m.codigo IN (");
            String mods = "";
            for(Integer mod : modalidades){
                mods = mods+","+mod;
            }
            sql.append(mods.replaceFirst(",", "")).append(") \n");
        }
        sql.append(" INNER JOIN horarioturma ht ON ht.turma = t.codigo \n");

        if (isAgendaOnline) {
            sql.append(" and ht.situacao = 'AT' ");
        }
        sql.append(" INNER JOIN ambiente a ON ht.ambiente = a.codigo \n");
        sql.append(" INNER JOIN nivelturma n ON ht.nivelturma = n.codigo \n");
        sql.append(" INNER JOIN colaborador c ON c.codigo = ht.professor \n");
        sql.append(" INNER JOIN pessoa p ON c.pessoa = p.codigo \n");

        if (isAgendaOnline) {
            sql.append(" INNER JOIN aulavendasonline avo ON ht.turma = avo.codigoturma and avo.ativo and avo.data_exclusao is null ");
            sql.append(" INNER JOIN produto prod ON prod.codigo = avo.codigoproduto ");
        }

        sql.append(" WHERE t.datafinalvigencia >= '").append(Uteis.getDataJDBC(inicio)).append("'");
        if (isApp) {
            sql.append(" AND m.utilizarturma is true ");
        }
        sql.append(" and emp.ativa \n");
        sql.append(somenteColetivas ? " and ht.situacao = 'AT' \n" : "");
        sql.append(" AND (t.usuariodesativou IS NULL OR t.datafinalvigencia >= '").append(Uteis.getDataJDBC(inicio)).append("' ) \n");
        if (situacaoHorario != null && !UteisValidacao.emptyString(situacaoHorario)) {
            if (situacaoHorario.contains("IN") && situacaoHorario.contains("AT")) {
                sql.append(" AND ht.situacao IN (").append(situacaoHorario).append(")\n");
            } else {
                if (situacaoHorario.contains("IN")) {
                    sql.append(" AND ht.situacao IN (").append(situacaoHorario).append(") AND ht.datasaiuturma IS NOT NULL and ht.datasaiuturma < '").append(Calendario.hoje()).append("' \n");
                }
                if (situacaoHorario.contains("AT")) {
                    sql.append(" AND (ht.situacao = 'AT' or (ht.datasaiuturma is not null and ht.datasaiuturma >= '").append(Calendario.hoje()).append("' )) \n");
                }
            }
        } else {
            sql.append(" AND (ht.situacao = 'AT' or (ht.datasaiuturma is not null and ht.datasaiuturma >= '").append(Calendario.hoje()).append("' )) \n");
        }
        if(UteisValidacao.notEmptyNumber(codigoHorario)){
            sql.append(" AND ht.codigo = ").append(codigoHorario);
        }

        if(UteisValidacao.notEmptyNumber(empresa)){
            sql.append(" AND (t.empresa = ").append(empresa).append(" OR t.empresa IS NULL)");
        }

        if(somenteColetivas){
            sql.append(" AND t.aulaColetiva ");
        }

        if(isApp){
            sql.append(" AND ht.liberadoMarcacaoApp ");
        }

        if(!UteisValidacao.emptyString(search)){
            List<Integer> horarios = consultarHorariosTurmaPorNomeAlunoOuMatricula(search, inicio, fim);
            sql.append(" and (upper(p.nome) like '%").append(search.toUpperCase()).append("%' ");
            sql.append(" or upper(t.identificador) like '%").append(search.toUpperCase()).append("%' ");
            sql.append(" or upper(t.descricao) like '%").append(search.toUpperCase()).append("%' ");
            sql.append(" or upper(m.nome) like '%").append(search.toUpperCase()).append("%' ");
            sql.append(" or ht.codigo in(").append(Uteis.concatenarListaVirgula(horarios)).append(")) ");
        }

        if (matricula != null) {
            try (ResultSet dataNascimento = ConexaoZWServiceImpl.criarConsulta("select p.datanasc from pessoa as p inner join cliente as c on c.pessoa = p.codigo where c.codigomatricula = " + matricula, con)) {
                if (dataNascimento.next()) {
                    Date dataNasc = dataNascimento.getDate("datanasc");
                    if (dataNasc == null) {
                        dataNasc = Calendario.hoje();
                    }
                    String dataNascCalcular = Uteis.getDataAplicandoFormatacao(dataNasc, "yyyy-MM-dd");
                    validaIdade = Uteis.calculaIdadeComMeses(dataNascCalcular);
                }
            }
        }

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                if (matricula != null) {
                    String[] split = validaIdade.split("-");
                    int idadeAlunoAno = 0;
                    int idadeAlunoMes = 0;
                    if(isNotBlank(split[0])) {
                        idadeAlunoAno = Integer.parseInt(split[0]);
                        idadeAlunoMes = Integer.parseInt(split[1]);
                    }
                    boolean isIdadeAlunoValida = validaIdadeAluno(
                            idadeAlunoAno,
                            idadeAlunoMes,
                            rs.getInt("idademinima"),
                            rs.getInt("idademinimameses"),
                            rs.getInt("idademaxima"),
                            rs.getInt("idademaximameses")
                    );
                    if (!isIdadeAlunoValida) {
                        continue;
                    }
                }
                List<AgendaTotalJSON> lista = mapa.get(rs.getString("diasemana"));
                if (lista == null) {
                    lista = new ArrayList<AgendaTotalJSON>();
                    mapa.put(rs.getString("diasemana"), lista);
                }
                AgendaTotalJSON item = new AgendaTotalJSON();
                item.setOcupacao(rs.getInt("ocupacao"));
                item.setEmpresa(rs.getInt("empresa"));
                item.setAulaCheia(rs.getBoolean("aulaColetiva"));
                item.setIntegracaoSpivi(rs.getBoolean("integracaoSpivi"));
                item.setPermitirAulaExperimental(rs.getBoolean("permitirAulaExperimental"));
                item.setInicio(rs.getString("horainicial"));
                item.setFim(rs.getString("horafinal"));
                item.setTitulo(rs.getString("identificador"));
                item.setTipo(rs.getString("nomemodalidade"));
                item.setCodigoTipo(rs.getInt("modalidade"));
                item.setLocal(rs.getString("nomeambiente"));
                item.setCodigoLocal(rs.getInt("ambiente"));
                item.setNrVagas(rs.getInt("nrmaximoaluno"));
                item.setResponsavel(rs.getString("nomeprofessor"));
                item.setCodigoResponsavel(rs.getInt("professor"));
                item.setCodigoPessoaResponsavel(rs.getInt("professorCodigoPessoa"));
                item.setId(String.valueOf(rs.getInt("horarioturma")));
                item.setCodigoNivel(rs.getInt("nivel"));
                item.setNivel(rs.getString("nomenivel"));
                item.setInicioVigencia(rs.getDate("datainicialvigencia"));
                item.setFimVigencia(rs.getDate("datafinalvigencia"));
                item.setMensagem(rs.getString("mensagem"));
                item.setMeta(rs.getDouble("meta"));
                item.setTolerancia(rs.getInt("toleranciaentradaminutos"));
                int tipotolerancia = rs.getInt("tipotolerancia");
                item.setTipoTolerancia(UteisValidacao.emptyNumber(tipotolerancia) ? TipoToleranciaAulaEnum.APOS_INICIO.getCodigo() : tipotolerancia);
                item.setTolerancia(rs.getInt("toleranciaentradaminutos"));
                item.setPontosBonus(rs.getInt("pontosbonus"));
                item.setBonificacao(rs.getDouble("bonificacao"));
                item.setJaMarcouEuQuero(rs.getBoolean("jamarcoueuquero"));
                item.setValidarRestricoesMarcacao(rs.getBoolean("validarrestricoesmarcacao"));
                item.setFotoProfessor(rs.getString("professorfotokey"));
                final String fotoModalidade = rs.getString("fotoModalidade");
                if (!UteisValidacao.emptyString(fotoModalidade)) {
                    item.setFotoModalidade(Aplicacao.obterUrlFotoDaNuvem( fotoModalidade));
                }
                item.setToleranciaApresentarApp(rs.getInt("minutosAposInicioApp"));
                item.setPermiteAlunoOutraEmpresa(rs.getBoolean("PermiteAlunoOutraEmpresa"));
                item.setCodigoTurma(rs.getInt("turma"));
                item.setSituacao(rs.getString("situacao"));
                item.setDataEntrouTurmaDt(rs.getTimestamp("dataentrouturma"));
                item.setDataSaiuTurmaDt(rs.getTimestamp("datasaiuturma"));
                item.setCor(rs.getString("cor"));
                item.setVisualizarProdutosGympass(rs.getBoolean("visualizarProdutosGympass"));
                item.setVisualizarProdutosTotalPass(rs.getBoolean("visualizarProdutosTotalPass"));
                item.setTipoReservaEquipamento(rs.getString("tiporeservaequipamento"));
                item.setMapaEquipamentos(rs.getString("mapaequipamentos"));
                String imageUrl = !UteisValidacao.emptyString(rs.getString("fotokey"))
                        ? Aplicacao.obterUrlFotoDaNuvem(rs.getString("fotokey")) : null;
                item.setImageUrl(imageUrl);
                item.setIdadeMinima(rs.getInt("idademinima"));
                item.setIdadeMaxima(rs.getInt("idademaxima"));
                item.setIdadeMinimaMeses(rs.getInt("idademinimameses"));
                item.setIdadeMaximaMeses(rs.getInt("idademaximameses"));
                item.setQtdeMaximaAlunoExperimental(rs.getInt("qtdemaximaalunoexperimental"));
                item.setNrVagasPreenchidasExperimental(rs.getInt("ocupacaoExperimental"));
                try{
                    item.setUrlVideoYoutube(rs.getString("urlVideoYoutube"));
                }catch (Exception e){
                }

                if (isAgendaOnline) {
                    item.setValorProduto(rs.getDouble("valorproduto"));
                    item.setCodigoProduto(rs.getInt("codigoProduto"));
                }

                try{
                    item.setNaoValidarModalidadeContrato(rs.getBoolean("naoValidarModalidadeContrato"));
                }catch (Exception ignore){}

                try{
                    item.setBloquearMatriculasAcimaLimite(rs.getBoolean("bloquearmatriculasacimalimite"));
                }catch (Exception ignore){}

                lista.add(item);
            }
        }
        return mapa;
    }

    public Map<String, List<AgendaTotalJSON>> consultarHorariosTurmaReposicaoParaAgenda(Date inicio, Date fim,
                                                                                        Integer empresa, List<Integer> modalidades,
                                                                                        boolean somenteColetivas, Integer codigoHorario,
                                                                                        Integer matricula, boolean isApp,
                                                                                        boolean isAgendaOnline, String search, String situacaoHorario) throws Exception{

        Map<String,List<AgendaTotalJSON>> mapa = new HashMap<String,List<AgendaTotalJSON>>();
        StringBuilder sql = new StringBuilder();
        String validaIdade = "";
        sql.append(" SELECT t.permitirAulaExperimental, t.permiteAlunoOutraEmpresa, t.naovalidarmodalidadecontrato, ");
        if (somenteColetivas) {
            sql.append(" (SELECT COUNT(*) FROM alunohorarioturma WHERE horarioturma = ht.codigo AND dia = '").append(Uteis.getDataJDBC(inicio)).append("') AS ocupacao, ");
        } else {
            sql.append(" t.ocupacao, ");
        }
        sql.append("t.codigo as turma, t.empresa,t.datainicialvigencia, t.datafinalvigencia,t.mensagem,t.meta,t.bonificacao,t.pontosbonus, \n");
        sql.append(" t.idademinima as idademinima, t.idademinimameses as idademinimameses, t.idademaxima as idademaxima, t.idademaximameses as idademaximameses, \n");
        sql.append(" t.tipotolerancia, t.identificador,t.aulaColetiva, t.integracaospivi, t.urlVideoYoutube,m.codigo as modalidade, m.nome as nomemodalidade, p.fotokey as professorfotokey, m.fotokey as fotoModalidade, p.codigo as professorCodigoPessoa,\n");
        sql.append(" ht.diasemana, ht.toleranciaentradaminutos,ht.professor,p.nome as nomeprofessor, ht.ambiente,a.descricao as nomeambiente,  \n");
        sql.append(" ht.situacao, ht.dataentrouturma, ht.datasaiuturma, t.cor, \n");
        sql.append(" ht.horainicial, ht.horafinal, ht.nrmaximoaluno, ht.codigo as horarioturma, n.codigo as nivel, n.descricao as nomenivel \n");
        sql.append(",exists(select dht.codigo from demandahorarioturma dht inner join cliente cli on cli.codigo = dht.cliente where cli.codigomatricula = ");
        sql.append(matricula);
        sql.append(" AND dht.horarioturma = ht.codigo) as jaMarcouEuQuero, t.validarrestricoesmarcacao, t.minutosAposInicioApp, t.fotokey, t.visualizarProdutosGympass, t.visualizarProdutosTotalPass ");

        if (isAgendaOnline) {
            sql.append("\n, prod.valorfinal as valorproduto, prod.codigo as codigoProduto ");
        }
        sql.append(" FROM reposicao r \n");
        sql.append(" INNER JOIN turma t ON t.codigo = r.turmadestino   \n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = r.horarioturma  \n");
        sql.append(" INNER JOIN empresa emp ON t.empresa = emp.codigo \n");
        sql.append(" INNER JOIN modalidade m ON m.codigo = t.modalidade \n");
        if(modalidades != null && !modalidades.isEmpty()){
            sql.append(" AND m.codigo IN (");
            String mods = "";
            for(Integer mod : modalidades){
                mods = mods+","+mod;
            }
            sql.append(mods.replaceFirst(",", "")).append(") \n");
        }

        if (isAgendaOnline) {
            sql.append(" and ht.situacao = 'AT' ");
        }
        sql.append(" INNER JOIN ambiente a ON ht.ambiente = a.codigo \n");
        sql.append(" INNER JOIN nivelturma n ON ht.nivelturma = n.codigo \n");
        sql.append(" INNER JOIN colaborador c ON c.codigo = ht.professor \n");
        sql.append(" INNER JOIN pessoa p ON c.pessoa = p.codigo \n");
        sql.append(" INNER JOIN cliente ON cliente.codigo = r.cliente  \n");
        sql.append(" INNER JOIN pessoa p2 ON p2.codigo = cliente.pessoa \n");

        if (isAgendaOnline) {
            sql.append(" INNER JOIN aulavendasonline avo ON ht.turma = avo.codigoturma and avo.ativo and avo.data_exclusao is null ");
            sql.append(" INNER JOIN produto prod ON prod.codigo = avo.codigoproduto ");
        }

        sql.append(" WHERE t.datafinalvigencia >= '").append(Uteis.getDataJDBC(inicio)).append("'");
        if (isApp) {
            sql.append(" AND m.utilizarturma is true ");
        }
        sql.append(" and emp.ativa \n");
        sql.append(somenteColetivas ? " and ht.situacao = 'AT' \n" : "");
        sql.append(" AND (t.usuariodesativou IS NULL OR t.datafinalvigencia >= '").append(Uteis.getDataJDBC(inicio)).append("' ) \n");
        if (situacaoHorario != null && !UteisValidacao.emptyString(situacaoHorario)) {
            if (situacaoHorario.contains("IN") && situacaoHorario.contains("AT")) {
                sql.append(" AND ht.situacao IN (").append(situacaoHorario).append(")\n");
            } else {
                if (situacaoHorario.contains("IN")) {
                    sql.append(" AND ht.situacao IN (").append(situacaoHorario).append(") AND ht.datasaiuturma IS NOT NULL and ht.datasaiuturma < '").append(Calendario.hoje()).append("' \n");
                }
                if (situacaoHorario.contains("AT")) {
                    sql.append(" AND (ht.situacao = 'AT' or (ht.datasaiuturma is not null and ht.datasaiuturma >= '").append(Calendario.hoje()).append("' )) \n");
                }
            }
        } else {
            sql.append(" AND (ht.situacao = 'AT' or (ht.datasaiuturma is not null and ht.datasaiuturma >= '").append(Calendario.hoje()).append("' )) \n");
        }
        if(UteisValidacao.notEmptyNumber(codigoHorario)){
            sql.append(" AND ht.codigo = ").append(codigoHorario);
        }

        if(UteisValidacao.notEmptyNumber(empresa)){
            sql.append(" AND (t.empresa = ").append(empresa).append(" OR t.empresa IS NULL)");
        }

        if(somenteColetivas){
            sql.append(" AND t.aulaColetiva ");
        }

        if(isApp){
            sql.append(" AND ht.liberadoMarcacaoApp ");
        }

        if(!UteisValidacao.emptyString(search)){
            List<Integer> horarios = consultarHorariosTurmaPorNomeAlunoOuMatricula(search, inicio, fim);
            sql.append(" and (upper(p2.nome) like '%").append(search.toUpperCase()).append("%' ");
            sql.append(" or upper(t.identificador) like '%").append(search.toUpperCase()).append("%' ");
            sql.append(" or upper(t.descricao) like '%").append(search.toUpperCase()).append("%' ");
            sql.append(" or upper(m.nome) like '%").append(search.toUpperCase()).append("%' ");
            sql.append(" or ht.codigo in(").append(Uteis.concatenarListaVirgula(horarios)).append(")) ");
            sql.append(" AND (t.usuariodesativou IS NULL OR t.datafinalvigencia >= '").append(Uteis.getDataJDBC(inicio)).append("' )");
            sql.append(" AND r.datareposicao = '").append(Uteis.getDataJDBC(inicio)).append("'");
            sql.append(" and ht.codigo not in (select cliente  from auladesmarcada a where horarioturma = ht.codigo and dataorigem = '").append(Uteis.getDataJDBC(inicio)).append("' )");
        }

        if (matricula != null) {
            try (ResultSet dataNascimento = ConexaoZWServiceImpl.criarConsulta("select p.datanasc from pessoa as p inner join cliente as c on c.pessoa = p.codigo where c.codigomatricula = " + matricula, con)) {
                if (dataNascimento.next()) {
                    Date dataNasc = dataNascimento.getDate("datanasc");
                    if (dataNasc == null) {
                        dataNasc = Calendario.hoje();
                    }
                    String dataNascCalcular = Uteis.getDataAplicandoFormatacao(dataNasc, "yyyy-MM-dd");
                    validaIdade = Uteis.calculaIdadeComMeses(dataNascCalcular);
                }
            }
        }

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                if (matricula != null) {
                    String[] split = validaIdade.split("-");
                    int idadeAlunoAno = 0;
                    int idadeAlunoMes = 0;
                    if(isNotBlank(split[0])) {
                        idadeAlunoAno = Integer.parseInt(split[0]);
                        idadeAlunoMes = Integer.parseInt(split[1]);
                    }
                    boolean isIdadeAlunoValida = validaIdadeAluno(
                            idadeAlunoAno,
                            idadeAlunoMes,
                            rs.getInt("idademinima"),
                            rs.getInt("idademinimameses"),
                            rs.getInt("idademaxima"),
                            rs.getInt("idademaximameses")
                    );
                    if (!isIdadeAlunoValida) {
                        continue;
                    }
                }
                List<AgendaTotalJSON> lista = mapa.get(rs.getString("diasemana"));
                if (lista == null) {
                    lista = new ArrayList<AgendaTotalJSON>();
                    mapa.put(rs.getString("diasemana"), lista);
                }
                AgendaTotalJSON item = new AgendaTotalJSON();
                item.setOcupacao(rs.getInt("ocupacao"));
                item.setEmpresa(rs.getInt("empresa"));
                item.setAulaCheia(rs.getBoolean("aulaColetiva"));
                item.setIntegracaoSpivi(rs.getBoolean("integracaoSpivi"));
                item.setPermitirAulaExperimental(rs.getBoolean("permitirAulaExperimental"));
                item.setInicio(rs.getString("horainicial"));
                item.setFim(rs.getString("horafinal"));
                item.setTitulo(rs.getString("identificador"));
                item.setTipo(rs.getString("nomemodalidade"));
                item.setCodigoTipo(rs.getInt("modalidade"));
                item.setLocal(rs.getString("nomeambiente"));
                item.setCodigoLocal(rs.getInt("ambiente"));
                item.setNrVagas(rs.getInt("nrmaximoaluno"));
                item.setResponsavel(rs.getString("nomeprofessor"));
                item.setCodigoResponsavel(rs.getInt("professor"));
                item.setCodigoPessoaResponsavel(rs.getInt("professorCodigoPessoa"));
                item.setId(String.valueOf(rs.getInt("horarioturma")));
                item.setCodigoNivel(rs.getInt("nivel"));
                item.setNivel(rs.getString("nomenivel"));
                item.setInicioVigencia(rs.getDate("datainicialvigencia"));
                item.setFimVigencia(rs.getDate("datafinalvigencia"));
                item.setMensagem(rs.getString("mensagem"));
                item.setMeta(rs.getDouble("meta"));
                item.setTolerancia(rs.getInt("toleranciaentradaminutos"));
                int tipotolerancia = rs.getInt("tipotolerancia");
                item.setTipoTolerancia(UteisValidacao.emptyNumber(tipotolerancia) ? TipoToleranciaAulaEnum.APOS_INICIO.getCodigo() : tipotolerancia);
                item.setTolerancia(rs.getInt("toleranciaentradaminutos"));
                item.setPontosBonus(rs.getInt("pontosbonus"));
                item.setBonificacao(rs.getDouble("bonificacao"));
                item.setJaMarcouEuQuero(rs.getBoolean("jamarcoueuquero"));
                item.setValidarRestricoesMarcacao(rs.getBoolean("validarrestricoesmarcacao"));
                item.setFotoProfessor(rs.getString("professorfotokey"));
                final String fotoModalidade = rs.getString("fotoModalidade");
                if (!UteisValidacao.emptyString(fotoModalidade)) {
                    item.setFotoModalidade(Aplicacao.obterUrlFotoDaNuvem( fotoModalidade));
                }
                item.setToleranciaApresentarApp(rs.getInt("minutosAposInicioApp"));
                item.setPermiteAlunoOutraEmpresa(rs.getBoolean("PermiteAlunoOutraEmpresa"));
                item.setCodigoTurma(rs.getInt("turma"));
                item.setSituacao(rs.getString("situacao"));
                item.setDataEntrouTurmaDt(rs.getTimestamp("dataentrouturma"));
                item.setDataSaiuTurmaDt(rs.getTimestamp("datasaiuturma"));
                item.setCor(rs.getString("cor"));
                item.setVisualizarProdutosGympass(rs.getBoolean("visualizarProdutosGympass"));
                item.setVisualizarProdutosTotalPass(rs.getBoolean("visualizarProdutosTotalPass"));
                String imageUrl = !UteisValidacao.emptyString(rs.getString("fotokey"))
                        ? Aplicacao.obterUrlFotoDaNuvem(rs.getString("fotokey")) : null;
                item.setImageUrl(imageUrl);

                try{
                    item.setUrlVideoYoutube(rs.getString("urlVideoYoutube"));
                }catch (Exception e){
                }

                if (isAgendaOnline) {
                    item.setValorProduto(rs.getDouble("valorproduto"));
                    item.setCodigoProduto(rs.getInt("codigoProduto"));
                }

                try{
                    item.setNaoValidarModalidadeContrato(rs.getBoolean("naoValidarModalidadeContrato"));
                }catch (Exception ignore){}

                lista.add(item);
            }
        }
        return mapa;
    }
    private List<Integer> consultarHorariosTurmaPorNomeAlunoOuMatricula(String param, Date inicio, Date fim) throws Exception{
        Set<Integer> horarios = new HashSet<>();
        horarios.add(0);
        boolean matricula = Uteis.isNumeroValido(param);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT m.horarioturma FROM matriculaalunohorarioturma m ");
        if(matricula){
            sql.append("INNER JOIN cliente c ON c.pessoa = m.pessoa ");
            sql.append("WHERE c.codigomatricula = ").append(param);
        } else {
            sql.append("INNER JOIN pessoa p ON p.codigo = m.pessoa ");
            sql.append("WHERE UPPER(p.nome) LIKE '%").append(param.toUpperCase()).append("%' ");
        }
        sql.append(" AND '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd"));
        sql.append("' BETWEEN m.datainicio AND m.datafim");

        try(ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)){
            while(rs.next()){
                horarios.add(rs.getInt("horarioturma"));
            }

            sql = new StringBuilder();
            sql.append("SELECT DISTINCT a.horarioturma FROM alunohorarioturma a ");
            sql.append("INNER JOIN cliente c ON c.codigo = a.cliente ");
            if(matricula){
                sql.append("WHERE c.codigomatricula = ").append(param);
            } else {
                sql.append("INNER JOIN pessoa p ON p.codigo = c.pessoa ");
                sql.append("WHERE UPPER(p.nome) LIKE '%").append(param.toUpperCase()).append("%' ");
            }
            sql.append(" AND a.dia");
            sql.append(" BETWEEN '").append(Uteis.getDataAplicandoFormatacao(inicio, "yyyy-MM-dd"));
            sql.append("' AND '").append(Uteis.getDataAplicandoFormatacao(fim, "yyyy-MM-dd")).append("' ");

            try (ResultSet rsA = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
                while (rsA.next()) {
                    horarios.add(rsA.getInt("horarioturma"));
                }
            }
        }
        return new ArrayList<>(horarios);
    }


    public Map<Integer, List<HistoricoProfessorTurmaVO>> montarHistoricoProfessor(Date inicio) throws Exception {
        Map<Integer, List<HistoricoProfessorTurmaVO>> mapa = new HashMap<Integer, List<HistoricoProfessorTurmaVO>>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select fotokey, nome, horarioturma, professor, inicio, fim from historicoprofessorturma h ");
        sql.append(" inner join colaborador c on h.professor = c.codigo ");
        sql.append(" inner join pessoa p on p.codigo = c.pessoa ");
        sql.append(" where inicio::date >= '").append(Uteis.getDataJDBC(inicio)).append("' or fim::date >= '").append(Uteis.getDataJDBC(inicio)).append("' ");
        sql.append(" order by inicio ");

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                List<HistoricoProfessorTurmaVO> horarios = mapa.get(rs.getInt("horarioturma"));
                if (horarios == null) {
                    horarios = new ArrayList<>();
                    mapa.put(rs.getInt("horarioturma"), horarios);
                }
                HistoricoProfessorTurmaVO hist = new HistoricoProfessorTurmaVO();
                hist.setInicio(rs.getDate("inicio"));
                hist.setFim(rs.getDate("fim"));
                hist.setFotoKey(rs.getString("fotokey"));
                hist.setProfessor(new ColaboradorVO());
                hist.getProfessor().setPessoa(new PessoaVO());
                hist.getProfessor().getPessoa().setNome(rs.getString("nome"));
                hist.getProfessor().setCodigo(rs.getInt("professor"));
                horarios.add(hist);
            }
        }

        return mapa;
    }

    public Map<Integer, List<Date>> getMapaAulasExcluidas() {
        return mapaAulasExcluidas;
    }

    public void setMapaAulasExcluidas(Map<Integer, List<Date>> mapaAulasExcluidas) {
        this.mapaAulasExcluidas = mapaAulasExcluidas;
    }

    public List<AgendadoTO> consultarAgendadosParaAgenda(Date inicio, Date fim, Integer empresa, Integer horarioTurma) throws Exception {
        List<AgendadoJSON> lista = new ArrayList<AgendadoJSON>();
        lista.addAll(consultarHorariosTurmaParaAgenda(inicio, empresa));
        lista.addAll(consultarAgendadosAulaColetiva(inicio, fim, empresa, horarioTurma));
        List<AgendadoTO> listaTO = new ArrayList<AgendadoTO>();
        for(AgendadoJSON j : lista){
            listaTO.add(new AgendadoTO(j));
        }
        return listaTO;
    }

    public List<AgendadoJSON> consultarAgendadosAulaColetiva(Date inicio, Date fim, Integer empresa, Integer horarioTurma) throws Exception{

        List<AgendadoJSON> lista = new ArrayList<AgendadoJSON>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ahtd.codigo as aulareposicao, aht.datalancamento, aht.autorizado, aht.autorizadoGestaoRede, aht.codAcessoAutorizado, aht.matriculaAutorizado, aht.passivo, aht.indicado, c.codigo as cliente, c.idselfloops, c.matricula, p.nome, p.codigo as pessoa, \n");
        sql.append(" t.codigo as turma, aht.codigo as alunohorarioturma, aht.aulaexperimental, aht.bookingid, pass.nome as passivoNome, ind.nomeIndicado as indicadoNome, \n");
        sql.append(" aht.desafio, \n");
        sql.append(" ht.codigo as horarioturma, s.situacao, aht.espera, aht.dia, s.situacaocontrato as situacaoContrato, \n");
        sql.append(" cast(coalesce((SELECT pac.pessoa FROM aulaavulsadiaria aad INNER JOIN \n");
        sql.append("         periodoacessocliente pac ON aad.codigo = pac.aulaavulsadiaria \n");
        sql.append("         where c.pessoa = pac.pessoa AND t.modalidade = aad.modalidade and aht.dia \n");
        sql.append("         BETWEEN pac.datainicioacesso and pac.datafinalacesso limit 1 \n");
        sql.append(" ), 0) as boolean) diaria, p.fotokey, pe.tipoTotalPass as totalPass, \n");
        sql.append(" hea.equipamento, p.datanasc as dataNascimento \n");
        sql.append(" from alunohorarioturma aht\n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = aht.horarioturma\n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma\n");
        sql.append(" LEFT JOIN cliente c ON c.codigo = aht.cliente\n");
        sql.append(" LEFT JOIN passivo pass ON pass.codigo = aht.passivo\n");
        sql.append(" LEFT JOIN indicado ind ON ind.codigo = aht.indicado\n");
        sql.append(" LEFT JOIN pessoa p ON p.codigo = c.pessoa\n");
        sql.append(" LEFT JOIN situacaoclientesinteticodw s on s.codigocliente = c.codigo \n");
        sql.append(" LEFT JOIN periodoacessocliente pe on pe.codigo = (select max(codigo) from periodoacessocliente  where pessoa = p.codigo and tipoTotalPass is true) \n");
        sql.append(" LEFT JOIN alunohorarioturmadesmarcado ahtd on ahtd.aulareposta = aht.codigo \n");
        sql.append(" LEFT JOIN horarioequipamentoaluno hea on hea.cliente = c.codigo and hea.horarioturma = aht.horarioturma and hea.diaAula = aht.dia \n");
        sql.append(" WHERE aht.dia BETWEEN '").append(Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio))).append("' ");
        sql.append(" AND '").append(Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59:59"))).append("' \n");
        sql.append(" AND (t.empresa = ").append(empresa).append(" OR t.empresa IS NULL)");

        if(horarioTurma != null){
            sql.append(" AND ht.codigo = ").append(horarioTurma);
        }

        List<Integer> alunosFixos = alunosFixos(fim, horarioTurma);

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                AgendadoJSON agendado = new AgendadoJSON();
                agendado.setHorarioMarcacao(rs.getTimestamp("datalancamento").getTime());
                agendado.setCodigoCliente(rs.getInt("cliente"));
                agendado.setMatricula(rs.getString("matricula"));
                agendado.setNome(!UteisValidacao.emptyString(rs.getString("passivoNome")) ? (rs.getString("passivoNome") + " (CRM)") :
                        ((!UteisValidacao.emptyString(rs.getString("indicadoNome")) ? (rs.getString("indicadoNome") + " (CRM)") : rs.getString("nome"))));
                Integer autorizado = rs.getInt("autorizado");
                if(!UteisValidacao.emptyNumber(autorizado)){
                    AutorizacaoAcessoGrupoEmpresarialVO acessoGrupoEmpresarialVO = consultarAutorizado(autorizado);
                    agendado.setCodigoCliente(acessoGrupoEmpresarialVO.getCodigo());
                    agendado.setMatricula("AUT" + (acessoGrupoEmpresarialVO.getCodigoMatricula() == null ?
                            "" :
                            String.valueOf(acessoGrupoEmpresarialVO.getCodigoMatricula())));
                    agendado.setNome(acessoGrupoEmpresarialVO.getNomePessoa());
                    agendado.setCidade(acessoGrupoEmpresarialVO.getIntegracao().getEmpresaRemota().getNome());
                    try (ResultSet rsConfirmadoAutorizado = ConexaoZWServiceImpl.criarConsulta("select codigo from aulaconfirmada where autorizado = " + autorizado
                            + " and horario = " + rs.getInt("horarioturma") + " and diaaula =  '"
                            + Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)) + "' ", con)) {
                        agendado.setConfirmado(rsConfirmadoAutorizado.next());
                    }
                }
                Boolean autorizadoGestaoRede = rs.getBoolean("autorizadoGestaoRede");
                if(autorizadoGestaoRede != null && autorizadoGestaoRede){
                    final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
                    CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
                    HttpPost httpPost = new HttpPost(url + "/prest/aulacheia/consultar-autorizado-gestao-rede");
                    List<NameValuePair> params = new ArrayList<>();
                    params.add(new BasicNameValuePair("chave", key));
                    params.add(new BasicNameValuePair("codAcessoAutorizado", rs.getString("codAcessoAutorizado")));
                    params.add(new BasicNameValuePair("matriculaAutorizado", rs.getString("matriculaAutorizado")));
                    httpPost.setEntity(new UrlEncodedFormEntity(params));
                    CloseableHttpResponse response = client.execute(httpPost);
                    ResponseHandler<String> handler = new BasicResponseHandler();
                    String body = handler.handleResponse(response);
                    client.close();
                    JSONObject consulta;
                    try{
                        consulta = new JSONObject(body).getJSONObject("content");
                        AutorizacaoAcessoGrupoEmpresarialVO autorizacaoAcessoGrupoEmpresarialVO = new AutorizacaoAcessoGrupoEmpresarialVO(consulta);
                        agendado.setCodigoCliente(autorizacaoAcessoGrupoEmpresarialVO.getCodigo());
                        agendado.setMatricula(autorizacaoAcessoGrupoEmpresarialVO.getCodigoAutorizacao());
                        agendado.setNome(autorizacaoAcessoGrupoEmpresarialVO.getNomePessoa());
                        agendado.setCidade(String.valueOf(consulta.getInt("empresaRemota")));
                        agendado.setAutorizadoGestaoRede(true);
                        agendado.setCodAcessoAutorizado(autorizacaoAcessoGrupoEmpresarialVO.getCodAcesso());
                        agendado.setMatriculaAutorizado(autorizacaoAcessoGrupoEmpresarialVO.getCodigoMatricula());
                        try (ResultSet rsConfirmadoAutorizado = ConexaoZWServiceImpl.criarConsulta("select codigo from aulaconfirmada "
                                + " where codAcessoAutorizado ILIKE '" + rs.getString("codAcessoAutorizado") + "'"
                                + " and horario = " + rs.getInt("horarioturma") + " and diaaula =  '"
                                + Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)) + "' "
                                + " and matriculaAutorizado = " + rs.getString("matriculaAutorizado"), con)) {
                            agendado.setConfirmado(rsConfirmadoAutorizado.next());
                        }

                    }catch (Exception e){
                        throw new Exception("Não foi possível consultar os dados do aluno");
                    }
                }
                agendado.setEquipamentoReservado(rs.getString("equipamento"));
                agendado.setCodigoPassivo(rs.getInt("passivo"));
                if(!UteisValidacao.emptyNumber(agendado.getCodigoPassivo())){
                    try (ResultSet rsConfirmadoPassivo = ConexaoZWServiceImpl.criarConsulta("select codigo from aulaconfirmada where passivo = " + agendado.getCodigoPassivo()
                            + " and horario = " + rs.getInt("horarioturma") + " and diaaula =  '"
                            + Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)) + "' ", con)) {
                        agendado.setConfirmado(rsConfirmadoPassivo.next());
                    }
                }
                agendado.setCodigoIndicado(rs.getInt("indicado"));
                if(!UteisValidacao.emptyNumber(agendado.getCodigoIndicado())){
                    try (ResultSet rsConfirmadoIndicado = ConexaoZWServiceImpl.criarConsulta("select codigo from aulaconfirmada where indicado = " + agendado.getCodigoIndicado()
                            + " and horario = " + rs.getInt("horarioturma") + " and diaaula =  '"
                            + Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)) + "' ", con)) {
                        agendado.setConfirmado(rsConfirmadoIndicado.next());
                    }
                }
                agendado.setCodigoPessoa(rs.getInt("pessoa"));
                agendado.setId_agendamento(String.valueOf(rs.getInt("horarioturma")));
                agendado.setInicio(Uteis.getDataAplicandoFormatacao(rs.getDate("dia"), "dd/MM/yyyy"));
                agendado.setFim(Uteis.getDataAplicandoFormatacao(rs.getDate("dia"), "dd/MM/yyyy"));
                agendado.setSituacaoContrato(rs.getString("situacaoContrato"));
                agendado.setSituacao(rs.getString("situacao"));
                agendado.setDiaria(rs.getBoolean("diaria"));
                agendado.setExperimental(rs.getBoolean("aulaexperimental"));
                agendado.setTotalPass(rs.getBoolean("totalPass"));
                agendado.setFixo(alunosFixos.contains(rs.getInt("cliente")));
                String bookingid = rs.getString("bookingid");
                agendado.setEspera(rs.getBoolean("espera"));
                agendado.setUserIdSelfloops(rs.getString("idselfloops"));
                agendado.setDataNascimento(Uteis.getDataAplicandoFormatacao(rs.getDate("dataNascimento"), "dd/MM/yyyy"));
                try {
                    if (rs.getString("situacao") != null && !rs.getString("situacao").equals("AT") && UteisValidacao.emptyString(bookingid)) {
                        try (ResultSet rsCheckinGympass = ConexaoZWServiceImpl.criarConsulta("SELECT EXISTS ( SELECT ic.token \n" +
                                "FROM infocheckin ic \n" +
                                "INNER JOIN periodoacessocliente pac ON pac.codigo = ic.periodoacesso \n" +
                                "WHERE ic.cliente = " + rs.getInt("cliente") + " \n" +
                                "AND '" + rs.getDate("dia") + "' BETWEEN pac.datainicioacesso AND pac.datafinalacesso) as temCheckin", con)) {
                            if (rsCheckinGympass.next()) {
                                agendado.setGymPass(rsCheckinGympass.getBoolean("temCheckin"));
                            }
                        }

                        // valida caso quando checkin não é feito automaticamente via chamada gympass webhook
                        if (!agendado.isGymPass()) {
                            try (ResultSet rsCheckinManualGympass = ConexaoZWServiceImpl.criarConsulta("SELECT EXISTS (" +
                                    "SELECT * FROM periodoacessocliente pac  \n" +
                                    "WHERE pac.pessoa = " + rs.getInt("pessoa") + " \n" +
                                    "AND pac.tokengympass IS NOT NULL AND pac.tokengympass <> ''" +
                                    "AND '" + rs.getDate("dia") + "' BETWEEN pac.datainicioacesso AND pac.datafinalacesso" +
                                    ") as temCheckin", con)) {
                                if (rsCheckinManualGympass.next()) {
                                    agendado.setGymPass(rsCheckinManualGympass.getBoolean("temCheckin"));
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    Uteis.logar(e, AgendaModoBDServiceImpl.class);
                }

                if(!UteisValidacao.emptyString(bookingid)){
                    agendado.setGymPass(true);
                }
                agendado.setDesafio(rs.getBoolean("desafio"));
                agendado.setFotokey(rs.getString("fotokey"));
                agendado.setPresencaReposicao(rs.getInt("aulareposicao") > 0);
                lista.add(agendado);
            }
        }
        return lista;
    }

    private List<Integer> alunosFixos(Date fim, Integer horarioTurma) throws Exception {

        String sql = "SELECT DISTINCT af.cliente" +
                " FROM alunofixoaula af" +
                " JOIN alunohorarioturma aht ON af.cliente = aht.cliente AND af.horarioturma = aht.horarioturma" +
                "  WHERE af.usuarioremoveu IS NULL\n" +
                "  and af.horarioturma = " + horarioTurma + " " +
                "  AND EXISTS (\n" +
                "  SELECT 1\n" +
                "  FROM alunohorarioturma aht_atual\n" +
                "  WHERE aht_atual.cliente = af.cliente\n" +
                "  AND aht_atual.horarioturma = af.horarioturma\n" +
                "  AND aht_atual.alunofixoaula = af.codigo\n" +
                "  AND aht_atual.dia =  '" + Uteis.getDataJDBC(fim) + "' );" ;

        List<Integer> clientes = new ArrayList<>();
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, con)) {
            while (rs.next()) {
                clientes.add(rs.getInt("cliente"));
            }
        }
        return clientes;
    }

    public List<AgendadoJSON> consultarHorariosTurmaParaAgenda(Date inicio, Integer empresa) throws Exception{
        List<AgendadoJSON> lista = new ArrayList<AgendadoJSON>();
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT sc.codigocliente as cliente, sc.situacao, sc.matricula, sc.telefonescliente, sc.nomecliente as nome, sc.codigopessoa as pessoa, \n");
        sql.append(" t.codigo as turma, ma.codigo as matriculahorarioturma,sc.situacaoContrato as situacaoContrato, \n");
        sql.append(" ht.codigo as horarioturma, ma.datainicio, ma.datafim, ma.contrato, p.fotokey, sc.datanascimento  as dataNascimento FROM matriculaalunohorarioturma ma  \n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = ma.horarioturma\n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma\n");
        sql.append(" INNER JOIN situacaoclientesinteticodw sc ON sc.codigopessoa = ma.pessoa\n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = ma.pessoa\n");
        sql.append(" WHERE ma.datafim >= '").append(Uteis.getDataJDBC(inicio)).append("'");
        sql.append(" AND ht.situacao = 'AT' \n");
        sql.append(" AND t.empresa = ").append(empresa);
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                AgendadoJSON agendado = new AgendadoJSON();
                agendado.setCodigoCliente(rs.getInt("cliente"));
                agendado.setCodigoContrato(rs.getInt("contrato"));
                agendado.setMatricula(rs.getString("matricula"));
                agendado.setNome(rs.getString("nome"));
                agendado.setTelefones(rs.getString("telefonescliente"));
                agendado.setCodigoPessoa(rs.getInt("pessoa"));
                agendado.setId_agendamento(String.valueOf(rs.getInt("horarioturma")));
                agendado.setInicio(Uteis.getDataAplicandoFormatacao(rs.getDate("datainicio"), "dd/MM/yyyy"));
                agendado.setFim(Uteis.getDataAplicandoFormatacao(rs.getDate("datafim"), "dd/MM/yyyy"));
                agendado.setSituacaoContrato(rs.getString("situacaoContrato"));
                agendado.setSituacao(rs.getString("situacao"));
                agendado.setFotokey(rs.getString("fotokey"));
                agendado.setDataNascimento(Uteis.getDataAplicandoFormatacao(rs.getDate("dataNascimento"), "dd/MM/yyyy"));
                lista.add(agendado);
            }
        }
        return lista;
    }

    public AutorizacaoAcessoGrupoEmpresarialVO consultarAutorizado(Integer codigo) throws Exception{
        AutorizacaoAcessoGrupoEmpresarialVO autorizado = new AutorizacaoAcessoGrupoEmpresarialVO();
        StringBuilder sql = new StringBuilder(" select i.nomeempresa,a.codigo, nomepessoa, codigomatricula  from autorizacaoacessogrupoempresarial a\n");
        sql.append(" inner join integracaoacessogrupoempresarial i on i.codigo = a.integracaoacessogrupoempresarial  ");
        sql.append(" where a.codigo = ").append(codigo);
        try (ResultSet resultSet = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            while (resultSet.next()) {

                autorizado.setCodigo(resultSet.getInt("codigo"));
                autorizado.setNomePessoa(resultSet.getString("nomepessoa"));
                autorizado.setCodigoMatricula(resultSet.getInt("codigomatricula"));
                autorizado.setIntegracao(new IntegracaoAcessoGrupoEmpresarialVO());
                autorizado.getIntegracao().setEmpresaRemota(new EmpresaVO());
                autorizado.getIntegracao().getEmpresaRemota().setNome(resultSet.getString("nomeempresa"));
            }
        }
        return autorizado;
    }

    public List<AgendadoTO> consultarReposicoesParaAgenda(Date inicio, Date fim, Integer empresa, Integer matricula, boolean usarDataOrigem, Integer modalidade, Integer tipomodalidade) throws Exception{
        inicio = Calendario.getDataComHoraZerada(inicio);
        fim = Calendario.getDataComHoraZerada(fim);
        List<AgendadoJSON> lista = new ArrayList<AgendadoJSON>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT r.datapresenca, sc.codigocliente as cliente, sc.matricula, sc.telefonescliente, cc.tipooperacaocreditotreino,  \n");
        sql.append(" sc.nomecliente as nome, sc.codigopessoa as pessoa, t.codigo as turma, r.codigo as reposicao, sc.situacao, sc.situacaoContrato as situacaoContrato,  \n");
        sql.append(" ht.codigo as horarioturma, r.horarioturmaorigem, r.datareposicao, r.contrato, ag.gympass, coalesce(ag.tipoagendamento, '') as tipoagendamento, \n");
        sql.append(" r.spiviSeatID, r.spivieventid, p.spiviclientid, p.fotokey, pe.tipoTotalPass as totalPass \n");
        sql.append("  FROM reposicao r INNER JOIN horarioturma ht ON ht.codigo = r.horarioturma \n");
        sql.append(" INNER JOIN turma t ON t.codigo = r.turmadestino \n");
        sql.append(" INNER JOIN modalidade m on m.codigo = t.modalidade \n");
        sql.append(" INNER JOIN cliente ON cliente.codigo = r.cliente \n");
        sql.append(" INNER JOIN situacaoclientesinteticodw sc ON sc.codigocliente = r.cliente \n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = sc.codigopessoa \n");
        sql.append(" LEFT JOIN controlecreditotreino cc ON cc.reposicao = r.codigo \n");
        sql.append(" LEFT JOIN agenda  ag ON ag.reposicao = r.codigo \n");
        sql.append(" LEFT JOIN periodoacessocliente pe on pe.codigo = (select max(codigo) from periodoacessocliente  where pessoa = p.codigo and tipoTotalPass is true) \n");
        if(matricula == null){
            sql.append(" WHERE r.datareposicao >= '").append(Uteis.getDataJDBCTimestamp(inicio)).append("'");
            sql.append(" AND r.datareposicao <= '").append(Uteis.getDataJDBCTimestamp(fim)).append("'\n");
            sql.append(" AND ht.situacao = 'AT'  \n");
            sql.append(" AND t.empresa = ").append(empresa);
        }else{
            String campo = "datareposicao";
            if(usarDataOrigem){
                campo = "dataorigem";
            }
            sql.append(" WHERE (r.").append(campo).append(" >= '").append(Uteis.getDataJDBCTimestamp(inicio)).append("'");
            sql.append(" AND r.").append(campo).append(" <= '").append(Uteis.getDataJDBCTimestamp(fim)).append("')\n");
            sql.append(" AND sc.matricula = ").append(matricula);
        }

        if (UteisValidacao.notEmptyNumber(tipomodalidade)) {
            sql.append(" and m.tipo = ").append(tipomodalidade);
        } else if (UteisValidacao.notEmptyNumber(modalidade)) {
            sql.append(" and m.codigo = ").append(modalidade);
        }

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                AgendadoJSON agendado = new AgendadoJSON();
                agendado.setCodigoCliente(rs.getInt("cliente"));
                agendado.setCodigoContrato(rs.getInt("contrato"));
                agendado.setMatricula(rs.getString("matricula"));
                agendado.setNome(rs.getString("nome"));
                agendado.setTelefones(rs.getString("telefonescliente"));
                agendado.setCodigoPessoa(rs.getInt("pessoa"));
                agendado.setId_agendamento(usarDataOrigem ? String.valueOf(rs.getInt("horarioturmaorigem")) : String.valueOf(rs.getInt("horarioturma")));
                agendado.setInicio(Uteis.getDataAplicandoFormatacao(rs.getDate("datareposicao"), "dd/MM/yyyy"));
                agendado.setFim(Uteis.getDataAplicandoFormatacao(rs.getDate("datareposicao"), "dd/MM/yyyy"));
                agendado.setUsaSaldo(rs.getInt("tipooperacaocreditotreino") == TipoOperacaoCreditoTreinoEnum.MARCOU_AULA.getCodigo());
                agendado.setSituacaoContrato(rs.getString("situacaoContrato"));
                agendado.setSituacao(rs.getString("situacao"));
                agendado.setConfirmado(rs.getDate("datapresenca") != null);
                agendado.setGymPass(rs.getBoolean("gymPass"));
                agendado.setTotalPass(rs.getBoolean("totalPass"));
                agendado.setDiaria(rs.getString("tipoagendamento").equalsIgnoreCase("DI"));
                agendado.setExperimental(rs.getString("tipoagendamento").equalsIgnoreCase("AE"));
                agendado.setSpiviSeat(rs.getInt("spiviseatid"));
                agendado.setSpiviEventID(rs.getInt("spivieventid"));
                agendado.setSpiviClientID(rs.getInt("spiviclientid"));
                agendado.setFotokey(rs.getString("fotokey"));
                lista.add(agendado);
            }
        }
        List<AgendadoTO> listaTO = new ArrayList<AgendadoTO>();
        for(AgendadoJSON j : lista){
            listaTO.add(new AgendadoTO(j));
        }
        return listaTO;
    }

    public Map<Integer, Map<String,AgendamentoDesmarcadoTO>> consultarDesmarcados( final String key, final Date inicio,
                                                                                   final Date fim, final Integer empresa) throws Exception {
        List<AgendamentoDesmarcadoJSON> dados = consultarAgendamentosDesmarcados(inicio, fim, empresa);
        Map<Integer, Map<String,AgendamentoDesmarcadoTO>> mapa = new HashMap<>();

        for (AgendamentoDesmarcadoJSON desmarcadoJSON : dados) {
            Map<String,AgendamentoDesmarcadoTO> mapaCliente = mapa.get(desmarcadoJSON.getCodigoCliente());
            if(mapaCliente == null){
                mapaCliente = new HashMap<>();
                mapa.put(desmarcadoJSON.getCodigoCliente(), mapaCliente);
            }
            mapaCliente.put(desmarcadoJSON.getIdAgendamento(),
                    new AgendamentoDesmarcadoTO(desmarcadoJSON.getIdAgendamento(),
                            desmarcadoJSON.getCodigoCliente(),
                            desmarcadoJSON.isReposto(),
                            desmarcadoJSON.getCodigoContrato(),
                            desmarcadoJSON.getJustificativa()));
        }
        return mapa;
    }

    public List<AgendamentoDesmarcadoJSON> consultarAgendamentosDesmarcados(Date inicio, Date fim, Integer empresa)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select cliente, dataorigem, datareposicao, horarioturma, contrato, justificativa from auladesmarcada ad inner join contrato con on con.codigo = ad.contrato  \n");
        sql.append(" where ad.empresa = ? and   ad.dataorigem BETWEEN ? and ?  \n");
        sql.append(" and  (ad.dataorigem::date   <=  con.vigenciaateajustada::date \n"); // para que contratos renovados antecipadamente não sejam afetados por aulas desmarcadas do contrato passado
        sql.append(" or (contratoresponsavelrenovacaomatricula = 0 and contratoresponsavelrematriculamatricula = 0))");//contratos trancados são considerados nessa condição
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, empresa);
        pst.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
        pst.setTimestamp(3, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59:59")));

        List<AgendamentoDesmarcadoJSON> lista;
        try (ResultSet rs = pst.executeQuery()) {
            lista = new ArrayList<AgendamentoDesmarcadoJSON>();
            while (rs.next()) {
                AgendamentoDesmarcadoJSON json = new AgendamentoDesmarcadoJSON();
                json.setCodigoCliente(rs.getInt("cliente"));
                json.setReposto(rs.getDate("datareposicao") != null);
                json.setIdAgendamento(rs.getInt("horarioturma") + "_" + Uteis.getData(rs.getDate("dataorigem"), "ddMMyy"));
                json.setCodigoContrato(rs.getInt("contrato"));
                if (rs.getString("justificativa") == null) {
                    json.setJustificativa("");
                } else {
                    json.setJustificativa(rs.getString("justificativa"));
                }
                lista.add(json);
            }
        }
        return lista;

    }

    public Map<Integer, Map<String,AgendamentoConfirmadoTO>> consultarConfirmados( final String key, final Date inicio,
                                                                                   final Date fim) throws Exception {
        List<AgendamentoConfirmadoJSON> dados = consultarAgendamentosConfirmados(inicio, fim);
        Map<Integer, Map<String,AgendamentoConfirmadoTO>> mapa = new HashMap<>();
        for (AgendamentoConfirmadoJSON confirmadoJSON : dados) {
            Map<String,AgendamentoConfirmadoTO> mapaCliente = mapa.get(confirmadoJSON.getCodigoCliente());
            if(mapaCliente == null){
                mapaCliente = new HashMap<>();
                mapa.put(confirmadoJSON.getCodigoCliente(), mapaCliente);
            }
            mapaCliente.put(confirmadoJSON.getIdAgendamento(),
                    new AgendamentoConfirmadoTO(confirmadoJSON.getIdAgendamento(),
                            confirmadoJSON.getCodigoCliente()));
        }
        return mapa;
    }

    public List<AgendamentoConfirmadoJSON> consultarAgendamentosConfirmados(Date inicio, Date fim)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select cliente, diaAula, horario from aulaconfirmada \n");
        sql.append("where diaAula BETWEEN ? and ? ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
        pst.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(fim, "23:59:59")));
        List<AgendamentoConfirmadoJSON> lista;
        try (ResultSet rs = pst.executeQuery()) {
            lista = new ArrayList<AgendamentoConfirmadoJSON>();
            while (rs.next()) {
                AgendamentoConfirmadoJSON json = new AgendamentoConfirmadoJSON();
                json.setCodigoCliente(rs.getInt("cliente"));
                json.setIdAgendamento(rs.getInt("horario") + "_" + Uteis.getData(rs.getDate("diaAula"), "ddMMyy"));
                lista.add(json);
            }
        }
        return lista;

    }


    public Set<Date> consultarFeriadosPorPeriodoEmpresaAulaCheia(Date inicio, Date fim, EmpresaVO empresaVO) throws Exception{
        StringBuilder sql = new StringBuilder();
        HashSet<Date> dias = new HashSet<>();
        HashSet<Date> recorrentes = new HashSet<>();

        sql.append("select dia from feriado where naorecorrente = false");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    if(Calendario.dataNoMesmoMes(rs.getDate("dia"), inicio) ||
                            Calendario.dataNoMesmoMes(rs.getDate("dia"), fim))
                        recorrentes.add(rs.getDate("dia"));
                }
            }
        }
        recorrentes.add(inicio);
        for(Date anoFeriado : recorrentes) {
            sql.delete(0, sql.length());
            sql.append("select codigo, dia, naorecorrente from ( select codigo, dia, naorecorrente  ");
            sql.append("   from feriado a where naorecorrente = false and nacional = true ");
            sql.append("union ");
            sql.append("select codigo, dia, naorecorrente  ");
            sql.append("   from feriado a where naorecorrente = true and nacional = true ");
            sql.append("union ");
            sql.append("select codigo, dia, naorecorrente from feriado a where estado = ? and nacional = false and estadual = true ");
            sql.append("union  ");
            sql.append("select codigo, dia, naorecorrente from feriado a where estado = ? and cidade = ? and nacional = false and estadual = false ");
            sql.append("union  ");
            sql.append("select codigo, dia, naorecorrente from feriado a where estado = ? and cidade = ? and nacional = false and estadual = false and naorecorrente = false");
            sql.append(") tb where ((dia between ? and ?) or ");
            sql.append(" (dia::date between '" +  Uteis.getDataJDBC(
                    Uteis.obterPrimeiroDiaMes(anoFeriado))
                    +"' and '"+
                    Uteis.getDataJDBC(
                            Uteis.obterUltimoDiaMes(anoFeriado))
                    +"' and naorecorrente = false)) order by dia asc; ");

            try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
                stm.setInt(1, empresaVO.getEstado().getCodigo());
                stm.setInt(2, empresaVO.getEstado().getCodigo());
                stm.setInt(3, empresaVO.getCidade().getCodigo());
                stm.setInt(4, empresaVO.getEstado().getCodigo());
                stm.setInt(5, empresaVO.getCidade().getCodigo());
                stm.setDate(6, Uteis.getDataJDBC(inicio));
                stm.setDate(7, Uteis.getDataJDBC(fim));


                try (ResultSet rs = stm.executeQuery()) {
                    while (rs.next()) {
                        if(!Calendario.maior(rs.getDate("dia"), fim) && (Calendario.dataNoMesmoMes(rs.getDate("dia"), inicio) ||
                                Calendario.dataNoMesmoMes(rs.getDate("dia"), fim))) {
                            dias.add(rs.getDate("dia"));
                        }
                    }
                }
            }
        }
        return dias;
    }

    public List<FeriadoVO> consultarFeriadoPorDiaAndMes(Date diaFeriado) throws Exception{
        String sqlStr = "SELECT codigo, naoRecorrente FROM Feriado WHERE date_part('day', dia) = date_part('day', cast('"
                + Uteis.getDataJDBC(diaFeriado) + "' as date)) "+
                "and date_part('month', dia) = date_part('month', cast('" + Uteis.getDataJDBC(diaFeriado) + "' as date))  ORDER BY dia";
        List<FeriadoVO> feriados = new ArrayList<>();
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                while (rs.next()) {
                    FeriadoVO f = new FeriadoVO();
                    f.setCodigo(rs.getInt("codigo"));
                    f.setNaoRecorrente(rs.getBoolean("naoRecorrente"));
                    feriados.add(f);
                }
            }
        }
        return feriados;
    }

    private ModalidadeVO consultarModalidade(Integer modalidade) throws Exception{
        ModalidadeVO modalidadeVO = new ModalidadeVO();
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("select tipo " +
                "from modalidade where codigo = modalidade", con)){
            while(rs.next()){
                modalidadeVO.setCodigo(modalidade);
                modalidadeVO.setTipo(rs.getInt("tipo"));
            }
        }
        return modalidadeVO;
    }

    private ColaboradorVO consultarColaborador(Integer colaborador) throws Exception{
        ColaboradorVO colaboradorVO = new ColaboradorVO();

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("select c.codigo, p.fotokey,\n" +
                "                p.nome, p.codigo as pessoa\n" +
                "        from colaborador c\n" +
                "        inner join pessoa p on p.codigo = c.pessoa" +
                " where c.codigo = " + colaborador, con)){
            while(rs.next()){
                colaboradorVO.setCodigo(colaborador);
                colaboradorVO.setPessoa(new PessoaVO());
                colaboradorVO.getPessoa().setCodigo(rs.getInt("pessoa"));
                colaboradorVO.getPessoa().setNome(rs.getString("nome"));
                String fotokey = rs.getString("fotokey");
                if(fotokey == null || fotokey.isEmpty()){
                    fotokey = "fotoPadrao.jpg";
                }
                colaboradorVO.getPessoa().setUrlFoto(Aplicacao.obterUrlFotoDaNuvem(fotokey));
            }
        }
        return colaboradorVO;
    }

    private Map<Integer, ColaboradorVO> colaboradorResponsavel(Map<String, List<AgendaTotalJSON>> turmasParaAgenda) throws Exception{
        Map<Integer, ColaboradorVO> colaboradorResponsavel = new HashMap<>();
        Set<Integer> codigos = new HashSet<>();
        String codigosString = "";
        for (List<AgendaTotalJSON> aulas : turmasParaAgenda.values()) {
            for(AgendaTotalJSON a : aulas){
                codigos.add(a.getCodigoResponsavel());
            }
        }
        for (Integer c : codigos){
            codigosString += "," + c;
        }
        if(codigosString.isEmpty()){
            return colaboradorResponsavel;
        }

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("select c.codigo, p.fotokey,\n" +
                "                p.nome, p.codigo as pessoa\n" +
                "        from colaborador c\n" +
                "        inner join pessoa p on p.codigo = c.pessoa" +
                " where c.codigo in (" + codigosString.replaceFirst(",", "") + ")", con)){
            while(rs.next()){
                ColaboradorVO colaboradorVO = new ColaboradorVO();
                colaboradorVO.setCodigo(rs.getInt("codigo"));
                colaboradorVO.setPessoa(new PessoaVO());
                colaboradorVO.getPessoa().setCodigo(rs.getInt("pessoa"));
                colaboradorVO.getPessoa().setNome(rs.getString("nome"));
                String fotokey = rs.getString("fotokey");
                colaboradorVO.getPessoa().setUrlFoto(Aplicacao.obterUrlFotoDaNuvem(fotokey));
                colaboradorResponsavel.put(colaboradorVO.getCodigo(), colaboradorVO);
            }
        }
        return colaboradorResponsavel;
    }

    public List<AgendaTotalTO> consultarAgendamentos( final String key,  final Date inicio,
                                                      final Date fim, final Integer empresa,
                                                      final String modalidade,
                                                      final String search,
                                                      final String situacaoHorario) throws Exception{
        List<AgendaTotalJSON> jsons = consultarParaAgenda(
                Calendario.getDataComHoraZerada(inicio),
                Calendario.getDataComHora(fim, "23:59:59"),
                null, montarListaModalidades(modalidade), empresa,
                modalidade != null && modalidade.equals("COLETIVAS"), search, situacaoHorario);
        List<AgendaTotalTO> lista = new ArrayList<AgendaTotalTO>();
        for(AgendaTotalJSON j : jsons){
            lista.add(new AgendaTotalTO(j));
        }
        return lista;
    }

    private List<Integer> montarListaModalidades(String modalidades) {
        List<Integer> listaModalidades = new ArrayList<Integer>();
        if (modalidades != null && !modalidades.equals("COLETIVAS")) {
            String[] split = modalidades.split(";");
            for (String mod : split) {
                try {
                    listaModalidades.add(Integer.valueOf(mod));
                } catch (Exception e) {
                }
            }
        }
        return listaModalidades;
    }

    public List<AgendaTotalJSON> consultarProximasAulasAulaCheia(Integer matricula)throws Exception{
        List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();
        Map<Integer, Map<String, AgendaTotalJSON>> mapa;
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlConsultaAulasAulaCheia(matricula, Calendario.hoje()), con)) {
            mapa = new HashMap<Integer, Map<String, AgendaTotalJSON>>();
            while (rs.next()) {
                AgendaTotalJSON item = montarDadosAgenda(rs);
                Map<String, AgendaTotalJSON> mapHour = mapa.get(rs.getInt("turma"));
                if (mapHour == null) {
                    mapHour = new HashMap<String, AgendaTotalJSON>();
                    mapa.put(rs.getInt("turma"), mapHour);
                }
                item.setInicio(Uteis.getData(rs.getDate("datainicio")) + " " + item.getInicio());
                item.setFim(Uteis.getData(rs.getDate("datainicio")) + " " + item.getFim());
                mapHour.put(Uteis.getDataAplicandoFormatacao(rs.getDate("datainicio"), "ddMMyyyy").concat(rs.getString("horainicial")), item);
            }
        }
        //consultar agendadas em outra unidade
        try {
            String sql = "select valorcampoalterado from log l " +
                    "inner join cliente c on c.pessoa = l.pessoa\n" +
                    "where nomeentidade = 'ALUNO_AULA_OUTRA_UNIDADE' and matricula = '" + matricula + "'";
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql, con)) {
                while (rs.next()) {
                    String valorcampoalterado = rs.getString("valorcampoalterado");
                    String dia = valorcampoalterado.split("início às ")[1];
                    AgendaTotalJSON item = new AgendaTotalJSON();
                    item.setAulaCheia(true);
                    item.setMatricula(matricula);
                    item.setTitulo(valorcampoalterado.split("aula ")[1].split(" com início")[0]);
                    Map<String, AgendaTotalJSON> mapHour = mapa.get(0);
                    if (mapHour == null) {
                        mapHour = new HashMap<String, AgendaTotalJSON>();
                        mapa.put(0, mapHour);
                    }
                    item.setInicio(dia);
                    mapHour.put(Uteis.getDataAplicandoFormatacao(rs.getDate("datainicio"), "ddMMyyyy").concat(rs.getString("horainicial")), item);
                }
            }
        }catch (Exception e){
            Uteis.logar(e, AgendaModoBDServiceImpl.class);
        }
        for(Map<String,AgendaTotalJSON> mapaData : mapa.values()){
            try {
                aulas.addAll(mapaData.values());
            } catch (Exception e) {
            }
        }
        return aulas;
    }

    public String sqlConsultaAulasAulaCheia(Integer matricula, Date dia) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("   SELECT t.codigo as turma, t.empresa,\n");
        sql.append(" t.identificador,t.aulaColetiva, m.codigo as modalidade, m.nome as nomemodalidade,\n" );
        sql.append(" ht.diasemana,ht.professor,p.nome as nomeprofessor, ht.ambiente,  \n" );
        sql.append(" ht.horainicial, ht.horafinal, ht.nrmaximoaluno, ht.codigo as horarioturma,maht.dia as datainicio,maht.dia as datafim, sc.codigocontrato as contrato,\n" );
        sql.append(" p.fotokey as fotoProfessor, \n");
        sql.append(" t.urlVideoYoutube \n");
        sql.append(" FROM alunohorarioturma maht  \n" );
        sql.append(" INNER JOIN situacaoclientesinteticodw sc ON maht.cliente = sc.codigocliente \n" );
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = maht.horarioturma\n" );
        sql.append(" INNER JOIN colaborador c ON c.codigo = ht.professor\n" );
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa\n" );
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma\n" );
        sql.append(" INNER JOIN modalidade m ON t.modalidade = m.codigo\n" );
//        sql.append(" INNER JOIN contrato con ON con.codigo = sc.codigocontrato AND con.situacao = 'AT'\n" );
        sql.append(" WHERE sc.matricula = ").append(matricula);
        sql.append(" and maht.dia >= '").append(Uteis.getDataJDBC(dia)).append("'");

        return sql.toString();
    }

    public List<String> aulasAutorizado(Integer matricula, String chaveAluno, Date dia) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select a.horarioturma, a.dia from alunohorarioturma a \n");
        sql.append(" inner join autorizacaoacessogrupoempresarial aa on aa.codigo = a.autorizado \n");
        sql.append(" inner join integracaoacessogrupoempresarial i on i.codigo = aa.integracaoacessogrupoempresarial \n");
        sql.append(" where aa.codigomatricula = ").append(matricula);
        sql.append(" and i.chave = '").append(chaveAluno).append("' \n");
        sql.append(" and a.dia::date = '");
        sql.append(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("'");
        try (ResultSet resultSet = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            List<String> aulas = new ArrayList<>();
            while (resultSet.next()) {
                aulas.add(resultSet.getInt("horarioturma") + "_" + Uteis.getData(resultSet.getDate("dia")));
            }
            return aulas;
        }
    }

    public List<String> aulasAutorizadoGestaoRede(Integer matriculaAutorizado, String codAcessoAutorizado, Date dia) throws Exception{
        // Usa a matrícula e busca o codAcesso do aluno, podem haver alunos com mesma matricula mas chaves diferentes
        StringBuilder sql = new StringBuilder();

        List<String> aulas = new ArrayList<>();

        if (!UteisValidacao.emptyString(codAcessoAutorizado) && !UteisValidacao.emptyNumber(matriculaAutorizado) ){
            sql.append(" select a.horarioturma, a.dia from alunohorarioturma a \n");
            sql.append(" where codacessoautorizado = '").append(codAcessoAutorizado).append("'\n");
            sql.append(" and matriculaautorizado = ").append(matriculaAutorizado).append("\n");
            sql.append(" and a.dia::date = '");
            sql.append(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append("'");
            try (ResultSet resultSet = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
                while (resultSet.next()) {
                    aulas.add(resultSet.getInt("horarioturma") + "_" + Uteis.getData(resultSet.getDate("dia")));
                }
            }
        }
        return aulas;

    }

    public Map<Integer, List<AmbienteAgendadoTO>> ambientesAgendados(String ctx,
                                                                     Date inicio,
                                                                     Date fim,
                                                                     Integer empresa) throws Exception {
        Map<Integer, List<AmbienteAgendadoTO>> ambientesAgendadosMap = new HashMap<>();
        JSONArray array = new JSONArray();

        StringBuilder sqlAulaColetiva = new StringBuilder();
        sqlAulaColetiva.append(" SELECT dia, t.ambiente, h.horainicial, h.horafinal, a.horarioturma FROM alunohorarioturma a \n");
        sqlAulaColetiva.append(" INNER JOIN horarioturma h ON h.codigo = a.horarioturma \n");
        sqlAulaColetiva.append(" INNER JOIN turma t ON t.codigo = h.turma \n");
        sqlAulaColetiva.append(" WHERE dia BETWEEN ? AND ? \n");
        sqlAulaColetiva.append(" AND t.empresa = ? \n");
        sqlAulaColetiva.append(" GROUP BY dia, t.ambiente, h.horainicial, h.horafinal, a.horarioturma \n");

        try (PreparedStatement stmt1 = con.prepareStatement(sqlAulaColetiva.toString())) {
            stmt1.setDate(1, Uteis.getDataJDBC(inicio));
            stmt1.setDate(2, Uteis.getDataJDBC(Uteis.getDataHora2359(fim)));
            stmt1.setInt(3, empresa);

            try (ResultSet resultSet = stmt1.executeQuery()) {
                while (resultSet.next()) {
                    JSONObject json = new JSONObject();
                    json.put("dia", resultSet.getTimestamp("dia").getTime());
                    json.put("ambiente", resultSet.getInt("ambiente"));
                    json.put("horarioturma", resultSet.getInt("horarioturma"));
                    json.put("horainicial", resultSet.getString("horainicial"));
                    json.put("horafinal", resultSet.getString("horafinal"));
                    array.put(json);
                }
            }
        }

        StringBuilder sqlAulaTurma = new StringBuilder();
        sqlAulaTurma.append(" SELECT h.ambiente, h.horainicial, h.horafinal, a.horarioturma FROM matriculaalunohorarioturma a \n");
        sqlAulaTurma.append(" INNER JOIN horarioturma h ON h.codigo = a.horarioturma \n");
        sqlAulaTurma.append(" INNER JOIN turma t ON t.codigo = h.turma \n");
        sqlAulaTurma.append(" WHERE datainicio <= ? \n");
        sqlAulaTurma.append(" AND datafim >= ? \n");
        sqlAulaTurma.append(" AND t.empresa = ? \n");
        sqlAulaTurma.append(" AND diasemananumero = ? \n");
        sqlAulaTurma.append(" GROUP BY h.ambiente, h.horainicial, h.horafinal, a.horarioturma \n");

        try (PreparedStatement stmt2 = con.prepareStatement(sqlAulaTurma.toString())) {
            stmt2.setDate(1, Uteis.getDataJDBC(inicio));
            stmt2.setDate(2, Uteis.getDataJDBC(Uteis.getDataHora2359(fim)));
            stmt2.setInt(3, empresa);
            stmt2.setInt(4, Uteis.getDiaDaSemanaNumero(inicio));

            try (ResultSet resultSet = stmt2.executeQuery()) {
                while (resultSet.next()) {
                    if (!UteisValidacao.emptyNumber(resultSet.getInt("ambiente"))) {
                        JSONObject json = new JSONObject();
                        json.put("dia", inicio.getTime());
                        json.put("ambiente", resultSet.getInt("ambiente"));
                        json.put("horarioturma", resultSet.getInt("horarioturma"));
                        json.put("horainicial", resultSet.getString("horainicial"));
                        json.put("horafinal", resultSet.getString("horafinal"));
                        array.put(json);
                    }
                }
            }
        }

        StringBuilder sqlReposicao = new StringBuilder();
        sqlReposicao.append(" SELECT ht.ambiente, ht.horainicial, ht.horafinal, ht.codigo AS horarioturma \n");
        sqlReposicao.append(" FROM reposicao r INNER JOIN horarioturma ht ON ht.codigo = r.horarioturma \n");
        sqlReposicao.append(" INNER JOIN turma t ON t.codigo = r.turmadestino \n");
        sqlReposicao.append(" INNER JOIN modalidade m ON m.codigo = t.modalidade \n");
        sqlReposicao.append(" INNER JOIN cliente ON cliente.codigo = r.cliente \n");
        sqlReposicao.append(" INNER JOIN situacaoclientesinteticodw sc ON sc.codigocliente = r.cliente \n");
        sqlReposicao.append(" INNER JOIN pessoa p ON p.codigo = sc.codigopessoa \n");
        sqlReposicao.append(" LEFT JOIN controlecreditotreino cc ON cc.reposicao = r.codigo \n");
        sqlReposicao.append(" LEFT JOIN agenda ag ON ag.reposicao = r.codigo \n");
        sqlReposicao.append(" WHERE r.datareposicao >= ? \n");
        sqlReposicao.append(" AND r.datareposicao <= ? \n");
        sqlReposicao.append(" AND ht.situacao = 'AT' \n");
        sqlReposicao.append(" AND t.empresa = ? \n");
        sqlReposicao.append(" GROUP BY ht.ambiente, ht.horainicial, ht.horafinal, ht.codigo \n");

        try (PreparedStatement stmt3 = con.prepareStatement(sqlReposicao.toString())) {
            stmt3.setDate(1, Uteis.getDataJDBC(inicio));
            stmt3.setDate(2, Uteis.getDataJDBC(Uteis.getDataHora2359(fim)));
            stmt3.setInt(3, empresa);

            try (ResultSet resultSet = stmt3.executeQuery()) {
                while (resultSet.next()) {
                    if (!UteisValidacao.emptyNumber(resultSet.getInt("ambiente"))) {
                        JSONObject json = new JSONObject();
                        json.put("dia", inicio.getTime());
                        json.put("ambiente", resultSet.getInt("ambiente"));
                        json.put("horarioturma", resultSet.getInt("horarioturma"));
                        json.put("horainicial", resultSet.getString("horainicial"));
                        json.put("horafinal", resultSet.getString("horafinal"));
                        array.put(json);
                    }
                }
            }
        }

        for (int i = 0; i < array.length(); i++) {
            AmbienteAgendadoTO ambienteAgendadoTO = new AmbienteAgendadoTO(array.getJSONObject(i));
            ambientesAgendadosMap
                    .computeIfAbsent(ambienteAgendadoTO.getAmbiente(), k -> new ArrayList<>())
                    .add(ambienteAgendadoTO);
        }

        return ambientesAgendadosMap;
    }

    @Override
    public String consultarProximasAulas(Integer matricula, Boolean proximos30dias) throws Exception {
        Integer saldo = 0;
        try {
            String consultarSaldoAluno = consultarSaldoAluno(matricula, false, null);
            String[] split = consultarSaldoAluno.split(";");
            saldo = Integer.valueOf(split[0]);
        } catch (Exception e) {
            Uteis.logar(e, AgendaModoBDServiceImpl.class);
        }

        verificarSituacaoAluno(matricula, null);

        Integer codigoCliente;
        Date agora;
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("SELECT empresa, codigo FROM cliente WHERE codigomatricula = " + matricula, con)) {
            codigoCliente = null;
            agora = Calendario.hoje();
            if (rs.next()) {
                codigoCliente = rs.getInt("codigo");
                TimeZone tz = TimeZone.getTimeZone(obterTimeZoneDefault(rs.getInt("empresa")));
                agora = Calendario.hojeCalendar(tz).getTime();
            }
        }
        Map<Date, List<Integer>> mapaAgendamentosDesmarcados = consultarAgendamentosDesmarcados(codigoCliente);
        List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();
        aulas.addAll(consultarProximasReposicoes(matricula, agora, false, null, null));
        if (proximos30dias) {
            aulas.addAll(consultarAulasProximos30dias(matricula, agora, mapaAgendamentosDesmarcados, (saldo + aulas.size())));
        } else {
            aulas.addAll(consultarProximasAulasModalidadesDiferentes(matricula, mapaAgendamentosDesmarcados));
        }
        aulas = Ordenacao.ordenarLista(aulas, "inicio");
        return new JSONArray(aulas.isEmpty() ? new ArrayList<AgendaTotalJSON>() : aulas).toString();
    }

    @Override
    public String consultarSaldoAluno(Integer matricula, Boolean forcarMarcar, Integer contrato) throws Exception {
        validaSituacaoContrato(matricula, contrato);

        if(contrato == null){
            return saldoSemContrato(matricula, forcarMarcar);
        }

        verificarSituacaoAluno(matricula, contrato);

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(" select tipohorario, quantidadecreditodisponivel from contratoduracaocreditotreino cdc \n"
                + " INNER JOIN contratoduracao cd ON cd.codigo = cdc.contratoduracao \n"
                + " where cd.contrato = " + contrato, con)) {
            if (rs.next()) {
                TipoHorarioCreditoTreinoEnum tipo = TipoHorarioCreditoTreinoEnum.getTipo(rs.getInt("tipohorario"));
                if (tipo.equals(TipoHorarioCreditoTreinoEnum.HORARIO_TURMA)) {
                    Integer saldoExtra = nrAulasARepor(matricula);
                    return (saldoExtra) + ";" + (forcarMarcar ? TipoHorarioCreditoTreinoEnum.LIVRE.getTipoMsg() : tipo.getTipoMsg());
                }
                Integer creditoDisponivel = consultarSaldoCredito(contrato);
                Integer marcacoesFuturas = numeroAulasExtraMarcadas(contrato); //reposicões futuras não vinculadas a desmarcações(ainda não debitaram crédito)
                Integer nrMarcacoesNoDia = nrReposicoesDoDia(contrato);//reposições de hoje que podem ter tido utilizacao
                Integer nrCreditosUsadosNoDia = nrCreditosUsadosDiaFaltaUtilizacao(contrato, Calendario.hoje()); //calculo leva em considaração aulas do dia, caso essa aulas já tenha abatido o crédito, elas devem ser retiradas do cálculo
                nrMarcacoesNoDia -= nrCreditosUsadosNoDia;
                if (nrMarcacoesNoDia <= 0) {
                    nrMarcacoesNoDia = 0; //reposições foram consumidas, não devem afetar cálculo;
                }
                Integer saldoVirtual = creditoDisponivel - marcacoesFuturas - nrMarcacoesNoDia;
                saldoVirtual = saldoVirtual < 0 ? 0 : saldoVirtual;
                return saldoVirtual + ";" + tipo.getTipoMsg();
            } else {
                return consultarAulasDesmarcadasAlunoSemReposicoes(matricula, true) + ";" + TipoHorarioCreditoTreinoEnum.HORARIO_TURMA.getTipoMsg();
            }
        }
    }

    public void validaSituacaoContrato(Integer matricula, Integer codValidarContrato) throws Exception {
        String situacao = "";
        Integer aux = 0;
        String sqlCodContrato = codValidarContrato != null && codValidarContrato > 0 ? " AND c.codigo = " + codValidarContrato + " " : " ";
        try (ResultSet rsContrato = ConexaoZWServiceImpl.criarConsulta("SELECT c.situacao " +
                "FROM contrato c\n" +
                "         JOIN pessoa p ON c.pessoa = p.codigo\n" +
                "         JOIN cliente c2 ON p.codigo = c2.pessoa\n" +
                " WHERE c2.codigomatricula = " + matricula + sqlCodContrato +
                " AND c.situacao <> 'IN'" +
                " GROUP BY c.situacao", con)) {
            while (rsContrato.next()) {
                situacao = rsContrato.getString("situacao");
                aux++;
            }
        }
        if (aux == 1) {
            verificaSituacaoContrato(situacao);
        }
        verificaAtestado(matricula, sqlCodContrato);
    }

    private void verificaAtestado(Integer matricula, String sqlCodContrato) throws Exception {
        String dataAtual = Uteis.getDataFormatoBD(new Date(System.currentTimeMillis()));
        try (ResultSet rsContrato = ConexaoZWServiceImpl.criarConsulta("select *\n" +
                "from contratooperacao\n" +
                "         JOIN contrato c on contratooperacao.contrato = c.codigo\n" +
                "         JOIN pessoa p ON c.pessoa = p.codigo\n" +
                "         JOIN cliente c2 ON p.codigo = c2.pessoa \n" +
                "where c2.codigomatricula = " + matricula + sqlCodContrato +
                "  and (tipooperacao = 'AT' or tipooperacao = 'CR')\n" +
                "  and datafimefetivacaooperacao >= '" + dataAtual +
                "'  and datainicioefetivacaooperacao <= '" + dataAtual + "'", con)) {
            while (rsContrato.next()) {
                String tipoOperacao = rsContrato.getString("tipooperacao");
                if (tipoOperacao.equals("CR")) {
                    throw new Exception("Aluno em Férias");
                } else if (tipoOperacao.equals("AT")) {
                    throw new Exception("Aluno em Atestado");
                }
            }
        }
    }

    private void verificaSituacaoContrato(String situacao) throws Exception {
        if (situacao.equals("TR")) {
            throw new Exception("Aluno está Trancado");
        } else if (situacao.equals("CR")) {
            throw new Exception("Aluno em Férias");
        }
    }

    public Integer consultarSaldoCredito(Integer codigoContrato) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select sum(quantidade) as total \n");
        sql.append("from controleCreditoTreino \n");
        sql.append("where contrato = ").append(codigoContrato);
        PreparedStatement pst = con.prepareStatement(sql.toString());
        try (ResultSet rs = pst.executeQuery()) {
            if (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;
    }

    private String saldoSemContrato(Integer matricula, Boolean forcarMarcar) throws Exception {
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(" select tipohorario,saldocreditotreino,s.codigocontrato  from contratoduracaocreditotreino cdc \n"
                + " INNER JOIN contratoduracao cd ON cd.codigo = cdc.contratoduracao \n"
                + " INNER JOIN situacaoclientesinteticodw s ON s.codigocontrato = cd.contrato and matricula = " + matricula, con)) {
            if (rs.next()) {
                TipoHorarioCreditoTreinoEnum tipo = TipoHorarioCreditoTreinoEnum.getTipo(rs.getInt("tipohorario"));
                if (tipo.equals(TipoHorarioCreditoTreinoEnum.HORARIO_TURMA)) {
                    Integer saldoExtra = nrAulasARepor(matricula);
                    return (saldoExtra) + ";" + (forcarMarcar ? TipoHorarioCreditoTreinoEnum.LIVRE.getTipoMsg() : tipo.getTipoMsg());
                }
                Integer marcacoesFuturas = numeroAulasExtraMarcadas(rs.getInt("codigocontrato")); //reposicões futuras não vinculadas a desmarcações(ainda não debitaram crédito)
                Integer nrMarcacoesNoDia = nrReposicoesDoDia(rs.getInt("codigocontrato"));//reposições de hoje que podem ter tido utilizacao
                Integer nrCreditosUsadosNoDia = nrCreditosUsadosDiaFaltaUtilizacao(rs.getInt("codigocontrato"), Calendario.hoje()); //calculo leva em considaração aulas do dia, caso essa aulas já tenha abatido o crédito, elas devem ser retiradas do cálculo
                nrMarcacoesNoDia -= nrCreditosUsadosNoDia;
                if (nrMarcacoesNoDia <= 0) {
                    nrMarcacoesNoDia = 0; //reposições foram consumidas, não devem afetar cálculo;
                }
                Integer saldoVirtual = (rs.getInt("saldocreditotreino") - marcacoesFuturas) - nrMarcacoesNoDia;
                return saldoVirtual.toString() + ";" + tipo.getTipoMsg();
            } else {
                return consultarAulasDesmarcadasAlunoSemReposicoes(matricula, true) + ";" + TipoHorarioCreditoTreinoEnum.HORARIO_TURMA.getTipoMsg();
            }
        }
    }

    public int numeroAulasExtraMarcadas(int codigoContrato) throws Exception {  // avalia aulas extras, tanto pra contrato com turma e contrato livre obrigatorio marcar horário
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT COUNT(r.codigo) AS qtde FROM reposicao r \n");
        sql.append(" LEFT JOIN auladesmarcada a on a.reposicao = r.codigo \n");
        sql.append(" WHERE  r.turmaorigem = r.turmadestino  \n");
        sql.append(" AND r.horarioturma = r.horarioturmaorigem  \n");
        sql.append(" AND cast(r.datareposicao as date) = cast (r.dataorigem as date)  \n");
        sql.append(" AND r.contrato = ").append(codigoContrato);
        sql.append(" AND a.codigo is null \n");
        sql.append(" AND r.datareposicao::date > '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("'");
        int numeroAulas;
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            numeroAulas = 0;
            while (rs.next()) {
                numeroAulas = rs.getInt("qtde");
            }
        }
        return numeroAulas;
    }

    public int nrReposicoesDoDia(int codigoContrato) throws Exception { // avalia aulas desmarcadas
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT COUNT(r.codigo) AS qtde FROM reposicao r \n");
        sql.append(" WHERE  r.contrato = ").append(codigoContrato);
        sql.append(" AND r.datareposicao = '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("'");
        int numeroAulas;
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            numeroAulas = 0;
            while (rs.next()) {
                numeroAulas = rs.getInt("qtde");
            }
        }
        return numeroAulas;
    }

    public int nrCreditosUsadosDiaFaltaUtilizacao(int codigoContrato, Date dataReferencia) throws Exception { // avalia aulas extras
        StringBuilder sql = new StringBuilder();
        sql.append(" select sum(quantidade)*-1 AS qtde from controlecreditotreino \n");
        sql.append(" WHERE   contrato = ").append(codigoContrato);
        sql.append(" AND tipooperacaocreditotreino in (").append(TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO.getCodigo());
        sql.append(",").append(TipoOperacaoCreditoTreinoEnum.UTILIZACAO.getCodigo()).append(") \n");
        sql.append(" AND dataoperacao::date = '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("'");
        int numeroAulas;
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            numeroAulas = 0;
            while (rs.next()) {
                numeroAulas = rs.getInt("qtde");
            }
        }
        return numeroAulas;
    }

    public Integer consultarAulasDesmarcadasAlunoSemReposicoes(Integer matricula, boolean contratoNormal) throws Exception {
        Integer validadeReposicao = 0;
        boolean habilitarSomaDeAulaNaoVigente = false;
        if (contratoNormal) {
            try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("select tempoAposFaltaReposicao, habilitarSomaDeAulaNaoVigente from empresa e inner join contrato c on c.empresa = e.codigo "
                    + "inner join situacaoclientesinteticodw s ON c.codigo = s.codigocontrato where  s.matricula = " + matricula, con)) {
                if (rs.next()) {
                    validadeReposicao = rs.getInt("tempoAposFaltaReposicao");
                    habilitarSomaDeAulaNaoVigente = rs.getBoolean("habilitarSomaDeAulaNaoVigente");
                }
            }
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT count(distinct a.codigo) as nr \n");
        sql.append("FROM auladesmarcada a \n");
        sql.append("INNER JOIN contrato c ON a.contrato = c.codigo AND c.situacao = 'AT'\n");
        sql.append("INNER JOIN cliente cli ON c.pessoa = cli.pessoa\n");
        sql.append("INNER JOIN contratomodalidade cm on cm.contrato = c.codigo\n");
        sql.append("INNER JOIN contratomodalidadeturma cmt on cmt.contratomodalidade = cm.codigo\n");
        sql.append("INNER JOIN horarioTurma ht ON ht.codigo = a.horarioTurma \n");
        sql.append("INNER JOIN turma t ON t.codigo = ht.turma \n");
        sql.append("INNER JOIN modalidade m ON t.modalidade = m.codigo \n");
        sql.append("INNER JOIN turma t2 on cmt.turma = t2.codigo \n");
        sql.append("WHERE  permiteReporAulaDesmarcada = true and datareposicao is null\n");
        sql.append(" AND cli.codigomatricula = ").append(matricula);
        // quando habilitarSomaDeAulaNaoVigente habilitado compara com outros códigos de turmas que não estão presentes no contratomodalidadeturma
        if (!habilitarSomaDeAulaNaoVigente) {
            sql.append(" AND t2.modalidade = t.modalidade \n");
        }
        sql.append(" AND m.utilizarturma is true \n");
        if (UteisValidacao.notEmptyNumber(validadeReposicao)) {
            sql.append(" and a.dataOrigem >= '").append(Uteis.getDataJDBC(Uteis.somarDias(Calendario.hoje(), -validadeReposicao))).append("' ");
        }
        int aulasDesmarcadas;
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            aulasDesmarcadas = rs.next() ? rs.getInt("nr") : 0;
        }
        return aulasDesmarcadas;
    }

    public Integer nrAulasARepor(Integer matricula) throws Exception {
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("select sc.validarSaldoCreditoTreino, sc.saldoCreditoTreino, sc.codigocontrato "
                + " FROM situacaoclientesinteticodw sc where matricula = " + matricula, con)) {
            if (rs.next()) {
                Integer contrato = rs.getInt("codigocontrato");
                Integer saldo = rs.getInt("saldoCreditoTreino");
                Integer nrAulasExtras = nrAulasExtras(matricula);
                Integer nrAulasDesmarcadas = consultarAulasDesmarcadasAlunoSemReposicoes(matricula, false);
                Integer nrAulasARepor = (nrAulasExtras < 0 ? 0 : nrAulasExtras) + nrAulasDesmarcadas;
                if (nrAulasARepor > saldo) {
                    nrAulasARepor = saldo;
                }
                if (nrAulasARepor > 0) {
                    return nrAulasARepor;
                }
            }
        }
        return 0;
    }

    public Integer nrAulasExtras(Integer matricula) throws Exception {
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("select sc.validarSaldoCreditoTreino, sc.saldoCreditoTreino, sc.codigocontrato "
                + " FROM situacaoclientesinteticodw sc where matricula = " + matricula, con)) {
            if (rs.next()) {
                Integer contrato = rs.getInt("codigocontrato");
                Integer saldo = rs.getInt("saldoCreditoTreino");
                Map<Integer, Date> mapaDia = verificarTotalCreditoPossivelUtilizar(contrato, Calendario.hoje(), obterUltimaDataMatriculaAluno(contrato), null);
                Integer nrAulasRestantesPossiveis = 0;  // quantidade de aulas do aluno dentro da vigencia da turma
                Integer nrAulasDesmarcadasPraFrente = calcularDesmarcadosReposicoes(contrato, Calendario.hoje(), false, false); // aulas futuras que foram desmarcadas
                Integer nrAulasDesmarcadas = calcularDesmarcadosReposicoes(contrato, null, true, null); // aulas desmarcadas aguardando reposicao(ainda não debitaram crédito)
                Set<Map.Entry<Integer, Date>> set = mapaDia.entrySet();
                for (Map.Entry<Integer, Date> ent : set) {
                    nrAulasRestantesPossiveis = ent.getKey();
                }
                Integer nrAulasExtraMarcadas = numeroAulasExtraMarcadas(contrato); //reposicões futuras não vinculadas a desmarcações(ainda não debitaram crédito)
                Integer nrReposicoesFuturasAulaDermacadas = nrReposicoesFututasDeAulasDesmarcadas(contrato); //reposicões futuras de aulas desmarcadas(ainda não debitaram crédito)
                Integer nrReposicoesNoDia = nrReposicoesDoDia(contrato);//reposições de hoje que podem ter tido utilizacao
                Integer quantidadeDeAulasFuturasNasTurmas = nrAulasDesmarcadasPraFrente > nrAulasRestantesPossiveis ? 0
                        : (nrAulasRestantesPossiveis - nrAulasDesmarcadasPraFrente); // Quantidade de aulas possívies menos desmarcações futuras.

                //sistema considera nos calculos acima, aula e reposições de hoje para frente. Caso o aluno tenha consumido a aula  ou reposicão de hoje
                // Essa aula/reposicao de hoje, já consumiu crédito e deve ser subtraída. Como falta só é computada de madruga, em primeiro momento não é necessário verificar
                // faltas. Mas consigerando uma melhoria futura, onde um processo rodasse de hora em hora já dando falta para alunos de turma que já passaram do horário,
                // irei verificar, mas como disse, do modo que está hoje não interfere. Ajuste manuais serão desconsiderados, pois afetam não tem ligações com as aulas de hoje.
                Integer nrCreditosUsadosNoDia = nrCreditosUsadosDiaFaltaUtilizacao(contrato, Calendario.hoje()); //calculo leva em considaração aulas do dia, caso essa aulas já tenha abatido o crédito, elas devem ser retiradas do cálculo
                nrCreditosUsadosNoDia = nrCreditosUsadosNoDia - nrReposicoesNoDia;
                if (nrCreditosUsadosNoDia >= 0) {
                    nrReposicoesNoDia = 0; //reposições foram consumidas, não devem afetar cálculo;
                } else {
                    nrReposicoesNoDia = Math.abs(nrCreditosUsadosNoDia);
                    nrCreditosUsadosNoDia = 0;  // créditos foram para reposições do dia
                }
                Integer nrAulasExtras = saldo - (quantidadeDeAulasFuturasNasTurmas - nrCreditosUsadosNoDia) - nrAulasExtraMarcadas - nrAulasDesmarcadas - nrReposicoesFuturasAulaDermacadas - nrReposicoesNoDia;
                return nrAulasExtras;
            }
        }
        return 0;
    }

    public Date obterUltimaDataMatriculaAluno(Integer codigoContrato) throws Exception {
        Date ultimaDataMatricula = null;
        try (ResultSet resultSet = ConexaoZWServiceImpl.criarConsulta("select max(datafim) as ultima from matriculaalunohorarioturma  where contrato  = " + codigoContrato, con)) {
            if (resultSet.next()) {
                ultimaDataMatricula = resultSet.getDate("ultima");
            }
        }
        if (ultimaDataMatricula == null) {
            try (ResultSet resultSet = ConexaoZWServiceImpl.criarConsulta("select vigenciaateajustada from contrato where codigo  = " + codigoContrato, con)) {
                if (resultSet.next()) {
                    ultimaDataMatricula = resultSet.getDate("vigenciaateajustada");
                }
            }
        }
        return ultimaDataMatricula;
    }

    public Map<Integer, Date> verificarTotalCreditoPossivelUtilizar(Integer codigoContrato, Date dataInicio, Date fim, final Integer saldo)throws Exception{
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("SELECT ht.diasemananumero FROM contratomodalidade cm\n" +
                " INNER JOIN contratomodalidadeturma cmt ON cmt.contratomodalidade = cm.codigo\n" +
                " INNER JOIN contratomodalidadehorarioturma cmht ON cmht.contratomodalidadeturma = cmt.codigo \n" +
                " INNER JOIN horarioturma ht ON ht.codigo = cmht.horarioturma \n" +
                "WHERE cm.contrato = " + codigoContrato, con)) {

            List<Integer> diasSemanasNumeros = new ArrayList<>();
            while (rs.next()) {
                   diasSemanasNumeros.add(rs.getInt("diasemananumero"));
            }
            List<Date> listaDias = Uteis.getDiasEntreDatas(dataInicio, fim);
            Integer totalCreditoPossivelUtilizar = 0;
            Date ultimoDia = null;
            for (Date dia: listaDias){
                Calendar diaVerificar = Calendario.getInstance();
                diaVerificar.setTime(dia);
                for (Integer diaNumero: diasSemanasNumeros) {
                    if (diaNumero.equals(diaVerificar.get(Calendar.DAY_OF_WEEK))){
                        totalCreditoPossivelUtilizar ++;
                        ultimoDia = dia;
                        break;
                    }
                }
                if(saldo != null && totalCreditoPossivelUtilizar >= saldo){ // caso o saldo seja informado, sistema irá limitar ao saldo
                    break;
                }
            }
            Map<Integer, Date> mapaDia = new HashMap<Integer, Date>();
            mapaDia.put(totalCreditoPossivelUtilizar,ultimoDia);
            return mapaDia;
        }
    }

    private int calcularDesmarcadosReposicoes(int codigoContrato, Date limite, boolean considerarPermiteReporAulaDesmarcada, Boolean considerarRepostas) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT  count(ad.codigo) as total \n");
        sql.append("from aulaDesmarcada  ad \n");
        sql.append("inner join horarioTurma ht on ht.codigo = ad.horarioTurma \n");
        sql.append(" where ad.contrato = ").append(codigoContrato);
        if (considerarPermiteReporAulaDesmarcada) {
            sql.append(" and permiteReporAulaDesmarcada = true ");
        }
        if (limite == null) {
            sql.append(" and datareposicao is null ");
        } else {
            sql.append(" and ad.dataOrigem::date >= '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("' ");
            if (considerarRepostas != null && considerarRepostas) {
                sql.append(" and ( datareposicao is null or to_timestamp(to_char(datareposicao, 'YYYY-MM-DD') ||' ' || ht.horaFinal,  'YYYY-MM-DD HH24:MI:SS') > '");
                sql.append(sdf.format(Calendario.hoje())).append("' )");
            }
        }
        int numeroReposicao;
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            numeroReposicao = 0;
            while (rs.next()) {
                numeroReposicao = rs.getInt("total");
            }
        }
        return numeroReposicao;
    }

    private int nrReposicoesFututasDeAulasDesmarcadas(int codigoContrato) throws Exception { // avalia aulas desmarcadas
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT COUNT(r.codigo) AS qtde FROM reposicao r \n");
        sql.append(" inner JOIN auladesmarcada a on a.reposicao = r.codigo \n");
        sql.append(" WHERE  r.contrato = ").append(codigoContrato);
        sql.append(" AND r.datareposicao > '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("'");
        int numeroAulas;
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
            numeroAulas = 0;
            while (rs.next()) {
                numeroAulas = rs.getInt("qtde");
            }
        }
        return numeroAulas;
    }

    public Map<Date, List<Integer>> consultarAgendamentosDesmarcados(Integer cliente)throws Exception{
        Map<Date, List<Integer>> mapaDesmarcados = new HashMap<Date, List<Integer>>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT dataorigem, horarioturma FROM auladesmarcada ad\n");
        sql.append("INNER JOIN cliente cli ON cli.codigo = ad.cliente\n");
        sql.append("INNER JOIN contrato c ON ad.contrato = c.codigo AND c.situacao = 'AT'\n");
        sql.append("WHERE dataorigem >= ? AND cliente = ? AND contratoanterior IS NULL");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(Calendario.hoje())));
        pst.setInt(2,cliente);
        try (ResultSet rs = pst.executeQuery()) {
            while (rs.next()) {
                List<Integer> horarios = mapaDesmarcados.get(rs.getDate("dataorigem"));
                if (horarios == null) {
                    horarios = new ArrayList<Integer>();
                    mapaDesmarcados.put(rs.getDate("dataorigem"), horarios);
                }
                horarios.add(rs.getInt("horarioturma"));
            }
        }
        return mapaDesmarcados;

    }

    public List<AgendaTotalJSON> consultarProximasReposicoes(Integer matricula, Date agora, boolean somenteMesmoDia, Integer modalidade, Integer tipoModalidade)throws Exception{
        List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlConsultaMatriculaHorarioTurma(matricula, false, agora, somenteMesmoDia, modalidade, tipoModalidade), con)) {
            while (rs.next()) {
                Date dataHoraRep = Calendario.getDataComHora(rs.getDate("datainicio"), rs.getString("horainicial"));
                if (dataHoraRep.before(agora) && Calendario.igual(Calendario.hoje(), Calendario.getDataComHoraZerada(agora))) {
                    continue;
                }
                AgendaTotalJSON dadosAgenda = montarDadosAgenda(rs);
                consultaOcupacoes(rs.getDate("datainicio") , dadosAgenda, con);
                try (ResultSet rsVideo = ConexaoZWServiceImpl.criarConsulta("select codigo , turma_codigo , linkvideo , professor  from turmavideo where turma_codigo = "+dadosAgenda.getCodigoTurma(), con)){
                    List<TurmaVideoDTO> linkVideos = new ArrayList<>();
                    while (rsVideo.next()) {
                        TurmaVideoDTO turmaVideoDTO = new TurmaVideoDTO();
                        turmaVideoDTO.setId(rsVideo.getInt("codigo"));
                        turmaVideoDTO.setLinkVideo(rsVideo.getString("linkvideo"));
                        turmaVideoDTO.setProfessor(rsVideo.getBoolean("professor"));
                        linkVideos.add(turmaVideoDTO);
                    }
                    dadosAgenda.setLinkVideos(linkVideos);
                }

                aulas.add(new AgendaTotalJSON(rs.getDate("datainicio"), dadosAgenda));
            }
        }
        return aulas;
    }

    public String sqlConsultaMatriculaHorarioTurma(Integer matricula, boolean desmarcadas, Date agora, boolean somenteMesmoDia, Integer modalidade,  Integer tipoModalidade) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT t.codigo as turma, t.empresa, \n");
        sql.append(" t.identificador,t.aulaColetiva, m.codigo as modalidade, m.nome as nomemodalidade, \n");
        sql.append(" ht.diasemana,ht.professor,p.nome as nomeprofessor, ht.ambiente,   \n");
        sql.append(" nivt.descricao,   \n");
        sql.append(" ht.horainicial, ht.horafinal, ht.nrmaximoaluno, ht.codigo as horarioturma,r.datareposicao as datainicio,r.datareposicao as datafim, r.contrato,\n");
        sql.append(" exists(select codigo from demandahorarioturma dht where dht.cliente = cli.codigo AND dht.horarioturma = ht.codigo) as jaMarcouEuQuero,\n");
        sql.append(" p.fotokey as fotoProfessor,\n");
        sql.append(" a.descricao as nomeambiente,\n");
        sql.append(" m.fotokey as fotomodalidade\n");
        sql.append(" FROM reposicao r  \n");
        sql.append(" INNER JOIN cliente cli ON r.cliente = cli.codigo \n");
        sql.append(" INNER JOIN contrato sc ON cli.pessoa = sc.pessoa AND r.contrato = sc.codigo \n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = r.horarioturma \n");
        sql.append(" INNER JOIN colaborador c ON c.codigo = ht.professor \n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa \n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma \n");
        sql.append(" INNER JOIN nivelturma nivt ON nivt.codigo = ht.nivelturma\n");
        sql.append(" INNER JOIN modalidade m ON t.modalidade = m.codigo \n");
        sql.append(" INNER JOIN contrato con ON con.codigo = r.contrato AND con.situacao = 'AT' \n");
        sql.append(" INNER JOIN ambiente a ON a.codigo = ht.ambiente \n");
        sql.append(" WHERE cli.codigomatricula = ").append(matricula);
        sql.append(" and r.datareposicao ").append(somenteMesmoDia ? "= '" : ">= '").append(Uteis.getDataJDBC(Calendario.getDataComHoraZerada(agora))).append("'");
        sql.append(" AND not t.aulacoletiva \n");
        sql.append(" AND t.datafinalvigencia >= '").append(Uteis.getDataJDBC(Calendario.getDataComHoraZerada(agora))).append("'");
        if (UteisValidacao.notEmptyNumber(tipoModalidade)) {
            sql.append(" and m.tipo = ").append(tipoModalidade);
        } else if (UteisValidacao.notEmptyNumber(modalidade)){
            sql.append(" and m.codigo = ").append(modalidade);
        }
        sql.append(" ORDER BY r.datareposicao");
        return sql.toString();
    }

    private String sqlConsultaMatriculaHorarioTurma(Integer matricula, String diaSemana, Date limite, boolean aulasFuturas, Integer modalidade, Integer tipoModalidade) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT t.codigo as turma, t.empresa, t.idademinima as idademinima, t.idademinimameses as idademinimameses, \n");
        sql.append(" t.idademaxima as idademaxima, t.idademaximameses as idademaximameses,\n");
        sql.append(" t.identificador,t.aulaColetiva, m.codigo as modalidade, m.nome as nomemodalidade,\n");
        sql.append(" nt.descricao,\n");
        sql.append(" ht.diasemana,ht.professor,p.nome as nomeprofessor, ht.ambiente, a.descricao as nomeambiente, con.vigenciaateajustada,  \n");
        sql.append(" ht.horainicial, ht.horafinal, ht.nrmaximoaluno, ht.codigo as horarioturma,maht.datainicio,maht.datafim, maht.contrato,con.vigenciade,maht.codigo as codigomatriculaalunohorarioturma,\n");
        sql.append(" p.fotokey as fotoProfessor,\n");
        sql.append(" exists(select codigo from demandahorarioturma dht where dht.cliente = cli.codigo AND dht.horarioturma = ht.codigo) as jaMarcouEuQuero, m.fotokey as fotoModalidade\n");
        sql.append(" FROM matriculaalunohorarioturma maht  \n");
        sql.append(" INNER JOIN cliente cli ON cli.pessoa = maht.pessoa\n");
        sql.append(" INNER JOIN horarioturma ht ON ht.codigo = maht.horarioturma\n");
        if(!UteisValidacao.emptyString(diaSemana)){
            sql.append(" AND ht.diasemana = '").append(diaSemana).append("'\n");
        }
        sql.append(" INNER JOIN colaborador c ON c.codigo = ht.professor\n");
        sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa\n");
        sql.append(" INNER JOIN turma t ON t.codigo = ht.turma\n");
        sql.append(" INNER JOIN nivelturma nt ON nt.codigo = ht.nivelturma\n");
        sql.append(" INNER JOIN modalidade m ON t.modalidade = m.codigo AND m.utilizarturma\n");
        sql.append(" INNER JOIN contrato con ON con.codigo = maht.contrato AND con.situacao = 'AT'\n");
        sql.append(" INNER JOIN ambiente a ON a.codigo = ht.ambiente \n");
        sql.append(" WHERE cli.codigomatricula = ").append(matricula);
        if(limite != null){
            sql.append(" AND maht.datafim >= '").append(Uteis.getDataJDBC(limite));
            sql.append("' AND maht.datainicio <= '").append(Uteis.getDataJDBC(limite)).append("' ");
        }
        if (UteisValidacao.notEmptyNumber(tipoModalidade)) {
            sql.append(" and m.tipo = ").append(tipoModalidade);
        } else if (UteisValidacao.notEmptyNumber(modalidade)) {
            sql.append(" and m.codigo = ").append(modalidade);
        }
        sql.append(" ORDER BY maht.datafim");

        return sql.toString();
    }

    public static void consultaOcupacoes(Date dia, AgendaTotalJSON agTmp, Connection con) throws Exception {
        int ocupacao;
        String dataDia = Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd");

        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta("select\n" +
                        "    (\n" +
                        "    select count(*)\n" +
                        "    from matriculaalunohorarioturma mat2\n" +
                        "    inner join cliente c on c.pessoa = mat2.pessoa\n" +
                        "    left join auladesmarcada ad on ad.cliente = c.codigo\n" +
                        "        and ad.horarioturma = mat2.horarioturma\n" +
                        "        and ad.dataorigem = '" + dataDia + "'\n" +
                        "    where mat2.horarioturma = " + agTmp.getId() + "\n" +
                        "        and '" + dataDia + "' between mat2.datainicio and mat2.datafim\n" +
                        "        and ad.codigo is null\n" +
                        ") + (\n" +
                        "    select count(*)\n" +
                        "    from reposicao rep2\n" +
                        "    where rep2.horarioturma = " + agTmp.getId() + "\n" +
                        "        and rep2.datareposicao = '" + dataDia + "'\n" +
                        ") as ocupacao;"
                , con)) {
            ocupacao = rs.next() ? rs.getInt("ocupacao") : 0;
        }
        agTmp.setOcupacao(ocupacao);
    }

    public List<AgendaTotalJSON> consultarAulasProximos30dias(Integer matricula,  Date agora,
                                                              Map<Date, List<Integer>> mapaDesmarcados,
                                                              Integer saldo)throws Exception{

        Set<Date> diasFeriados = null;
        Integer codigoEmpresa = 0;
        EmpresaVO empresaVO = null;
        List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();
        List<AgendaTotalJSON> aulasMarcadas;
        Map<Integer, String> mapadiasSemana;
        Date limite;
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlConsultaMatriculaHorarioTurma(matricula, null, null,true, null, null), con)) {
            aulasMarcadas = new ArrayList<>();
            mapadiasSemana = new HashMap<>();
            limite = agora;
            while (rs.next()) {
                limite = Uteis.somarDias(rs.getDate("vigenciaateajustada"), 1);
                mapadiasSemana.put(rs.getInt("horarioturma"), rs.getString("diasemana"));
                aulasMarcadas.add(montarDadosAgenda(rs));
                codigoEmpresa = rs.getInt("empresa");
            }
            if (limite.compareTo(Uteis.somarDias(agora, 30)) > 0) {
                limite = Uteis.somarDias(Calendario.hoje(), 30);
            }
        }
        if (UteisValidacao.notEmptyNumber(codigoEmpresa)) {
            empresaVO = consultarEmpresa(codigoEmpresa);
            diasFeriados = consultarPorPeriodoEmpresaAulaCheia(Calendario.hoje(), limite, empresaVO);
        }

        Integer quantidadeCredtidoDisponivel;
        try (ResultSet qtd = ConexaoZWServiceImpl.criarConsulta("select saldocreditotreino,codigocliente,matricula  from situacaoclientesinteticodw  where   matricula = " + matricula, con)) {
            quantidadeCredtidoDisponivel = 0;
            while (qtd.next()) {
                try {
                    quantidadeCredtidoDisponivel = qtd.getInt("saldocreditotreino");
                } catch (Exception e) {
                }
            }
        }

        List<Date> diasEntreDatas = Uteis.getDiasEntreDatas(Calendario.hoje(), limite);
        for(Date dia : diasEntreDatas){
            for(AgendaTotalJSON agTmp : aulasMarcadas){
                AgendaTotalJSON ag = (AgendaTotalJSON) SerializationUtils.clone(agTmp);
                if (!UteisValidacao.emptyList(aulas) && (aulas.size() == (quantidadeCredtidoDisponivel - saldo))) {
                    break;
                }

                List<Integer> horariosDesmarcados = mapaDesmarcados.get(Calendario.getDataComHoraZerada(dia));
                DiasSemana diaSemana = DiasSemana.getDiaSemana(Uteis.obterDiaSemanaData(dia));
                Date agDia = Calendario.getDataComHora(dia, ag.getInicio());
                if(agDia.after(agora)
                        && (horariosDesmarcados == null || !horariosDesmarcados.contains(Integer.valueOf(ag.getId())))
                        && Calendario.maiorOuIgual(dia, ag.getInicioVigencia())
                        && Calendario.menorOuIgual(dia, ag.getFimVigencia())
                        && diaSemana.getCodigo().equals(mapadiasSemana.get(Integer.valueOf(ag.getId())))){
                    if (diasFeriados != null ) {
                        if (empresaVO.isPermMarcarAulaFeriado() && isFeriado(dia, diasFeriados)) {
                            final Long horaAbertura = Calendario.pegaHoraEmMilisegundos(empresaVO.getHoraAberturaFeriado());
                            final Long horaFechamento = Calendario.pegaHoraEmMilisegundos(empresaVO.getHoraFechamentoFeriado());
                            final Long horaInicio = Calendario.pegaHoraEmMilisegundos(ag.getInicio());
                            final Long horaFim = Calendario.pegaHoraEmMilisegundos(ag.getFim());

                            if (!isHorarioIntervaloNaoPermitido(horaAbertura, horaFechamento, horaInicio, horaFim)) {
                                consultaOcupacoes(dia,ag,con);
                                aulas.add(new AgendaTotalJSON(dia, ag));
                            }
                        } else {
                            if (!isFeriado(dia, diasFeriados)) {
                                consultaOcupacoes(dia,ag,con);
                                aulas.add(new AgendaTotalJSON(dia, ag));
                            }
                        }
                    }
                }
            }
        }
        return aulas;
    }

    public Set<Date> consultarPorPeriodoEmpresaAulaCheia(Date inicio, Date fim, EmpresaVO empresaVO) throws Exception{
        StringBuilder sql = new StringBuilder();
        HashSet<Date> dias = new HashSet<>();
        HashSet<Date> recorrentes = new HashSet<>();

        sql.append("select dia from feriado where naorecorrente = false");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                while (rs.next()) {
                    if(Calendario.dataNoMesmoMes(rs.getDate("dia"), inicio) ||
                            Calendario.dataNoMesmoMes(rs.getDate("dia"), fim))
                        recorrentes.add(rs.getDate("dia"));
                }
            }
        }
        recorrentes.add(inicio);
        for(Date anoFeriado : recorrentes) {
            sql.delete(0, sql.length());
            sql.append("select codigo, dia, naorecorrente from ( select codigo, dia, naorecorrente  ");
            sql.append("   from feriado a where naorecorrente = false and nacional = true ");
            sql.append("union ");
            sql.append("select codigo, dia, naorecorrente  ");
            sql.append("   from feriado a where naorecorrente = true and nacional = true ");
            sql.append("union ");
            sql.append("select codigo, dia, naorecorrente from feriado a where estado = ? and nacional = false and estadual = true ");
            sql.append("union  ");
            sql.append("select codigo, dia, naorecorrente from feriado a where estado = ? and cidade = ? and nacional = false and estadual = false ");
            sql.append("union  ");
            sql.append("select codigo, dia, naorecorrente from feriado a where estado = ? and cidade = ? and nacional = false and estadual = false and naorecorrente = false");
            sql.append(") tb where ((dia between ? and ?) or ");
            sql.append(" (dia::date between '" +  Uteis.getDataJDBC(
                    Uteis.obterPrimeiroDiaMes(anoFeriado))
                    +"' and '"+
                    Uteis.getDataJDBC(
                            Uteis.obterUltimoDiaMes(anoFeriado))
                    +"' and naorecorrente = false)) order by dia asc; ");

            try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
                stm.setInt(1, empresaVO.getEstado().getCodigo());
                stm.setInt(2, empresaVO.getEstado().getCodigo());
                stm.setInt(3, empresaVO.getCidade().getCodigo());
                stm.setInt(4, empresaVO.getEstado().getCodigo());
                stm.setInt(5, empresaVO.getCidade().getCodigo());
                stm.setDate(6, Uteis.getDataJDBC(inicio));
                stm.setDate(7, Uteis.getDataJDBC(fim));


                try (ResultSet rs = stm.executeQuery()) {
                    while (rs.next()) {
                        if(!Calendario.maior(rs.getDate("dia"), fim) && (Calendario.dataNoMesmoMes(rs.getDate("dia"), inicio) ||
                                Calendario.dataNoMesmoMes(rs.getDate("dia"), fim))) {
                            dias.add(rs.getDate("dia"));
                        }
                    }
                }
            }
        }
        return dias;

    }

    private boolean isFeriado(Date dia, Set<Date> diasFeriados) {
        for (Date dataFeriado : diasFeriados) {
            if (Calendario.dataNoMesmoDiaMes(dataFeriado, dia)) {
                return true;
            }
        }
        return false;
    }

    public List<AgendaTotalJSON> consultarProximasAulasModalidadesDiferentes(Integer matricula, Map<Date, List<Integer>> mapaDesmarcados)throws Exception{
        List<AgendaTotalJSON> aulas = new ArrayList<AgendaTotalJSON>();
        Map<Integer, Map<Date, AgendaTotalJSON>> mapa;
        try (ResultSet rs = ConexaoZWServiceImpl.criarConsulta(sqlConsultaMatriculaHorarioTurma(matricula, null, null,false, null, null), con)) {
            mapa = new HashMap<Integer, Map<Date, AgendaTotalJSON>>();
            while (rs.next()) {
                Integer codigoHorarioTurma = rs.getInt("horarioturma");
                AgendaTotalJSON item = montarDadosAgenda(rs);
                Map<Date, AgendaTotalJSON> mapHour = mapa.get(rs.getInt("turma"));
                if (mapHour == null) {
                    mapHour = new HashMap<Date, AgendaTotalJSON>();
                    mapa.put(rs.getInt("turma"), mapHour);
                }
                Date dataReferencia = Calendario.hoje();
                Date proximaAula = null;
                while (proximaAula == null) {
                    Date possivelProximaAula = proximaAula(rs.getString("horainicial"), rs.getDate("datainicio"), rs.getString("diasemana"), dataReferencia);
                    if (mapaDesmarcados.get(Calendario.getDataComHoraZerada(possivelProximaAula)) == null
                            || !mapaDesmarcados.get(Calendario.getDataComHoraZerada(possivelProximaAula)).contains(codigoHorarioTurma)) {
                        proximaAula = possivelProximaAula;
                    } else {
                        dataReferencia = Uteis.somarDias(possivelProximaAula, 1);
                    }
                }
                consultaOcupacoes(proximaAula,item,con);
                item.setInicio(Uteis.getData(proximaAula) + " " + item.getInicio());
                item.setFim(Uteis.getData(proximaAula) + " " + item.getFim());
                mapHour.put(proximaAula, item);
            }
        }
        for(Map<Date,AgendaTotalJSON> mapaData : mapa.values()){
            try {
                List<Date> datas = new ArrayList<Date>(mapaData.keySet());
                Collections.sort(datas);
                aulas.add(mapaData.get(datas.get(0)));
            } catch (Exception e) {
            }
        }
        return aulas;
    }

    private Date proximaAula(String horaInicio, Date inicioMatricula, String diaSemana, Date data){
        DiasSemana diaSemanaEnum = DiasSemana.getDiaSemana(diaSemana);
        if(Calendario.maior(inicioMatricula,data)){
            return Calendario.getDataComHora(Calendario.proximoDiaSemana(diaSemanaEnum.getNumeral(), inicioMatricula), horaInicio);
        }
        DiasSemana diaSemanaHoje = DiasSemana.getDiaSemana(Uteis.obterDiaSemanaData(data));
        Date proximaData = diaSemanaEnum.equals(diaSemanaHoje) ?
                Calendario.getDataComHora(data, horaInicio)
                : Calendario.getDataComHora(Calendario.proximoDiaSemana(diaSemanaEnum.getNumeral(), data), horaInicio);
        return proximaData.before(data) ?
                Calendario.getDataComHora(Calendario.proximoDiaSemana(diaSemanaEnum.getNumeral(), proximaData), horaInicio) :
                proximaData;
    }

    public Boolean buscaAutorizadoPorMatricula(Integer integer, String chaveOrigem, String tipoAula) throws Exception {
        try {
            StringBuilder sql = sqlIntegracaoAutorizado(integer, chaveOrigem);
            try (ResultSet resultSet = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
                while (resultSet.next()) {
                    String modalidadeEncoded = URLEncoder.encode(tipoAula.trim(), StandardCharsets.UTF_8.toString());

                    String resposta = ExecuteRequestHttpService.executeRequestGET(resultSet.getString("urlzillyonweb") + "/prest/aulacheia/validar-modalidade?"
                            + "pessoa=" + resultSet.getInt("codigopessoa") + "&chave=" + resultSet.getString("chave") + "&modalidade=" + modalidadeEncoded.trim());

                    JSONObject jsonObject = new JSONObject(resposta);
                    return jsonObject.getBoolean("content");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private static StringBuilder sqlIntegracaoAutorizado(Integer integer, String chaveOrigem) {
        StringBuilder sql = new StringBuilder(" select i.chave, a.codigomatricula, a.nomepessoa, a.codigopessoa, i.urlzillyonweb  from autorizacaoacessogrupoempresarial a\n");
        sql.append(" inner join integracaoacessogrupoempresarial i on i.codigo = a.integracaoacessogrupoempresarial  ");
        sql.append(" where codigomatricula = ").append(integer);
        sql.append(" and i.chave = '").append(chaveOrigem).append("'");
        return sql;
    }

    public Boolean buscaAutorizadoPorMatriculaSemValidarModalidade(String ctx, Integer integer, String chaveOrigem, AutorizacaoAcessoGrupoEmpresarialVO autorizacaoAcessoGrupoEmpresarialVO) {
        if (autorizacaoAcessoGrupoEmpresarialVO != null && !UteisValidacao.emptyNumber(autorizacaoAcessoGrupoEmpresarialVO.getCodigo())) {
            try {
                String resposta = ExecuteRequestHttpService.executeRequestGET(autorizacaoAcessoGrupoEmpresarialVO.getIntegracao().getUrlZillyonWeb() + "/prest/aulacheia/validar-modalidade?"
                        + "pessoa=" + autorizacaoAcessoGrupoEmpresarialVO.getCodigoPessoa() + "&chave=" + autorizacaoAcessoGrupoEmpresarialVO.getIntegracao().getChave());

                JSONObject jsonObject = new JSONObject(resposta);
                return jsonObject.getBoolean("content");
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            StringBuilder sql = sqlIntegracaoAutorizado(integer, chaveOrigem);
            try (ResultSet resultSet = ConexaoZWServiceImpl.criarConsulta(sql.toString(), con)) {
                while (resultSet.next()) {
                    String resposta = ExecuteRequestHttpService.executeRequestGET(resultSet.getString("urlzillyonweb") + "/prest/aulacheia/validar-modalidade?"
                            + "pessoa=" + resultSet.getInt("codigopessoa") + "&chave=" + resultSet.getString("chave"));

                    JSONObject jsonObject = new JSONObject(resposta);
                    return jsonObject.getBoolean("content");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return false;
    }
}
