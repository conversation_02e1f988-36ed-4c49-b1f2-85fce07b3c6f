/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.programa;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.cliente.PessoaPrescricaoDTO;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.perfil.PerfilDTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoAndamento;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.acompanhamento.AcompanhamentoSimplesJSON;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.cliente.ClienteJSON;
import br.com.pacto.controller.json.programa.ProgramaTreinoJSONControle;
import br.com.pacto.controller.json.programa.read.OrigemEnum;
import br.com.pacto.controller.json.programa.read.ProgramaTreinoJSON;
import br.com.pacto.controller.json.programa.read.ProgramaVersaoJSON;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoAndamentoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.ColaboradoresExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.ProgramaTreinoExcecoes;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.perfil.PerfilService;
import br.com.pacto.service.intf.programa.PrescricaoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.bean.ItemTO;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.ui.ModelMap;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static br.com.pacto.bean.configuracoes.ConfiguracoesEnum.HABILITAR_OBRIGATORIEDADE_APROVACAO_PROFESSOR;
import static br.com.pacto.bean.configuracoes.ConfiguracoesEnum.TEMPO_APROVACAO_AUTOMATICA;

/**
 * <AUTHOR> Alcides
 */
@Service
@ContextConfiguration(locations = {"/applicationContext.xml"})
public class PrescricaoServiceImpl implements PrescricaoService {

    @Autowired
    private ClienteSinteticoDao clienteSinteticoDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ProfessorSinteticoDao professorsinteticoDao;
    @Autowired
    private ProgramaTreinoDao programaTreinoDao;
    @Autowired
    private ProgramaTreinoService ps;
    @Autowired
    private ConfiguracaoSistemaService configService;
    @Autowired
    private UsuarioService usuarioService;

    @Autowired
    private PerfilService perfilService;

    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    public List<Integer> matriculasPrescricao(Integer empresa, JSONObject filtros) throws ServiceException {
        List<PessoaPrescricaoDTO> pessoaPrescricaoDTOS = listaPrescricao(empresa, filtros, null, true);
        return new ArrayList<Integer>(){{
            pessoaPrescricaoDTOS.forEach(p -> add(Integer.valueOf(p.getMatricula())));
        }};
    }

    public List<PessoaPrescricaoDTO> listaPrescricao(Integer empresa, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        return listaPrescricao(empresa, filtros, paginadorDTO, false);
    }
    public List<PessoaPrescricaoDTO> listaPrescricaoV2(Integer empresa, JSONObject filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        return listaPrescricaoV2(empresa, filtros, paginadorDTO, false);
    }
    public List<PessoaPrescricaoDTO> listaPrescricao(Integer empresa, JSONObject filtros, PaginadorDTO paginadorDTO, boolean somenteMatricula) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer usuarioID = sessaoService.getUsuarioAtual().getId();
            ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema diasAntes = css.consultarPorTipo(ctx, ConfiguracoesEnum.DIAS_ANTES_VENCIMENTO);
            Date limitAVencer = Uteis.somarDias(Calendario.hoje(), diasAntes.getValorAsInteger());
            List<PessoaPrescricaoDTO> prescricoes = null;
            JSONObject filtroPrimario = filtros.optJSONObject("primario");
            Boolean colaborador = false;
            Usuario usuarios = usuarioService.obterPorId(ctx, usuarioID);

            AtomicBoolean temPermissaoTELA_PRESCRICAO_TREINO= new AtomicBoolean(false);
            AtomicBoolean temPermissaoVER_ALUNOS_OUTRAS_CARTEIRAS= new AtomicBoolean(false);
            AtomicBoolean temPermissaoALUNOS= new AtomicBoolean(false);

            AtomicBoolean permissaoCriar = new AtomicBoolean(false);
            AtomicBoolean permissaoRevisar = new AtomicBoolean(false);
            AtomicBoolean permissaoRenovar = new AtomicBoolean(false);

            PerfilDTO permissoesPerfil = perfilService.obterPerfil(usuarios.getPerfil().getCodigo());

            temPermissaoTELA_PRESCRICAO_TREINO.set(permissoesPerfil.getFuncionalidades().getTela_prescricao_treino());
            temPermissaoVER_ALUNOS_OUTRAS_CARTEIRAS.set(permissoesPerfil.getFuncionalidades().getVer_alunos_outras_carteiras());
            if(!permissoesPerfil.getRecursos().getAlunos().isEmpty()){
                temPermissaoALUNOS.set(true);
            }

            if(!permissoesPerfil.getRecursos().getPrograma_treino().isEmpty()){
                for (TipoPermissaoEnum item : permissoesPerfil.getRecursos().getPrograma_treino()) {
                    if(item.equals(TipoPermissaoEnum.INCLUIR) || item.equals(TipoPermissaoEnum.TOTAL)){
                        permissaoCriar.set(true);
                        permissaoRenovar.set(true);
                    }
                    if(item.equals(TipoPermissaoEnum.EDITAR) || item.equals(TipoPermissaoEnum.TOTAL)){
                        permissaoRevisar.set(true);
                    }
                }
            }

            if (filtroPrimario != null && !UteisValidacao.emptyString(filtroPrimario.optString("id"))) {
                colaborador = filtroPrimario.getString("id").equals("COLABORADORES");
            }
            if (colaborador) {
                prescricoes = colaboradores(empresa, ctx, filtros, paginadorDTO, limitAVencer);
            } else {
                if (filtros.has("terciario") && filtros.get("terciario") != null && !filtros.get("terciario").toString().equals("[]")) {
                    try {
                        JSONArray arrayTerciario = new JSONArray();
                        JSONObject status = new JSONObject();
                        JSONObject jsonObject = new JSONObject(filtros.get("terciario").toString());
                        if (jsonObject.has("nome") && !jsonObject.get("nome").equals(null)) {
                            status.put("nome", new JSONObject(filtros.get("terciario").toString()).get("nome"));
                            status.put("id", "STATUS");
                            arrayTerciario.put(status);
                            filtros.put("terciario", arrayTerciario);
                        }
                    } catch (Exception ex) {}
                }
                if(temPermissaoVER_ALUNOS_OUTRAS_CARTEIRAS.get()){
                    if(!filtros.get("primario").equals(null)){
                        JSONObject my_obj = new JSONObject(filtros.get("primario").toString());
                        if(my_obj.get("id") == "prof"+usuarios.getProfessor().getCodigo() || my_obj.get("id").equals("TODOS_ALUNOS")){
                            if(!filtroPrimario.get("id").equals("TODOS_ALUNOS")){
                                JSONObject primario =  new JSONObject();
                                primario.put("nome",usuarios.getProfessor().getNome());
                                primario.put("id","prof"+usuarios.getProfessor().getCodigo());
                                filtros.put("primario",primario);
                            }
                        }
                    }

                    prescricoes = alunos(empresa, ctx, filtros, paginadorDTO, limitAVencer, somenteMatricula);
                }else{
                    if (filtroPrimario == null) {
                        JSONObject filtro = new JSONObject();
                        String valorNull = null;
                        filtro.put("search", valorNull);
                        JSONObject primario =  new JSONObject();
                        primario.put("nome",usuarios.getProfessor().getNome());
                        primario.put("id","prof"+usuarios.getProfessor().getCodigo());
                        filtro.put("primario",primario);
                        filtro.put("pessoaProfessorLogado",usuarios.getProfessor().getCodigoPessoa());
                        if(filtros.get("secundario")!=null){
                            filtro.put("secundario" , filtros.get("secundario"));
                        }else{
                            filtro.put("secundario",valorNull);
                        }

                        prescricoes = alunos(empresa, ctx, filtro, paginadorDTO, limitAVencer, somenteMatricula);
                    }else{

                        JSONObject filtro = new JSONObject(filtros.get("primario").toString());
                        if(filtro.get("id") == "prof"+usuarios.getProfessor().getCodigo() || filtro.get("id").equals("TODOS_ALUNOS")){
                            if(filtroPrimario.get("id").equals("TODOS_ALUNOS")){
                                JSONObject primario =  new JSONObject();
                                primario.put("nome",usuarios.getProfessor().getNome());
                                primario.put("id","prof"+usuarios.getProfessor().getCodigo());
                                filtros.put("primario",primario);
                            }
                        }

                        String valorNull = null;
                        filtro.put("search", valorNull);
                        filtro.put("primario",filtros.get("primario"));
                        if (filtros.get("terciario") != null) {
                            filtro.put("terciario" , filtros.get("terciario"));
                        }
                        filtro.put("pessoaProfessorLogado",usuarios.getProfessor().getCodigoPessoa());
                        if(filtros.get("secundario")!=null){
                            filtro.put("secundario" , filtros.get("secundario"));
                        }else{
                            filtro.put("secundario",valorNull);
                        }
                        prescricoes = alunos(empresa, ctx, filtro, paginadorDTO, limitAVencer, somenteMatricula);
                    }
                }
            }

            for(Integer i = 0; i < prescricoes.size(); i++){
                prescricoes.get(i).setPermissaoRevisar(permissaoRevisar.get());
                prescricoes.get(i).setPermissaoCriar(permissaoCriar.get());
                prescricoes.get(i).setPermissaoRenovar(permissaoRenovar.get());
            }

                return prescricoes;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<PessoaPrescricaoDTO> listaPrescricaoV2(Integer empresa, JSONObject filtros, PaginadorDTO paginadorDTO, boolean somenteMatricula) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer usuarioID = sessaoService.getUsuarioAtual().getId();
            ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema diasAntes = css.consultarPorTipo(ctx, ConfiguracoesEnum.DIAS_ANTES_VENCIMENTO);
            Date limitAVencer = Uteis.somarDias(Calendario.hoje(), diasAntes.getValorAsInteger());
            List<PessoaPrescricaoDTO> prescricoes = null;
            JSONObject filtroPrimario = filtros.optJSONObject("primario");
            Boolean colaborador = false;
            Usuario usuarios = usuarioService.obterPorId(ctx, usuarioID);

            AtomicBoolean temPermissaoTELA_PRESCRICAO_TREINO= new AtomicBoolean(false);
            AtomicBoolean temPermissaoVER_ALUNOS_OUTRAS_CARTEIRAS= new AtomicBoolean(false);
            AtomicBoolean temPermissaoALUNOS= new AtomicBoolean(false);

            AtomicBoolean permissaoCriar = new AtomicBoolean(false);
            AtomicBoolean permissaoRevisar = new AtomicBoolean(false);
            AtomicBoolean permissaoRenovar = new AtomicBoolean(false);

            PerfilDTO permissoesPerfil = perfilService.obterPerfil(usuarios.getPerfil().getCodigo());

            temPermissaoTELA_PRESCRICAO_TREINO.set(permissoesPerfil.getFuncionalidades().getTela_prescricao_treino());
            temPermissaoVER_ALUNOS_OUTRAS_CARTEIRAS.set(permissoesPerfil.getFuncionalidades().getVer_alunos_outras_carteiras());
            if(!permissoesPerfil.getRecursos().getAlunos().isEmpty()){
                temPermissaoALUNOS.set(true);
            }

            if(!permissoesPerfil.getRecursos().getPrograma_treino().isEmpty()){
                for (TipoPermissaoEnum item : permissoesPerfil.getRecursos().getPrograma_treino()) {
                    if(item.equals(TipoPermissaoEnum.INCLUIR) || item.equals(TipoPermissaoEnum.TOTAL)){
                        permissaoCriar.set(true);
                        permissaoRenovar.set(true);
                    }
                    if(item.equals(TipoPermissaoEnum.EDITAR) || item.equals(TipoPermissaoEnum.TOTAL)){
                        permissaoRevisar.set(true);
                    }
                }
            }

            if (filtroPrimario != null && !UteisValidacao.emptyString(filtroPrimario.optString("id"))) {
                colaborador = filtroPrimario.getString("id").equals("COLABORADORES");
            }
            if (colaborador) {
                prescricoes = colaboradores(empresa, ctx, filtros, paginadorDTO, limitAVencer);
            } else {
                if (filtros.has("terciario") && !filtros.get("terciario").equals(null)) {
                    JSONArray arrayTerciario = new JSONArray();
                    JSONObject status = new JSONObject();
                    try {
                        JSONObject jsonObject = new JSONObject(filtros.get("terciario").toString());
                        if (jsonObject.has("nome") && !jsonObject.get("nome").equals(null)) {
                            if(!jsonObject.get("nome").equals("situacao_cliente_prescricao_treino")){
                                status.put("nome", new JSONObject(filtros.get("terciario").toString()).get("nome"));
                                status.put("id", "STATUS");
                                arrayTerciario.put(status);
                                filtros.put("terciario", arrayTerciario);
                            }
                        }
                    }catch (Exception ex){}

                }
                if(temPermissaoVER_ALUNOS_OUTRAS_CARTEIRAS.get()){
                    boolean primarioColaborador = false;
                    if(!filtros.get("primario").equals(null)){
                        try {
                            JSONObject my_obj = new JSONObject(filtros.get("primario").toString());
                            if(my_obj.has("id")){
                                if(my_obj.get("id") == "prof"+usuarios.getProfessor().getCodigo() || my_obj.get("id").equals("TODOS_ALUNOS")){
                                    if(!filtroPrimario.get("id").equals("TODOS_ALUNOS")){
                                        JSONObject primario =  new JSONObject();
                                        primario.put("nome",usuarios.getProfessor().getNome());
                                        primario.put("id","prof"+usuarios.getProfessor().getCodigo());
                                        filtros.put("primario",primario);
                                    }
                                }
                            }else{
                                JSONObject my_objss = new JSONObject(filtros.get("primario").toString());
                                if(my_objss.getJSONObject("value").has("id")){
                                    if(my_objss.getJSONObject("value").get("id") == "prof"+usuarios.getProfessor().getCodigo() || my_objss.getJSONObject("value").get("id").equals("TODOS_ALUNOS")){
                                        if(!filtroPrimario.getJSONObject("value").get("id").equals("TODOS_ALUNOS")){
                                            JSONObject primario =  new JSONObject();
                                            primario.put("nome",usuarios.getProfessor().getNome());
                                            primario.put("id","prof"+usuarios.getProfessor().getCodigo());
                                            filtros.put("primario",filtroPrimario.getJSONObject("value"));
                                        }
                                    }
                                }else{
                                    JSONObject primario =  new JSONObject();
                                    primario.put("nome","Todos os alunos");
                                    primario.put("id","TODOS_ALUNOS");
                                }
                            }
                        }catch (Exception ex){
                            JSONArray arrayFiltroPrimario = filtros.optJSONArray("primario");
                            JSONObject primario =  new JSONObject();
                            if (arrayFiltroPrimario != null && arrayFiltroPrimario.length() > 0) {
                                for (int i = 0; i < arrayFiltroPrimario.length(); i++) {
                                    if(arrayFiltroPrimario.getJSONObject(i).get("id").toString().equals("COLABORADORES")){
                                        primarioColaborador = true;
                                        primario.put("id","COLABORADORES");
                                    }else{
                                        primario.put("id","prof"+arrayFiltroPrimario.getJSONObject(i).get("id").toString());
                                    }
                                }
                            }
                            filtros.put("primario",arrayFiltroPrimario);
                        }
                    }
                    if(primarioColaborador){
                        prescricoes = colaboradores(empresa, ctx, filtros, paginadorDTO, limitAVencer);
                    }else{
                        prescricoes = alunosV2(empresa, ctx, filtros, paginadorDTO, limitAVencer, somenteMatricula);
                    }

                }else{
                    if (filtroPrimario == null) {
                        JSONObject filtro = new JSONObject();
                        String valorNull = null;
                        filtro.put("search", valorNull);
                        JSONObject primario =  new JSONObject();
                        primario.put("nome",usuarios.getProfessor().getNome());
                        primario.put("id","prof"+usuarios.getProfessor().getCodigo());
                        filtro.put("primario",primario);
                        filtro.put("pessoaProfessorLogado",usuarios.getProfessor().getCodigoPessoa());
                        if(filtros.get("secundario")!=null){
                            filtro.put("secundario" , filtros.get("secundario"));
                        }else{
                            filtro.put("secundario",valorNull);
                        }

                        prescricoes = alunos(empresa, ctx, filtro, paginadorDTO, limitAVencer, somenteMatricula);
                    }else{

                        JSONObject filtro = new JSONObject(filtros.get("primario").toString());
                        if(filtro.get("id") == "prof"+usuarios.getProfessor().getCodigo() || filtro.get("id").equals("TODOS_ALUNOS")){
                            if(filtroPrimario.get("id").equals("TODOS_ALUNOS")){
                                JSONObject primario =  new JSONObject();
                                primario.put("nome",usuarios.getProfessor().getNome());
                                primario.put("id","prof"+usuarios.getProfessor().getCodigo());
                                filtros.put("primario",primario);
                            }
                        }

                        String valorNull = null;
                        filtro.put("search", valorNull);
                        filtro.put("primario",filtros.get("primario"));
                        if (filtros.get("terciario") != null) {
                            filtro.put("terciario" , filtros.get("terciario"));
                        }
                        filtro.put("pessoaProfessorLogado",usuarios.getProfessor().getCodigoPessoa());
                        if(filtros.get("secundario")!=null){
                            filtro.put("secundario" , filtros.get("secundario"));
                        }else{
                            filtro.put("secundario",valorNull);
                        }
                        prescricoes = alunos(empresa, ctx, filtro, paginadorDTO, limitAVencer, somenteMatricula);
                    }
                }
            }

            for(Integer i = 0; i < prescricoes.size(); i++){
                prescricoes.get(i).setPermissaoRevisar(permissaoRevisar.get());
                prescricoes.get(i).setPermissaoCriar(permissaoCriar.get());
                prescricoes.get(i).setPermissaoRenovar(permissaoRenovar.get());
            }

            return prescricoes;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }


    private List<PessoaPrescricaoDTO> alunosV2(Integer empresa,
                                             String ctx, JSONObject filtros,
                                             PaginadorDTO paginadorDTO, Date limitAvencer,
                                             boolean somenteMatriculas) throws Exception {
        ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
        ConfiguracaoSistema duracaoNaAcademia = css.consultarPorTipo(ctx, ConfiguracoesEnum.DURACAO_ALUNO_NA_ACADEMIA);
        Date inicioNaAcademia = Uteis.somarCampoData(Calendario.hoje(), Calendar.MINUTE, -duracaoNaAcademia.getValorAsInteger());
        ConfiguracaoSistema configuracaoBI = css.consultarPorTipo(ctx, ConfiguracoesEnum.PERIODO_USADO_BI);
        int diasPesquisa = Integer.parseInt(configuracaoBI.getValor());
        int diasParaFrente = diasPesquisa > 0 ? diasPesquisa : 30;
        int diasParaTras = diasPesquisa > 0 ? diasPesquisa : 30;

        Date dataInicioPesquisa = Uteis.somarCampoData(Calendario.hoje(), Calendar.DAY_OF_MONTH, -diasParaTras);
        Date dataFimPesquisa = Uteis.somarCampoData(Calendario.hoje(), Calendar.DAY_OF_MONTH, diasParaFrente);
        Date dataHoje = Calendario.hoje();
        List<Integer> alunosAulaAvulsa = new ArrayList<>();
        List<Integer> alunosDiaria = new ArrayList<>();
        List<Integer> alunosFreePass = new ArrayList<>();
        List<Integer> alunosGymPass = new ArrayList<>();
        List<Integer> alunosTotalPass = new ArrayList<>();
        List<Integer> alunosAtestado = new ArrayList<>();
        List<Integer> alunosAvencer = new ArrayList<>();
        List<Integer> alunosCancelado = new ArrayList<>();
        List<Integer> alunosDesistente = new ArrayList<>();
        List<Integer> alunosDependente = new ArrayList<>();
        List<Integer> alunosFerias = new ArrayList<>();
        List<Integer> alunosTrancado = new ArrayList<>();
        List<Integer> alunosVencido = new ArrayList<>();

        List<PessoaPrescricaoDTO> alunos = new ArrayList<>();
        String idFiltroPrimario = "";
        JSONObject filtroPrimario = filtros.optJSONObject("primario");
        if (filtroPrimario != null && !UteisValidacao.emptyString(filtroPrimario.optString("id"))) {
            idFiltroPrimario = filtroPrimario.getString("id");
        }else{
            try {
                JSONObject my_obj = new JSONObject(filtros.get("primario").toString());
                if(my_obj.getJSONObject("value").has("id")){
                    filtroPrimario.put("id", (String) my_obj.getJSONObject("value").get("id"));
                    idFiltroPrimario = filtroPrimario.getString("id");
                }
            }catch (Exception ex) {
                JSONArray arrayFiltroPrimario = filtros.optJSONArray("primario");
                if (arrayFiltroPrimario != null && arrayFiltroPrimario.length() > 0) {
                    String codigosProfessores = "";
                    for (int i = 0; i < arrayFiltroPrimario.length(); i++) {
                        codigosProfessores += "," + arrayFiltroPrimario.getJSONObject(i).get("id").toString().replace("prof", "");
                    }
                    idFiltroPrimario = codigosProfessores.replaceFirst(",", "");
                }
            }
        }
        String idFiltroSecundario="";
        JSONArray jsonArrayObjeto = new JSONArray(filtros.get("secundario").toString());
        idFiltroSecundario = String.valueOf(jsonArrayObjeto.getJSONObject(0).get("id"));

        // Verifica filtro "GERADO_POR_IA" no primário
        boolean isFiltroGeradoPorIa = "GERADO_POR_IA".equals(idFiltroPrimario);
        String arrayId = null;
        try{
            arrayId = filtroPrimario.getString("id").replace("prof", "");
        }catch (Exception ex) {
            arrayId = idFiltroPrimario;
        }

        String origemAcao = "";
        JSONObject filtroQuartenario = filtros.optJSONObject("quartenario");
        if (filtroQuartenario != null && !UteisValidacao.emptyString(filtroQuartenario.optString("nome"))) {
            origemAcao = filtroQuartenario.getString("nome");
        }

        StringBuilder sql = new StringBuilder();
        sql.append(" select count(DISTINCT c.codigo) as cont \n");
        sql.append(" from clientesintetico c \n");
        if ( !idFiltroSecundario.equals("TODOS") && ("BI_TREINO".equals(origemAcao) || ("TELA_PRESCRICAO_TREINO".equals(origemAcao) && (!"TODOS_ALUNOS".equals(arrayId)  || !arrayId.contains("TODOS_ALUNOS")) ))) {
            sql.append(" INNER JOIN professorsintetico pro on pro.codigo = c.professorsintetico_codigo \n");
        } else {
            sql.append(" LEFT JOIN professorsintetico pro on pro.codigo = c.professorsintetico_codigo \n");
        }
        sql.append(" LEFT JOIN programatreino obj ON c.codigo = obj.cliente_codigo \n");
        sql.append(" LEFT JOIN programatreino obj2 ON obj.cliente_codigo = obj2.cliente_codigo AND obj2.dataTerminoPrevisto > obj.dataTerminoPrevisto \n");
        sql.append(" LEFT JOIN pessoa p ON c.pessoa_codigo = p.codigo \n");

        if (idFiltroPrimario.equals("CARTEIRA_NA_ACADEMIA") || idFiltroPrimario.equals("NA_ACADEMIA") || arrayId.contains("CARTEIRA_NA_ACADEMIA") || arrayId.contains("NA_ACADEMIA") || isFiltroGeradoPorIa) {
            sql.append(" left join usuario u on u.cliente_codigo = c.codigo \n");
            sql.append(" left join statuspessoa s on u.codigo = s.usuario_codigo \n");
        }
        sql.append(" WHERE c.empresa = ").append(empresa);

        if (!UteisValidacao.emptyString(idFiltroPrimario)) {

            if(!arrayId.contains("TODOS_ALUNOS")){
                if(!arrayId.contains("CARTEIRA") && !arrayId.contains("CARTEIRA_NA_ACADEMIA") && !arrayId.contains("NA_ACADEMIA") && !arrayId.contains("GERADO_POR_IA")){
                    sql.append(" and c.professorSintetico_codigo in ( ").append(arrayId.replaceAll("[^0-9,]", "-1")).append(" ) ");
                } else {
                    String arrayFiltro[] = arrayId.split(",");
                    for(String item : arrayFiltro){
                        switch (item) {
                            case "CARTEIRA":
                                sql.append(" and pro.codigopessoa = ").append(filtros.getInt("pessoaProfessorLogado"));
                                break;
                            case "CARTEIRA_NA_ACADEMIA":
                                sql.append(" and pro.codigopessoa = ").append(filtros.getInt("pessoaProfessorLogado"));
                                sql.append(" and s.datainicioevento >= '");
                                sql.append(Uteis.getDataAplicandoFormatacao(inicioNaAcademia, "yyyy-MM-dd HH:mm:ss")).append("' ");
                                break;
                            case "NA_ACADEMIA":
                                sql.append(" and s.datainicioevento >= '");
                                sql.append(Uteis.getDataAplicandoFormatacao(inicioNaAcademia, "yyyy-MM-dd HH:mm:ss")).append("' ");
                                break;
                            case "GERADO_POR_IA":
                                sql.append(" and obj.isgeradoporia is true ");
                                break;
                        }
                    }
                }
            }
        }

        JSONArray filtroProfessores = filtros.optJSONArray("professores");
        if (filtroProfessores != null && filtroProfessores.length() > 0) {
            String codigosProfessores = "";
            for (int i = 0; i < filtroProfessores.length(); i++) {
                codigosProfessores += "," + filtroProfessores.getInt(i);
            }
            sql.append(" and pro.codigoColaborador in (").append(codigosProfessores.replaceFirst(",", "")).append(")");
        }

        JSONArray filtroSecundario = new JSONArray();
        if (filtros.optJSONArray("secundario") != null && filtros.optJSONArray("secundario").length() > 0) {
            filtroSecundario = filtros.optJSONArray("secundario");
        } else {
            filtroSecundario = new JSONArray("[{\"nome\":\"Sem treino\",\"id\":\"SEM_TREINO\"},{\"nome\":\"Vencido\",\"id\":\"VENCIDO\"},{\"nome\":\"Em dia\",\"id\":\"EM_DIA\"},{\"nome\":\"Treinos futuros\",\"id\":\"TREINO_FUTURO\"},{\"nome\":\"A vencer\",\"id\":\"A_VENCER\"},{\"nome\":\"A Aprovar\",\"id\":\"A_APROVAR\"}]");
        }

        if (filtroSecundario != null && filtroSecundario.length() > 0) {
            StringBuilder secundario = new StringBuilder();
            boolean isFirstCondition = true;
            for (int i = 0; i < filtroSecundario.length(); i++) {
                JSONObject item = filtroSecundario.getJSONObject(i);
                if (!"GERADO_POR_IA".equals(item.getString("id")) && item.getString("id") != null ) {
                    switch (item.getString("id")) {
                        case "SEM_TREINO":
                            if (!isFirstCondition) {
                                secundario.append(" or ");
                            }
                            secundario.append(" (obj.codigo IS NULL) ");
                            isFirstCondition = false;
                            break;
                        case "EM_DIA":
                            if (!isFirstCondition) {
                                secundario.append(" or ");
                            }
                            secundario.append(" (obj.dataTerminoPrevisto::date > '")
                                    .append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"))
                                    .append("' AND obj.dataInicio::date <= '")
                                    .append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"))
                                    .append("' AND obj.dataTerminoPrevisto::date > '")
                                    .append(Uteis.getDataAplicandoFormatacao(limitAvencer, "yyyy-MM-dd"))
                                    .append("' AND obj.codigo IS NOT NULL) ");
                            isFirstCondition = false;
                            break;
                        case "TREINO_FUTURO":
                            if (!isFirstCondition) {
                                secundario.append(" or ");
                            }
                            secundario.append(" (obj.dataInicio::date > '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("') ");
                            isFirstCondition = false;
                            break;
                        case "A_VENCER":
                            if (!isFirstCondition) {
                                secundario.append(" or ");
                            }
                            secundario.append(" (obj.dataTerminoPrevisto::date >= '")
                                    .append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"))
                                    .append("' AND obj.dataTerminoPrevisto::date <= '")
                                    .append(Uteis.getDataAplicandoFormatacao(limitAvencer, "yyyy-MM-dd"))
                                    .append("' AND obj.codigo IS NOT NULL AND obj2.codigo IS NULL) ");
                            isFirstCondition = false;
                            break;
                        case "VENCIDO":
                            if (!isFirstCondition) {
                                secundario.append(" or ");
                            }
                            secundario.append(" (obj.dataTerminoPrevisto::date < '")
                                    .append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"))
                                    .append("' AND obj.codigo IS NOT NULL AND obj2.codigo IS NULL) ");
                            isFirstCondition = false;
                            break;
                        case "A_APROVAR":
                            if (!isFirstCondition) {
                                secundario.append(" or ");
                            }
                            secundario.append(" obj.emrevisaoprofessor is true ");
                            isFirstCondition = false;
                            break;
                    }
                }
            }
            if (secundario.length() > 0) {
                sql.append(" AND (").append(secundario.toString().replaceFirst(" OR ", " ")).append(")");
            }
        }

        boolean filtroTerciarioAplicado = false;
        JSONArray filtroTerciario = filtros.optJSONArray("terciario");
        if (filtroTerciario != null && filtroTerciario.length() > 0) {
            for (int i = 0; i < filtroTerciario.length(); i++) {
                JSONObject item = filtroTerciario.getJSONObject(i);
                switch (item.getString("id")) {
                    case "STATUS":
                        sql.append(" and c.situacao ='").append(item.get("nome")).append("' \n");
                        if (!SuperControle.independente(ctx)) {
                            sql.append(" and c.situacaocontrato in ('AV', 'NO') \n");
                        }
                        filtroTerciarioAplicado = true;
                        break;
                }
            }
        }

        // origem BI_TREINO e TELA_PRESCRICAO_TREINO considera apenas alunos AT AV e AT NO
        if (!filtroTerciarioAplicado && ("BI_TREINO".equals(origemAcao) || "TELA_PRESCRICAO_TREINO".equals(origemAcao))) {
            if(filtros.has("terciario") && !filtros.get("terciario").equals(null) && !filtros.get("terciario").toString().equals("[]")){
                try {
                    JSONArray arrayTerciario = new JSONArray(filtros.get("terciario").toString());
                    if(arrayTerciario != null && arrayTerciario.length() > 0){
                        boolean alunoPass = false;
                        for (int i = 0; i < arrayTerciario.length(); i++) {
                            JSONObject item = arrayTerciario.getJSONObject(i);
                            alunoPass = (item.getString("id").equals("TP")
                                    || item.getString("id").equals("GY")
                                    || item.getString("id").equals("FR")
                                    || item.getString("id").equals("DI")
                                    || item.getString("id").equals("AA")
                                    || item.getString("id").equals("AE")
                                    || item.getString("id").equals("AV")
                                    || item.getString("id").equals("CA")
                                    || item.getString("id").equals("DEP")
                                    || item.getString("id").equals("DE")
                                    || item.getString("id").equals("CR")
                                    || item.getString("id").equals("TR")
                                    || item.getString("id").equals("VE")
                            );
                        }
                        if(alunoPass){
                            String varCaoncat ="";
                            StringBuilder sqlZW = new StringBuilder();
                            sqlZW.append("select * from ( ");
                            sqlZW.append(" select distinct c.codigo ");
                            sqlZW.append(",exists(SELECT codigo FROM PeriodoAcessoCliente WHERE pessoa = p.codigo AND tipoAcesso = 'PL'  AND dataInicioAcesso <= '").append(Uteis.getDataJDBC(dataHoje)).append("' and dataFinalAcesso >= '").append(Uteis.getDataJDBC(dataHoje)).append("') as freepass ");
                            sqlZW.append(",exists(SELECT codigo FROM PeriodoAcessoCliente WHERE pessoa = p.codigo AND tipoAcesso = 'DI'  AND dataInicioAcesso <= '").append(Uteis.getDataJDBC(dataHoje)).append("' and dataFinalAcesso >= '").append(Uteis.getDataJDBC(dataHoje)).append("') as diaria ");
                            sqlZW.append(",exists(SELECT codigo FROM PeriodoAcessoCliente WHERE pessoa = p.codigo AND tipoAcesso = 'AA'  AND dataInicioAcesso <= '").append(Uteis.getDataJDBC(dataHoje)).append("' and dataFinalAcesso >= '").append(Uteis.getDataJDBC(dataHoje)).append("') as aulaAvulsa ");
                            sqlZW.append(",(sw.situacao in ('VI','IN') and exists(SELECT codigo FROM PeriodoAcessoCliente WHERE pessoa = p.codigo AND tipoAcesso ='PL' AND tokengympass is not null and tokengympass <> '' and length(coalesce(c.gympassUniqueToken,'')) > 0)) as gympass ");
                            sqlZW.append(",(sw.situacao in ('VI','IN') and exists(SELECT codigo FROM periodoacessocliente WHERE pessoa = p.codigo AND tipototalpass = true)) as totalpass ");
                            sqlZW.append(", CASE WHEN c.situacao = 'AE' or sw.situacaoContrato = 'AE'  THEN true ELSE false END AS atestado ");
                            sqlZW.append(", CASE WHEN c.situacao = 'AV' or sw.situacaoContrato = 'AV'  THEN true ELSE false END AS avencer ");
                            sqlZW.append(", CASE WHEN c.situacao = 'CA' or sw.situacaoContrato = 'CA'  THEN true ELSE false END AS cancelado ");
                            sqlZW.append(", CASE WHEN c.situacao = 'DE' or sw.situacaoContrato = 'DE'  THEN true ELSE false END AS desistente ");
                            sqlZW.append(", CASE WHEN c.situacao = 'CR' or sw.situacaoContrato = 'CR'  THEN true ELSE false END AS ferias ");
                            sqlZW.append(", CASE WHEN c.situacao = 'TR' or sw.situacaoContrato = 'TR'  THEN true ELSE false END AS trancado ");
                            sqlZW.append(", CASE WHEN c.situacao = 'VE' or sw.situacaoContrato = 'VE'  THEN true ELSE false END AS vencido ");
                            sqlZW.append(", coalesce(c.TitularPlanoCompartilhado,0) > 0 as dependente ");
                            StringBuilder filtro = new StringBuilder();
                            for (int i = 0; i < arrayTerciario.length(); i++) {
                                JSONObject item = arrayTerciario.getJSONObject(i);

                                if(item.getString("id").equals("TP")) {
                                    if (filtro.length() > 0) {
                                        filtro.append(" or ");
                                    }
                                    filtro.append("sql.totalpass");

                                }else if(item.getString("id").equals("GY")){
                                    if (filtro.length() > 0) {
                                        filtro.append(" or ");
                                    }
                                    filtro.append("sql.gympass");

                                }else if(item.getString("id").equals("FR")){
                                    if (filtro.length() > 0) {
                                        filtro.append(" or ");
                                    }
                                    filtro.append("sql.freepass");

                                }else if(item.getString("id").equals("DI")){
                                    if (filtro.length() > 0) {
                                        filtro.append(" or ");
                                    }
                                    filtro.append("sql.diaria");

                                }else if(item.getString("id").equals("AA")){
                                    if (filtro.length() > 0) {
                                        filtro.append(" or ");
                                    }
                                    filtro.append("sql.aulaAvulsa");

                                }else if(item.getString("id").equals("AE")){
                                    if (filtro.length() > 0) {
                                        filtro.append(" or ");
                                    }
                                    filtro.append("sql.atestado");

                                }else if(item.getString("id").equals("AV")){
                                    if (filtro.length() > 0) {
                                        filtro.append(" or ");
                                    }
                                    filtro.append("sql.avencer");

                                }else if(item.getString("id").equals("CA")){
                                    if (filtro.length() > 0) {
                                        filtro.append(" or ");
                                    }
                                    filtro.append("sql.cancelado");

                                }else if(item.getString("id").equals("DE")){
                                    if (filtro.length() > 0) {
                                        filtro.append(" or ");
                                    }
                                    filtro.append("sql.desistente");

                                }else if(item.getString("id").equals("DEP")){
                                    if (filtro.length() > 0) {
                                        filtro.append(" or ");
                                    }
                                    filtro.append("sql.dependente");

                                }else if(item.getString("id").equals("CR")){
                                    if (filtro.length() > 0) {
                                        filtro.append(" or ");
                                    }
                                    filtro.append("sql.ferias");

                                }else if(item.getString("id").equals("TR")){
                                    if (filtro.length() > 0) {
                                        filtro.append(" or ");
                                    }
                                    filtro.append("sql.trancado");

                                }else if(item.getString("id").equals("VE")){
                                    if (filtro.length() > 0) {
                                        filtro.append(" or ");
                                    }
                                    filtro.append("sql.vencido");
                                }
                            }
                            sqlZW.append(" from cliente c inner join pessoa p on p.codigo = c.pessoa ");
                            sqlZW.append(" inner join situacaoclientesinteticodw sw on sw.codigopessoa = p.codigo ");
                            sqlZW.append(" inner join periodoacessocliente pac on pac.pessoa = p.codigo ");
                            sqlZW.append(" )as sql where 1 = 1 ");
                            if(filtro.length() > 0){
                                sqlZW.append(" AND (").append(filtro).append(") \n");
                            }

                            try (Connection conZW = conexaoZWService.conexaoZw(ctx);

                                 ResultSet rsEmp = ConexaoZWServiceImpl.criarConsulta(sqlZW.toString(), conZW)) {
                                while (rsEmp.next()) {
                                    varCaoncat+= ",'"+rsEmp.getString("codigo")+"'";
                                    if(rsEmp.getBoolean("diaria")){
                                        alunosDiaria.add(rsEmp.getInt("codigo"));
                                    }
                                    if(rsEmp.getBoolean("freepass")){
                                        alunosFreePass.add(rsEmp.getInt("codigo"));
                                    }
                                    if(rsEmp.getBoolean("aulaavulsa")){
                                        alunosAulaAvulsa.add(rsEmp.getInt("codigo"));
                                    }
                                    if(rsEmp.getBoolean("gympass")){
                                        alunosGymPass.add(rsEmp.getInt("codigo"));
                                    }
                                    if(rsEmp.getBoolean("totalpass")){
                                        alunosTotalPass.add(rsEmp.getInt("codigo"));
                                    }
                                    if(rsEmp.getBoolean("atestado")){
                                        alunosAtestado.add(rsEmp.getInt("codigo"));
                                    }
                                    if(rsEmp.getBoolean("avencer")){
                                        alunosAvencer.add(rsEmp.getInt("codigo"));
                                    }
                                    if(rsEmp.getBoolean("cancelado")){
                                        alunosCancelado.add(rsEmp.getInt("codigo"));
                                    }
                                    if(rsEmp.getBoolean("desistente")){
                                        alunosDesistente.add(rsEmp.getInt("codigo"));
                                    }
                                    if(rsEmp.getBoolean("dependente")){
                                        alunosDependente.add(rsEmp.getInt("codigo"));
                                    }
                                    if(rsEmp.getBoolean("ferias")){
                                        alunosFerias.add(rsEmp.getInt("codigo"));
                                    }
                                    if(rsEmp.getBoolean("trancado")){
                                        alunosTrancado.add(rsEmp.getInt("codigo"));
                                    }
                                    if(rsEmp.getBoolean("vencido")){
                                        alunosVencido.add(rsEmp.getInt("codigo"));
                                    }
                                }
                            }catch (Exception ex){}

                            if(!varCaoncat.equals("")){
                                sql.append(" and  c.codigocliente in ("+ varCaoncat.replaceFirst(",", "") +") \n");
                            }else{
                                sql.append(" and  c.codigocliente in (0) \n");
                            }
                        }else{
                            String varCaoncat ="";
                            for (int i = 0; i < arrayTerciario.length(); i++) {
                                JSONObject item = arrayTerciario.getJSONObject(i);
                                varCaoncat+= ",'"+item.getString("id")+"'";
                            }
                            sql.append(" and ( ");
                            sql.append(" c.situacao  in ("+ varCaoncat.replaceFirst(",", "") +") \n");
                            if (!SuperControle.independente(ctx)) {
                                sql.append(" or c.situacaocontrato  in ("+ varCaoncat.replaceFirst(",", "") +")\n");
                            }
                            sql.append(" )");
                        }
                    }else{
                        sql.append(" and c.situacao ='AT' \n");
                        if (!SuperControle.independente(ctx)) {
                            sql.append(" and c.situacaocontrato in ('AV', 'NO') \n");
                        }
                    }

                }catch (Exception ex){
                    sql.append(" and c.situacao ='AT' \n");
                    if (!SuperControle.independente(ctx)) {
                        sql.append(" and c.situacaocontrato in ('AV', 'NO') \n");
                    }
                }

            }
        }

        if (filtros != null && !UteisValidacao.emptyString(filtros.optString("search"))) {
            if (filtros.getString("search").matches("\\d+")) {
                sql.append(" and c.matricula = ").append(filtros.getString("search")).append(" ");
            } else {
                sql.append(" and c.nome ilike '").append(filtros.getString("search")).append("%'");
            }
        }
        if(paginadorDTO != null){
            try (ResultSet rsCount = clienteSinteticoDao.createStatement(ctx, sql.toString())) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(new Long(rsCount.getInt("cont")));
                }
            }
        }

        String ordenacao = filtros.optString("ordenacao", "ALUNO_ASC");
        if (UteisValidacao.emptyString(ordenacao)) {
            sql.append(" order by terminoUltimoPrograma is null desc, terminoUltimoPrograma, c.situacao, c.nome ");
        } else if (ordenacao.equals("ALUNO_ASC")) {
            sql.append(" order by c.nome asc ");
        } else if (ordenacao.equals("ALUNO_DESC")) {
            sql.append(" order by c.nome desc ");
        } else if (ordenacao.equals("DATAFIM_ASC")) {
            sql.append(" order by terminoUltimoPrograma asc nulls last ");
        } else if (ordenacao.equals("DATAFIM_DESC")) {
            sql.append(" order by terminoUltimoPrograma desc nulls last ");
        } else if(ordenacao.startsWith("status")){
            sql.append(" order by  c.situacao ").append(ordenacao.contains("DESC") ? "desc" : "asc");
            sql.append(", c.situacaocontrato ").append(ordenacao.contains("DESC") ? "desc" : "asc");
        } else if(ordenacao.startsWith("professor")){
            sql.append(" order by pro.nome ").append(ordenacao.contains("DESC") ? "desc" : "asc");
        } else if(ordenacao.startsWith("situacao")){
            sql.append(" order by obj.dataTerminoPrevisto is null ").append(ordenacao.contains("DESC") ? "desc" : "asc");
            sql.append(", obj.dataTerminoPrevisto ").append(ordenacao.contains("DESC") ? "desc" : "asc");
        } else {
            sql.append(" order by c.nome asc ");
        }




        String codigosCliente;
        StringBuilder codigosProgramas = new StringBuilder();

        String sqlReplace = sql.toString().replace("count(DISTINCT c.codigo) as cont ",
                somenteMatriculas
                        ? " c.matricula "
                        : " DISTINCT c.codigocliente, c.matricula, c.nome, p.fotokey, terminoUltimoPrograma, " +
                        "c.situacao, c.situacaoContrato, pro.nome as professornome, " +
                        "terminoUltimoPrograma is null as terminoultimoprogramanull, obj.codigo as codPrograma ,c.dataultimoacesso, obj.isgeradoporia, obj.emrevisaoprofessor, obj.datalancamento, ROW_NUMBER() OVER (PARTITION BY c.codigocliente ORDER BY obj.dataTerminoPrevisto DESC NULLS LAST) as rn ");




        StringBuilder sqlBuilder = new StringBuilder();
        if(!somenteMatriculas) {
            sqlBuilder.append("WITH ProgramasOrdenados AS ( ");
            sqlBuilder.append(sqlReplace);
            sqlBuilder.append(" )SELECT codigocliente, matricula, nome,fotokey,terminoUltimoPrograma,situacao, situacaoContrato, professornome, terminoUltimoPrograma IS NULL as terminoultimoprogramanull, codPrograma, dataultimoacesso, isgeradoporia, emrevisaoprofessor, datalancamento,rn ");
            sqlBuilder.append(" FROM ProgramasOrdenados ");
            sqlBuilder.append(" WHERE rn = 1 ");
            sqlBuilder.append(" order by ");
            if (UteisValidacao.emptyString(ordenacao)) {
                sqlBuilder.append("terminoUltimoPrograma is null desc, terminoUltimoPrograma, situacao, nome ");
            } else if (ordenacao.equals("ALUNO_ASC")) {
                sqlBuilder.append("nome asc ");
            } else if (ordenacao.equals("ALUNO_DESC")) {
                sqlBuilder.append("nome desc ");
            } else if (ordenacao.equals("DATAFIM_ASC")) {
                sqlBuilder.append("terminoUltimoPrograma asc nulls last ");
            } else if (ordenacao.equals("DATAFIM_DESC")) {
                sqlBuilder.append("terminoUltimoPrograma desc nulls last ");
            } else {
                sqlBuilder.append(sqlReplace);
            }
        }

        if(paginadorDTO != null){
            sqlBuilder.append(" limit ").append(paginadorDTO.getSize());
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(1l);
            }
            if(paginadorDTO.getPage() > 0){
                sqlBuilder.append(" offset ").append(paginadorDTO.getSize() * (paginadorDTO.getPage() - 1));
            }
        }
        try (ResultSet rs = clienteSinteticoDao.createStatement(ctx,sqlBuilder.toString())) {
            codigosCliente = "";
            while (rs.next()) {
                boolean geradoPorIa = rs.getBoolean("isgeradoporia");
                if (geradoPorIa) {
                    boolean emRevisaoProfessor = rs.getBoolean("emrevisaoprofessor");

                    ConfiguracaoSistema configObrigatoriedade = configService.consultarPorTipo(ctx, HABILITAR_OBRIGATORIEDADE_APROVACAO_PROFESSOR);
                    boolean obrigatoriedadeAprovacaoProfessor = configObrigatoriedade != null && Boolean.parseBoolean(configObrigatoriedade.getValor());

                    if (!obrigatoriedadeAprovacaoProfessor && geradoPorIa && emRevisaoProfessor) {
                        ConfiguracaoSistema configFluxoAprovacao = configService.consultarPorTipo(ctx, TEMPO_APROVACAO_AUTOMATICA);
                        Integer tempoAprovacao = (configFluxoAprovacao != null && configFluxoAprovacao.getValor() != null && !UteisValidacao.emptyString(configFluxoAprovacao.getValor()))
                                ? Integer.valueOf(configFluxoAprovacao.getValor())
                                : 0;

                        Date dataLancamento = rs.getTimestamp("datalancamento");
                        long diferencaEmMinutos = ChronoUnit.MINUTES.between(dataLancamento.toInstant(), Calendario.hoje().toInstant());

                        if (tempoAprovacao == 0 || diferencaEmMinutos > tempoAprovacao) {
                            ProgramaTreino programaTreino = programaTreinoDao.obterPorId(ctx, rs.getInt("codprograma"));
                            if (programaTreino == null) {
                                throw new ServiceException(ProgramaTreinoExcecoes.PROGRAMA_TREINO_NAO_ENCONTRADO);
                            }

                            programaTreino.setEmRevisaoProfessor(false);
                            programaTreinoDao.alterar(ctx, programaTreino);
                            break;
                        }
                    }
                }

                PessoaPrescricaoDTO prescricaoDTO = new PessoaPrescricaoDTO();
                if (somenteMatriculas) {
                    prescricaoDTO.setMatricula(String.valueOf(rs.getInt("matricula")));
                } else {
                    prescricaoDTO.setTipo("Aluno");
                    prescricaoDTO.setCodigoCliente(rs.getInt("codigocliente"));
                    codigosCliente += "," + prescricaoDTO.getCodigoCliente();
                    if (!UteisValidacao.emptyNumber(rs.getInt("codPrograma"))) {
                        codigosProgramas.append(",").append(rs.getInt("codPrograma"));
                        prescricaoDTO.setCodigoPrograma(rs.getInt("codPrograma"));
                    }
                    prescricaoDTO.setNome(rs.getString("nome").toLowerCase());
                    prescricaoDTO.setSituacao(rs.getString("situacao"));
                    prescricaoDTO.setSituacaoContrato(rs.getString("situacaoContrato"));
                    prescricaoDTO.setGeradoPorIA(rs.getBoolean("isgeradoporia"));
                    String professornome = rs.getString("professornome");
                    if (professornome == null) {
                        prescricaoDTO.setProfessor("");
                    } else {
                        prescricaoDTO.setProfessor(professornome.toLowerCase());
                    }
                    prescricaoDTO.setMatricula(String.valueOf(rs.getInt("matricula")));
                    prescricaoDTO.setSituacaoTreino("SEM_TREINO");
                    if(rs.getDate("dataultimoacesso") !=null){
                        prescricaoDTO.setUltimoAcesso(Uteis.getData(rs.getDate("dataultimoacesso"))  );
                    }
                    if (SuperControle.independente(ctx)) {
                        String fotokey = rs.getString("fotokey");
                        if (!UteisValidacao.emptyString(fotokey) && !fotokey.equals("fotoPadrao.jpg")) {
                            prescricaoDTO.setUrlFoto(Aplicacao.obterUrlFotoDaNuvem(fotokey));
                        }
                    }
                }
                if(alunosFreePass.contains(rs.getInt("codigocliente"))){
                    prescricaoDTO.setFreepass(true);
                }
                if(alunosDiaria.contains(rs.getInt("codigocliente"))){
                    prescricaoDTO.setDiaria(true);
                }
                if(alunosAulaAvulsa.contains(rs.getInt("codigocliente"))){
                    prescricaoDTO.setAulaAvulsa(true);
                }
                if(alunosGymPass.contains(rs.getInt("codigocliente"))){
                    prescricaoDTO.setGympass(true);
                }
                if(alunosTotalPass.contains(rs.getInt("codigocliente"))){
                    prescricaoDTO.setTotalpass(true);
                }
                if(alunosAtestado.contains(rs.getInt("codigocliente"))){
                    prescricaoDTO.setAtestado(true);
                }
                if(alunosAvencer.contains(rs.getInt("codigocliente"))){
                    prescricaoDTO.setAvencer(true);
                }
                if(alunosCancelado.contains(rs.getInt("codigocliente"))){
                    prescricaoDTO.setCancelado(true);
                }
                if(alunosDesistente.contains(rs.getInt("codigocliente"))){
                    prescricaoDTO.setDesistente(true);
                }
                if(alunosDependente.contains(rs.getInt("codigocliente"))){
                    prescricaoDTO.setDependente(true);
                }
                if(alunosFerias.contains(rs.getInt("codigocliente"))){
                    prescricaoDTO.setFerias(true);
                }
                if(alunosTrancado.contains(rs.getInt("codigocliente"))){
                    prescricaoDTO.setTrancado(true);
                }
                if(alunosVencido.contains(rs.getInt("codigocliente"))){
                    prescricaoDTO.setVencido(true);
                }

                alunos.add(prescricaoDTO);
            }
        }
        if(somenteMatriculas){
            return alunos;
        }
        montarProgramasListaPrescricao(ctx, codigosProgramas.toString(), alunos, limitAvencer, true);
        if (!SuperControle.independente(ctx)) {
            montarDadosZW(ctx, empresa, codigosCliente, alunos);
        }
        return alunos;
    }

    private List<PessoaPrescricaoDTO> colaboradores(Integer empresa, String ctx, JSONObject filtros, PaginadorDTO paginadorDTO, Date limitAvencer) throws Exception {
        List<PessoaPrescricaoDTO> colaboradores = new ArrayList<>();
        String tipos = "";
        JSONArray filtroSecundario = filtros.optJSONArray("secundario");
        if (filtroSecundario != null && filtroSecundario.length() > 0) {
            for (int i = 0; i < filtroSecundario.length(); i++) {
                JSONObject item = filtroSecundario.getJSONObject(i);
                tipos += "," + item.getString("id");
            }
        }
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("empresa", empresa.toString()));
        params.add(new BasicNameValuePair("search", filtros != null && !UteisValidacao.emptyString(filtros.optString("search")) ? filtros.optString("search") : ""));
        params.add(new BasicNameValuePair("tipos", tipos));
        if(paginadorDTO.getPage() > 0){
            params.add(new BasicNameValuePair("page", String.valueOf(paginadorDTO.getSize() * (paginadorDTO.getPage() - 1))));
        }else{
            params.add(new BasicNameValuePair("page", String.valueOf(0)));
        }
        params.add(new BasicNameValuePair("size", String.valueOf(paginadorDTO.getSize())));
        params.add(new BasicNameValuePair("limitAvencer", Uteis.getData(limitAvencer)));
        JSONObject retorno = chamadaZW(ctx, "/prest/treino/prescricao-colaboradores", params);
        JSONArray array = retorno.getJSONArray("lista");
        paginadorDTO.setQuantidadeTotalElementos(Long.valueOf(retorno.optInt("total")));
        String codigosColaboradores = "";
        for (int i = 0; i < array.length(); i++) {
            JSONObject json = array.getJSONObject(i);
            PessoaPrescricaoDTO prescricaoDTO = new PessoaPrescricaoDTO();
            prescricaoDTO.setTipo("Colaborador");
            prescricaoDTO.setCodigoColaborador(json.getInt("id"));
            codigosColaboradores += "," + prescricaoDTO.getCodigoColaborador();
            prescricaoDTO.setNome(json.getString("nome").toLowerCase());
            prescricaoDTO.setMatricula(json.optString("tipo") == null ?
                    "-" :
                    json.optString("tipo"));
            prescricaoDTO.setSituacaoTreino("SEM_TREINO");
            prescricaoDTO.setUrlFoto(UteisValidacao.emptyString(json.getString("foto")) ? null : json.getString("foto"));
            colaboradores.add(prescricaoDTO);
        }
        montarProgramas(ctx, codigosColaboradores, colaboradores, limitAvencer, false);
        return colaboradores;
    }

    private List<PessoaPrescricaoDTO> alunos(Integer empresa,
                                             String ctx, JSONObject filtros,
                                             PaginadorDTO paginadorDTO, Date limitAvencer,
                                             boolean somenteMatriculas) throws Exception {
        ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
        ConfiguracaoSistema duracaoNaAcademia = css.consultarPorTipo(ctx, ConfiguracoesEnum.DURACAO_ALUNO_NA_ACADEMIA);
        Date inicioNaAcademia = Uteis.somarCampoData(Calendario.hoje(), Calendar.MINUTE, -duracaoNaAcademia.getValorAsInteger());
        ConfiguracaoSistema configuracaoBI = css.consultarPorTipo(ctx, ConfiguracoesEnum.PERIODO_USADO_BI);
        int diasPesquisa = Integer.parseInt(configuracaoBI.getValor());
        int diasParaFrente = diasPesquisa > 0 ? diasPesquisa : 30;
        int diasParaTras = diasPesquisa > 0 ? diasPesquisa : 30;

        Date dataInicioPesquisa = Uteis.somarCampoData(Calendario.hoje(), Calendar.DAY_OF_MONTH, -diasParaTras);
        Date dataFimPesquisa = Uteis.somarCampoData(Calendario.hoje(), Calendar.DAY_OF_MONTH, diasParaFrente);

        List<PessoaPrescricaoDTO> alunos = new ArrayList<>();
        String idFiltroPrimario = "";
        JSONObject filtroPrimario = filtros.optJSONObject("primario");
        if (filtroPrimario != null && !UteisValidacao.emptyString(filtroPrimario.optString("id"))) {
            idFiltroPrimario = filtroPrimario.getString("id");
        }

        // Verifica filtro "GERADO_POR_IA" no primário
        boolean isFiltroGeradoPorIa = "GERADO_POR_IA".equals(idFiltroPrimario);

        String origemAcao = "";
        JSONObject filtroQuartenario = filtros.optJSONObject("quartenario");
        if (filtroQuartenario != null && !UteisValidacao.emptyString(filtroQuartenario.optString("nome"))) {
            origemAcao = filtroQuartenario.getString("nome");
        }

        StringBuilder sql = new StringBuilder();
        sql.append(" select count(DISTINCT c.codigo) as cont \n");
        sql.append(" from clientesintetico c \n");
        if ("BI_TREINO".equals(origemAcao) || ("TELA_PRESCRICAO_TREINO".equals(origemAcao) && !"TODOS_ALUNOS".equals(idFiltroPrimario))) {
            sql.append(" INNER JOIN professorsintetico pro on pro.codigo = c.professorsintetico_codigo \n");
        } else {
            sql.append(" LEFT JOIN professorsintetico pro on pro.codigo = c.professorsintetico_codigo \n");
        }
        sql.append(" LEFT JOIN programatreino obj ON c.codigo = obj.cliente_codigo \n");
        sql.append(" LEFT JOIN programatreino obj2 ON obj.cliente_codigo = obj2.cliente_codigo AND obj2.dataTerminoPrevisto > obj.dataTerminoPrevisto \n");
        sql.append(" LEFT JOIN pessoa p ON c.pessoa_codigo = p.codigo \n");

        if (idFiltroPrimario.equals("CARTEIRA_NA_ACADEMIA") || idFiltroPrimario.equals("NA_ACADEMIA") || isFiltroGeradoPorIa) {
            sql.append(" left join usuario u on u.cliente_codigo = c.codigo \n");
            sql.append(" left join statuspessoa s on u.codigo = s.usuario_codigo \n");
        }
        sql.append(" WHERE c.empresa = ").append(empresa);

        if (!UteisValidacao.emptyString(idFiltroPrimario)) {
            String id = filtroPrimario.getString("id");
            if (id.startsWith("prof")) {
                sql.append(" and c.professorSintetico_codigo = ").append(id.replace("prof", ""));
            } else {
                switch (id) {
                    case "CARTEIRA":
                        sql.append(" and pro.codigopessoa = ").append(filtros.getInt("pessoaProfessorLogado"));
                        break;
                    case "CARTEIRA_NA_ACADEMIA":
                        sql.append(" and pro.codigopessoa = ").append(filtros.getInt("pessoaProfessorLogado"));
                        sql.append(" and s.datainicioevento >= '");
                        sql.append(Uteis.getDataAplicandoFormatacao(inicioNaAcademia, "yyyy-MM-dd HH:mm:ss")).append("' ");
                        break;
                    case "NA_ACADEMIA":
                        sql.append(" and s.datainicioevento >= '");
                        sql.append(Uteis.getDataAplicandoFormatacao(inicioNaAcademia, "yyyy-MM-dd HH:mm:ss")).append("' ");
                        break;
                    case "GERADO_POR_IA":
                        sql.append(" and obj.isgeradoporia is true ");
                        break;
                }
            }
        }

        JSONArray filtroProfessores = filtros.optJSONArray("professores");
        if (filtroProfessores != null && filtroProfessores.length() > 0) {
            String codigosProfessores = "";
            for (int i = 0; i < filtroProfessores.length(); i++) {
                codigosProfessores += "," + filtroProfessores.getInt(i);
            }
            sql.append(" and pro.codigoColaborador in (").append(codigosProfessores.replaceFirst(",", "")).append(")");
        }

        JSONArray filtroSecundario = new JSONArray();
        if (filtros.optJSONArray("secundario") != null && filtros.optJSONArray("secundario").length() > 0) {
            filtroSecundario = filtros.optJSONArray("secundario");
        } else {
            filtroSecundario = new JSONArray("[{\"nome\":\"Sem treino\",\"id\":\"SEM_TREINO\"},{\"nome\":\"Vencido\",\"id\":\"VENCIDO\"},{\"nome\":\"Em dia\",\"id\":\"EM_DIA\"},{\"nome\":\"Treinos futuros\",\"id\":\"TREINO_FUTURO\"},{\"nome\":\"A vencer\",\"id\":\"A_VENCER\"},{\"nome\":\"A Aprovar\",\"id\":\"A_APROVAR\"}]");
        }

        if (filtroSecundario != null && filtroSecundario.length() > 0) {
            StringBuilder secundario = new StringBuilder();
            boolean isFirstCondition = true;
            for (int i = 0; i < filtroSecundario.length(); i++) {
                JSONObject item = filtroSecundario.getJSONObject(i);
                if (!"GERADO_POR_IA".equals(item.getString("id"))) {
                    switch (item.getString("id")) {
                        case "SEM_TREINO":
                            if (!isFirstCondition) {
                                secundario.append(" or ");
                            }
                            secundario.append(" (obj.codigo IS NULL) ");
                            isFirstCondition = false;
                            break;
                        case "EM_DIA":
                            if (!isFirstCondition) {
                                secundario.append(" or ");
                            }
                            secundario.append(" (obj.dataTerminoPrevisto::date > '")
                                    .append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"))
                                    .append("' AND obj.dataInicio::date <= '")
                                    .append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"))
                                    .append("' AND obj.dataTerminoPrevisto::date > '")
                                    .append(Uteis.getDataAplicandoFormatacao(limitAvencer, "yyyy-MM-dd"))
                                    .append("' AND obj.codigo IS NOT NULL) ");
                            isFirstCondition = false;
                            break;
                        case "TREINO_FUTURO":
                            if (!isFirstCondition) {
                                secundario.append(" or ");
                            }
                            secundario.append(" (obj.dataInicio::date > '").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("') ");
                            isFirstCondition = false;
                            break;
                        case "A_VENCER":
                            if (!isFirstCondition) {
                                secundario.append(" or ");
                            }
                            secundario.append(" (obj.dataTerminoPrevisto::date >= '")
                                    .append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"))
                                    .append("' AND obj.dataTerminoPrevisto::date <= '")
                                    .append(Uteis.getDataAplicandoFormatacao(limitAvencer, "yyyy-MM-dd"))
                                    .append("' AND obj.codigo IS NOT NULL AND obj2.codigo IS NULL) ");
                            isFirstCondition = false;
                            break;
                        case "VENCIDO":
                            if (!isFirstCondition) {
                                secundario.append(" or ");
                            }
                            secundario.append(" (obj.dataTerminoPrevisto::date < '")
                                    .append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"))
                                    .append("' AND obj.codigo IS NOT NULL AND obj2.codigo IS NULL) ");
                            isFirstCondition = false;
                            break;
                        case "A_APROVAR":
                            if (!isFirstCondition) {
                                secundario.append(" or ");
                            }
                            secundario.append(" obj.emrevisaoprofessor is true ");
                            isFirstCondition = false;
                            break;
                    }
                }
            }
            if (secundario.length() > 0) {
                sql.append(" AND (").append(secundario.toString().replaceFirst(" OR ", " ")).append(")");
            }
        }

        boolean filtroTerciarioAplicado = false;
        JSONArray filtroTerciario = filtros.optJSONArray("terciario");
        String filtroTerciarioAplicadoStr = "";
        if (filtroTerciario != null && filtroTerciario.length() > 0) {
            for (int i = 0; i < filtroTerciario.length(); i++) {
                JSONObject item = filtroTerciario.getJSONObject(i);
                switch (item.getString("id")) {
                    case "STATUS":
                        sql.append(" and c.situacao ='").append(item.get("nome")).append("' \n");
                        if (!SuperControle.independente(ctx)) {
                            sql.append(" and c.situacaocontrato in ('AV', 'NO') \n");
                        }
                        filtroTerciarioAplicado = true;
                        break;
                    default:
                        if ("AT".equals(item.getString("id")) || "IN".equals(item.getString("id")) || "VI".equals(item.getString("id"))) {
                            filtroTerciarioAplicadoStr += "," + item.getString("id");
                        }
                        break;
                }
            }
        }
        if (!UteisValidacao.emptyString(filtroTerciarioAplicadoStr) && !filtroTerciarioAplicado) {
            filtroTerciarioAplicadoStr = filtroTerciarioAplicadoStr.replaceFirst(",", "").replace(",", "','");
            sql.append(" and c.situacao in ('").append(filtroTerciarioAplicadoStr).append("') \n");
        }

        // origem BI_TREINO e TELA_PRESCRICAO_TREINO considera apenas alunos AT AV e AT NO
        if (!filtroTerciarioAplicado && ("BI_TREINO".equals(origemAcao) || "TELA_PRESCRICAO_TREINO".equals(origemAcao))) {
            sql.append(" and c.situacao ='AT' \n");
            if (!SuperControle.independente(ctx)) {
                sql.append(" and c.situacaocontrato in ('AV', 'NO') \n");
            }
        }

        if (filtros != null && !UteisValidacao.emptyString(filtros.optString("search"))) {
            if (filtros.getString("search").matches("\\d+")) {
                sql.append(" and c.matricula = ").append(filtros.getString("search")).append(" ");
            } else {
                sql.append(" and c.nome ilike '").append(filtros.getString("search")).append("%'");
            }
        }
        if(paginadorDTO != null){
            try (ResultSet rsCount = clienteSinteticoDao.createStatement(ctx, sql.toString())) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(new Long(rsCount.getInt("cont")));
                }
            }
        }

        String ordenacao = filtros.optString("ordenacao");
        if(UteisValidacao.emptyString(ordenacao)){
            sql.append(" order by terminoUltimoPrograma is null desc, terminoUltimoPrograma, c.situacao, c.nome ");
        } else if(ordenacao.startsWith("aluno")){
            sql.append(" order by c.nome ").append(ordenacao.contains("DESC") ? "desc" : "asc");
        } else if(ordenacao.startsWith("status")){
            sql.append(" order by  c.situacao ").append(ordenacao.contains("DESC") ? "desc" : "asc");
            sql.append(", c.situacaocontrato ").append(ordenacao.contains("DESC") ? "desc" : "asc");
        } else if(ordenacao.startsWith("professor")){
            sql.append(" order by pro.nome ").append(ordenacao.contains("DESC") ? "desc" : "asc");
        } else if(ordenacao.startsWith("situacao")){
            sql.append(" order by terminoUltimoPrograma is null ").append(ordenacao.contains("DESC") ? "desc" : "asc");
            sql.append(", terminoUltimoPrograma ").append(ordenacao.contains("DESC") ? "desc" : "asc");
        }

        if(paginadorDTO != null){
            sql.append(" limit ").append(paginadorDTO.getSize());
            if (paginadorDTO.getPage() == null) {
                paginadorDTO.setPage(1l);
            }
            if(paginadorDTO.getPage() > 0){
                sql.append(" offset ").append(paginadorDTO.getSize() * (paginadorDTO.getPage() - 1));
            }
        }


        String codigosCliente;
        StringBuilder codigosProgramas = new StringBuilder();
        try (ResultSet rs = clienteSinteticoDao.createStatement(ctx,
                sql.toString().replace("count(DISTINCT c.codigo) as cont ",
                        somenteMatriculas
                                ? " c.matricula "
                                : " DISTINCT c.codigocliente, c.matricula, c.nome, p.fotokey, terminoUltimoPrograma, " +
                                  "c.situacao, c.situacaoContrato, pro.nome as professornome, " +
                                  "terminoUltimoPrograma is null as terminoultimoprogramanull, obj.codigo as codPrograma ,c.dataultimoacesso, obj.isgeradoporia, obj.emrevisaoprofessor, obj.datalancamento "))) {
            codigosCliente = "";
            while (rs.next()) {
                PessoaPrescricaoDTO prescricaoDTO = new PessoaPrescricaoDTO();
                if (somenteMatriculas) {
                    prescricaoDTO.setMatricula(String.valueOf(rs.getInt("matricula")));
                } else {
                    boolean geradoPorIa = rs.getBoolean("isgeradoporia");
                    if (geradoPorIa) {
                        boolean emRevisaoProfessor = rs.getBoolean("emrevisaoprofessor");

                        ConfiguracaoSistema configObrigatoriedade = configService.consultarPorTipo(ctx, HABILITAR_OBRIGATORIEDADE_APROVACAO_PROFESSOR);
                        boolean obrigatoriedadeAprovacaoProfessor = configObrigatoriedade != null && Boolean.parseBoolean(configObrigatoriedade.getValor());

                        if (!obrigatoriedadeAprovacaoProfessor && geradoPorIa && emRevisaoProfessor) {
                            ConfiguracaoSistema configFluxoAprovacao = configService.consultarPorTipo(ctx, TEMPO_APROVACAO_AUTOMATICA);
                            Integer tempoAprovacao = (configFluxoAprovacao != null && configFluxoAprovacao.getValor() != null && !UteisValidacao.emptyString(configFluxoAprovacao.getValor()))
                                    ? Integer.valueOf(configFluxoAprovacao.getValor())
                                    : 0;

                            Date dataLancamento = rs.getTimestamp("datalancamento");
                            long diferencaEmMinutos = ChronoUnit.MINUTES.between(dataLancamento.toInstant(), Calendario.hoje().toInstant());

                            if (tempoAprovacao == 0 || diferencaEmMinutos > tempoAprovacao) {
                                ProgramaTreino programaTreino = programaTreinoDao.obterPorId(ctx, rs.getInt("codprograma"));
                                if (programaTreino == null) {
                                    throw new ServiceException(ProgramaTreinoExcecoes.PROGRAMA_TREINO_NAO_ENCONTRADO);
                                }

                                programaTreino.setEmRevisaoProfessor(false);
                                programaTreinoDao.alterar(ctx, programaTreino);
                                break;
                            }
                        }
                    }

                    prescricaoDTO.setTipo("Aluno");
                    prescricaoDTO.setCodigoCliente(rs.getInt("codigocliente"));
                    codigosCliente += "," + prescricaoDTO.getCodigoCliente();
                    if (!UteisValidacao.emptyNumber(rs.getInt("codPrograma"))) {
                        codigosProgramas.append(",").append(rs.getInt("codPrograma"));
                        prescricaoDTO.setCodigoPrograma(rs.getInt("codPrograma"));
                    }
                    prescricaoDTO.setNome(rs.getString("nome").toLowerCase());
                    prescricaoDTO.setSituacao(rs.getString("situacao"));
                    prescricaoDTO.setSituacaoContrato(rs.getString("situacaoContrato"));
                    prescricaoDTO.setGeradoPorIA(rs.getBoolean("isgeradoporia"));
                    String professornome = rs.getString("professornome");
                    if (professornome == null) {
                        prescricaoDTO.setProfessor("");
                    } else {
                        prescricaoDTO.setProfessor(professornome.toLowerCase());
                    }
                    prescricaoDTO.setMatricula(String.valueOf(rs.getInt("matricula")));
                    prescricaoDTO.setSituacaoTreino("SEM_TREINO");
                    if(rs.getDate("dataultimoacesso") !=null){
                        prescricaoDTO.setUltimoAcesso(Uteis.getData(rs.getDate("dataultimoacesso"))  );
                    }
                    if (SuperControle.independente(ctx)) {
                        String fotokey = rs.getString("fotokey");
                        if (!UteisValidacao.emptyString(fotokey) && !fotokey.equals("fotoPadrao.jpg")) {
                            prescricaoDTO.setUrlFoto(Aplicacao.obterUrlFotoDaNuvem(fotokey));
                        }
                    }
                }
                alunos.add(prescricaoDTO);
            }
        }
        if(somenteMatriculas){
            return alunos;
        }
        montarProgramasListaPrescricao(ctx, codigosProgramas.toString(), alunos, limitAvencer, true);
        if (!SuperControle.independente(ctx)) {
            montarDadosZW(ctx, empresa, codigosCliente, alunos);
        }
        return alunos;
    }

    private void montarDadosZW(String ctx, Integer empresa, String codigosClientes, List<PessoaPrescricaoDTO> alunos) throws Exception {
        try {
            if (alunos.isEmpty()) {
                return;
            }
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("chave", ctx));
            params.add(new BasicNameValuePair("empresa", empresa.toString()));
            params.add(new BasicNameValuePair("codigosClientes", codigosClientes.replaceFirst(",", "")));
            JSONObject retorno = chamadaZW(ctx, "/prest/treino/prescricao-clientes", params);
            JSONArray array = retorno.getJSONArray("lista");
            for (int i = 0; i < array.length(); i++) {
                JSONObject json = array.getJSONObject(i);
                Integer codigo = json.getInt("codigo");
                Optional<PessoaPrescricaoDTO> opt = alunos.stream()
                        .filter(o -> o.getCodigoCliente().equals(codigo))
                        .findFirst();
                if (opt.isPresent()) {
                    PessoaPrescricaoDTO prescricaoDTO = opt.get();
                    prescricaoDTO.setUrlFoto(json.optString("foto"));
                    prescricaoDTO.setIndicacaoMedica(json.optString("mensagem"));
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, PrescricaoService.class);
        }

    }

    private void montarProgramas(String ctx, String codigos, List<PessoaPrescricaoDTO> alunos, Date limitAVencer, boolean cliente) throws Exception {
        if (!alunos.isEmpty()) {
            StringBuilder sqlProgramas = new StringBuilder();
            sqlProgramas.append("SELECT p.codigocolaborador, p.nome, p.codigo AS codprograma, p.datainicio, p.dataterminoprevisto,\n");
            sqlProgramas.append(cliente ? " c.codigocliente, " : "");
            sqlProgramas.append(" p.totalaulasprevistas, p.nrtreinosrealizados \n");
            sqlProgramas.append("FROM programatreino p \n");
            if (cliente) {
                sqlProgramas.append("INNER JOIN clientesintetico c ON c.codigo = p.cliente_codigo \n");
            }
            sqlProgramas.append("LEFT JOIN programatreino pt2 ON p.cliente_codigo = pt2.cliente_codigo AND pt2.dataTerminoPrevisto > p.dataTerminoPrevisto \n");
            sqlProgramas.append("WHERE pt2.codigo IS NULL \n");
            if (cliente) {
                sqlProgramas.append("AND c.codigocliente IN (").append(codigos.replaceFirst(",", "")).append(") \n");
            } else {
                sqlProgramas.append("AND p.codigocolaborador IN (").append(codigos.replaceFirst(",", "")).append(") \n");
            }

            try (ResultSet rs = programaTreinoDao.createStatement(ctx, sqlProgramas.toString())) {
                while (rs.next()) {
                    Integer codigo = cliente ? rs.getInt("codigocliente") : rs.getInt("codigocolaborador");
                    Optional<PessoaPrescricaoDTO> opt = alunos.stream()
                            .filter(o -> (cliente && o.getCodigoCliente().equals(codigo)) || (!cliente && o.getCodigoColaborador().equals(codigo)))
                            .findFirst();
                    if (opt.isPresent()) {
                        PessoaPrescricaoDTO prescricaoDTO = opt.get();
                        prescricaoDTO.setPrograma(rs.getString("nome"));
                        Integer codigoPrograma = rs.getInt("codprograma");
                        prescricaoDTO.setCodigoPrograma(codigoPrograma);
                        Date datainicio = rs.getDate("datainicio");
                        Date termino = rs.getDate("dataterminoprevisto");
                        double nrTreinosPrevistos = rs.getInt("totalaulasprevistas");
                        double nrTreinosRealizados = ps.obterQuantidadeExecucoesTreinoRealizados(ctx, codigoPrograma);
                        double porcentagem = nrTreinosPrevistos > 0.0 ? (nrTreinosRealizados / nrTreinosPrevistos) * 100.0 : 0.0;
                        prescricaoDTO.setAndamento(new BigDecimal(porcentagem).setScale(2, RoundingMode.HALF_UP).intValue());
                        prescricaoDTO.setInicio(Uteis.getData(datainicio));
                        prescricaoDTO.setFim(Uteis.getData(termino));

                        if (Calendario.maiorOuIgual(termino, Calendario.hoje()) && Calendario.menorOuIgual(termino, limitAVencer)) {
                            prescricaoDTO.setSituacaoTreino("A_VENCER");
                        } else if (Calendario.menor(termino, Calendario.hoje())) {
                            prescricaoDTO.setSituacaoTreino("VENCIDO");
                        } else {
                            prescricaoDTO.setSituacaoTreino("ATIVO");
                        }
                    }
                }
            }
        }
    }

    private void montarProgramasListaPrescricao(String ctx, String codigosProgramas, List<PessoaPrescricaoDTO> alunos, Date limitAVencer, boolean cliente) throws Exception {
        if (!alunos.isEmpty() && !UteisValidacao.emptyString(codigosProgramas)) {
            StringBuilder sqlProgramas = new StringBuilder();
            sqlProgramas.append("SELECT p.codigocolaborador, p.nome, p.codigo AS codprograma, p.datainicio, p.dataterminoprevisto, p.isgeradoporia as geradoPorIA, p.emrevisaoprofessor as isRevisadoProfessor, p.datalancamento, \n");
            sqlProgramas.append(cliente ? " c.codigocliente, " : "");
            sqlProgramas.append(" p.totalaulasprevistas, p.nrtreinosrealizados \n");
            sqlProgramas.append("FROM programatreino p \n");
            if (cliente) {
                sqlProgramas.append("INNER JOIN clientesintetico c ON c.codigo = p.cliente_codigo \n");
            }
            sqlProgramas.append("LEFT JOIN programatreino pt2 ON p.cliente_codigo = pt2.cliente_codigo AND pt2.dataTerminoPrevisto > p.dataTerminoPrevisto \n");
            sqlProgramas.append("WHERE p.codigo IN (").append(codigosProgramas.replaceFirst(",", "")).append(") \n");

            try (ResultSet rs = programaTreinoDao.createStatement(ctx, sqlProgramas.toString())) {
                while (rs.next()) {
                    Integer codigo = rs.getInt("codprograma");
                    Optional<PessoaPrescricaoDTO> opt = alunos.stream()
                            .filter(o -> (cliente && codigo.equals(o.getCodigoPrograma())))
                            .findFirst();
                    if (opt.isPresent()) {
                        PessoaPrescricaoDTO prescricaoDTO = opt.get();
                        prescricaoDTO.setPrograma(rs.getString("nome"));
                        Integer codigoPrograma = rs.getInt("codprograma");
                        prescricaoDTO.setCodigoPrograma(codigoPrograma);
                        Date datainicio = rs.getDate("datainicio");
                        Date termino = rs.getDate("dataterminoprevisto");
                        double nrTreinosPrevistos = rs.getInt("totalaulasprevistas");
                        double nrTreinosRealizados = ps.obterQuantidadeExecucoesTreinoRealizados(ctx, codigoPrograma);
                        double porcentagem = nrTreinosPrevistos > 0.0 ? (nrTreinosRealizados / nrTreinosPrevistos) * 100.0 : 0.0;
                        prescricaoDTO.setAndamento(new BigDecimal(porcentagem).setScale(2, RoundingMode.HALF_UP).intValue());
                        prescricaoDTO.setInicio(Uteis.getData(datainicio));
                        prescricaoDTO.setFim(Uteis.getData(termino));
                        prescricaoDTO.setGeradoPorIA(rs.getBoolean("geradoPorIA"));
                        prescricaoDTO.setRevisadoProfessor(rs.getBoolean("isRevisadoProfessor"));

                        Timestamp dataLancamentoTimestamp = rs.getTimestamp("datalancamento");

                        prescricaoDTO.setDatalancamento(dataLancamentoTimestamp);

                        if (Calendario.maior(datainicio, Calendario.hoje())) {
                            prescricaoDTO.setSituacaoTreino("TREINO_FUTURO");
                        } else if (Calendario.maiorOuIgual(termino, Calendario.hoje()) && Calendario.menorOuIgual(termino, limitAVencer)) {
                            prescricaoDTO.setSituacaoTreino("A_VENCER");
                        } else if (Calendario.menor(termino, Calendario.hoje())) {
                            prescricaoDTO.setSituacaoTreino("VENCIDO");
                        } else {
                            prescricaoDTO.setSituacaoTreino("ATIVO");
                        }
                    }
                }
            }
        }
    }


    private JSONObject chamadaZW(String ctx,
                                 String endpoint,
                                 List<NameValuePair> params
    ) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    public List<ClienteJSON> consultarProfessorTreinoPorNome(final String ctx,
                                                             final String filtro) throws ServiceException {
        List<ClienteJSON> professores = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select distinct p.codigocolaborador, p.nome from professorsintetico p \n");
        sql.append(" inner join programatreino pt on pt.codigocolaborador = p.codigocolaborador \n");
        sql.append(" where p.nome ilike '").append(filtro).append("%'");
        if (UteisValidacao.somenteNumeros(filtro)) {
            sql.append(" or p.codigocolaborador = ").append(filtro);
        }
        sql.append(" order by p.nome limit 10 \n");
        try {
            try (ResultSet rs = programaTreinoDao.createStatement(ctx, sql.toString())) {
                while (rs.next()) {
                    ClienteJSON cliente = new ClienteJSON();
                    cliente.setCodigo(rs.getInt("codigocolaborador"));
                    cliente.setNome(rs.getString("nome") + " (Colaborador)");
                    cliente.setUserName("C0LAB_" + rs.getInt("codigocolaborador"));
                    cliente.setMatricula(rs.getInt("codigocolaborador"));
                    cliente.setCodigoClienteZW(rs.getInt("codigocolaborador"));
                    cliente.setCodigoPessoaZW(rs.getInt("codigocolaborador"));
                    professores.add(cliente);
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, PrescricaoService.class);
        }
        return professores;

    }


    public List<ItemTO> filtroPrincipal(Integer empresa, FiltroColaboradorJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer usuarioID = sessaoService.getUsuarioAtual().getId();
            Usuario usuarios = usuarioService.obterPorId(ctx, usuarioID);

            AtomicBoolean temPermissaoTELA_PRESCRICAO_TREINO= new AtomicBoolean(false);
            AtomicBoolean temPermissaoVER_ALUNOS_OUTRAS_CARTEIRAS= new AtomicBoolean(false);
            AtomicBoolean temPermissaoALUNOS= new AtomicBoolean(false);

            usuarios.getPerfil().getPermissoes().forEach(
                permissao -> {
                    if (permissao.getRecurso().equals(RecursoEnum.TELA_PRESCRICAO_TREINO)){
                        temPermissaoTELA_PRESCRICAO_TREINO.set(permissao.getTipoPermissoes().size() > 0);
                    }
                    if(permissao.getRecurso().equals(RecursoEnum.VER_ALUNOS_OUTRAS_CARTEIRAS)){
                        temPermissaoVER_ALUNOS_OUTRAS_CARTEIRAS.set(permissao.getTipoPermissoes().size() > 0);
                    }
                    if(permissao.getRecurso().equals(RecursoEnum.ALUNOS)){
                        temPermissaoALUNOS.set(permissao.getTipoPermissoes().size() > 0);
                    }
                }
            );

            List<ItemTO> listaRet = new ArrayList<>();
            if (filtros.getParametros() == null || filtros.getParametros().isEmpty()) {
                listaRet.add(new ItemTO("CARTEIRA_NA_ACADEMIA", "label_carteira_academia"));
                listaRet.add(new ItemTO("CARTEIRA", "label_carteira"));
                listaRet.add(new ItemTO("TODOS_ALUNOS", "label_todos_alunos"));
                listaRet.add(new ItemTO("NA_ACADEMIA", "label_na_academia"));
                listaRet.add(new ItemTO("COLABORADORES", "label_colaboradores"));
                listaRet.add(new ItemTO("GERADO_POR_IA", "label_gerado_por_ia"));
            } else {
                if ("CARTEIRA_NA_ACADEMIA".contains(filtros.getParametros().toUpperCase()) || "MINHA CARTEIRA NA ACADEMIA".contains(filtros.getParametros().toUpperCase())) {
                    listaRet.add(new ItemTO("CARTEIRA_NA_ACADEMIA", "label_carteira_academia"));
                }
                if ("CARTEIRA".contains(filtros.getParametros().toUpperCase()) || "MINHA CARTEIRA".contains(filtros.getParametros().toUpperCase())) {
                    listaRet.add(new ItemTO("CARTEIRA", "label_carteira"));
                }
                if ("TODOS_ALUNOS".contains(filtros.getParametros().toUpperCase()) || "TODOS ALUNOS".contains(filtros.getParametros().toUpperCase())) {
                    listaRet.add(new ItemTO("TODOS_ALUNOS", "label_todos_alunos"));
                }
                if ("NA_ACADEMIA".contains(filtros.getParametros().toUpperCase()) || "ALUNOS NA ACADEMIA".contains(filtros.getParametros().toUpperCase())) {
                    listaRet.add(new ItemTO("NA_ACADEMIA", "label_na_academia"));
                }
                if ("COLABORADORES".contains(filtros.getParametros().toUpperCase())) {
                    listaRet.add(new ItemTO("COLABORADORES", "label_colaboradores"));
                }
                if ("GERADO_POR_IA".contains(filtros.getParametros().toUpperCase()) || "TREINO POR I.A".contains(filtros.getParametros().toUpperCase())) {
                    listaRet.add(new ItemTO("GERADO_POR_IA", "label_gerado_por_ia"));
                }
            }

            paginadorDTO.setSort("nome,ASC");
            List<ProfessorSintetico> lista = professorsinteticoDao.listarColaboradores(ctx, filtros, paginadorDTO, empresa, false);
            for (ProfessorSintetico p : lista) {
                listaRet.add(new ItemTO("prof".concat(p.getCodigo().toString()), p.getNome()));
            }

            return listaRet;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(ColaboradoresExcecoes.ERRO_BUSCAR_COLABORADORES, e);
        }
    }

    public void setAtualColaborador(Integer codigoColaborador, String ctx,
                                     ModelMap mm,
                                     ConfiguracaoSistema configSeriesSet,
                                     String ordenarFicha,
                                     ViewUtils viewUtils,
                                     OrigemEnum origemEnum) throws Exception {
        ProgramaVersaoJSON progJSON = obterVersaoUltimoProgramaVigente(ctx, codigoColaborador);
        ProgramaTreino programa = ps.obterPorId(ctx, progJSON.getCodPrograma());
        ProgramaTreinoAndamento programaTreinoAndamento = UtilContext.getBean(ProgramaTreinoAndamentoDao.class)
                .findObjectByAttributes(ctx, new String[]{"programa.codigo"}, new Object[]{programa.getCodigo()}, "codigo");
        ps.ordenarFichas(ctx, programa);
        if (programa.getNrTreinosRealizados() != null
                && programa.getTotalAulasPrevistas() != null
                && programa.getNrTreinosRealizados() >= programa.getTotalAulasPrevistas()) {
            ConfiguracaoSistema cfgEmitirExecutados = configService.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_IMPRESSAO_FICHA_APOS_TODAS_EXECUCOES);
            if (cfgEmitirExecutados.getValorAsBoolean()) {
                throw new ServiceException(viewUtils.getMensagem("todasexecucoes"));
            }
        }
        if (Calendario.menor(programa.getDataTerminoPrevisto(), Calendario.hoje())) {
            ConfiguracaoSistema cfgEmitirVencidos = configService.consultarPorTipo(ctx, ConfiguracoesEnum.EMITIR_FICHA_APOS_VENCIMENTO_TREINO);
            if (!cfgEmitirVencidos.getValorAsBoolean()) {
                throw new ServiceException(viewUtils.getMensagem("mobile.programatreinovencido"));
            }
        }
        ConfiguracaoSistema conf = configService.consultarPorTipo(ctx, ConfiguracoesEnum.USAR_NOVA_MONTAGEM);
        programa.setUsarNovaMontagemFicha(Boolean.valueOf(conf.getValor()));
        ProgramaTreinoJSON programaJSON = ProgramaTreinoJSONControle.preencherProgramaJSON(programa, ctx, null, configSeriesSet.getValorAsBoolean(), ordenarFicha, origemEnum);
        ProgramaTreinoAndamento andamento = ps.obterAndamento(ctx, programa);
        if (UteisValidacao.emptyNumber(programa.getTotalAulasPrevistas())) {
            try {
                ps.calcularAulasPrevistas(programa);
            } catch (Exception e) {
                Uteis.logar(e, ProgramaTreinoJSONControle.class);
                programa.setTotalAulasPrevistas(0);
            }
        }
        mm.addAttribute("programa", programaJSON);
        if (andamento == null) {
            AcompanhamentoSimplesJSON acompanhamentoSimplesJSON = new AcompanhamentoSimplesJSON(String.format("0/%s dias", programa.getTotalAulasPrevistas()), 0.0);
            acompanhamentoSimplesJSON.setFrequenciaSemanal(0);
            mm.addAttribute("acompanhamento", acompanhamentoSimplesJSON);
        } else {
            Integer quantidadeExecucoes = programa.getNrTreinosRealizados();
            AcompanhamentoSimplesJSON acompanhamentoSimplesJSON = new AcompanhamentoSimplesJSON(
                    String.format("%s/%s dias", quantidadeExecucoes,
                            programa.getTotalAulasPrevistas()),
                    programaTreinoAndamento.getPercentualExecucoesFrequenciaAteHoje(quantidadeExecucoes));
            acompanhamentoSimplesJSON.setNrTreinos(quantidadeExecucoes);
            acompanhamentoSimplesJSON.setAulasPrevistas(programa.getTotalAulasPrevistas());
            mm.addAttribute("acompanhamento", acompanhamentoSimplesJSON);
        }
    }

    public ProgramaVersaoJSON obterVersaoUltimoProgramaVigente(final String ctx,
                                                               final Integer codigoColaborador) throws ServiceException {
        try {
            StringBuilder query = new StringBuilder();
            query.append(" select prog.codigo, prog.versao, prog.dataterminoprevisto from programatreino  prog \n");
            query.append(" where prog.codigoColaborador = ").append(codigoColaborador).append(" \n");
            query.append(" order by prog.dataterminoprevisto desc limit 1 \n");
            try (ResultSet rs = programaTreinoDao.createStatement(ctx,
                    query.toString())) {
                while (rs.next()) {
                    ProgramaVersaoJSON pvJson = new ProgramaVersaoJSON(rs.getInt("codigo"), rs.getInt("versao"));
                    return pvJson;
                }
            }
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
        return null;

    }

}
