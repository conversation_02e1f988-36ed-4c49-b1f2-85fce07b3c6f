package br.com.pacto.service.authenticacao;

import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.dto.authenticacaoMs.AutenticacaoDTO;
import br.com.pacto.dto.authenticacaoMs.AutenticacaoTokenDTO;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.service.discovery.DiscoveryMsService;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class AuthenticacaoMsService {

    public static AutenticacaoTokenDTO getAutenticacaoTokenDTO(String chaveDestino, String token) throws Exception {
        String urlAuthenticacaoMs = DiscoveryMsService.urls().getServiceUrls().getAutenticacaoUrl() + "/login";

        // Refatorar usuário e chave para autentificação
        AutenticacaoDTO autenticacaoDTO = new AutenticacaoDTO();
        autenticacaoDTO.setChave(chaveDestino);
        autenticacaoDTO.setUsername("PACTOBR");
        if (Aplicacao.isAmbienteTeste()) {
            autenticacaoDTO.setSenha(Aplicacao.getProp(Aplicacao.senhaPactoBrTeste));
        } else {
            autenticacaoDTO.setSenha(Aplicacao.getProp(Aplicacao.senhaPactoBr));
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);

        String ret;
        try (CloseableHttpClient client = ExecuteRequestHttpService.createConnector()) {
            HttpPost httpPost = new HttpPost(urlAuthenticacaoMs);
            httpPost.setEntity(new StringEntity(new JSONObject(autenticacaoDTO).toString(), ContentType.APPLICATION_JSON));
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
            try (CloseableHttpResponse response = client.execute(httpPost)) {
                ret = EntityUtils.toString(response.getEntity());
            }
            return JSONMapper.getObject(new JSONObject(ret).getJSONObject("content"), AutenticacaoTokenDTO.class);
        }
    }
}
