package br.com.pacto.service.login;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.json.JSONObject;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TokenSolicitacaoDTO {

    private String usuarioGeral;
    private Integer usuariozw;
    private String usernamezw;
    private String usernametreino;
    private String ip;

    private String responsavel; //usuario que soliciou originou
    private String chave; //chave de onde originou a solicitação
    private Integer empresa; //empresa de onde originou a solicitação

    private String email; //novo email
    private String telefone; //novo telefone

    private String codigoVerificacao; //codigo do sms enviado ou do email
    private Boolean codigoViaEmail;
    private Boolean enviarLink;
    private Boolean novoLogin;
    public TokenSolicitacaoDTO() {

    }

    public TokenSolicitacaoDTO(Usuario usuarioVO, String chave, Integer empresa,
                               UsuarioSimplesDTO usuarioResponsavelVO, String ipCliente) {
        if (usuarioVO != null) {
            this.usuarioGeral = usuarioVO.getUsuarioGeral();
            this.usuariozw = usuarioVO.getCodigo();
            this.usernametreino = usuarioVO.getUserName();
        }
        this.chave = chave;
        this.empresa = empresa;
        this.ip = ipCliente;

        //defini para qual link será direcionado
        this.novoLogin = Boolean.valueOf(Aplicacao.getProp(Aplicacao.enableNewLogin));
        JSONObject json = new JSONObject();
        json.put("origem", "Treino Web");
        json.put("chave", chave);
        json.put("empresa", empresa);
        if (usuarioResponsavelVO != null) {
            json.put("username", usuarioResponsavelVO.getUsername());
            json.put("usuario_zw", usuarioResponsavelVO.getId());
            json.put("token", usuarioResponsavelVO.getToken());
        }
        this.responsavel = json.toString();
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getUsuarioGeral() {
        return usuarioGeral;
    }

    public void setUsuarioGeral(String usuarioGeral) {
        this.usuarioGeral = usuarioGeral;
    }

    public Boolean getCodigoViaEmail() {
        return codigoViaEmail;
    }

    public void setCodigoViaEmail(Boolean codigoViaEmail) {
        this.codigoViaEmail = codigoViaEmail;
    }

    public Boolean getEnviarLink() {
        return enviarLink;
    }

    public void setEnviarLink(Boolean enviarLink) {
        this.enviarLink = enviarLink;
    }

    public Boolean getNovoLogin() {
        return novoLogin;
    }

    public void setNovoLogin(Boolean novoLogin) {
        this.novoLogin = novoLogin;
    }

    public Integer getUsuariozw() {
        return usuariozw;
    }

    public void setUsuariozw(Integer usuariozw) {
        this.usuariozw = usuariozw;
    }

    public String getUsernametreino() {
        return usernametreino;
    }

    public void setUsernametreino(String usernametreino) {
        this.usernametreino = usernametreino;
    }

    public String getUsernamezw() {
        return usernamezw;
    }

    public void setUsernamezw(String usernamezw) {
        this.usernamezw = usernamezw;
    }

    public String getCodigoVerificacao() {
        return codigoVerificacao;
    }

    public void setCodigoVerificacao(String codigoVerificacao) {
        this.codigoVerificacao = codigoVerificacao;
    }
}
