package br.com.pacto.service.login;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "DTO para transferência de dados de token de autenticação e verificação")
public class TokenDTO {

    @ApiModelProperty(value = "Token de autenticação ou verificação", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @ApiModelProperty(value = "Identificador do usuário geral no sistema", example = "usuario123")
    private String usuarioGeral;

    @ApiModelProperty(value = "Endereço IP de onde originou a solicitação", example = "*************")
    private String ip;

    @ApiModelProperty(value = "Origem da solicitação", example = "WEB")
    private String origem;

    @ApiModelProperty(value = "Código de verificação para confirmação (SMS, Email, etc.)", example = "123456")
    private String codigoVerificacao;

    public TokenDTO() {
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUsuarioGeral() {
        return usuarioGeral;
    }

    public void setUsuarioGeral(String usuarioGeral) {
        this.usuarioGeral = usuarioGeral;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public String getCodigoVerificacao() {
        return codigoVerificacao;
    }

    public void setCodigoVerificacao(String codigoVerificacao) {
        this.codigoVerificacao = codigoVerificacao;
    }
}
