/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.scheduling.agendamento;

import br.com.pacto.base.scheduling.base.TaskBase;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.agenda.TipoLembreteEnum;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.util.UtilContext;
import it.sauronsoftware.cron4j.TaskExecutionContext;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class TaskNotificationAgendamento extends TaskBase {

    public TaskNotificationAgendamento(final String ctx) {
        super(ctx);
    }

    @Override
    public void execute(TaskExecutionContext executor) throws RuntimeException {
        try {
            Uteis.logar(null, "Started -> " + TaskNotificationAgendamento.class.getSimpleName() + " " + getCtx());
            AgendamentoService agendamentoService = (AgendamentoService) UtilContext.getBean(AgendamentoService.class);
            //
            List<Agendamento> listaProximas2Horas = agendamentoService.consultarPrevistosNosProximosMinutos(getCtx(),
                    Calendario.hoje(), null, null,0, TipoLembreteEnum.DUAS_HORAS);
            for (Agendamento agendamento : listaProximas2Horas) {
                notificar(agendamento, TipoLembreteEnum.DUAS_HORAS);
            }
            
            List<Agendamento> listaProximasUmDia = agendamentoService.consultarPrevistosNosProximosMinutos(getCtx(),
                    Calendario.hoje(), null, null, TipoLembreteEnum.UM_DIA.getnMinutos() - 120, TipoLembreteEnum.UM_DIA);
            for (Agendamento agendamento : listaProximasUmDia) {
                notificar(agendamento, TipoLembreteEnum.UM_DIA);
            }
            //            
            List<Agendamento> listaNovos = agendamentoService.consultarAgendamentosNovosOuAlterados(getCtx(),
                    null, null, TipoLembreteEnum.AGENDAMENTO_NOVO);
            for (Agendamento agendamento : listaNovos) {
                notificar(agendamento, TipoLembreteEnum.AGENDAMENTO_NOVO,
                        TipoNotificacaoEnum.AGENDAMENTO_NOVO, "lembrete.agendamento.novo");
            }

            ConfiguracaoSistemaService configuracaoSistemaService = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            if(configuracaoSistemaService.notificacaoConfigurada(getCtx(), TipoNotificacaoEnum.AGENDAMENTO_ALTERADO)){
                List<Agendamento> listaAlterados = agendamentoService.consultarAgendamentosNovosOuAlterados(getCtx(),
                        null, null, TipoLembreteEnum.AGENDAMENTO_ALTERADO);
                for (Agendamento agendamento : listaAlterados) {
                    notificar(agendamento, TipoLembreteEnum.AGENDAMENTO_ALTERADO,
                            TipoNotificacaoEnum.AGENDAMENTO_ALTERADO, "lembrete.agendamento.alterado");
                }
            }

        } catch (ServiceException ex) {
            Logger.getLogger(TaskNotificationAgendamento.class.getName()).log(Level.SEVERE, null, ex);
            if (ex.getMessage() != null && ex.getMessage().contains("Cannot open connection")) {
                Logger.getLogger(TaskNotificationAgendamento.class.getName()).log(Level.SEVERE,
                        "REMOVI " + this.getClass().getName() + "_" + this.getCtx(), ex);
//                TaskCollector[] arr =
//                        executor.getScheduler().getTaskCollectors();
//                for (int i = 0; i < arr.length; i++) {
//                    TaskCollectorBase taskCollectorBase = (TaskCollectorBase) arr[i];
//                    if (taskCollectorBase.getKey().equals(getCtx())) {
//                        int n = taskCollectorBase.getTasks().size();
//                        for (int k = 0; k < n; k++) {
//                            taskCollectorBase.getTasks().remove(k);
//                        }
//                    }
//                }

            }
        } finally {
            Uteis.logar(null, "Ended -> " + TaskNotificationAgendamento.class.getSimpleName() + " " + getCtx());
        }
    }
}
