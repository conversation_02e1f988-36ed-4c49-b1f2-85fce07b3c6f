package br.com.pacto.base.jpa.migrador;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;

import java.sql.ResultSet;
import java.util.Date;

@ClasseProcesso(
        autor = "Alisson Melo",
        data = "08/02/2025",
        descricao = "Criar novas colunas na tabela respostaclienteparq e atualizar registros antigos",
        motivacao = "TW-1502")
public class Migration_TW_1502 implements MigracaoVersaoInterface {

    @Override
    public void executar(String ctx, UsuarioDao dao) throws Exception {
        try {
            dao.executeNativeSQL(ctx, "ALTER TABLE respostaclienteparq ADD COLUMN ativo BOOLEAN DEFAULT FALSE;");
            dao.executeNativeSQL(ctx, "ALTER TABLE respostaclienteparq ADD COLUMN parqPositivo BOOLEAN DEFAULT FALSE;");
            dao.executeNativeSQL(ctx, "ALTER TABLE respostaclienteparq ADD COLUMN dataUltimaEdicao TIMESTAMP;");
        } catch (Exception e) {
            Uteis.logar(e, AA_FirstMigration.class);
        }

        // o processo a seguir tem a finalidade de garantir a consistência dos dados para o padrão correto do parq,
        // assim, fazendo com que o parq mais recente respondido pelo aluno e que tenha a url da assinatura,
        // seja marcado como ativo, e os anteriores caso existam como inativos.
        // por regra, para que o parq seja válido ele deve ter a assinatura do aluno preenchida.
        try {
            try (ResultSet rsCliente = dao.createStatement(ctx,
                    "select distinct(cs.codigo) \n" +
                    "from clientesintetico cs \n" +
                    "inner join respostaclienteparq rcp on rcp.cliente_codigo = cs.codigo \n" +
                    "order by cs.codigo")) {
                while (rsCliente.next()) {
                    try (ResultSet rsRespostaParq = dao.createStatement(ctx,
                            "select codigo, urlassinatura \n" +
                            "from respostaclienteparq r \n" +
                            "where cliente_codigo = " + rsCliente.getInt("codigo") + " \n" +
                            "order by dataresposta desc")) {
                        boolean primeiro = true;
                        while (rsRespostaParq.next()) {
                            try {
                                if (primeiro && !UteisValidacao.emptyString(rsRespostaParq.getString("urlassinatura"))) {
                                    primeiro = false;
                                    dao.executeNativeSQL(ctx, "UPDATE respostaclienteparq SET ativo = true where codigo = " + rsRespostaParq.getInt("codigo"));
                                } else {
                                    dao.executeNativeSQL(ctx, "UPDATE respostaclienteparq SET ativo = false where codigo = " + rsRespostaParq.getInt("codigo"));
                                }
                            } catch (Exception e) {
                                Uteis.logarDebug("Erro ao tentar atualizar campo ativo do respostaclienteparq: " + e.getMessage());
                            }
                            // Processo para povoar coluna parqPositivo
                            try (ResultSet rsRespostaCliente = dao.createStatement(ctx,
                                    "select exists (\n" +
                                            "   select * from respostacliente r \n" +
                                            "   where respostaclienteparq_codigo = " + rsRespostaParq.getInt("codigo") + " \n" +
                                            "   and resposta ilike 'SIM' \n" +
                                            ") as parqpositivo")) {
                                if (rsRespostaCliente.next()) {
                                    if (rsRespostaCliente.getBoolean("parqpositivo")) {
                                        try {
                                            dao.executeNativeSQL(ctx, "UPDATE respostaclienteparq SET parqPositivo = true where codigo = " + rsRespostaParq.getInt("codigo"));
                                        } catch (Exception e) {
                                            Uteis.logarDebug("Erro ao tentar atualizar campo parqPositivo do respostaclienteparq: " + e.getMessage());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, AA_FirstMigration.class);
        }
    }

}
