/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.base.jpa;

import br.com.pacto.base.jpa.annotation.ClasseProcesso;
import br.com.pacto.base.jpa.annotation.Processo;
import br.com.pacto.base.jpa.bean.HistoricoVersao;
import br.com.pacto.base.jpa.bean.ValidadorVersao;
import br.com.pacto.base.jpa.bean.Versao;
import br.com.pacto.base.jpa.dao.intf.HistoricoVersaoDao;
import br.com.pacto.base.jpa.dao.intf.ValidadorVersaoDao;
import br.com.pacto.base.jpa.service.intf.PovoadorService;
import br.com.pacto.base.jpa.service.intf.VersaoService;
import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import br.com.pacto.bean.avaliacao.PerimetriaEnum;
import br.com.pacto.bean.benchmark.Benchmark;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoPerimetro;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.musculo.GrupoMuscular;
import br.com.pacto.bean.musculo.PontosMuscularEnum;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.pessoa.Pessoa;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.tipowod.TipoWod;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.dao.impl.usuario.UsuarioDaoImpl;
import br.com.pacto.dao.intf.agenda.AgendamentoDao;
import br.com.pacto.dao.intf.atividade.AtividadeFichaDao;
import br.com.pacto.dao.intf.atividade.AtividadeGrupoMuscularDao;
import br.com.pacto.dao.intf.aula.AulaDiaExcecaoDao;
import br.com.pacto.dao.intf.aula.AulaDiaExclusaoDao;
import br.com.pacto.dao.intf.aula.ProfessorSubstituidoDao;
import br.com.pacto.dao.intf.aulapersonal.LiberacaoCheckInDao;
import br.com.pacto.dao.intf.avaliacao.AvaliacaoFisicaDao;
import br.com.pacto.dao.intf.avaliacao.ItemAvaliacaoFisicaDao;
import br.com.pacto.dao.intf.avaliacao.ParQDao;
import br.com.pacto.dao.intf.avaliacao.PesoOsseoDao;
import br.com.pacto.dao.intf.cliente.ClienteObservacaoDao;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.configuracoes.ConfiguracaoSistemaDao;
import br.com.pacto.dao.intf.configuracoes.ConfiguracaoSistemaUsuarioDao;
import br.com.pacto.dao.intf.configuracoes.LinkPredefinidoDao;
import br.com.pacto.dao.intf.dashboardbi.GeracaoRankingProfessoresDao;
import br.com.pacto.dao.intf.disponibilidade.HorarioDisponibilidadeDao;
import br.com.pacto.dao.intf.gympass.ConfigGymPassDao;
import br.com.pacto.dao.intf.locacao.AgendamentoLocacaoDao;
import br.com.pacto.dao.intf.locacao.LocacaoHorarioDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.musculo.GrupoMuscularDao;
import br.com.pacto.dao.intf.perfil.PerfilDao;
import br.com.pacto.dao.intf.perfil.permissao.PermissaoDao;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.dao.intf.serie.SerieDao;
import br.com.pacto.dao.intf.tipoevento.TipoEventoDao;
import br.com.pacto.dao.intf.tvgestor.AlunoFavoritoDao;
import br.com.pacto.dao.intf.usuario.UsuarioDao;
import br.com.pacto.dao.intf.wod.WodDao;
import br.com.pacto.enumerador.crossfit.TipoWodEnum;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.agenda.AgendaServiceImpl;
import br.com.pacto.service.impl.benchmark.BaseBenchmark;
import br.com.pacto.service.impl.conexao.ConexaoZWServiceImpl;
import br.com.pacto.service.intf.agenda.DisponibilidadeService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.benchmark.BenchmarkService;
import br.com.pacto.service.intf.cliente.ClientePesquisaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gestao.DashboardBIService;
import br.com.pacto.service.intf.musculo.GrupoMuscularService;
import br.com.pacto.service.intf.pessoa.PessoaService;
import br.com.pacto.service.intf.processo.SincronizacaoEmpresaService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.tipowod.TipoWodService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.wod.WodService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.hibernate.Transaction;
import org.json.JSONArray;
import org.json.JSONObject;
import org.reflections.Reflections;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.scanners.TypeAnnotationsScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;
import org.reflections.util.FilterBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;

import javax.persistence.PersistenceException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 *
 * <AUTHOR>
 */
@Component
public class MigracaoDadosJPAService {

    @Autowired
    private VersaoService versaoService;
    @Autowired
    private HistoricoVersaoDao historicoDao;
    @Autowired
    private UsuarioDao usuarioDao;
    @Autowired
    private PerfilDao perfilDao;
    @Autowired
    private PermissaoDao permissaoDao;
    @Autowired
    private TipoEventoDao tipoEventoDao;
    @Autowired
    private ProfessorSinteticoDao professorDao;
    @Autowired
    private WodDao wodDao;
    @Autowired
    private PovoadorService povoadorService;
    @Autowired
    private ProgramaTreinoService programaService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private PessoaService pessoaService;
    @Autowired
    private AtividadeFichaDao atividadeFichaDao;
    @Autowired
    private ClienteSinteticoDao clienteSinteticoDao;
    @Autowired
    private AtividadeGrupoMuscularDao atividadeGrupoMuscularDao;
    @Autowired
    private ConfiguracaoSistemaDao configuracaoDao;
    @Autowired
    private GrupoMuscularDao grupoMuscularDao;
    @Autowired
    private GrupoMuscularService grupoService;
    @Autowired
    private TipoWodService tipoWodService;
    @Autowired
    private WodService wodService;
    @Autowired
    private BenchmarkService benchmarkService;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;
    @Autowired
    private ClientePesquisaService clientePesquisaService;
    @Autowired
    private AvaliacaoFisicaService avaliacaoFisicaService;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;
    @Autowired
    private DisponibilidadeService disponibilidadeService;
    @Autowired
    private SincronizacaoEmpresaService sincronizacaoEmpresaService;
    @Autowired
    private SerieDao serieDao;
    @Autowired
    private AgendamentoDao agendamentoDao;
    @Autowired
    private ConexaoZWServiceImpl conexaoZWService;
    @Autowired
    private ConfigGymPassDao configGymPassDao;
    @Autowired
    private ItemAvaliacaoFisicaDao itemAvaliacaoFisicaDao;
    @Autowired
    private AvaliacaoFisicaDao avaliacaoFisicaDao;
    @Autowired
    private PesoOsseoDao pesoOsseoDao;
    @Autowired
    private AulaDiaExclusaoDao aulaDiaExclusaoDao;
    @Autowired
    private AulaDiaExcecaoDao aulaDiaExcecaoDao;
    @Autowired
    private AlunoFavoritoDao alunoFavoritoDao;
    @Autowired
    private ClienteObservacaoDao clienteObservacaoDao;
    @Autowired
    private LiberacaoCheckInDao liberacaoCheckInDao;
    @Autowired
    private ConfiguracaoSistemaUsuarioDao configuracaoSistemaUsuarioDao;
    @Autowired
    private ProfessorSubstituidoDao professorSubstituidoDao;
    @Autowired
    private GeracaoRankingProfessoresDao geracaoRankingProfessoresDao;
    @Autowired
    private LinkPredefinidoDao linkPredefinidoDao;
    @Autowired
    private ValidadorVersaoDao validadorVersaoDao;
    @Autowired
    private AgendamentoLocacaoDao agendamentoLocacaoDao;
    @Autowired
    private LogDao logDao;
    @Autowired
    private ParQDao parQDao;
    @Autowired
    private LocacaoHorarioDao locacaoHorarioDao;

    private static int versaoBD = 156;

    private void verificaTabelaVersao(final String ctx) throws Exception {
        List<Versao> l = versaoService.obterTodos(ctx);
        if (l == null || l.isEmpty()) {
            Versao v = new Versao();
            v.setNumero(0);
            versaoService.inserir(ctx, v);
            povoadorService.run(ctx);
        }
    }

    private Versao versaoAtual(final String ctx) throws Exception {
        List<Versao> l = versaoService.obterTodos(ctx);
        return l.get(0);
    }

    private boolean permiteExecucao(final String ctx, Processo o, final Integer versao) throws Exception {
        List<HistoricoVersao> l = historicoDao.findListByAttributes(ctx, new String[]{"versao"}, new Object[]{versao}, null, 0);
        return (o.permitirMultiplasExecucoes() || l == null || l.isEmpty());
    }

    private void atualizarVersao(final String ctx, final int novoValor) throws Exception {
        Versao v = versaoAtual(ctx);
        v.setNumero(novoValor);
        versaoService.alterar(ctx, v);
    }

    public void atualizar(final String ctx) throws ClassNotFoundException, IllegalAccessException, IllegalArgumentException, InvocationTargetException, InstantiationException, NoSuchMethodException {
        preparingHistoricoVersao(ctx);
        updateSequencialVersions(ctx);
        updateProccessVersions(ctx);
    }

    private void preparingHistoricoVersao(String ctx) {
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE historicoversao ADD COLUMN classeMigrador TEXT;", false);
        } catch (Exception ignored) {

        }
        try {
            usuarioDao.executeNativeSQL(ctx, "alter table historicoversao alter column descricao type TEXT ;", false);
        } catch (Exception ignored) {

        }
    }

    private void updateSequencialVersions(String ctx) {
        try {
            verificaTabelaVersao(ctx);
            int versaoBDAtual = versaoAtual(ctx).getNumero();
            for (versaoBDAtual++; versaoBDAtual <= versaoBD; versaoBDAtual++) {
                System.out.println("Atualizar Dados Básicos da chave " + ctx + " para versao -> " + versaoBDAtual);

                Class partypes[] = new Class[1];
                partypes[0] = String.class;

                String[] parvalues = new String[1];
                parvalues[0] = ctx;

                Class cls = this.getClass();
                Method meth = cls.getMethod("migracaoVersao" + versaoBDAtual, partypes);
                Processo anotacao = meth.getAnnotation(Processo.class);
                String exception = null;
                if (permiteExecucao(ctx, anotacao, versaoBDAtual)) {
                    if (anotacao.ignorarErro()) {
                        try {
                            meth.invoke(this, parvalues);
                        } catch (Exception e) {
                            exception = e.getMessage();
                            Uteis.logar(e, cls);
                        }
                    } else {
                        meth.invoke(this, parvalues);
                    }
                    atualizarVersao(ctx, versaoBDAtual);

                    if (anotacao != null) {
                        String descricao = String.format(
                                "-- Descricao: %s<br/>" +
                                "-- Motivacao: %s<br/>",
                                anotacao.descricao(),
                                anotacao.motivacao());
                        historicoDao.insert(ctx, new HistoricoVersao(anotacao.autor(), anotacao.data(), Calendario.hoje(), versaoBDAtual, descricao, exception, null));
                    }
                }
                System.out.println("     Sucesso! " + versaoBDAtual);
            }
        } catch (Exception ex) {
            Logger.getLogger(MigracaoDadosJPAService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private void updateProccessVersions(String ctx) {
        try {
            List<String> migrateClasses = findAllMigrateClassesToRun(ctx);

            for (String migrateClass : migrateClasses) {
                System.out.println("Atualizar dados da chave " + ctx + " com o processo -> " + migrateClass);

                Class<?> clazz = Class.forName("br.com.pacto.base.jpa.migrador" + "." + migrateClass);
                ClasseProcesso anotacao = clazz.getAnnotation(ClasseProcesso.class);
                String descricao = getDescriptionByAnnotation(anotacao);

                Object instancia = clazz.getDeclaredConstructor().newInstance();
                Method metodoExecutar = clazz.getMethod("executar", String.class, UsuarioDao.class);
                metodoExecutar.invoke(instancia, ctx, usuarioDao);

                historicoDao.insert(ctx, new HistoricoVersao(anotacao.autor(), anotacao.data(), Calendario.hoje(), null, descricao, null, migrateClass));

                System.out.println("     Sucesso! " + migrateClass);
            }
        } catch (Exception ex) {
            Logger.getLogger(MigracaoDadosJPAService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private String getDescriptionByAnnotation(ClasseProcesso anotacao) {
        String descricao = null;
        if (anotacao != null) {
            descricao = String.format("-- Descricao: %s<br/>"
                            + "-- Autor: %s<br />"
                            + "-- Data: %s<br/>"
                            + "-- Motivacao: %s<br/>"
                            + "-- RepetirCasoErro: %s<br/>",
                    anotacao.descricao(),
                    anotacao.autor(),
                    anotacao.data(),
                    anotacao.motivacao(),
                    false);
        }
        return descricao;
    }

    private List<String> findAllMigrateClassesToRun(String ctx) throws Exception {
        List<String> classesAnotadas = this.listProccess();
        List<HistoricoVersao> versoesAtualizadas = new ArrayList<>();
        if (!classesAnotadas.isEmpty()) {
            versoesAtualizadas = historicoDao.findAllByClassesMigradoras(ctx, classesAnotadas);
        }
        Set<String> classesAtualizadas = versoesAtualizadas.stream()
                .map(HistoricoVersao::getClasseMigrador)
                .collect(Collectors.toSet());

        return classesAnotadas.stream()
                .filter(classe -> !classesAtualizadas.contains(classe))
                .collect(Collectors.toList());
    }

    private List<String> listProccess() {
        String pacote = "br.com.pacto.base.jpa.migrador";
        Reflections reflections = new Reflections(new ConfigurationBuilder()
                .setUrls(ClasspathHelper.forPackage(pacote))
                .setScanners(new TypeAnnotationsScanner(), new SubTypesScanner(false))
                .filterInputsBy(new FilterBuilder().includePackage(pacote)));

        Set<Class<?>> classes = reflections.getTypesAnnotatedWith(ClasseProcesso.class);
        List<String> classNames = new ArrayList<>();
        for (Class<?> clazz : classes) {
            classNames.add(clazz.getSimpleName());
        }
        Collections.sort(classNames);
        return classNames;
    }

    @Processo(autor = "Waller Maciel",
            data = "22/01/2014",
            descricao = "Adicionar perfis de acesso aos usuários já existentes ",
            motivacao = "Restringir e personalizar a visão das aplicações baseados em perfis de usuários diferentes.")
    public void migracaoVersao1(final String ctx) throws Exception {
        //
        List<Usuario> l = usuarioDao.findAll(ctx);
        Perfil perfilProfessor = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_PROFESSOR);
        if (perfilProfessor.getPermissoes().isEmpty()) {
            perfilProfessor.addPermissao(new Permissao(RecursoEnum.ACOMPANHAR, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor));
            perfilProfessor.addPermissao(new Permissao(RecursoEnum.ALUNOS, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor));
            perfilProfessor.addPermissao(new Permissao(RecursoEnum.NOTIFICACOES, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor));
            perfilProfessor.addPermissao(new Permissao(RecursoEnum.GESTAO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor));
            perfilProfessor.addPermissao(new Permissao(RecursoEnum.ADD_ALUNO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor));
            perfilProfessor.addPermissao(new Permissao(RecursoEnum.PROGRAMA_TREINO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor));
            perfilDao.update(ctx, perfilProfessor);
        }
        Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_COORDENADOR);
        if (perfilCoordenador.getPermissoes().isEmpty()) {
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.ACOMPANHAR, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.ALUNOS, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.APARELHOS, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.ATIVIDADES, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.CATEGORIA_ATIVIDADE, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.CATEGORIA_FICHAS, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.CONFIGURACOES_EMPRESA, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.FICHAS_PRE_DEFINIDAS, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.GESTAO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.GRUPOS_MUSCULARES, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.IMAGENS, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.MUSCULOS, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.NIVEIS, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.NOTIFICACOES, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.ADD_ALUNO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.PROGRAMA_TREINO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.TIPO_EVENTO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilCoordenador.addPermissao(new Permissao(RecursoEnum.OBJETIVOS, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador));
            perfilDao.update(ctx, perfilCoordenador);
        }

        for (Usuario usuario : l) {
            if (usuario.getTipo().equals(TipoUsuarioEnum.PROFESSOR)) {
                usuario.setPerfil(perfilProfessor);
                usuarioDao.update(ctx, usuario);
            } else if (usuario.getTipo().equals(TipoUsuarioEnum.COORDENADOR)) {
                usuario.setPerfil(perfilCoordenador);
                usuarioDao.update(ctx, usuario);
            }
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "06/02/2014",
            descricao = "Povoar o campo ordem das atividades das fichas que não estava sendo setado",
            motivacao = "Ordenar as atividades dentro da ficha")
    public void migracaoVersao2(final String ctx) throws Exception {
        //conteúdo do método foi comentado de forma proposital para evitar que o mesmo seja rodado
//        atividadeFichaDao.executeQuery(ctx, "UPDATE AtividadeFicha av SET ordem = (SELECT (COUNT(codigo)+1) FROM AtividadeFicha "
//                + "WHERE ficha.codigo = av.ficha.codigo and codigo < av.codigo) where av.ordem is null");
    }

    @Processo(autor = "Joao Alcides",
            data = "06/02/2014",
            descricao = "Cria permissões para o perfil de consultor e adiciona permissão de add aluno ao perfis existentes",
            motivacao = "Povoar permissões do novo perfil de usuário")
    public void migracaoVersao3(final String ctx) throws Exception {

        Perfil perfilConsultor = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_CONSULTOR);
        Perfil perfilProfessor = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_PROFESSOR);
        Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_COORDENADOR);

        if (perfilConsultor.getPermissoes().isEmpty()) {
            perfilConsultor.addPermissao(new Permissao(RecursoEnum.ADD_ALUNO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilConsultor));
            perfilDao.update(ctx, perfilConsultor);
        }
        Permissao permissao1 = new Permissao(RecursoEnum.ADD_ALUNO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor);
        permissaoDao.insert(ctx, permissao1);
        Permissao permissao2 = new Permissao(RecursoEnum.ADD_ALUNO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador);
        permissaoDao.insert(ctx, permissao2);

    }

    @Processo(autor = "Waller Maciel",
            data = "18/02/2014",
            descricao = "Recriar tabelas de Auditoria do Sistema para que o CustomRevisionEntity possa funcionar",
            motivacao = "Adicionar informações na auditoria como: usuário logado, ip e página do usuário")
    public void migracaoVersao4(final String ctx) throws Exception {
        //Removido porque era referente a tabela de auditoria
    }

    @Processo(autor = "Joao Alcides",
            data = "20/02/2014",
            descricao = "Remove registros associados a valores de enums que serão retirados",
            motivacao = "Resolver o problema gerado ao retirar valores de enum",
            permitirMultiplasExecucoes = false)
    public void migracaoVersao5(final String ctx) throws Exception {
        //26/05/2015 - WM.: Se tornou obsoleta essa migração, por isso foi comentada para não excluir dados incorretamente.
        //remover de TipoAgendamentoEnum o valor TipoAgendamentoEnum.DISPONIBILIDADES id = 0
        //tipoEventoDao.removeEnumValue(ctx, "comportamento", 0);
        //remover de RecursoEnum o valor RecursoEnum.AGENDA id = 12
        //permissaoDao.removeEnumValue(ctx, "recurso", 12);
    }

    @Processo(autor = "Joao Alcides",
            data = "05/03/2014",
            descricao = "Preencher os relacionamentos do Programa e Notificações com professor",
            motivacao = "Manter relacionamento que antes não era preenchido",
            permitirMultiplasExecucoes = true)
    public void migracaoVersao6(final String ctx) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" UPDATE programatreino pt SET professormontou_codigo = ");
        sql.append(" (SELECT professorsintetico_codigo FROM clientesintetico cs WHERE cs.codigo = pt.cliente_codigo),");
        sql.append(" professorcarteira_codigo = (SELECT professorsintetico_codigo FROM clientesintetico cs WHERE cs.codigo = pt.cliente_codigo)");
        sql.append(" where professorcarteira_codigo is null or professormontou_codigo is null;");

        sql.append(" UPDATE notificacao nt SET professor_codigo = ");
        sql.append("(SELECT professorsintetico_codigo FROM clientesintetico cs WHERE cs.codigo = nt.cliente_codigo) where professor_codigo is null;");
        professorDao.executeNativeSQL(ctx, sql.toString());
    }

    @Processo(autor = "Joao Alcides",
            data = "11/03/2014",
            descricao = "Preencher os relacionamentos do Programa e Notificações com professor",
            motivacao = "Manter relacionamento que antes não era preenchido",
            permitirMultiplasExecucoes = true)
    public void migracaoVersao7(final String ctx) throws Exception {
        tipoEventoDao.executeNativeSQL(ctx, "UPDATE tipoevento SET apenasalunoscarteira = true where apenasalunoscarteira is null");
    }

    @Processo(autor = "Joao Alcides",
            data = "12/03/2014",
            descricao = "Dar permissão de cadastro de usuario e perfil de usuario",
            motivacao = "Povoar permissões do novo perfil de usuário")
    public void migracaoVersao8(final String ctx) throws Exception {

        Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_COORDENADOR);
        Permissao permissao1 = new Permissao(RecursoEnum.PERFIL_USUARIO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador);
        permissaoDao.insert(ctx, permissao1);
        Permissao permissao2 = new Permissao(RecursoEnum.USUARIOS, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador);
        permissaoDao.insert(ctx, permissao2);

    }

    @Processo(autor = "Waller Maciel",
            data = "12/03/2014",
            descricao = "Campos com data type incorreto",
            motivacao = "Campos como Date e deviam ser Timestamp",
            permitirMultiplasExecucoes = true)
    public void migracaoVersao9(final String ctx) throws Exception {
        usuarioDao.executeNativeSQL(ctx, "ALTER TABLE clientesintetico ALTER dataultimoacesso TYPE timestamp without time zone;");
        usuarioDao.executeNativeSQL(ctx, "ALTER TABLE clientesintetico ALTER dataUltimoContatoCRM TYPE timestamp without time zone;");
    }

    @Processo(autor = "Joao Alcides",
            data = "03/04/2014",
            descricao = "Dar permissão de cadastro de programa",
            motivacao = "Povoar permissões do programa de treino")
    public void migracaoVersao10(final String ctx) throws Exception {

        Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_COORDENADOR);
        Permissao permissao1 = new Permissao(RecursoEnum.PROGRAMA_TREINO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador);
        permissaoDao.insert(ctx, permissao1);

        Perfil perfilConsultor = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_CONSULTOR);
        Permissao permissao3 = new Permissao(RecursoEnum.PROGRAMA_TREINO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilConsultor);
        permissaoDao.insert(ctx, permissao3);

        Perfil perfilProfessor = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_PROFESSOR);
        Permissao permissao4 = new Permissao(RecursoEnum.PROGRAMA_TREINO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor);
        permissaoDao.insert(ctx, permissao4);

    }

    @Processo(autor = "Joao Alcides",
            data = "06/05/2014",
            descricao = "Alterar campo carga da serie e serierealizada",
            motivacao = "Campos como integer vão mudar para double",
            permitirMultiplasExecucoes = true)
    public void migracaoVersao11(final String ctx) throws Exception {
        usuarioDao.executeNativeSQL(ctx, "ALTER TABLE serie ALTER carga TYPE float;");
        usuarioDao.executeNativeSQL(ctx, "ALTER TABLE serierealizada ALTER carga TYPE float;");
    }

    @Processo(autor = "Waller Maciel",
            data = "22/05/2014",
            descricao = "Retirar do Enum MetodoExecucaoEnum o valor ALTERNADO_SEGMENTO do indice 0",
            motivacao = "Enum value desnecessário",
            permitirMultiplasExecucoes = true)
    public void migracaoVersao12(final String ctx) throws Exception {
        usuarioDao.executeNativeSQL(ctx, "UPDATE atividadeFicha set metodoExecucao = 0 where metodoExecucao = 9;");
        usuarioDao.executeNativeSQL(ctx, "UPDATE ficha set ativo = true where ativo is null;");
    }

    @Processo(autor = "Joao Alcides",
            data = "06/06/2014",
            descricao = "Povoar alunos com treino mas sem vinculo com professor",
            motivacao = "Dar consistência aos indicadores",
            permitirMultiplasExecucoes = true)
    public void migracaoVersao13(final String ctx) throws Exception {
        usuarioDao.executeNativeSQL(ctx, "UPDATE clientesintetico cli set professorsintetico_codigo  = (select professorCarteira_codigo from programatreino where cliente_codigo = cli.codigo limit 1) where professorsintetico_codigo is null;");
        usuarioDao.executeNativeSQL(ctx, "UPDATE treinorealizado tre set professor_codigo = (select professorCarteira_codigo from programatreino where cliente_codigo = tre.cliente_codigo limit 1)where professor_codigo is null;");
    }

    @Processo(autor = "Waller Maciel",
            data = "13/06/2014",
            descricao = "Reconstrói as constraints que impedem a exclusão do histórico de execuções do Programa",
            motivacao = "Permitir a deleção em Cascata de uma Série, Atividade,Ficha e Programa de Treino",
            permitirMultiplasExecucoes = true)
    public void migracaoVersao14(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE serierealizada DROP CONSTRAINT fk77c730f3936cf894;");
        } catch (Exception e) {}
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE serierealizada DROP CONSTRAINT fk77c730f37f40fbb4;");
        } catch (Exception e) {}
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE serierealizada DROP CONSTRAINT fk77c730f37c22ee91;");
        } catch (Exception e) {}
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE treinorealizado DROP CONSTRAINT fk7bb8913a9dd8a79;");
        } catch (Exception e) {}
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE notificacao DROP CONSTRAINT fke06d5afafd99035c;");
        } catch (Exception e) {}
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE lembreteagendamento DROP CONSTRAINT fk19fc8913bbbb329a;");
        } catch (Exception e) {}
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE programatreinoandamento DROP CONSTRAINT fk776fadc7bbbb329a;");
        } catch (Exception e) {}
        //
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE serierealizada ADD CONSTRAINT fk77c730f3936cf894 FOREIGN KEY (serie_codigo) REFERENCES serie (codigo) ON UPDATE NO ACTION ON DELETE CASCADE;");
        } catch (Exception e) {}
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE serierealizada ADD CONSTRAINT fk77c730f37c22ee91 FOREIGN KEY (atividadeficha_codigo) REFERENCES atividadeficha (codigo) ON UPDATE NO ACTION ON DELETE CASCADE;");
        } catch (Exception e) {}
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE serierealizada ADD CONSTRAINT fk77c730f37f40fbb4 FOREIGN KEY (treinorealizado_codigo) REFERENCES treinorealizado (codigo) ON UPDATE NO ACTION ON DELETE CASCADE;");
        } catch (Exception e) {}
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE treinorealizado ADD CONSTRAINT fk7bb8913a9dd8a79 FOREIGN KEY (programatreinoficha_codigo) REFERENCES programatreinoficha (codigo) ON UPDATE NO ACTION ON DELETE CASCADE;");
        } catch (Exception e) {}
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE notificacao ADD CONSTRAINT fke06d5afafd99035c FOREIGN KEY (serierealizada_codigo) REFERENCES serierealizada (codigo) ON UPDATE NO ACTION ON DELETE CASCADE;");
        } catch (Exception e) {}
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE lembreteagendamento ADD CONSTRAINT fk19fc8913bbbb329a FOREIGN KEY (programa_codigo) REFERENCES programatreino (codigo) ON UPDATE NO ACTION ON DELETE CASCADE;");
        } catch (Exception e) {}
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE programatreinoandamento ADD CONSTRAINT fk776fadc7bbbb329a FOREIGN KEY (programa_codigo) REFERENCES programatreino (codigo) ON UPDATE NO ACTION ON DELETE CASCADE;");
        } catch (Exception e) {}
    }

    @Processo(autor = "Waller Maciel",
            data = "06/07/2014",
            descricao = "Insere relacionamentos para Dias da Semana da Ficha",
            motivacao = "Uma Ficha agora pode ser usada mais de uma vez por semana",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao15(final String ctx) throws Exception {
        try {
            StringBuilder s = new StringBuilder();
            s.append("insert into programatreinoficha_diasemana (programatreinoficha_codigo, diasemana) ");
            s.append("select codigo, CASE WHEN diasemana = 1 THEN 'DM' ");
            s.append("			WHEN diasemana = 2 THEN 'SG' ");
            s.append("			WHEN diasemana = 3 THEN 'TR' ");
            s.append("			WHEN diasemana = 4 THEN 'QA' ");
            s.append("			WHEN diasemana = 5 THEN 'QI' ");
            s.append("			WHEN diasemana = 6 THEN 'SX' ");
            s.append("			WHEN diasemana = 7 THEN 'SB' ");
            s.append("               END ");
            s.append("FROM programatreinoficha ");
            s.append("where tipoexecucao = 1 and programa_codigo not in (select programa_codigo from programatreinoficha_diasemana) ");
            usuarioDao.executeNativeSQL(ctx, s.toString());
        } catch (Exception e) {}
    }

    @Processo(autor = "Waller Maciel",
            data = "22/07/2014",
            descricao = "Atualiza codigo da empresa do ZW na tabela ProfessorSintetico",
            motivacao = "Preencher empresa em ProfessorSintetico",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao16(final String ctx) throws Exception {
        try {
            StringBuilder s = new StringBuilder();
            s.append("update professorsintetico set empresa_codigo = (select codigo from empresa where codZW in (select empresazw from usuario where usuario.professor_codigo = professorsintetico.codigo));");
            usuarioDao.executeNativeSQL(ctx, s.toString());
        } catch (Exception e) {}
    }

    @Processo(autor = "Joao Alcides",
            data = "23/10/2014",
            descricao = "Povoar treino andamento",
            motivacao = "Corrigir problema nos dados dos alunos que usam ficham impressa",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao17(final String ctx) throws Exception {
        try {
            programaService.povoarAndamentoTreino(ctx);
        } catch (Exception e) {}
    }

    @Processo(autor = "Joao Alcides",
            data = "28/01/2014",
            descricao = "Add unique constrainst",
            motivacao = "Não permitir duplicidade",
            permitirMultiplasExecucoes = true)
    public void migracaoVersao18(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE clientesintetico ADD CONSTRAINT clientesintetico_codigopessoa_key UNIQUE (codigopessoa, matricula);");
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE usuario ADD CONSTRAINT usuario_professor_codigo_key UNIQUE (professor_codigo, cliente_codigo);");
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE professorsintetico ADD CONSTRAINT professorsintetico_codigocolaborador_key UNIQUE (codigocolaborador);");
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE programatreinoandamento ADD CONSTRAINT programatreinoandamento_programa_codigo_key UNIQUE (programa_codigo);");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Processo(autor = "Waller Maciel",
            data = "09/02/2015",
            descricao = "Criar indices faltantes no Banco de Dados",
            motivacao = "Melhorar consultas a banco de dados",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao19(final String ctx) throws Exception {
        try {
            String sql = "SELECT('create index idx_' || relname || '_' ||\n"
                    + "   array_to_string(column_name_list, '_') || ' on ' || conrelid ||\n"
                    + "   ' (' || array_to_string(column_name_list, ',') || ')') AS sql\n"
                    + "FROM (SELECT DISTINCT\n"
                    + "        conrelid,\n"
                    + "        array_agg(attname)   column_name_list,\n"
                    + "        array_agg(attnum) AS column_list\n"
                    + "      FROM pg_attribute\n"
                    + "        JOIN (SELECT\n"
                    + "                cast(conrelid as REGCLASS),\n"
                    + "                conname,\n"
                    + "                unnest(conkey) AS column_index\n"
                    + "              FROM (SELECT DISTINCT\n"
                    + "                      conrelid,\n"
                    + "                      conname,\n"
                    + "                      conkey\n"
                    + "                    FROM pg_constraint\n"
                    + "                      JOIN pg_class ON pg_class.oid = pg_constraint.conrelid\n"
                    + "                      JOIN pg_namespace ON pg_namespace.oid = pg_class.relnamespace\n"
                    + "                    WHERE nspname !~ '^pg_' AND nspname <> 'information_schema'\n"
                    + "                   ) fkey\n"
                    + "             ) fkey\n"
                    + "          ON fkey.conrelid = pg_attribute.attrelid\n"
                    + "             AND fkey.column_index = pg_attribute.attnum\n"
                    + "      GROUP BY conrelid, conname\n"
                    + "     ) candidate_index\n"
                    + "  JOIN pg_class ON pg_class.oid = candidate_index.conrelid\n"
                    + "  LEFT JOIN pg_index ON pg_index.indrelid = conrelid\n"
                    + "                        AND cast(indkey as TEXT) = array_to_string(column_list, ' ')\n"
                    + "WHERE indexrelid IS NULL;";
            List<String> commands = usuarioDao.listStringWithParam(ctx, sql, null, null);
            for (String createIdx : commands) {
                usuarioDao.executeNativeSQL(ctx, createIdx);
            }
        } catch (Exception e) {}
    }

    @Processo(autor = "Waller Maciel",
            data = "15/05/2015",
            descricao = "Povoar Ultima Execução da Ficha",
            motivacao = "Povoar campo para ser utilizado pelo RetiraFicha",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao20(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx,
                    "update ficha set ultimaexecucao = \n"
                            + "(select max(tr.datainicio)\n"
                            + "	from treinorealizado tr \n"
                            + "	inner join programatreinoficha ptf on ptf.codigo = tr.programatreinoficha_codigo \n"
                            + "	inner join ficha f on f.codigo = ptf.ficha_codigo\n"
                            + "	where f.codigo = ficha.codigo)\n"
                            + "where ultimaExecucao is null");
        } catch (Exception e) {}
    }

    @Processo(autor = "Waller Maciel",
            data = "17/05/2015",
            descricao = "Atualiza número de versão do Programa de Treino Renovação",
            motivacao = "Aplicativo Pacto Treino não difere um programa de Treino que foi renovado",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao21(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx, "update programatreino pv "
                    + "set versao = cast((select (max(cast(p.versao as integer)) +1) from programatreino p "
                    + "                 where p.cliente_codigo = pv.cliente_codigo) as character varying) \n"
                    + "where current_timestamp between pv.datainicio and pv.dataterminoprevisto and pv.programatreinorenovacao is not null");
        } catch (Exception e) {}
    }

    @Processo(autor = "Waller Maciel",
            data = "17/05/2015",
            descricao = "Atualiza número de versão do Programa de Treino Renovação",
            motivacao = "Aplicativo Pacto Treino não difere um programa de Treino que foi renovado",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao22(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx, "UPDATE professorsintetico SET professortw = true; ");
            usuarioDao.executeNativeSQL(ctx, "UPDATE professorsintetico SET professortw = false WHERE "
                    + " (codigo IN( SELECT professor_codigo FROM usuario WHERE tipo IN (4))  \n"
                    + " or (personal and codigo NOT IN (SELECT professor_codigo FROM usuario WHERE tipo IN (1))))\n"
                    + " and codigo not in (select distinct professorsintetico_codigo from clientesintetico "
                    + " where professorsintetico_codigo  is not null);");
        } catch (Exception e) {}
    }

    @Processo(autor = "Joao Alcides",
            data = "29/07/2015",
            descricao = "Cria usuario PACTOBR para empresas que não vão usar integração com ZW",
            motivacao = "Ter o usuário pronto para configurar",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao23(final String ctx) throws Exception {
        try {
            Usuario user = usuarioService.consultarPorUserName(ctx, "PACTOBR");
            if (user != null && user.getCodigo() != null && user.getCodigo() > 0) {
                return;
            }
            String modulos = Aplicacao.getProp(ctx, Aplicacao.modulos);
            if (!modulos.contains("ZW")) {
                Pessoa pessoa = new Pessoa();
                pessoa.setNome("PACTO MÉTODO GESTÃO");
                pessoa = pessoaService.inserirPessoa(ctx, pessoa);
                ProfessorSintetico professor = new ProfessorSintetico();
                professor.setAtivo(true);
                professor.setPessoa(pessoa);
                professor.setCodigoPessoa(pessoa.getCodigo());
                professor.setNome("PACTO MÉTODO GESTÃO");
                List<Empresa> empresas = empresaService.obterTodos(ctx);
                if (empresas == null || empresas.isEmpty()) {
                    Empresa empresa = new Empresa("EMPRESA");
                    empresa = empresaService.inserir(ctx, empresa);
                    empresa.setCodZW(empresa.getCodigo());
                    professor.setEmpresa(empresaService.alterar(ctx, empresa));
                } else {
                    professor.setEmpresa(empresas.get(0));
                }
                professor.setPersonaInterno(false);
                professor.setPersonal(false);
                professor.setPosPago(false);
                professor.setProfessorTW(true);
                professor = professorDao.insert(ctx, professor);
                professor.setCodigoColaborador(professor.getCodigo());
                professor = professorDao.update(ctx, professor);
                Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_COORDENADOR);
                usuarioService.gerarUsuario(ctx, "PACTOBR", "JSP)34X&*", professor, TipoUsuarioEnum.ROOT, perfilCoordenador);
            }
        } catch (Exception e) {}
    }


    @Processo(autor = "Joao Alcides",
            data = "29/01/2016",
            descricao = "Aumentar o do campo complemento da tabela Serie ",
            motivacao = "O campo complemento pode possuir mais que 255 caracteres",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao24(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE serie ALTER complemento TYPE text;");
        } catch (Exception e) {}
    }

       @Processo(autor = "Marcos André",
            data = "01/02/2016",
            descricao = "Aumentar o do campo complemento da tabela Serie ",
            motivacao = "O campo complemento pode possuir mais que 255 caracteres",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao25(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx, "update programatreino  set versao = versao + 1;");
        } catch (Exception e) {}
    }


    @Processo(autor = "Rafael Carvalhedo",
            data = "10/12/2015",
            descricao = "Aumentar o do campo observação da tabela ClienteObservacao ",
            motivacao = "O campo observação pode possuir mais que 255 caracteres",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao26(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE clienteobservacao ALTER observacao TYPE text;");
        } catch (Exception e) {}
    }

    @Processo(autor = "Rafael Carvalhedo",
            data = "26/01/2016",
            descricao = "Dar permissão de ver bi de outros professores ",
            motivacao = "Inserir permissões em todos os perfis")
    public void migracaoVersao27(final String ctx) throws Exception {
        try {
            List<Perfil> perfis = perfilDao.findAll(ctx);
            for (Perfil perfil : perfis) {
                Permissao permissao = new Permissao(RecursoEnum.VER_BI_OUTROS_PROFESSORES, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfil);
                permissaoDao.insert(ctx, permissao);
            }
        } catch (Exception e) {}
    }

    @Processo(autor = "Joao Alcides",
            data = "07/04/2016",
            descricao = "Dar permissão",
            motivacao = "Inserir permissões em todos os perfis")
    public void migracaoVersao28(final String ctx) throws Exception {
        try {
            List<Perfil> perfis = perfilDao.findAll(ctx);
            for (Perfil perfil : perfis) {
                Permissao permissaotv = new Permissao(RecursoEnum.TVGESTOR, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfil);
                permissaoDao.insert(ctx, permissaotv);

                Permissao permissaoranking = new Permissao(RecursoEnum.RANKING_PROFESSORES, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfil);
                permissaoDao.insert(ctx, permissaoranking);

                Permissao permissaografico = new Permissao(RecursoEnum.GRAFICO_BI, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfil);
                permissaoDao.insert(ctx, permissaografico);
            }
        } catch (Exception e) {}
    }

    @Processo(autor = "Fred Policarpo",
            data = "04/07/2016",
            descricao = "Marcar na ficha do aluno quando o nome de uma atividade for alterada manualmente.",
            motivacao = "Atualizar ficha quanto nome de atividade for alterado.")
    public void migracaoVersao29(final String ctx) throws Exception {
        try {
            atividadeFichaDao.executeNativeSQL(ctx, "update atividadeficha set nomeatividadealteradomanualmente=true"
                    + " where codigo in (select af.codigo from atividadeficha af inner join atividade at on "
                    + " at.codigo=af.atividade_codigo and af.nome <> at.nome)");
        } catch (Exception e) {}
    }

    @Processo(autor = "Fred Policarpo",
            data = "07/07/2016",
            descricao = "Adiciona restrição de unicidade na coluna codigopessoa da tabela ClienteSintetico.",
            motivacao = "Evitar que seja criado mais de um registro no Cliente Sintético para a mesma pessoa.")
    public void migracaoVersao30(final String ctx) throws Exception {
//        POSSIBILIDADE DE CAUSAR DIVERGÊNCIA EM BANCO APÓS ANALISAR UM BANCO, POIS ESTAVA TENTANDO DELETAR UM REGISTRO DE ALUNO INDEVIDAMENTE
//        String sql = "select codigopessoa from (select codigopessoa, count(codigo) as total from clientesintetico\n" +
//                        "group by codigopessoa\n" +
//                        "order by total desc) sql where total > 1";
//
//        try (ResultSet resultSet = clienteSinteticoDao.createStatement(ctx, sql)) {
//
//            while (resultSet.next()) {
//                int codigoPessoa = resultSet.getInt("codigopessoa");
//
//                String sqlClientesDuplicados = "select codigo from clientesintetico where codigopessoa="
//                        + codigoPessoa + " order by codigo asc";
//
//                List<Integer> codigosClientesDuplicados = new ArrayList<Integer>();
//                Integer clienteOriginal = null;
//                try (ResultSet rsClientsDuplicados = clienteSinteticoDao.createStatement(ctx, sqlClientesDuplicados)) {
//
//                    while (rsClientsDuplicados.next()) {
//                        if (clienteOriginal == null)
//                            clienteOriginal = rsClientsDuplicados.getInt("codigo");
//                        else
//                            codigosClientesDuplicados.add(rsClientsDuplicados.getInt("codigo"));
//                    }
//                }
//
//                for (Integer codigoClienteDuplicado : codigosClientesDuplicados) {
//                    clienteSinteticoDao.executeNativeSQL(ctx, "update treinorealizado set cliente_codigo="
//                            + clienteOriginal
//                            + " where cliente_codigo="
//                            + codigoClienteDuplicado);
//
//                    clienteSinteticoDao.executeNativeSQL(ctx, "update notificacao set cliente_codigo="
//                            + clienteOriginal
//                            + " where cliente_codigo="
//                            + codigoClienteDuplicado);
//
//                    clienteSinteticoDao.executeNativeSQL(ctx, "update programatreino set cliente_codigo="
//                            + clienteOriginal
//                            + " where cliente_codigo="
//                            + codigoClienteDuplicado);
//
//                    clienteSinteticoDao.executeNativeSQL(ctx, "delete from statuspessoa where usuario_codigo in"
//                            + " (select codigo from usuario where cliente_codigo =" + codigoClienteDuplicado + ")");
//
//                    clienteSinteticoDao.executeNativeSQL(ctx, "delete from usuario where cliente_codigo="
//                            + codigoClienteDuplicado);
//
//                    clienteSinteticoDao.executeNativeSQL(ctx, "delete from clientesintetico where codigo="
//                            + codigoClienteDuplicado);
//                }
//            }
//        }
    }

    @Processo(autor = "Rafael Carvalhedo",
            data = "10/12/2015",
            descricao = "Aumentar o do campo descricoesModalidades da tabela ClienteObservacao ",
            motivacao = "O campo descricoesModalidades pode possuir mais que 255 caracteres de acordo com a quantidade de modalidades do aluno.",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao31(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE clientesintetico ALTER descricoesModalidades TYPE text;");
        } catch (Exception e) {}
    }

    @Processo(autor = "Rafael Carvalhedo",
            data = "05/12/2016",
            descricao = "Alterar o valor default do campo onde o mesmo já fora criado",
            motivacao = "Corrigir problema onde o campo gerava erro null.",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao32(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE treinorealizado ALTER COLUMN executadofichadia SET DEFAULT true;");
        } catch (Exception e) {}
        try {
            usuarioDao.executeNativeSQL(ctx, "UPDATE treinorealizado SET executadofichadia = false WHERE executadofichadia is null;");
        } catch (Exception e) {}
    }

    @Processo(autor = "Rafael Carvalhedo",
            data = "03/01/2017",
            descricao = "Corrigir dados de permissão duplicados para o mesmo perfil",
            motivacao = "Se deve ao fato de não conseguir alterar a permissão, onde é consultado uma permissão e al",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao33(final String ctx) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("Select permissao.codigo as cod1,permissao.perfil_codigo as perfil1,aux.codigo as cod2,");
            sql.append("aux.perfil_codigo as perfil2,permissao.recurso as recurso from permissao \n");
            sql.append("INNER JOIN permissao aux ON aux.recurso = permissao.recurso AND aux.perfil_codigo = permissao.perfil_codigo\n");
            sql.append("WHERE permissao.codigo != aux.codigo\n");
            try (ResultSet rs = permissaoDao.createStatement(ctx, sql.toString())) {
                while (rs.next()) {
                    Integer perfil = rs.getInt("perfil1");
                    Integer recurso = rs.getInt("recurso");

                    Integer codigoUltimaRev = 0;
                    sql = new StringBuilder();
                    sql.append(" Select max(p.codigo) as codigo from permissao p\n");
                    sql.append(" WHERE p.recurso = ").append(recurso).append(" AND p.perfil_codigo = ").append(perfil);
                    try (ResultSet resultSet = permissaoDao.createStatement(ctx, sql.toString())) {
                        if (resultSet.next()) {
                            codigoUltimaRev = resultSet.getInt("codigo");
                        }
                    }

                    sql = new StringBuilder();
                    sql.append(" DELETE FROM permissao_tipopermissoes  where permissao_codigo  in (Select codigo from permissao where codigo !=").append(codigoUltimaRev);
                    sql.append(" AND perfil_codigo = \n").append(perfil);
                    sql.append(" AND recurso = ").append(recurso).append(");");
                    permissaoDao.executeNativeSQL(ctx, sql.toString());

                    sql = new StringBuilder();
                    sql.append(" DELETE FROM PERMISSAO\n");
                    sql.append(" WHERE codigo !=").append(codigoUltimaRev).append(" AND perfil_codigo = ").append(perfil).append("\n");
                    sql.append(" AND recurso = ").append(recurso);
                    permissaoDao.executeNativeSQL(ctx, sql.toString());
                }
            }
        } catch (Exception e) {}
    }

    @Processo(autor = "Rafael Carvalhedo",
            data = "09/02/2014",
            descricao = "Adiciona permissão de add aluno ao perfis que não possuam a mesma",
            motivacao = "Povoar permissões do perfil de usuário",
            ignorarErro = true)
    public void migracaoVersao34(final String ctx) throws Exception {
        try {
            Perfil perfilConsultor = perfilDao.findObjectByAttribute(ctx, "nome", Perfil.NOME_PERFIL_CONSULTOR);
            Perfil perfilProfessor = perfilDao.findObjectByAttribute(ctx, "nome", Perfil.NOME_PERFIL_PROFESSOR);
            Perfil perfilCoordenador = perfilDao.findObjectByAttribute(ctx, "nome", Perfil.NOME_PERFIL_COORDENADOR);

            if (!perfilConsultor.isItemHabilitado(RecursoEnum.ADD_ALUNO, TipoPermissaoEnum.TOTAL)) {
                Permissao permissao = new Permissao(RecursoEnum.ADD_ALUNO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilConsultor);
                permissaoDao.insert(ctx, permissao);
            }
            if (!perfilProfessor.isItemHabilitado(RecursoEnum.ADD_ALUNO, TipoPermissaoEnum.TOTAL)) {
                Permissao permissao = new Permissao(RecursoEnum.ADD_ALUNO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor);
                permissaoDao.insert(ctx, permissao);
            }
            if (!perfilCoordenador.isItemHabilitado(RecursoEnum.ADD_ALUNO, TipoPermissaoEnum.TOTAL)) {
                Permissao permissao = new Permissao(RecursoEnum.ADD_ALUNO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador);
                permissaoDao.insert(ctx, permissao);
            }
        } catch (Exception e) {}
    }

    @Processo(autor = "Rafael Carvalhedo",
            data = "19/01/2016",
            descricao = "Remove as configurações duplicadas",
            motivacao = "Gerar erro ao consultar objetos por atributos, onde os registros estão duplicados",
            ignorarErro = true)
    public void migracaoVersao35(final String ctx) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" Select cs.* from configuracaosistema  cs\n");
            sql.append(" INNER JOIN ConfiguracaoSistema cs1 ON cs1.configuracao = cs.configuracao\n");
            sql.append(" where cs.codigo != cs1.codigo and ((cs.configuracao != 33) or (cs1.configuracao = 33 and cs.configuracao = 33 and cs.tiponotificacao = cs1.tiponotificacao))");
            try (ResultSet rs = configuracaoDao.createStatement(ctx, sql.toString())) {
                while (rs.next()) {
                    Integer tipoNotificacao = rs.getInt("tiponotificacao");
                    Integer configuracao = rs.getInt("configuracao");
                    if (UteisValidacao.emptyNumber(tipoNotificacao) && !UteisValidacao.emptyNumber(configuracao)) {
                        sql = new StringBuilder();
                        sql.append(" Select MAX(cs.codigo) as codigo from configuracaosistema  cs\n");
                        sql.append(" INNER JOIN ConfiguracaoSistema cs1 ON cs1.configuracao = cs.configuracao\n");
                        sql.append(" where cs.codigo != cs1.codigo and cs.configuracao = ").append(configuracao);
                        Integer ultimaConfig;
                        try (ResultSet rsConfig = configuracaoDao.createStatement(ctx, sql.toString())) {
                            ultimaConfig = null;
                            while (rsConfig.next()) {
                                ultimaConfig = rsConfig.getInt("codigo");
                            }
                        }
                        if (!UteisValidacao.emptyNumber(ultimaConfig)) {
                            sql = new StringBuilder();
                            sql.append("DELETE FROM ConfiguracaoSistema\n");
                            sql.append(" WHERE configuracao = ").append(configuracao).append(" and codigo != ").append(ultimaConfig);
                            permissaoDao.executeNativeSQL(ctx, sql.toString());
                        }
                    } else if (!UteisValidacao.emptyNumber(tipoNotificacao) && !UteisValidacao.emptyNumber(configuracao)) {
                        sql = new StringBuilder();
                        sql.append(" Select MAX(cs.codigo) as codigo from configuracaosistema  cs\n");
                        sql.append(" INNER JOIN ConfiguracaoSistema cs1 ON cs1.tiponotificacao = cs.tiponotificacao\n");
                        sql.append(" where cs.codigo != cs1.codigo and cs.configuracao = 33 and cs.tiponotificacao = ").append(tipoNotificacao);
                        Integer ultimaConfig;
                        try (ResultSet rsConfig = configuracaoDao.createStatement(ctx, sql.toString())) {
                            ultimaConfig = null;
                            while (rsConfig.next()) {
                                ultimaConfig = rsConfig.getInt("codigo");
                            }
                        }
                        if (!UteisValidacao.emptyNumber(ultimaConfig)) {
                            sql = new StringBuilder();
                            sql.append("DELETE FROM ConfiguracaoSistema\n");
                            sql.append(" WHERE configuracao = 33 and tiponotificacao = ").append(tipoNotificacao).append(" and codigo != ").append(ultimaConfig);
                            permissaoDao.executeNativeSQL(ctx, sql.toString());
                        }
                    }
                }
            }
        } catch (Exception e) {}
    }

    @Processo(autor = "Rafael Carvalhedo",
            data = "21/07/2016",
            descricao = "Povoa as tabelas de tipoBenchmark e Benchmark com os dados iniciais definidos pelo sistema.",
            motivacao = "Pois os dados iniciais são padrões para todas a academias que utilizam crossfit")
    public void migracaoVersao36(final String ctx) throws Exception {
        try {
            BaseBenchmark.montarBaseDadosBenchMark(ctx);
            List<Perfil> perfis = perfilDao.findAll(ctx);
            for (Perfil perfil : perfis) {
                Permissao permissaotipobench = new Permissao(RecursoEnum.BENCHMARK, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfil);
                permissaoDao.insert(ctx, permissaotipobench);

                Permissao permissaobench = new Permissao(RecursoEnum.TIPO_BENCHMARK, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfil);
                permissaoDao.insert(ctx, permissaobench);

                Permissao permissaowod = new Permissao(RecursoEnum.WOD, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfil);
                permissaoDao.insert(ctx, permissaowod);

                Permissao permissaoranking = new Permissao(RecursoEnum.RANKING_WOD, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfil);
                permissaoDao.insert(ctx, permissaoranking);
            }
        } catch (Exception e) {}
    }

    @Processo(autor = "Glauco Troncha Camargo",
            data = "26/05/2017",
            descricao = "Aumentar o do campo colaboradores da tabela ClienteSintetico ",
            motivacao = "O campo colaboradores pode possuir mais que 255 caracteres",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao37(final String ctx) throws Exception {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "ALTER TABLE clientesintetico ALTER colaboradores TYPE text;");
        } catch (Exception e) {}
    }

    @Processo(autor = "Joao Alcides",
            data = "01/06/2017",
            descricao = "Povoa as permissões de anamnese",
            motivacao = "Povoa as permissões de anamnese")
    public void migracaoVersao38(final String ctx) throws Exception {
        try {
            List<Perfil> perfis = perfilDao.findAll(ctx);
            for (Perfil perfil : perfis) {
                Permissao permissaotipobench = new Permissao(RecursoEnum.ANAMNESE, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfil);
                permissaoDao.insert(ctx, permissaotipobench);
            }
        } catch (Exception e) {}
    }

    @Processo(autor = "Glauco Troncha Camargo",
            data = "31/07/2017",
            descricao = "Aumentar o do campo XXXX da tabela YYYY",
            motivacao = "O campo XXXX pode possuir mais que 255 caracteres",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao39(final String ctx) throws Exception {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "ALTER TABLE ficha ALTER mensagemaluno TYPE text;");
        } catch (Exception e) {}
    }

    @Processo(autor = "Joao Alcides",
            data = "01/06/2017",
            descricao = "Povoa os relacionamentos grupo muscular x perimetros",
            motivacao = "Povoa os relacionamentos grupo muscular x perimetros")
    public void migracaoVersao40(final String ctx) throws Exception {
        try {
            try (ResultSet statement = atividadeFichaDao.createStatement(ctx, "select count(*) as nr from grupomuscular_perimetros  ")) {
                if (statement.next()) {
                    int nr = statement.getInt("nr");
                    if (nr == 0) {
                        grupoService.povoarPerimetros(ctx);
                    }
                }
            }
        } catch (Exception e) {}
    }

    @Processo(autor = "Marcos André",
            data = "04/02/2018",
            descricao = "processo ignorado",
            motivacao = "Correção em um erro de merge que causou o pulo de um processo")
    public void migracaoVersao41(final String ctx) throws Exception {
    }

    @Processo(autor = "Luiz Felipe",
            data = "27/12/2017",
            descricao = "Adicionado Nivel no ScoreTreino",
            motivacao = "Adicionado Nivel no ScoreTreino",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao42(final String ctx) throws Exception {
        try {
        clienteSinteticoDao.executeNativeSQL(ctx, "update scoretreino SET nivelcrossfit = 1 where rx = false;");
        clienteSinteticoDao.executeNativeSQL(ctx, "update scoretreino SET nivelcrossfit = 3 where rx = true;");
        } catch (Exception e) {}
    }

    @Processo(autor = "Wendersonn Santiago",
            data = "16/02/2018",
            descricao = "Adicionado remove_acento_upper",
            motivacao = "Adicionado remove_acento_upper",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao43(final String ctx) throws Exception {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "CREATE OR REPLACE FUNCTION remove_acento_upper(input text)\n" +
                    "  RETURNS text AS\n" +
                    "$BODY$\n" +
                    "DECLARE\n" +
                    "  retorno text = '';\n" +
                    "BEGIN\t\n" +
                    "  retorno = upper(TRANSLATE(input,'áàãâäÁÀÃÂÄéèêëÉÈÊËíìîïÍÌÎÏóòõôöÓÒÕÔÖúùûüÚÙÛÜñÑçÇÿýÝ','aaaaaAAAAAeeeeEEEEiiiiIIIIoooooOOOOOuuuuUUUUnNcCyyY'));\n" +
                    "  retorno = regexp_replace(retorno, 'LL', 'L', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'FF', 'F', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'E', 'I', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'PH', 'F', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'W', 'U', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'NN', 'M', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'N', 'M', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'MM', 'M', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'Y', 'I', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'CH', 'X', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'TT', 'T', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'RR', 'R', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'SS', 'C', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'CC', 'T', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'TH', 'T', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'O', 'U', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'Z', 'S', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'HA', 'A', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'HE', 'E', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'HI', 'I', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'HO', 'U', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'HU', 'U', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'CK', 'C', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'K', 'C', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'SC', 'C', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'SH', 'X', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'Q', 'C', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'GUE', 'GE', 'gi');\n" +
                    "  retorno = regexp_replace(retorno, 'GUI', 'GI', 'gi');\n" +
                    "  retorno = regexp_replace(retorno,'[^%AEIYOUMRL ]B', 'B','gi');\n" +
                    "  retorno = regexp_replace(retorno,'[^%AEIYOUMRL ]C', 'C','gi');\n" +
                    "  retorno = regexp_replace(retorno,'[^%AEIYOUMRL ]D', 'D','gi');\n" +
                    "  retorno = regexp_replace(retorno,'[^%AEIYOUMRL ]F', 'F','gi');\n" +
                    "  retorno = regexp_replace(retorno,'[^%AEIYOUMRL ]G', 'G','gi');\n" +
                    "  retorno = regexp_replace(retorno,'[^%AEIYOUMRL ]H', 'H','gi');\n" +
                    "  retorno = regexp_replace(retorno,'[^%AEIYOUMRL ]J', 'J','gi');\n" +
                    "  retorno = regexp_replace(retorno,'[^%AEIYOUMRL ]M', 'M','gi');\n" +
                    "  retorno = regexp_replace(retorno,'[^%AEIYOUMRL ]P', 'P','gi');\n" +
                    "  retorno = regexp_replace(retorno,'[^%AEIYOUMRL ]Q', 'Q','gi');\n" +
                    "  retorno = regexp_replace(retorno,'[^%AEIYOUMRL ]S', 'S','gi');\n" +
                    "  retorno = regexp_replace(retorno,'[^%AEIYOUMRL ]T', 'T','gi');\n" +
                    "  retorno = regexp_replace(retorno,'[^%AEIYOUMRL ]V', 'V','gi');\n" +
                    "  RETURN retorno; END; $BODY$\n" +
                    "  LANGUAGE plpgsql IMMUTABLE STRICT\n" +
                    "  COST 500;");
            clienteSinteticoDao.executeNativeSQL(ctx, "update clientesintetico  set nomeconsulta = remove_acento_upper(nome);");
        } catch (Exception e) {}
    }

    @Processo(autor = "Joao Alcides",
            data = "23/02/2018",
            descricao = "Corrigir frase parq",
            motivacao = "Corrigir frase parq",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao44(final String ctx) throws Exception {
        try {
            String update = "update pergunta " +
                    " set descricao = 'Alguma vez seu médico disse que você possui algum problema cardíaco e recomendou que você só praticasse atividade física sob supervisão médica?'" +
                    " where descricao = \n" +
                    " 'Alguma vez seu médico disse que você possui algum problema cardíaco e recomendou que você só praticasse atividade física sob prescrição médica?'";
            clienteSinteticoDao.executeNativeSQL(ctx, update);
        } catch (Exception e) {}
    }

     @Processo(autor = "Arthur Bastos",
            data = "17/11/2017",
            descricao = "Povoa os dados da tabela TipoWod",
            motivacao = "Povoa os dados da tabela TipoWod")
    public void migracaoVersao45(final String ctx) throws Exception {
        try {
            for (TipoWodEnum tipoWod : TipoWodEnum.values()) {
                TipoWod tipoWodTabela = new TipoWod();
                tipoWodTabela.setNome(tipoWod.getNome());
                StringBuilder campoResultado = new StringBuilder("");
                for (String resultado : tipoWod.getCamposResultado()) {
                    if (campoResultado.toString().isEmpty()) {
                        campoResultado.append(resultado);
                    } else {
                        campoResultado.append(",").append(resultado);
                    }
                }
                if (tipoWodTabela.getNome().equals("For Time")) {
                    tipoWodTabela.setCamposResultado("rx,tempo,comentario");
                } else if (tipoWodTabela.getNome().equals("For Reps and Time")) {
                    tipoWodTabela.setCamposResultado("rx,repeticoes,tempo,comentario");
                } else if (tipoWodTabela.getNome().equals("For Weight")) {
                    tipoWodTabela.setCamposResultado("rx,peso,comentario");
                } else if (tipoWodTabela.getNome().equals("Amrap")) {
                    tipoWodTabela.setCamposResultado("rx,rounds,repeticoes,comentario");
                }
                tipoWodTabela.setOrderBy(tipoWod.getOrderBy());
                try {
                    tipoWodService.gravarTipoWod(ctx, tipoWodTabela);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            List<Wod> listaWod = wodService.obterTodos(ctx);

            for (Wod wod : listaWod) {
                if (wod.getTipoWod() != null) {
                    wod.setTipoWodTabela(tipoWodService.obterPorNome(ctx, wod.getTipoWod().getNome()));
                    wodService.alterar(ctx, wod);
                }
            }

            List<Benchmark> listaBenchmark = benchmarkService.obterTodos(ctx);

            for (Benchmark benchmark : listaBenchmark) {
                if (benchmark.getTipoWod() != null) {
                    benchmark.setTipoWodTabela(tipoWodService.obterPorNome(ctx, benchmark.getTipoWod().getNome()));
                    benchmarkService.alterar(ctx, benchmark);
                }
            }
        } catch (Exception e) {}
    }

    @Processo(autor = "Joao Alcides",
            data = "06/03/2018",
            descricao = "Aumentar o do campo XXXX da tabela YYYY",
            motivacao = "O campo XXXX pode possuir mais que 255 caracteres",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao46(final String ctx) throws Exception {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "ALTER TABLE avaliacaofisica ALTER recomendacoes TYPE text;");
        } catch (Exception e) {}
    }

    @Processo(autor = "Marcos Andre",
            data = "03/04/2018",
            descricao = "Ajusta as informações dos acessos e execuções",
            motivacao = "Corrigir inconsistências nos dados",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao47(final String ctx) throws Exception {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "update acessosexecucoesbi set ano = Extract('Year' From dia);");
            clienteSinteticoDao.executeNativeSQL(ctx, "update acessosexecucoesbi set mes = Extract('Month' From dia)");
            clienteSinteticoDao.executeNativeSQL(ctx, "delete from acessosexecucoesbi  where codigo in (\n" +
                    "select a.codigo from acessosexecucoesbi a inner join (\n" +
                    "select professor,dia,count(codigo), max(codigo) as codigomaior from acessosexecucoesbi group by 1,2 having count(codigo) > 1 order by 2 desc) as foo on foo.dia = a.dia and foo.professor = a.professor  and a.codigo <> foo.codigomaior)");
        } catch (Exception e) {}
    }

    @Processo(autor = "Luiz Felipe",
            data = "28/03/2018",
            descricao = "Alteração de coluna valor nas configurações do sistema",
            motivacao = "Alteração de coluna valor nas configurações do sistema",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao48(final String ctx) throws Exception {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "ALTER TABLE configuracaosistema ALTER COLUMN valor TYPE text;");
        } catch (Exception e) {}
    }

    @Processo(autor = "Luiz Felipe",
            data = "26/04/2018",
            descricao = "Alteração de ordem rx para nivelCrossfit",
            motivacao = "Alteração de ordem rx para nivelCrossfit",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao49(final String ctx) throws Exception {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "update tipowod  set  orderby = replace(orderby , 'rx', 'nivelCrossfit') , camposresultado = replace(camposresultado , 'rx', 'nivelCrossfit');");
        } catch (Exception e) {}
    }

    @Processo(autor = "Luiz Felipe",
            data = "01/05/2018",
            descricao = "Alterações Wod para professor",
            motivacao = "Alterações Wod para professor",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao50(final String ctx) throws Exception {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "update comentariowod set usuario_codigo = (select codigo from usuario where cliente_codigo = comentariowod.cliente_codigo);");
            clienteSinteticoDao.executeNativeSQL(ctx, "update comentariowodlike set usuario_codigo = (select codigo from usuario where cliente_codigo = comentariowodlike.cliente_codigo);");
            clienteSinteticoDao.executeNativeSQL(ctx, "update scoretreino set usuario_codigo = (select codigo from usuario where cliente_codigo = scoretreino.cliente_codigo);");
        } catch (Exception e) {}
    }

    @Processo(autor = "Joao Alcides",
            data = "26/06/2018",
            descricao = "Alterar default de configuração",
            motivacao = "Alterar default de configuração",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao51(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "update configuracaosistema set valor = 'TREINO(MODULO_TREINAR)' " +
                    "where valor <> 'CROSSFIT(HABILITAR_CROSSFIT)' and configuracao = "+ConfiguracoesEnum.MODULO_PRINCIPAL_APLICATIVO.ordinal());
        }catch (Exception e){
            //ignore
        }


    }

    @Processo(autor = "Luiz Felipe",
            data = "02/07/2018",
            descricao = "Alteração de coluna descricao na atividade",
            motivacao = "Alteração de coluna descricao na atividade ",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao52(final String ctx) throws Exception {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "alter table atividade alter column descricao TYPE text;");
        } catch (Exception e) {}
    }

    @Processo(autor = "Glauco Troncha Camargo",
            data = "18/07/2018",
            descricao = "Povoando a coluna empresa do StatusPessoa",
            motivacao = "Povoando a coluna empresa do StatusPessoa ",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao53(final String ctx) throws Exception {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "UPDATE statuspessoa SET empresa_codigo = (SELECT empresazw FROM usuario WHERE usuario.codigo = statuspessoa.usuario_codigo);");
        } catch (Exception e) {}
    }

    @Processo(autor = "Joao Alcides",
            data = "18/07/2018",
            descricao = "Povoa as permissões de metodo de execucao",
            motivacao = "Povoa as permissões de metodo de execucao")
    public void migracaoVersao54(final String ctx) throws Exception {
        try {
            List<Perfil> perfis = perfilDao.findAll(ctx);
            for (Perfil perfil : perfis) {
                Permissao permissaotipobench = new Permissao(RecursoEnum.TROCAR_METODO_EXECUCAO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfil);
                permissaoDao.insert(ctx, permissaotipobench);
            }
        } catch (Exception e) {}
    }

    @Processo(autor = "Wanderson Pimenta",
            data = "31/08/2018",
            descricao = "Povoa a email no cadastro de empresa quando treino independente ",
            motivacao = "Povoa a email no cadastro de empresa quando treino independente")
    public void migracaoVersao55(final String ctx) throws Exception {
            /*treino independente descontinuado*/
    }

    @Processo(autor = "Wanderson Pimenta",
            data = "18/09/2018",
            descricao = "Correcao de ordem de fichas ",
            motivacao = "Correcao de ordem de fichas")
    public void migracaoVersao56(final String ctx) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE atividadeficha ");
            sql.append("SET    ordem = new_ordem ");
            sql.append("from   ( ");
            sql.append("   SELECT   codigo, ");
            sql.append("            row_number () over (ORDER BY ordem, codigo) new_ordem ");
            sql.append("   FROM     atividadeficha ");
            sql.append("   WHERE    ficha_codigo IN( ");
            sql.append("               SELECT     a.ficha_codigo ");
            sql.append("               FROM       atividadeficha a ");
            sql.append("               inner join ( ");
            sql.append("                  SELECT   ficha_codigo, ");
            sql.append("                           ordem ");
            sql.append("                  FROM     atividadeficha ");
            sql.append("                  GROUP BY ficha_codigo, ");
            sql.append("                           ordem ");
            sql.append("                  HAVING   count(1) > 1 ) b ");
            sql.append("               ON         a.ficha_codigo = b.ficha_codigo ");
            sql.append("               AND        a.ordem = b.ordem ");
            sql.append("               GROUP BY   a.ficha_codigo)");
            sql.append("      ) correcao WHERE    atividadeficha.codigo = correcao.codigo;");

            clienteSinteticoDao.executeNativeSQL(ctx, sql.toString());
        } catch (Exception e) {}
    }

    @Processo(autor = "Wanderson Pimenta",
            data = "18/09/2018",
            descricao = "Correcao de coluna ativo na modalidade",
            motivacao = "Correcao de coluna ativo na modalidade ",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao57(final String ctx) throws Exception {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "alter table modalidade alter column ativo SET default true;");
            clienteSinteticoDao.executeNativeSQL(ctx, "update modalidade set ativo = true where ativo is null;");
        } catch (Exception e) {}
    }
    @Processo(autor = "Eder Cristian",
            data = "27/09/2018",
            descricao = "Criacao de index na tabela wod por empresa e dia",
            motivacao = "Melhorar o plano cartesiano",
            ignorarErro = true)
    public void migracaoVersao58(final String ctx) throws Exception {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "create index idx_empresa on wod using btree (empresa asc nulls first);");
            clienteSinteticoDao.executeNativeSQL(ctx, "create index idx_empresa_dia on wod using btree " +
                    "(empresa asc nulls first, dia asc nulls first);");
        } catch (Exception e) {}
    }

    @Processo(autor = "Wanderson Pimenta",
            data = "03/12/2018",
            descricao = "Correcao de dadis regados pelo nova montagem de treirno",
            motivacao = "dados estava fazendo retiraficha imprimir incorretamente",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao59(final String ctx) throws Exception {
        Transaction transaction = clienteSinteticoDao.getCurrentSession(ctx).beginTransaction();

        try {
            StringBuilder sb1 = new StringBuilder();
            sb1.append("UPDATE atividadeficha ")
                    .append("SET versao = coalesce(versao , 0) + 1 ")
                    .append("FROM (")
                    .append("	select atividadeficha_codigo from serie  ")
                    .append("	where  cargacomp = '0.0' or cargacomp = cast(carga as character varying) or repeticaocomp = '0' or repeticaocomp = cast(repeticao as character varying) ")
                    .append(") as B ")
                    .append("WHERE atividadeficha.codigo = B.atividadeficha_codigo; ");

            StringBuilder sb2 = new StringBuilder();
            sb2.append("update serie set cargacomp = null, repeticaocomp = null  ")
                    .append("where  cargacomp = '0.0' or cargacomp = cast(carga as character varying) or repeticaocomp = '0' or repeticaocomp = cast(repeticao as character varying) ");


            clienteSinteticoDao.executeNativeSQL(ctx, sb1.toString());
            clienteSinteticoDao.executeNativeSQL(ctx, sb2.toString());

            transaction.commit();
        }catch (Exception e){
            transaction.rollback();
        }



    }
    @Processo(autor = "Paulo Junior",
            data = "18/01/2019",
            descricao = "Inserindo grupos musculares predefinidos",
            motivacao = "grupos musculares com ponto muscular vinculado, esses dados vão está influenciando no mapa de calor, na tela de perfil do aluno")
    public void migracaoVersao60(final String ctx) {
        Transaction transaction = grupoMuscularDao.getCurrentSession(ctx).beginTransaction();

        try {
            List<GrupoMuscular> grupos = new ArrayList<>();
            GrupoMuscular grupo1 = new GrupoMuscular();
            grupo1.setNome("ABDOMEN");
            grupo1.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo1.getPerimetros().add(PerimetriaEnum.CIRCUNFERENCIA_ABDOMINAL);
            grupo1.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo1.getPontosMuscular().add(PontosMuscularEnum.ABDOMEN);
            grupos.add(grupo1);

            GrupoMuscular grupo2 = new GrupoMuscular();
            grupo2.setNome("ABDUTORES");
            grupo2.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo2.getPerimetros().add(PerimetriaEnum.COXA_DISTAL_DIR);
            grupo2.getPerimetros().add(PerimetriaEnum.COXA_DISTAL_ESQ);
            grupo2.getPerimetros().add(PerimetriaEnum.COXA_MEDIAL_DIR);
            grupo2.getPerimetros().add(PerimetriaEnum.COXA_MEDIAL_ESQ);
            grupo2.getPerimetros().add(PerimetriaEnum.COXA_PROXIMAL_DIR);
            grupo2.getPerimetros().add(PerimetriaEnum.COXA_PROXIMAL_ESQ);
            grupo2.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo2.getPontosMuscular().add(PontosMuscularEnum.COXA_PROXIMAL);
            grupo2.getPontosMuscular().add(PontosMuscularEnum.COXA_MEDIAL);
            grupo2.getPontosMuscular().add(PontosMuscularEnum.COXA_DISTAL);
            grupos.add(grupo2);

            GrupoMuscular grupo3 = new GrupoMuscular();
            grupo3.setNome("ADUTORES");
            grupo3.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo3.getPerimetros().add(PerimetriaEnum.COXA_PROXIMAL_ESQ);
            grupo3.getPerimetros().add(PerimetriaEnum.COXA_PROXIMAL_DIR);
            grupo3.getPerimetros().add(PerimetriaEnum.COXA_MEDIAL_ESQ);
            grupo3.getPerimetros().add(PerimetriaEnum.COXA_MEDIAL_DIR);
            grupo3.getPerimetros().add(PerimetriaEnum.COXA_DISTAL_DIR);
            grupo3.getPerimetros().add(PerimetriaEnum.COXA_DISTAL_ESQ);
            grupo3.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo3.getPontosMuscular().add(PontosMuscularEnum.COXA_PROXIMAL);
            grupo3.getPontosMuscular().add(PontosMuscularEnum.COXA_MEDIAL);
            grupo3.getPontosMuscular().add(PontosMuscularEnum.COXA_DISTAL);
            grupos.add(grupo3);

            GrupoMuscular grupo4 = new GrupoMuscular();
            grupo4.setNome("ANTEBRAÇO");
            grupo4.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo4.getPerimetros().add(PerimetriaEnum.ANTEBRACO_DIR);
            grupo4.getPerimetros().add(PerimetriaEnum.ANTEBRACO_ESQ);
            grupo4.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo4.getPontosMuscular().add(PontosMuscularEnum.ANTEBRACO);
            grupos.add(grupo4);

            GrupoMuscular grupo5 = new GrupoMuscular();
            grupo5.setNome("ANTERIOR DE COXA");
            grupo5.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo5.getPerimetros().add(PerimetriaEnum.COXA_PROXIMAL_ESQ);
            grupo5.getPerimetros().add(PerimetriaEnum.COXA_PROXIMAL_DIR);
            grupo5.getPerimetros().add(PerimetriaEnum.COXA_MEDIAL_ESQ);
            grupo5.getPerimetros().add(PerimetriaEnum.COXA_MEDIAL_DIR);
            grupo5.getPerimetros().add(PerimetriaEnum.COXA_DISTAL_DIR);
            grupo5.getPerimetros().add(PerimetriaEnum.COXA_DISTAL_ESQ);
            grupo5.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo5.getPontosMuscular().add(PontosMuscularEnum.COXA_PROXIMAL);
            grupo5.getPontosMuscular().add(PontosMuscularEnum.COXA_MEDIAL);
            grupo5.getPontosMuscular().add(PontosMuscularEnum.COXA_DISTAL);
            grupos.add(grupo5);

            GrupoMuscular grupo6 = new GrupoMuscular();
            grupo6.setNome("BÍCEPS");
            grupo6.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo6.getPerimetros().add(PerimetriaEnum.BRACO_CONTRAIDO_DIR);
            grupo6.getPerimetros().add(PerimetriaEnum.BRACO_CONTRAIDO_ESQ);
            grupo6.getPerimetros().add(PerimetriaEnum.BRACO_RELAXADO_DIR);
            grupo6.getPerimetros().add(PerimetriaEnum.BRACO_RELAXADO_ESQ);
            grupo6.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo6.getPontosMuscular().add(PontosMuscularEnum.BRACO);
            grupos.add(grupo6);

            GrupoMuscular grupo7 = new GrupoMuscular();
            grupo7.setNome("DELTÓIDE");
            grupo7.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo7.getPerimetros().add(PerimetriaEnum.OMBRO);
            grupo7.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo7.getPontosMuscular().add(PontosMuscularEnum.OMBRO);
            grupos.add(grupo7);

            GrupoMuscular grupo8 = new GrupoMuscular();
            grupo8.setNome("DORSAL");
            grupo8.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo8.getPerimetros().add(PerimetriaEnum.OMBRO);
            grupo8.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo8.getPontosMuscular().add(PontosMuscularEnum.OMBRO);
            grupos.add(grupo8);

            GrupoMuscular grupo9 = new GrupoMuscular();
            grupo9.setNome("GLÚTEO");
            grupo9.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo9.getPerimetros().add(PerimetriaEnum.GLUTEO);
            grupo9.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo9.getPontosMuscular().add(PontosMuscularEnum.GLUTEO);
            grupos.add(grupo9);

            GrupoMuscular grupo10 = new GrupoMuscular();
            grupo10.setNome("LOMBAR");
            grupo10.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo10.getPerimetros().add(PerimetriaEnum.CIRCUNFERENCIA_ABDOMINAL);
            grupo10.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo10.getPontosMuscular().add(PontosMuscularEnum.ABDOMEN);
            grupos.add(grupo10);

            GrupoMuscular grupo11 = new GrupoMuscular();
            grupo11.setNome("MANGUITO");
            grupo11.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo11.getPerimetros().add(PerimetriaEnum.OMBRO);
            grupo11.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo11.getPontosMuscular().add(PontosMuscularEnum.OMBRO);
            grupos.add(grupo11);

            GrupoMuscular grupo12 = new GrupoMuscular();
            grupo12.setNome("PANTURRILHA");
            grupo12.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo12.getPerimetros().add(PerimetriaEnum.PANTURRILHA_DIR);
            grupo12.getPerimetros().add(PerimetriaEnum.PANTURRILHA_ESQ);
            grupo12.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo12.getPontosMuscular().add(PontosMuscularEnum.PANTURRILHA);
            grupos.add(grupo12);

            GrupoMuscular grupo13 = new GrupoMuscular();
            grupo13.setNome("PEITORAL");
            grupo13.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo13.getPerimetros().add(PerimetriaEnum.TORAX);
            grupo13.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo13.getPontosMuscular().add(PontosMuscularEnum.TORAX);
            grupos.add(grupo13);

            GrupoMuscular grupo14 = new GrupoMuscular();
            grupo14.setNome("POSTERIOR DE COXA");
            grupo14.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo14.getPerimetros().add(PerimetriaEnum.COXA_PROXIMAL_ESQ);
            grupo14.getPerimetros().add(PerimetriaEnum.COXA_PROXIMAL_DIR);
            grupo14.getPerimetros().add(PerimetriaEnum.COXA_MEDIAL_ESQ);
            grupo14.getPerimetros().add(PerimetriaEnum.COXA_MEDIAL_DIR);
            grupo14.getPerimetros().add(PerimetriaEnum.COXA_DISTAL_DIR);
            grupo14.getPerimetros().add(PerimetriaEnum.COXA_DISTAL_ESQ);
            grupo14.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo14.getPontosMuscular().add(PontosMuscularEnum.COXA_PROXIMAL);
            grupo14.getPontosMuscular().add(PontosMuscularEnum.COXA_MEDIAL);
            grupo14.getPontosMuscular().add(PontosMuscularEnum.COXA_DISTAL);
            grupos.add(grupo14);

            GrupoMuscular grupo15 = new GrupoMuscular();
            grupo15.setNome("QUADRICEPS");
            grupo15.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo15.getPerimetros().add(PerimetriaEnum.COXA_PROXIMAL_ESQ);
            grupo15.getPerimetros().add(PerimetriaEnum.COXA_PROXIMAL_DIR);
            grupo15.getPerimetros().add(PerimetriaEnum.COXA_MEDIAL_ESQ);
            grupo15.getPerimetros().add(PerimetriaEnum.COXA_MEDIAL_DIR);
            grupo15.getPerimetros().add(PerimetriaEnum.COXA_DISTAL_DIR);
            grupo15.getPerimetros().add(PerimetriaEnum.COXA_DISTAL_ESQ);
            grupo15.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo15.getPontosMuscular().add(PontosMuscularEnum.COXA_PROXIMAL);
            grupo15.getPontosMuscular().add(PontosMuscularEnum.COXA_MEDIAL);
            grupo15.getPontosMuscular().add(PontosMuscularEnum.COXA_DISTAL);
            grupos.add(grupo15);

            GrupoMuscular grupo16 = new GrupoMuscular();
            grupo16.setNome("TIBIAL ANTERIOR");
            grupo16.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo16.getPerimetros().add(PerimetriaEnum.PANTURRILHA_ESQ);
            grupo16.getPerimetros().add(PerimetriaEnum.PANTURRILHA_DIR);
            grupo16.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo16.getPontosMuscular().add(PontosMuscularEnum.PANTURRILHA);
            grupos.add(grupo16);

            GrupoMuscular grupo17 = new GrupoMuscular();
            grupo17.setNome("TRAPÉZIO");
            grupo17.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo17.getPontosMuscular().add(PontosMuscularEnum.COSTAS);
            grupos.add(grupo17);

            GrupoMuscular grupo18 = new GrupoMuscular();
            grupo18.setNome("TRICEPS");
            grupo18.setPerimetros(new HashSet<PerimetriaEnum>());
            grupo18.getPerimetros().add(PerimetriaEnum.BRACO_CONTRAIDO_DIR);
            grupo18.getPerimetros().add(PerimetriaEnum.BRACO_CONTRAIDO_ESQ);
            grupo18.getPerimetros().add(PerimetriaEnum.BRACO_RELAXADO_DIR);
            grupo18.getPerimetros().add(PerimetriaEnum.BRACO_RELAXADO_ESQ);
            grupo18.setPontosMuscular(new HashSet<PontosMuscularEnum>());
            grupo18.getPontosMuscular().add(PontosMuscularEnum.BRACO);
            grupos.add(grupo18);

            StringBuilder hql = new StringBuilder();
            hql.append("SELECT obj FROM GrupoMuscular obj WHERE upper(obj.nome) = :nome");
            HashMap<String, Object> param = new HashMap<>();
            for (GrupoMuscular grupoMuscular : grupos) {
                param.put("nome", grupoMuscular.getNome());
                GrupoMuscular grupoMusc = grupoService.obterObjetoPorParam(ctx, hql.toString(), param);

                if (grupoMusc == null || UteisValidacao.emptyNumber(grupoMusc.getCodigo())) {
                    grupoService.inserir(ctx, grupoMuscular);
                } else {
                    grupoMuscular.setCodigo(grupoMusc.getCodigo());
                    grupoService.alterar(ctx, grupoMuscular);
                }
            }

        } catch (Exception e) {
            transaction.rollback();
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "08/03/2019",
            descricao = "Povoar TAB index da configuração de perimetria",
            motivacao = "Povoar TAB index da configuração de perimetria",
            ignorarErro = true)
    public void migracaoVersao61(final String ctx) throws Exception {
        try {
        List<ConfiguracaoPerimetro> lista = configuracaoSistemaService.obterCfgsPerimetro(ctx);
        configuracaoSistemaService.preencherPerimetriasAutomatico(true, lista);
        configuracaoSistemaService.gravarNovaOrdemPerimetros(ctx, lista);
        } catch (Exception e) {}
    }

    @Processo(autor = "Paulo Junior",
            data = "16/04/2019",
            descricao = "Atualizando a coluna valor da configuração do sistema",
            motivacao = "Coluna valor está sendo atualizada, por motivo que a configuração do token na olympia precisa ser do tipo Text")
    public void migracaoVersao62(final String ctx) {
        //Removido porque era referente a tabela de auditoria
    }

    @Processo(autor = "Marcos",
            data = "17/08/2019",
            descricao = "importando alunos ZW",
            motivacao = "deixar a migracao de dados transparente")
    public void migracaoVersao63(final String ctx) {
        try {
            if(br.com.pacto.controller.json.base.SuperControle.independente(ctx)) {
                List<ClienteSintetico> clientes = clienteSinteticoDao.findAll(ctx);
                for (ClienteSintetico cliente: clientes){
                    clientePesquisaService.inserirDandoFlush(ctx, cliente);
                }
            } else {
                final String url = Aplicacao.getPropOAMD(ctx, Aplicacao.urlZillyonWebInteg);
                IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
                String retorno = integracaoWS.buscarClientesPesquisaTW(url, ctx);
                JSONArray jsonArray = new JSONArray(retorno);
                if (jsonArray.length() > 0) {
                    for (int i = 0; i < jsonArray.length(); i++) {
                        JSONObject jObj = jsonArray.optJSONObject(i);
                        clientePesquisaService.inserirDandoFlush(ctx, jObj);
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
    }


    @Processo(autor = "Marcelo Anderson",
            data = "06/01/2020",
            descricao = "Inserir default de configuração",
            motivacao = "Inserir default de configuração",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao64(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "update configuracaosistema set valor = 'TREINO' " +
                    "where configuracao = " + ConfiguracoesEnum.LINK_APP_EMAIL.ordinal());
        }catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Paulo Junior",
            data = "07/02/2020",
            descricao = "Popular resultado de perdeuPeso, perdeuPercentualGordura e ganhouMassaMagra, do clienteSintetico",
            motivacao = "Melhorar carregamento do BI da Avaliação Fisica",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao65(final String ctx) throws Exception {
        try {
            // Refatoração pois chamada ao avaliacaoFisicaService.atualizarResultadoEvolucaoAluno() ESTAVA dando erro não tratado
            // e também getCurrentSession(ctx).clear() estava afetando a atualização da versão do banco.
            List<ClienteSintetico> clientes = clienteSinteticoDao.findAll(ctx);
            for (ClienteSintetico cliente : clientes) {
                try {
                    StringBuilder sql = new StringBuilder("SELECT obj FROM AvaliacaoFisica obj WHERE obj.cliente.codigo = :cliente and obj.dataAvaliacao is not null ");
                    sql.append(" ORDER by obj.dataAvaliacao DESC, obj.codigo DESC ");
                    HashMap params = new HashMap<String, Object>();
                    params.put("cliente", cliente.getCodigo());
                    List<AvaliacaoFisica> avaliacoesCliente = avaliacaoFisicaDao.findByParam(ctx, sql.toString(), params);

                    if(!avaliacoesCliente.isEmpty()){
                        AvaliacaoFisica primeiraAvaliacao = avaliacoesCliente.get(avaliacoesCliente.size() - 1);
                        AvaliacaoFisica atualAvaliacao = avaliacoesCliente.get(0);
                        cliente.setPesoInicio(primeiraAvaliacao.getPeso());
                        cliente.setPesoAtual(atualAvaliacao.getPeso());
                        cliente.setPercentualGorduraInicio(primeiraAvaliacao.getPercentualGordura());
                        cliente.setPercentualGorduraAtual(atualAvaliacao.getPercentualGordura());
                        if(UteisValidacao.emptyNumber(primeiraAvaliacao.getMassaMagra()) && !UteisValidacao.emptyNumber(primeiraAvaliacao.getPercentualMassaMagra())){
                            cliente.setMassaMagraInicio((primeiraAvaliacao.getPeso()*primeiraAvaliacao.getPercentualMassaMagra())/100.0);
                        } else {
                            cliente.setMassaMagraInicio(primeiraAvaliacao.getMassaMagra());
                        }
                        if(UteisValidacao.emptyNumber(atualAvaliacao.getMassaMagra()) && !UteisValidacao.emptyNumber(atualAvaliacao.getPercentualMassaMagra())){
                            cliente.setMassaMagraAtual((atualAvaliacao.getPeso()*atualAvaliacao.getPercentualMassaMagra())/100.0);
                        } else {
                            cliente.setMassaMagraAtual(atualAvaliacao.getMassaMagra());
                        }
                        clienteSinteticoService.alterar(ctx, cliente);
                    }
                } catch (Exception e) {}
            }
        } catch (Exception e) {}
    }

    @Processo(autor = "Paulo Junior",
            data = "14/02/2020",
            descricao = "Atualizando foreignKey da serie com atividadeFicha, colocando action on delete cascade",
            motivacao = "Estava dando erro de constraint, na hr de escluir atividadeFicha, mesmo se tivesse excluido a serie antes",
            ignorarErro = false,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao66(final String ctx) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT tc.constraint_name FROM information_schema.table_constraints AS tc");
            sql.append(" JOIN information_schema.key_column_usage AS kcu");
            sql.append(" ON tc.constraint_name = kcu.constraint_name");
            sql.append(" AND tc.table_schema = kcu.table_schema");
            sql.append(" JOIN information_schema.constraint_column_usage AS ccu");
            sql.append(" ON ccu.constraint_name = tc.constraint_name");
            sql.append(" AND ccu.table_schema = tc.table_schema");
            sql.append(" WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name= \'serie\'");

            List<String> foreignKeys = serieDao.listOfObjects(ctx, sql.toString());
            for (String foreignKey : foreignKeys) {
                String sqlRemoveConstraints = "ALTER TABLE serie DROP CONSTRAINT " + foreignKey;

                serieDao.executeNativeSQL(ctx, sqlRemoveConstraints);
            }

            String sqlCreateConstraints = "ALTER TABLE serie ADD CONSTRAINT fk_serie_atividadeficha_codigo FOREIGN KEY (atividadeficha_codigo) REFERENCES atividadeficha (codigo) ON UPDATE NO ACTION ON DELETE CASCADE";

            serieDao.executeNativeSQL(ctx, sqlCreateConstraints);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Processo(autor = "Paulo Junior",
            data = "13/03/2020",
            descricao = "Verificando alunos que não tem usuário cadastrado",
            motivacao = "Quando aluno não tem usuário cadastrado, não consegui fazer sincronização dos dados alterados no ZW")
    public void migracaoVersao67(final String ctx) {
        try {
            IntegracaoCadastrosWSConsumer integracao = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);
            String urlZW = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
            List<ClienteSintetico> clientes = clienteSinteticoDao.consultarAlunosSemUsuario(ctx);
            for (ClienteSintetico cliente : clientes) {
                integracao.verificarUsuarioMovel(urlZW, ctx, cliente.getCodigoCliente(), cliente.getMatricula());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Processo(autor = "Paulo Junior",
            data = "13/03/2020",
            descricao = "Atualizando professor da carteira, no programa treino virgente, de um aluno em especifico",
            motivacao = "Não consegui simular o fluxo que causou o problema, do ticket AKATSUKI-610")
    public void migracaoVersao68(final String ctx) {
        try {
            // Não mais necessário estar executando para bancos novos
            // programaService.atualizarProfessorCarteiraProgramaVirgente("7051f727bbcf404a5dfbf48ada9b88d2", "20558");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "26/03/2020",
            descricao = "Corrigir problema na atv ficha",
            motivacao = "Corrigir problema na atv ficha",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao69(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "update AtividadeFicha set metodoexecucao = null where metodoexecucao > 57 ");
        }catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "02/07/2020",
            descricao = "Corrigir problema no programa aluno",
            motivacao = "Corrigir problema no programa aluno",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao70(final String ctx) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("update programatreino set professormontou_codigo =");
            sql.append(" (select codigo from professorsintetico where nome ilike '%pacto%' limit 1)");
            sql.append(" where professormontou_codigo is null");
            configuracaoDao.executeNativeSQL(ctx, sql.toString());
        }catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Joao ALcides",
            data = "02/07/2020",
            descricao = "gerar config de disponibilidade",
            motivacao = "gerar config de disponibilidade",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao71(final String ctx) throws Exception {
        try {
//            disponibilidadeService.gerarConfigDisponibilidadePorAgendamentos(ctx);
            //migrado para o 73
        }catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Marcelo Anderson",
            data = "05/08/2020",
            descricao = "Popular checkbox aluno marcar aula pelo app.",
            motivacao = "Corrigir problema no programa aluno",
            ignorarErro = true,
            permitirMultiplasExecucoes = false)
    public void migracaoVersao72(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "UPDATE public.tipoevento SET permitirapp=false;");
        }catch (Exception e){
            //ignore
        }
    }


    @Processo(autor = "Joao ALcides",
            data = "10/08/2020",
            descricao = "gerar config de disponibilidade",
            motivacao = "gerar config de disponibilidade",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao73(final String ctx) throws Exception {
        try {
            disponibilidadeService.gerarConfigDisponibilidadePorAgendamentos(ctx);
        }catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "16/09/2020",
            descricao = "adicionar extensão ignorar acentuação bdmusc",
            motivacao = "consultas que ignoram acentuação",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao74(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "CREATE EXTENSION unaccent;");
        }catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Alisson Melo",
            data = "16/11/2020",
            descricao = "Setar true para primeiro acesso configuração SMS_NOTIFICACAO",
            motivacao = "Manter valor true padrão antes do usuário salvar a config desejada.",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao75(final String ctx) throws Exception {
        try {
            ConfiguracaoSistema css = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.SMS_NOTIFICACAO);
            css.getValorAsBoolean();
        }catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Gabriel Andrade",
            data = "18/02/2021",
            descricao = "alimentar tabela ClientePesquisa",
            motivacao = "Clientes do treino independente não são encontratos na barra de pesquisa.")
    public void migracaoVersao76(final String ctx) {
        Transaction transaction = configuracaoDao.getCurrentSession(ctx).beginTransaction();
        try {
            if(br.com.pacto.controller.json.base.SuperControle.independente(ctx)) {
                List<ClienteSintetico> clientes = clienteSinteticoDao.consultarClientesNaoInclusoClientePesquisa(ctx);
                for (ClienteSintetico cliente : clientes) {
                    clientePesquisaService.inserirDandoFlush(ctx, cliente);
                }
            }
        } catch (Exception e) {
            transaction.rollback();
        }
    }

    @Processo(autor = "Valeria Gouveia",
            data = "02/03/2021",
            descricao = "Aumentar o do campo logbalanca da tabela Serie ",
            motivacao = "O campo complemento pode possuir mais que 255 caracteres",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao77(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE avaliacaofisica ALTER logbalanca TYPE text;");
        } catch (Exception e) {}
    }

    @Processo(autor = "Murillo Lima",
            data = "22/04/2021",
            descricao = "Criar a coluna partetecnicaskill na tabela wod",
            motivacao = "Criar a coluna partetecnicaskill para receber os dados do campo partetecnicaskill",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao78(final String ctx) throws Exception {
        try {
            wodDao.executeNativeSQL(ctx, "ALTER TABLE wod ALTER COLUMN partetecnicaskill TYPE text;");
        }catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Murillo Lima",
            data = "22/04/2021",
            descricao = "Preencher a coluna partetecnicaskill da tabela wod com os dados da coluna aquecimento da tabela wod",
            motivacao = "Migrar os dados para a coluna correta",
            permitirMultiplasExecucoes = true)
    public void migracaoVersao79(final String ctx) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("WITH subquery AS (select aquecimento, codigo from wod order by codigo asc) ");
            sql.append(" UPDATE wod ");
            sql.append(" SET partetecnicaskill=subquery.aquecimento ");
            sql.append(" FROM subquery ");
            sql.append(" WHERE wod.codigo = subquery.codigo;");
            wodDao.executeNativeSQL(ctx, sql.toString());

            sql = new StringBuilder();
            sql.append(" SELECT codigo FROM wod;");
            try (ResultSet statement = wodDao.createStatement(ctx, sql.toString())) {
                Integer ultimoUpdate = null;
                while (statement.next()) {
                    ultimoUpdate = statement.getInt("codigo");
                    sql = new StringBuilder();
                    sql.append("UPDATE wod SET aquecimento = '' WHERE wod.codigo = ").append(ultimoUpdate).append(";");
                    wodDao.executeNativeSQL(ctx, sql.toString());
                }
            }
        }catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Alisson Melo",
            data = "30/04/2021",
            descricao = "Atualizar status de reagendado para aguardando confirmação",
            motivacao = "Recurso não será mais utilizado no treino 3.0",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao80(final String ctx) throws Exception {
        try {
            agendamentoDao.executeNativeSQL(ctx,"UPDATE agendamento SET status = 0 WHERE status = 5;");
        } catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Eder Cristian",
            data = "01/06/2021",
            descricao = "Criacao de index na tabela cliente por empresa",
            motivacao = "Melhorar consultas",
            ignorarErro = true)
    public void migracaoVersao81(final String ctx) throws Exception {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "create index idx_clientesintetico_empresa on clientesintetico using btree (empresa asc nulls first);");
        } catch (Exception ex) {}
    }

    @Processo(autor = "Valeria Gouveia",
            data = "15/06/2021",
            descricao = "Drop Constraint nome",
            motivacao = "Permitir cadastros de aparelho com mesmo nome tambem para cross",
            ignorarErro = true)
    public void migracaoVersao82(final String ctx) throws Exception {
        try {
            clienteSinteticoDao.executeNativeSQL(ctx, "ALTER TABLE aparelho DROP CONSTRAINT aparelho_nome_key;");
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }

    @Processo(autor = "Valeria Gouveia",
            data = "09/07/2021",
            descricao = "Permissao para tornar treino prédefinido perfil coordenador e professor",
            motivacao = "feature KZN-994")
    public void migracaoVersao83(final String ctx) throws Exception {
        try {
            Perfil perfilProfessor = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_PROFESSOR);
            Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_COORDENADOR);

            Permissao permissao1 = new Permissao(RecursoEnum.TORNAR_PROGRAMA_PREDEFINIDO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor);
            permissaoDao.insert(ctx, permissao1);
            Permissao permissao2 = new Permissao(RecursoEnum.TORNAR_PROGRAMA_PREDEFINIDO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador);
            permissaoDao.insert(ctx, permissao2);
        } catch (Exception ex) {}
    }

    @Processo(autor = "Joao Alcides",
            data = "06/09/2021",
            descricao = "Importar cadastros não importados devido a erro no zw",
            motivacao = "Importar cadastros não importados devido a erro no zw")
    public void migracaoVersao84(final String ctx) throws Exception {
        try {
            sincronizacaoEmpresaService.sincronizarPorProblemaZW(ctx);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "20/09/2021",
            descricao = "Atualizar complemento de nome da ficha com nome editado no treino antigo",
            motivacao = "Recurso não será mais utilizado no treino 3.0",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao85(final String ctx) throws Exception {
        try {
            agendamentoDao.executeNativeSQL(ctx,"update atividadeficha\n" +
                    "    set complementonomeatividade = nome\n" +
                    "    where nomeatividadealteradomanualmente\n" +
                    "    and (complementonomeatividade is null or complementonomeatividade = '')");
        } catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Douglas Souza",
            data = "11/11/2021",
            descricao = "Atualizar dados de avaliacao fisica no cliente sintetico",
            motivacao = "Corrigir numero do BI da avaliacao fisica",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao86(final String ctx) throws Exception {
        try {
            agendamentoDao.executeNativeSQL(ctx,"update clientesintetico c \n" +
                    "set massamagrainicio = (select massamagra from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao, codigo limit 1),\n" +
                    "massamagraatual = (select massamagra from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao desc, codigo desc limit 1),\n" +
                    "pesoinicio = (select peso from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao, codigo limit 1),\n" +
                    "pesoatual = (select peso from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao desc, codigo desc limit 1),\n" +
                    "percentualgordurainicio = (select percentualgordura from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao, codigo limit 1),\n" +
                    "percentualgorduraatual = (select percentualgordura from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao desc, codigo desc limit 1)\n" +
                    "where exists (select codigo from avaliacaofisica a where cliente_codigo = c.codigo limit 1);");
        } catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Waller Maciel",
            data = "20/12/2021",
            descricao = "Re-executar processo que possuia erro de sintaxe, acusado pelos logs dos testes automatizados",
            motivacao = "Re-executar processo que possuia erro de sintaxe, acusado pelos logs dos testes automatizados",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao87(final String ctx) throws Exception {
        try {
            migracaoVersao78(ctx);
        } catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "29/03/2022",
            descricao = "Povoar config de bloqueio de aula",
            motivacao = "Iniciar valor default em nova config",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao88(final String ctx) throws Exception {
        try {
            ConfiguracaoSistema css = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_MESMO_AMBIENTE);
            css.getValorAsBoolean();
        }catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Murillo Lima",
            data = "03/05/2022",
            descricao = "Alterar a coluna observacao na tabela agendamento",
            motivacao = "Alterar a coluna observacao para receber mais do que 255 caracteres",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao89(final String ctx) throws Exception {
        try {
            agendamentoDao.executeNativeSQL(ctx, "ALTER TABLE agendamento ALTER observacao TYPE text;");
        }catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Murillo Roseno",
            data = "19/05/2022",
            descricao = "Re-executar processo para popular resultado de perdeuPeso, perdeuPercentualGordura e ganhouMassaMagra, do clienteSintetico",
            motivacao = "Re-executar processo para melhorar carregamento do BI da Avaliação Fisica",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao90(final String ctx) throws Exception {
        try {
            migracaoVersao65(ctx);
        } catch (Exception e){
            //ignore
        }
    }


    @Processo(autor = "Douglas Souza",
            data = "11/08/2022",
            descricao = "Atualizar dados de avaliacao fisica no cliente sintetico",
            motivacao = "Corrigir numero do BI da avaliacao fisica",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao91(final String ctx) throws Exception {
        try {
            agendamentoDao.executeNativeSQL(ctx,"update clientesintetico c \n" +
                    "set massamagrainicio = (select massamagra from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao, codigo limit 1),\n" +
                    "massamagraatual = (select massamagra from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao desc, codigo desc limit 1),\n" +
                    "pesoinicio = (select peso from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao, codigo limit 1),\n" +
                    "pesoatual = (select peso from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao desc, codigo desc limit 1),\n" +
                    "percentualgordurainicio = (select percentualgordura from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao, codigo limit 1),\n" +
                    "percentualgorduraatual = (select percentualgordura from avaliacaofisica a2 where cliente_codigo = c.codigo order by dataavaliacao desc, codigo desc limit 1)\n" +
                    "where exists (select codigo from avaliacaofisica a where cliente_codigo = c.codigo limit 1);");
        } catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "13/08/2022",
            descricao = "Povoar tabela com indicadores",
            motivacao = "Poder ordenar em banco a listagem de indicadores",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao92(final String ctx) throws Exception {
        try {
            DashboardBIService dashboardBIService = UtilContext.getBean(DashboardBIService.class);
            dashboardBIService.reloadIndicadores(ctx);
        } catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Alisson Melo",
              data = "16/08/2022",
              descricao = "Povoar config de habilitar fila de espera",
              motivacao = "Iniciar valor default em nova config",
              ignorarErro = true,
              permitirMultiplasExecucoes = true)
    public void migracaoVersao93(final String ctx) throws Exception {
        try {
            ConfiguracaoSistema css = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_FILA_ESPERA);
            css.getValorAsBoolean();
        } catch (Exception e) {
            //ignore
        }
    }

    @Processo(autor = "Murillo Lima",
            data = "29/08/2022",
            descricao = "Adicionar a coluna serieRealizada na tabela serie",
            motivacao = "Adicionar a coluna serieRealizada na tabela serie para receber valor booleanos",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao94(final String ctx) throws Exception {
        try {
            serieDao.executeNativeSQL(ctx, "ALTER TABLE serie ADD serierealizada bool NULL;");
        }catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "13/09/2022",
            descricao = "Add coluna ativo em ConfiguracaoRankingProfessores",
            motivacao = "Inativar registros em  ConfiguracaoRankingProfessores",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao95(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE ConfiguracaoRankingProfessores ADD ativo boolean;");
        }catch (Exception e){
            //ignore
        }

        try {
            configuracaoDao.executeNativeSQL(ctx, "update ConfiguracaoRankingProfessores set ativo = true;");
        }catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Lucas da Silva Rezende",
            data = "12/09/2022",
            descricao = "Remoção de coluna e fk criada na tabela usuario para a tabela usuarioemail",
            motivacao = "Remoção de coluna e fk criada na tabela usuario para a tabela usuarioemail",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao96(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx, "ALTER TABLE usuario DROP CONSTRAINT usuario_email_key;");
            usuarioDao.executeNativeSQL(ctx,"ALTER TABLE usuario DROP COLUMN usuarioemail_codigo;");
        }catch (Exception e){
            //ignore
        }
    }

    @Processo(autor = "Lucas da Silva Rezende",
            data = "11/11/2022",
            descricao = "Alteração do da coluna usuário para não nula",
            motivacao = "Coluna usuário da tabela UsuarioEmail está permitido valores nulos, alterando para não ser permitido",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao97(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx,"DELETE FROM usuarioemail WHERE usuario IS NULL;");
            usuarioDao.executeNativeSQL(ctx,"ALTER TABLE usuarioemail ALTER COLUMN usuario SET NOT NULL;");
        }catch (Exception e){
            //ignore
        }
    }
    @Processo(autor = "Joao Alcides",
            data = "06/12/2022",
            descricao = "Criar coluna usuario em bancos onde o hibernate não criou",
            motivacao = "Coluna usuário da tabela UsuarioEmail está permitido valores nulos, alterando para não ser permitido",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao98(final String ctx) throws Exception {
        try {
            try (ResultSet rs = usuarioDao.createStatement(ctx, " SELECT * FROM information_schema.columns " +
                    " WHERE table_schema = 'public' AND table_name = 'usuarioemail'\n" +
                    " and column_name = 'usuario';")) {
                if (rs.next()) {
                    return;
                }
            }
            usuarioDao.executeNativeSQL(ctx,"DELETE FROM usuarioemail;");
            usuarioDao.executeNativeSQL(ctx,"alter table usuarioemail add column usuario int not null;\n" +
                    "alter table usuarioemail add \n" +
                    "CONSTRAINT usuarioemail_usuario FOREIGN KEY (usuario)\n" +
                    "      REFERENCES public.usuario (codigo) MATCH SIMPLE\n" +
                    "      ON UPDATE NO ACTION ON DELETE NO action;");

        }catch (Exception e){
            e.printStackTrace();
        }
    }


    @Processo(autor = "Lucas da Silva Rezende",
            data = "01/12/2022",
            descricao = "Permissão para desvincular usuário da academia.",
            motivacao = "Hotfix MI-322")
    public void migracaoVersao99(final String ctx) throws Exception {
        try {
            Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_COORDENADOR);

            Permissao permissao1 = new Permissao(RecursoEnum.DESVINCULAR_USUARIO, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador);
            permissaoDao.insert(ctx, permissao1);
        } catch (Exception e) {}
    }

    @Processo(autor = "Joao Alcides",
            data = "23/01/2023",
            descricao = "Add coluna terminoprogramavigente em clientesintetico",
            motivacao = "Otimizar consultas",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao100(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE clientesintetico ADD COLUMN terminoUltimoPrograma timestamp without time zone;");
        } catch (Exception e) {}
        try {
            configuracaoDao.executeNativeSQL(ctx, "update clientesintetico c \n" +
                    "set terminoUltimoPrograma = (select max(dataterminoprevisto) from programatreino where cliente_codigo = c.codigo)");
        } catch (Exception e) {}
    }

	@Processo(autor = "Kaio Sanchez",
            data = "06/04/2023",
            descricao = "Corrige nrtreinosrealizados na tabela programatreino",
            motivacao = "Corrigir dados incorretos salvos anteriormente",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao101(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "update programatreino ptr\n" +
                    "set nrtreinosrealizados = (select nrtreinos\n" +
                    "                           from programatreino pt\n" +
                    "                                    join programatreinoandamento\n" +
                    "                                         on pt.codigo = programatreinoandamento.programa_codigo\n" +
                    "                           where nrtreinos <> pt.nrtreinosrealizados and ptr.codigo = pt.codigo);");
        } catch (Exception e) {}
    }

    @Processo(autor = "Ivan Alves",
            data = "26/04/2023",
            descricao = "Add tabela para vincular as respostas do PARQ feitas através do totem(zw-auto)",
            motivacao = "Corrigir respostas PARQ feitas no totem",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao102(final String ctx) throws Exception {
        try {
            StringBuilder s = new StringBuilder();
            s.append("INSERT INTO respostaclienteparq(dataresposta, urlassinatura, cliente_codigo, usuario_codigo) \n");
            s.append("SELECT '2023-04-26 12:00:00.000', (SELECT urlassinatura FROM avaliacaofisica av WHERE length(urlassinatura) > 0 AND av.cliente_codigo = rc.cliente_codigo LIMIT 1), rc.cliente_codigo, null \n");
            s.append("FROM respostacliente rc where rc.respostaclienteparq_codigo is null \n");
            s.append("GROUP BY rc.cliente_codigo; \n");
            configuracaoDao.executeNativeSQL(ctx, s.toString());
        } catch (Exception e) {}
        try {
            StringBuilder s = new StringBuilder();
            s.append("UPDATE respostacliente rc SET respostaclienteparq_codigo = rcp.codigo \n");
            s.append("FROM respostaclienteparq rcp \n");
            s.append("WHERE rcp.cliente_codigo = rc.cliente_codigo \n");
            s.append("AND rc.respostaclienteparq_codigo is null; \n");
            configuracaoDao.executeNativeSQL(ctx, s.toString());
        } catch (Exception e) {}
    }

    @Processo(autor = "Ivan Alves",
            data = "12/05/2023",
            descricao = "Remover respostas parq adicionadas pela versão 102",
            motivacao = "Ocasionou alguns erros em uma base e cliente vai refazer o parq para ficar na data que ele preencher",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao103(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "UPDATE respostacliente SET respostaclienteparq_codigo = null WHERE respostaclienteparq_codigo IN (SELECT codigo FROM respostaclienteparq WHERE dataresposta = '2023-04-26 12:00:00');");
            configuracaoDao.executeNativeSQL(ctx, "DELETE FROM respostaclienteparq rcp WHERE not exists (SELECT respostaclienteparq_codigo FROM respostacliente rc WHERE rc.respostaclienteparq_codigo = rcp.codigo);");
        } catch (Exception e) {}
    }

    @Processo(autor = "Alisson Melo",
            data = "04/09/2023",
            descricao = "Povoar configgympass que não estiverem com campos preenchidos",
            motivacao = "Registro existe porém está com os campos vazios",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao104(final String ctx) throws Exception {
        try {
            Map<Integer, String> mapEmpresaZW = new HashMap<>();
            try(Connection conZW = conexaoZWService.conexaoZw(ctx)){
                try (ResultSet rsEmpresaZw = ConexaoZWServiceImpl.criarConsulta("select codigo, codigogympass from empresa e", conZW)) {
                    while (rsEmpresaZw.next()) {
                        mapEmpresaZW.put(rsEmpresaZw.getInt("codigo"), rsEmpresaZw.getString("codigogympass"));
                    }
                }
            }


            if (mapEmpresaZW.size() > 0) {
                Usuario user = usuarioService.consultarPorUserName(ctx, "PACTOBR");
                try (ResultSet rsConfigTR = configGymPassDao.createStatement(ctx,
                        "select c.codigo, c.codigogympass, c.nome as nomeEmpresaConfig, e.codzw, e.nome as nomeEmpresa " +
                                "from configgympass c inner join empresa e on e.codigo = c.empresa_codigo")) {
                    while (rsConfigTR.next()) {
                        if (UteisValidacao.emptyString(rsConfigTR.getString("codigogympass")) && UteisValidacao.emptyString(rsConfigTR.getString("nomeEmpresaConfig"))) {
                            StringBuilder update = new StringBuilder();
                            update.append(" update configgympass set ");
                            update.append(" codigogympass = '").append(mapEmpresaZW.get(rsConfigTR.getInt("codzw"))).append("',");
                            update.append(" usargympassbooking = true").append(",");
                            update.append(" nome = '").append(rsConfigTR.getString("nomeEmpresa")).append("',");
                            update.append(" datalancamento = '").append(new Date()).append("',");
                            update.append(" usuariolancou_codigo = ").append(user != null ? user.getCodigo() : null);
                            update.append(" where codigo = ").append(rsConfigTR.getInt("codigo"));
                            configGymPassDao.executeNativeSQL(ctx, update.toString());
                        }
                    }
                }
            }
        } catch (Exception e) { }
    }

    @Processo(autor = "Kaio Sanchez",
            data = "13/09/2023",
            descricao = "Cria colunas emRevisaoProfessor e isGeradoPorIA",
            motivacao = "APP-4059",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao105(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE programatreino\n" +
                    " ADD COLUMN isGeradoPorIA BOOLEAN DEFAULT FALSE NOT NULL, \n" +
                    " ADD COLUMN emRevisaoProfessor BOOLEAN DEFAULT FALSE NOT NULL; ");
        } catch (Exception e) {}
    }

    @Processo(autor = "Anderson Rosa",
            data = "25/09/2023",
            descricao = "Update corrigir data entrou no horario da turma",
            motivacao = "E3-305",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao106(final String ctx) throws Exception {
        try {
            try(Connection conZW = conexaoZWService.conexaoZw(ctx)){
                try (ResultSet rs = usuarioDao.createStatement(ctx, "select\n" +
                        "\tchavePrimaria as codigoHorario\n" +
                        "from\n" +
                        "\tlog\n" +
                        "where\n" +
                        "\tdescricao <> 'INCLUSÃO DE HORARIO TURMA'\n" +
                        "\tand chaveprimaria in (\n" +
                        "\tselect\n" +
                        "\t\tchaveprimaria as codigoHorario\n" +
                        "\tfrom\n" +
                        "\t\tlog\n" +
                        "\twhere\n" +
                        "\t\tdescricao ilike '%ALTERAÇÃO DE HORARIO TURMA%')\n" +
                        "\t\tand dataalteracao > '2023-08-31'\n" +
                        "\t\tgroup by codigoHorario")) {

                    while (rs.next()) {
                        if (isNotBlank(rs.getString("codigoHorario"))) {
                            try (PreparedStatement insert = conZW.prepareStatement(
                                    "update horarioturma set dataentrouturma = null where codigo = " + rs.getString("codigoHorario"))) {
                                insert.execute();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "04/10/2023",
            descricao = "Processo 106 nao rodou havia erro no sql",
            motivacao = "E3-305",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao107(final String ctx) throws Exception {
        migracaoVersao106(ctx);
    }


    @Processo(autor = "Kaio Sanchez",
            data = "28/07/2023",
            descricao = "Cria tabela de avaliação de professor",
            motivacao = "APP-3679",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao108(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "CREATE TABLE avaliacaoprofessor\n" +
                    "(\n" +
                    "    codigo       SERIAL PRIMARY KEY,\n" +
                    "    dataregistro TIMESTAMP NOT NULL DEFAULT NOW(),\n" +
                    "    dataupdate   TIMESTAMP,\n" +
                    "    usuario      INTEGER REFERENCES usuario (codigo),\n" +
                    "    professor    INTEGER REFERENCES professorsintetico (codigo),\n" +
                    "    notaavaliada INTEGER,\n" +
                    "    empresa      INTEGER\n" +
                    ");");
        } catch (Exception e) {}
    }

    @Processo(autor = "Alisson Melo",
              data = "25/10/2023",
              descricao = "Corrigir código ItemAvaliacaoFisicaEnum lançado incorreto após versão do dia 23/10/2023",
              motivacao = "SD2-5865",
              ignorarErro = true,
              permitirMultiplasExecucoes = true)
    public void migracaoVersao109(final String ctx) throws Exception {
        try {
            itemAvaliacaoFisicaDao.executeNativeSQL(ctx, "UPDATE itemavaliacaofisica\n" +
                    "SET item = \n" +
                    "    CASE\n" +
                    "        WHEN datalancamento between '2023-10-23 23:59:59' and '2023-10-25 23:59:59' AND item IN (3, 4, 5, 6) THEN item + 12\n" +
                    "        WHEN datalancamento between '2023-10-23 23:59:59' and '2023-10-25 23:59:59' AND item IN (7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18) THEN item - 4\n" +
                    "        ELSE item\n" +
                    "    END\n" +
                    "WHERE datalancamento between '2023-10-23 23:59:59' and '2023-10-25 23:59:59';"
            );
        } catch (Exception e) { }
    }

    private void removendoChaveEstrangeiraUsuarioEntidadesTreino(String ctx, Connection conTW) throws Exception {
        //Removendo relacionamento com a tabela usuario.
        try {
            String alterTW = "ALTER TABLE linkpredefinido DROP CONSTRAINT fkfe8305d3281c1186";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave estrangeira linkpredefinido chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
        try {
            String alterTW = "ALTER TABLE geracaorankingprofessores DROP CONSTRAINT fkcb82396bf588cb7a";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave estrangeira geracaorankingprofessores chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
        try {
            String alterTW = "ALTER TABLE professorsubstituido DROP CONSTRAINT fkc35bfdc87ea4611";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave estrangeira professorsubstituido chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
        try {
            String alterTW = "ALTER TABLE configuracaosistemausuario DROP CONSTRAINT fka6abda5b281c1186";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave estrangeira configuracaosistemausuario chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
        try {
            String alterTW = "ALTER TABLE agendamento DROP CONSTRAINT fk8f03ca2510c3259d";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave estrangeira agendamento chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
        try {
            String alterTW = "ALTER TABLE agendamento DROP CONSTRAINT fk8f03ca25db2214b6";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave estrangeira agendamento2 chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
        try {
            String alterTW = "ALTER TABLE liberacaocheckin DROP CONSTRAINT fk7b17394b40d3b0ca";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave estrangeira liberacaocheckin chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
        try {
            String alterTW = "ALTER TABLE clienteobservacao DROP CONSTRAINT fk6b91e933281c1186";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave estrangeira clienteobservacao chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
        try {
            String alterTW = "ALTER TABLE alunofavorito DROP CONSTRAINT fk5e730791281c1186";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave estrangeira alunofavorito chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
        try {
            String alterTW = "ALTER TABLE configgympass DROP CONSTRAINT fk2dfe8aaadb2214b6";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave estrangeira configgympass chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
        try {
            String alterTW = "ALTER TABLE auladiaexclusao DROP CONSTRAINT fkfc2bf1bb281c1186";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave estrangeira auladiaexclusao chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
        try {
            String alterTW = "ALTER TABLE auladiaexcecao DROP CONSTRAINT fk292740e9281c1186";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave estrangeira auladiaexcecao chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
        try {
            String alterTW = "ALTER TABLE avaliacaofisica DROP CONSTRAINT fk4d36ae50f588cb7a";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave estrangeira avaliacaofisica chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
        try {
            String alterTW = "ALTER TABLE itemavaliacaofisica DROP CONSTRAINT fke30712bdf588cb7a";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave estrangeira itemavaliacaofisica chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
        try {
            String alterTW = "ALTER TABLE pesoosseo DROP CONSTRAINT fkd69f3348f588cb7a";
            try (Statement stm = conTW.createStatement()) {
                stm.execute(alterTW);
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro remover chave pesoosseo itemavaliacaofisica chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
    }

    private void updateColunasNecessariasUsuarioZwPovoandoComDadosUsuarioTw(String ctx, Connection conZw) throws Exception {
        //Por precaução caso o processo que cria essas colunas abaixo nao tenha rodado no zw.
        try {
            String alterZW = "alter table usuario add column perfilTw integer";
            try (Statement stm = conZw.createStatement()) {
                stm.execute(alterZW);
            }
        } catch (Exception ignore) {
        }
        try {
            String alterZW = "alter table usuario add column statusTw integer";
            try (Statement stm = conZw.createStatement()) {
                stm.execute(alterZW);
            }
        } catch (Exception ignore) {
        }
        try {
            String alterZW = "alter table usuario add column tipoTw integer";
            try (Statement stm = conZw.createStatement()) {
                stm.execute(alterZW);
            }
        } catch (Exception ignore) {
        }

        //Povoando colunas novas no usuario do zw com os dados da coluna usuario do treino.
        List users = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw, Obj.Perfil_codigo, Obj.status, Obj.tipo FROM Usuario Obj" +
                " WHERE (Obj.usuariozw <> 0 or Obj.usuariozw <> null)", new HashMap<>(), 0, 0);
        JSONArray array = new JSONArray(users);
        for (int i = 0; i < array.length(); i++) {
            JSONArray objs = (JSONArray) array.get(i);
            try {
                String updateZW = "update usuario set perfilTw = " + objs.get(1) + " where codigo = " + objs.get(0);
                try (Statement stm = conZw.createStatement()) {
                    stm.execute(updateZW);
                }
            } catch (Exception ex) {
                Uteis.logarDebug(String.format("Erro update coluna perfilTw chave %s and instance: %s",
                        ctx,
                        Aplicacao.instanceName));
                System.out.println(ex.getMessage());
                ex.printStackTrace();
                throw new Exception(ex);
            }
            try {
                String updateZW = "update usuario set statusTw = " + objs.get(2) + " where codigo = " + objs.get(0);
                try (Statement stm = conZw.createStatement()) {
                    stm.execute(updateZW);
                }
            } catch (Exception ex) {
                Uteis.logarDebug(String.format("Erro update coluna statusTw chave %s and instance: %s",
                        ctx,
                        Aplicacao.instanceName));
                System.out.println(ex.getMessage());
                ex.printStackTrace();
                throw new Exception(ex);
            }
            try {
                String updateZW = "update usuario set tipoTw = " + objs.get(3) + " where codigo = " + objs.get(0);
                try (Statement stm = conZw.createStatement()) {
                    stm.execute(updateZW);
                }
            } catch (Exception ex) {
                Uteis.logarDebug(String.format("Erro update coluna tipoTw chave %s and instance: %s",
                        ctx,
                        Aplicacao.instanceName));
                System.out.println(ex.getMessage());
                ex.printStackTrace();
                throw new Exception(ex);
            }
        }
    }

    private void migrarColunasComCodigoUsuarioTreinoParaCodigoUsuarioZW(String ctx, Connection conTW) throws Exception {
        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para avaliacao fisica.
        List avaliacoes = avaliacaoFisicaDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.responsavellancamento_codigo FROM AvaliacaoFisica Obj", new HashMap<>(), 0, 0);
        JSONArray array2 = new JSONArray(avaliacoes);
        for (int i = 0; i < array2.length(); i++) {
            JSONArray objs = (JSONArray) array2.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update avaliacaofisica set responsavellancamento_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario avaliacaofisica chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }

        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para item avaliacao fisica.
        List itemAvaliacoes = itemAvaliacaoFisicaDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.responsavellancamento_codigo FROM ItemAvaliacaoFisica Obj", new HashMap<>(), 0, 0);
        JSONArray array3 = new JSONArray(itemAvaliacoes);
        for (int i = 0; i < array3.length(); i++) {
            JSONArray objs = (JSONArray) array3.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update itemavaliacaofisica set responsavellancamento_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario itemavaliacaofisica chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }

        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para peso osseo.
        List pesoosseos = pesoOsseoDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.responsavellancamento_codigo FROM PesoOsseo Obj", new HashMap<>(), 0, 0);
        JSONArray array4 = new JSONArray(pesoosseos);
        for (int i = 0; i < array4.length(); i++) {
            JSONArray objs = (JSONArray) array4.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update pesoosseo set responsavellancamento_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario pesoosseo chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }
        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para aulas excluidas.
        List aulasexclusao = aulaDiaExclusaoDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.usuario_codigo FROM AulaDiaExclusao Obj", new HashMap<>(), 0, 0);
        JSONArray array5 = new JSONArray(aulasexclusao);
        for (int i = 0; i < array5.length(); i++) {
            JSONArray objs = (JSONArray) array5.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update auladiaexclusao set usuario_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario auladiaexclusao chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }
        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para aulas excecao.
        List aulasexcecao = aulaDiaExcecaoDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.usuario_codigo FROM AulaDiaExcecao Obj", new HashMap<>(), 0, 0);
        JSONArray array6 = new JSONArray(aulasexcecao);
        for (int i = 0; i < array6.length(); i++) {
            JSONArray objs = (JSONArray) array6.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update auladiaexcecao set usuario_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario auladiaexcecao chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }
        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para configs gym pass.
        List configsgympas = configGymPassDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.usuariolancou_codigo FROM ConfigGymPass Obj", new HashMap<>(), 0, 0);
        JSONArray array7 = new JSONArray(configsgympas);
        for (int i = 0; i < array7.length(); i++) {
            JSONArray objs = (JSONArray) array7.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update configgympass set usuariolancou_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario configgympass chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }
        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para aluno favorito.
        List alunosFavoritos = alunoFavoritoDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.usuario_codigo FROM AlunoFavorito Obj", new HashMap<>(), 0, 0);
        JSONArray array8 = new JSONArray(alunosFavoritos);
        for (int i = 0; i < array8.length(); i++) {
            JSONArray objs = (JSONArray) array8.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update alunofavorito set usuario_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario alunofavorito chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }
        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para aluno favorito.
        List clienteObservacoes = clienteObservacaoDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.usuario_codigo FROM ClienteObservacao Obj", new HashMap<>(), 0, 0);
        JSONArray array9 = new JSONArray(clienteObservacoes);
        for (int i = 0; i < array9.length(); i++) {
            JSONArray objs = (JSONArray) array9.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update clienteobservacao set usuario_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario clienteobservacao chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }
        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para liberacao chekin.
        List liberacaoChekin = liberacaoCheckInDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.usuarioliberou_codigo FROM LiberacaoCheckin Obj", new HashMap<>(), 0, 0);
        JSONArray array10 = new JSONArray(liberacaoChekin);
        for (int i = 0; i < array10.length(); i++) {
            JSONArray objs = (JSONArray) array10.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update liberacaocheckin set usuarioliberou_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario liberacaocheckin chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }
        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para agendamento lancou.
        List agendamentosLancou = agendamentoDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.usuariolancou_codigo FROM Agendamento Obj", new HashMap<>(), 0, 0);
        JSONArray array11 = new JSONArray(agendamentosLancou);
        for (int i = 0; i < array11.length(); i++) {
            JSONArray objs = (JSONArray) array11.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update agendamento set usuariolancou_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario agendamento chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }
        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para agendamento ult alterou.
        List agendamentosAlterou = agendamentoDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.usuarioultalteracao_codigo FROM Agendamento Obj", new HashMap<>(), 0, 0);
        JSONArray array12 = new JSONArray(agendamentosAlterou);
        for (int i = 0; i < array12.length(); i++) {
            JSONArray objs = (JSONArray) array12.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update agendamento set usuarioultalteracao_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario agendamento2 chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }
        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para configuracao sistema usuario.
        List configuracoessistemausuario = configuracaoSistemaUsuarioDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.usuario_codigo FROM ConfiguracaoSistemaUsuario Obj", new HashMap<>(), 0, 0);
        JSONArray array13 = new JSONArray(configuracoessistemausuario);
        for (int i = 0; i < array13.length(); i++) {
            JSONArray objs = (JSONArray) array13.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update configuracaosistemausuario set usuario_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario configuracaosistemausuario chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }
        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para professor substituido.
        List professorSubstituidos = professorSubstituidoDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.usuariosubstituiu_codigo FROM ProfessorSubstituido Obj", new HashMap<>(), 0, 0);
        JSONArray array14 = new JSONArray(professorSubstituidos);
        for (int i = 0; i < array14.length(); i++) {
            JSONArray objs = (JSONArray) array14.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update professorsubstituido set usuariosubstituiu_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario professorsubstituido chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }
        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para geracao ranking professores.
        List geracaorankprofessores = geracaoRankingProfessoresDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.responsavellancamento_codigo FROM GeracaoRankingProfessores Obj", new HashMap<>(), 0, 0);
        JSONArray array15 = new JSONArray(geracaorankprofessores);
        for (int i = 0; i < array15.length(); i++) {
            JSONArray objs = (JSONArray) array15.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update geracaorankingprofessores set responsavellancamento_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario geracaorankingprofessores chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }
        //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para links predefinidos.
        List linkspredefinidos = linkPredefinidoDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.usuario_codigo FROM LinkPredefinido Obj", new HashMap<>(), 0, 0);
        JSONArray array16 = new JSONArray(linkspredefinidos);
        for (int i = 0; i < array16.length(); i++) {
            JSONArray objs = (JSONArray) array16.get(i);
            List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
            if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && codigosUsuario.get(0) != "")) {
                try {
                    String updateTW = "update linkpredefinido set usuario_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                    try (Statement stm = conTW.createStatement()) {
                        stm.execute(updateTW);
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update usuario linkpredefinido chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }
    }

    private void criarUsuarioZwUsuariosApenasTreino(String ctx, Connection conTw, Connection conZw) {
        try {
            List usuario = usuarioDao.findByParamNative(ctx, "SELECT P.nome, Obj.username, Obj.senha, P.codigocolaborador, Obj.codigo FROM Usuario Obj  inner join professorsintetico P on P.codigo = Obj.professor_codigo WHERE (Obj.usuariozw is null and Obj.cliente_codigo is null)", new HashMap<>(), 0, 0);
            JSONArray array = new JSONArray(usuario);
            for (int i = 0; i < array.length(); i++) {
                Integer codigoUsuarioZw = null;
                JSONArray obj = (JSONArray) array.get(i);
                try {
                    try (PreparedStatement insert = conZw.prepareStatement("insert into usuario (administrador, nome, username, senha, colaborador, tipousuario) values (false, " +
                            "'" + obj.get(0) + "', '" + obj.get(1) + "', '" + obj.get(2) + "', " + obj.get(3) + ", 'CE') RETURNING codigo;")) {
                        try (ResultSet rs = insert.executeQuery()) {
                            if (rs.next()) {
                                codigoUsuarioZw = rs.getInt("codigo");
                            }
                        }
                    }
                    try (Statement stm = conTw.createStatement()) {
                        stm.execute("update usuario set usuariozw = " + codigoUsuarioZw + " where codigo = " + obj.get(4));
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro criar usuario era apenas treino chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                    throw new Exception(ex);
                }
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro criar usuario era apenas treino chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "05/06/2023",
            descricao = "E3-6",
            motivacao = "E3-6",
            permitirMultiplasExecucoes = true)
    public void migracaoVersao110(final String ctx) throws Exception {
        try {
            String nomeProcesso = "unificarUsuarioTreinoZw";
            boolean podeAtualizar = true;
            try {
                ValidadorVersao v = validadorVersaoDao.findObjectByAttributes(ctx, new String[]{"nomeProcesso"}, new Object[]{nomeProcesso}, "codigo");
                podeAtualizar = !v.getSucesso();
            } catch (Exception ignore) {
            }

            if (!br.com.pacto.controller.json.base.SuperControle.independente(ctx) && podeAtualizar) {
                Uteis.logarDebug(String.format("Iniciei unificacao usuario treino para zw chave: %s and instance: %s " + Uteis.getDataAplicandoFormatacao(new Date(), "dd/MM/yyyy HH:mm:ss"),
                        ctx,
                        Aplicacao.instanceName));
                try (Connection conZw = conexaoZWService.conexaoZw(ctx)) {
                    Connection conTW = usuarioDao.getConnection(ctx);
                    Uteis.logarDebug(String.format("Unificacao usuario treino para zw criarUsuarioZwUsuariosApenasTreino chave: %s and instance: %s " + Uteis.getDataAplicandoFormatacao(new Date(), "dd/MM/yyyy HH:mm:ss"),
                            ctx,
                            Aplicacao.instanceName));
                    criarUsuarioZwUsuariosApenasTreino(ctx, conTW, conZw);
                    Uteis.logarDebug(String.format("Unificacao usuario treino para zw removendoChaveEstrangeiraUsuarioEntidadesTreino chave: %s and instance: %s " + Uteis.getDataAplicandoFormatacao(new Date(), "dd/MM/yyyy HH:mm:ss"),
                            ctx,
                            Aplicacao.instanceName));
                    removendoChaveEstrangeiraUsuarioEntidadesTreino(ctx, conTW);
                    Uteis.logarDebug(String.format("Unificacao usuario treino para zw criarColunasNecessariasUsuarioZwPovoandoComDadosUsuarioTw chave: %s and instance: %s " + Uteis.getDataAplicandoFormatacao(new Date(), "dd/MM/yyyy HH:mm:ss"),
                            ctx,
                            Aplicacao.instanceName));
                    updateColunasNecessariasUsuarioZwPovoandoComDadosUsuarioTw(ctx, conZw);
                    Uteis.logarDebug(String.format("Unificacao usuario treino para zw migrarColunasComCodigoUsuarioTreinoParaCodigoUsuarioZW chave: %s and instance: %s " + Uteis.getDataAplicandoFormatacao(new Date(), "dd/MM/yyyy HH:mm:ss"),
                            ctx,
                            Aplicacao.instanceName));
                    migrarColunasComCodigoUsuarioTreinoParaCodigoUsuarioZW(ctx, conTW);
                    validadorVersaoDao.insert(ctx, new ValidadorVersao(true, nomeProcesso));
                    Uteis.logarDebug(String.format("Terminei unificacao usuario treino para zw chave: %s and instance: %s " + Uteis.getDataAplicandoFormatacao(new Date(), "dd/MM/yyyy HH:mm:ss"),
                            ctx,
                            Aplicacao.instanceName));
                    migrarColunasComCodigoUsuarioTreinoParaCodigoUsuarioZW(ctx, conTW);
                }
            }
        } catch (Exception e) {
            Uteis.logarDebug(String.format("Erro na unificacao usuario treino para zw chave: %s and instance: %s " + Uteis.getDataAplicandoFormatacao(new Date(), "dd/MM/yyyy HH:mm:ss"),
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(e.getMessage());
            e.getStackTrace();
            throw new Exception(e);
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "28/11/2023",
            descricao = "Cria tabela de atividades relacionadas",
            motivacao = "normalizar banco de dados dos clientes pois o auto ddl não funcionou corretamente",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao111(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE public.atividade ADD ultimaedicao int8 NULL;");
        } catch (Exception e) {}
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE public.atividade ADD nomeoriginalia varchar(255) NULL;");
        } catch (Exception e) {}

        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE public.atividade ADD editadopor varchar(255) NULL;");
        } catch (Exception e) {}
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE public.atividade ADD idia varchar(255) NULL;");
        } catch (Exception e) {}

        try {
            configuracaoDao.executeNativeSQL(ctx, "CREATE TABLE public.atividadealternativa (\n" +
                    "                codigo serial4 NOT NULL,\n" +
                    "                atividadealternativa int4 NULL,\n" +
                    "                atividade_codigo int4 NULL,\n" +
                    "                CONSTRAINT atividadealternativa_pkey PRIMARY KEY (codigo)\n" +
                    "        );");

            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE public.atividadealternativa ADD CONSTRAINT fk8ef44c424f9ebaff FOREIGN KEY (atividade_codigo) REFERENCES public.atividade(codigo);");
        } catch (Exception e) {}



    }

    @Processo(autor = "Murillo Roseno",
            data = "04/12/2023",
            descricao = "Remove os itens de avaliação física duplicados no cálculo de rml",
            motivacao = "M2-256",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao112(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx,  "DELETE FROM itemavaliacaofisica AS iaf\n" +
                    "WHERE iaf.item IN (15, 16, 17, 18, 1, 2)\n" +
                    "AND EXISTS (\n" +
                    "    SELECT 1\n" +
                    "    FROM itemavaliacaofisica AS iaf_inner\n" +
                    "    WHERE iaf_inner.avaliacaofisica_codigo = iaf.avaliacaofisica_codigo  \n" +
                    "    AND iaf_inner.item = iaf.item\n" +
                    "    GROUP BY iaf_inner.avaliacaofisica_codigo, iaf_inner.item\n" +
                    "    HAVING MAX(iaf_inner.codigo) <> iaf.codigo\n" +
                    ") \n" +
                    "AND iaf.datalancamento >= '2023-11-20';");
        } catch (Exception e) {}
    }

    @Processo(autor = "Anderson Rosa",
            data = "05/06/2023",
            descricao = "E3-6",
            motivacao = "E3-6",
            permitirMultiplasExecucoes = true)
    public void migracaoVersao113(final String ctx) throws Exception {
        migracaoVersao110(ctx);
    }

    @Processo(autor = "Anderson Rosa",
            data = "08/12/2023",
            descricao = "E3-00",
            motivacao = "Povoador da coluna nova para identificar tipo do perfil",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao114(final String ctx) throws Exception {
        //processo removido, não se faz mas necessario.
    }

    @Processo(autor = "Anderson Rosa",
            data = "11/12/2023",
            descricao = "E03-71",
            motivacao = "Ajustar responsavel avaliacao fisica",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao115(final String ctx) throws Exception {
        try {
            Connection conTW = avaliacaoFisicaDao.getConnection(ctx);
            List avaliacoes = avaliacaoFisicaDao.findByParamNative(ctx, "SELECT Obj.codigo FROM AvaliacaoFisica Obj", new HashMap<>(), 0, 0);
            JSONArray array2 = new JSONArray(avaliacoes);
            for (int i = 0; i < array2.length(); i++) {
                Integer avaliacaoCodigo = (Integer) array2.get(i);
                List userName = logDao.findByParamNative(ctx, "SELECT Obj.responsavelalteracao FROM Log Obj WHERE Obj.chaveprimaria = '" + avaliacaoCodigo + "' and Obj.descricao = 'INCLUSÃO DE AVALIAÇÃO FÍSICA'", new HashMap<>(), 1, 0);
                List codUser = null;
                if (isNull(userName) || userName.isEmpty()) {
                    codUser = logDao.findByParamNative(ctx, "SELECT responsavellancamento_codigo FROM Avaliacaofisica_aud Obj WHERE Obj.revtype = 0 and Obj.codigo = " + avaliacaoCodigo, new HashMap<>(), 1, 0);
                    userName = codUser;
                }
                if (!isNull(userName) && !userName.isEmpty() && (userName.get(0) != null && userName.get(0) != "")) {
                    List usuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE " + ((!isNull(codUser) && !codUser.isEmpty()) ? "Obj.codigo = " + codUser.get(0) : "Obj.username = '" + userName.get(0) + "'"), new HashMap<>(), 1, 0);
                    if (!isNull(usuario) && !usuario.isEmpty() && (usuario.get(0) != null && usuario.get(0) != "")) {
                        try {
                            String updateTW = "update avaliacaofisica set responsavellancamento_codigo = " + usuario.get(0) + " where codigo = " + avaliacaoCodigo;
                            try (Statement stm = conTW.createStatement()) {
                                stm.execute(updateTW);
                            }
                        } catch (Exception ex) {
                            Uteis.logarDebug(String.format("Erro update processo ajustar responsavel avaliacaofisica chave %s and instance: %s",
                                    ctx,
                                    Aplicacao.instanceName));
                            System.out.println(ex.getMessage());
                            ex.printStackTrace();
                        }
                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro processo ajustar responsavel avaliacaofisica chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
    }

    @Processo(autor = "Luiz Felipe",
            data = "11/12/2023",
            descricao = "Setar true para configuração DESATIVAR_TELA_ALUNO_TREINO",
            motivacao = "Setar true para configuração DESATIVAR_TELA_ALUNO_TREINO",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao116(final String ctx) throws Exception {
        try {
            if (!br.com.pacto.controller.json.base.SuperControle.independente(ctx)) {
                configuracaoDao.executeNativeSQL(ctx, "update configuracaosistema set valor = 'true' where configuracao = " + ConfiguracoesEnum.DESATIVAR_TELA_ALUNO_TREINO.ordinal());
            }
        } catch (Exception e) {
            //ignore
        }
    }

    @Processo(autor = "Anderson Rosa",
            data = "13/12/2023",
            descricao = "E03-77",
            motivacao = "Remover Chave Estrangeira Tabela Usuario na RespostaParq",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao117(final String ctx) throws Exception {
        try {
            String nomeProcesso = "migrarCodigosParqUsuarioTreinoZw";
            boolean podeAtualizar = true;
            try {
                ValidadorVersao v = validadorVersaoDao.findObjectByAttributes(ctx, new String[]{"nomeProcesso"}, new Object[]{nomeProcesso}, "codigo");
                podeAtualizar = !v.getSucesso();
            } catch (Exception ignore) {
            }

            if (!br.com.pacto.controller.json.base.SuperControle.independente(ctx) && podeAtualizar) {
                //Removendo relacionamento com a tabela usuario.
                try (Connection conTW = parQDao.getConnection(ctx)) {
                    try {
                        String alterTW = "ALTER TABLE respostaclienteparq DROP CONSTRAINT fk1e080ae9281c1186";
                        try (Statement stm = conTW.createStatement()) {
                            stm.execute(alterTW);
                        }
                    } catch (Exception ex) {
                        Uteis.logarDebug(String.format("Erro remover chave estrangeira respostaclienteparq chave %s and instance: %s",
                                ctx,
                                Aplicacao.instanceName));
                        System.out.println(ex.getMessage());
                        ex.printStackTrace();
                    }

                    //Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para parq.
                    List respostasParq = parQDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.usuario_codigo FROM respostaclienteparq Obj", new HashMap<>(), 0, 0);
                    JSONArray array2 = new JSONArray(respostasParq);
                    for (int i = 0; i < array2.length(); i++) {
                        JSONArray objs = (JSONArray) array2.get(i);
                        List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
                        if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && isNotBlank(codigosUsuario.get(0).toString()))) {
                            try {
                                String updateTW = "update respostaclienteparq set usuario_codigo = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                                try (Statement stm = conTW.createStatement()) {
                                    stm.execute(updateTW);
                                }
                            } catch (Exception ex) {
                                Uteis.logarDebug(String.format("Erro update usuario respostaclienteparq chave %s and instance: %s",
                                        ctx,
                                        Aplicacao.instanceName));
                                System.out.println(ex.getMessage());
                                ex.printStackTrace();
                            }
                        }
                    }
                    validadorVersaoDao.insert(ctx, new ValidadorVersao(true, nomeProcesso, Calendario.hoje()));
                }
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro processo ajustar responsavel avaliacaofisica chave %s and instance: %s",
                    ctx,
                    Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "27/12/2023",
            descricao = "Ajustar responsavel avaliacaofisica",
            motivacao = "Ajustar responsavel avaliacaofisica",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao118(final String ctx) throws Exception {
        try {
            if (!br.com.pacto.controller.json.base.SuperControle.independente(ctx)) {
                avaliacaoFisicaService.processoAjustarResponsavel(ctx);
            }
        } catch (Exception e) {
            //ignore
        }
    }

    @Processo(autor = "Anna Carolina",
            data = "31/01/2024",
            descricao = "M2-567",
            motivacao = "Incluir dois novos tipos de WOD como padrão",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao119(final String ctx) throws Exception {
        try {
            TipoWod emomExiste = tipoWodService.obterPorNome(ctx, "EMOM");
            if (emomExiste == null) {
                TipoWod emomTipoWod = new TipoWod();
                emomTipoWod.setNome("EMOM");
                emomTipoWod.setCamposResultado("rx,repeticoes,tempo,comentario");
                emomTipoWod.setOrderBy(null);
                tipoWodService.gravarTipoWod(ctx, emomTipoWod);
            }

            TipoWod tabataExiste = tipoWodService.obterPorNome(ctx, "Tabata");
            if (tabataExiste == null) {
                TipoWod tabataTipoWod = new TipoWod();
                tabataTipoWod.setNome("Tabata");
                tabataTipoWod.setCamposResultado("rx,repeticoes,tempo,comentario");
                tabataTipoWod.setOrderBy(null);
                tipoWodService.gravarTipoWod(ctx, tabataTipoWod);
            }

         } catch (Exception e) {
            // Ignorar exceções
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "13/03/2024",
            descricao = "Cria coluna idOperacaoEmMassa",
            motivacao = "E2-813",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao120(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE programatreino\n" +
                    " ADD COLUMN idOperacaoEmMassa TEXT; ");
        } catch (Exception e) {}
    }

    @Processo(autor = "Anna Carolina",
            data = "26/03/2024",
            descricao = "Permissao para visualizar a modal de média de avaliação dos alunos - APP",
            motivacao = "feature TW-2")
    public void migracaoVersao121(final String ctx) throws Exception {
        try {
            Perfil perfilProfessor = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_PROFESSOR);
            Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_COORDENADOR);

            Permissao permissao1 = new Permissao(RecursoEnum.AVALIACAO_MEDIA_ALUNOS, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor);
            permissaoDao.insert(ctx, permissao1);
            Permissao permissao2 = new Permissao(RecursoEnum.AVALIACAO_MEDIA_ALUNOS, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador);
            permissaoDao.insert(ctx, permissao2);
        } catch (Exception e) {}
    }

    @Processo(autor = "Kaio Ribeiro Sanchez",
            data = "27/03/2024",
            descricao = "Cria tabela de avaliação do wod",
            motivacao = "APPS-584",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao122(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "create table wodavaliacao\n" +
                    "(\n" +
                    "    codigo           serial primary key,\n" +
                    "    wod              integer not null references wod,\n" +
                    "    usuario          integer not null references usuario,\n" +
                    "    empresa          integer not null references empresa,\n" +
                    "    comentario       text,\n" +
                    "    nota             integer constraint wodavaliacao_nota_check check ((nota >= 1) AND (nota <= 5)),\n" +
                    "    percepcaoesforco integer constraint wodavaliacao_percepcaoesforco_check check ((percepcaoesforco >= 1) AND (percepcaoesforco <= 10)),\n" +
                    "    constraint unique_wod_usuario unique (wod, usuario)\n" +
                    ");\n");
        } catch (Exception e) {}
    }


    @Processo(autor = "Joao Alcides",
            data = "10/04/2024",
            descricao = "Cria coluna usarNaPrescricao",
            motivacao = "TW-6",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao123(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE atividade\n" +
                    " ADD COLUMN usarNaPrescricao BOOLEAN DEFAULT TRUE; ");
        } catch (Exception e) {}
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE log\n" +
                    " ADD COLUMN ia BOOLEAN DEFAULT false; ");
        } catch (Exception e) {}
    }

    @Processo(autor = "Alisson Melo",
              data = "25/06/2024",
              descricao = "Alterar coluna configuracao de configuracaosistema para string",
              motivacao = "Refatoração configuracaosistema devido problemas de conflito master com develop",
              ignorarErro = true,
              permitirMultiplasExecucoes = true)
    public void migracaoVersao124(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE configuracaosistema ALTER COLUMN configuracao TYPE varchar(255);");

            List configuracoes = configuracaoDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.configuracao FROM configuracaosistema Obj", new HashMap<>(), 0, 0);
            JSONArray array = new JSONArray(configuracoes);
            for (int i = 0; i < array.length(); i++) {
                JSONArray objs = (JSONArray) array.get(i);
                ConfiguracoesEnum config = ConfiguracoesEnum.getFromOrdinal(Integer.parseInt(objs.getString(1)));
                try {
                    configuracaoDao.executeNativeSQL(ctx, "UPDATE configuracaosistema SET configuracao = '" + config.name() + "' where codigo = " + objs.get(0));
                }  catch (Exception ex) {
                    Uteis.logarDebug(String.format("Erro update configuracao configuracaosistema chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
                    System.out.println(ex.getMessage());
                    ex.printStackTrace();
                }
            }
        } catch (Exception ex) {}
    }

    @Processo(autor = "Kaio Sanchez",
            data = "01/05/2024",
            descricao = "Remove registros duplicados e atualizaa tabela atividadealternativa para não aceitar que uma mesma atividade tenha duas atividades alternativas iguais",
            motivacao = "APPS-972",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao125(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "DELETE FROM atividadealternativa\n" +
                    " WHERE codigo IN (\n" +
                    "    SELECT codigo\n" +
                    "    FROM (\n" +
                    "             SELECT codigo,\n" +
                    "                    ROW_NUMBER() OVER (PARTITION BY atividadealternativa, atividade_codigo ORDER BY codigo) AS row_num\n" +
                    "             FROM atividadealternativa\n" +
                    "         ) AS duplicates\n" +
                    "    WHERE duplicates.row_num > 1\n" +
                    ");");
        } catch (Exception e) {}
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE atividadealternativa\n" +
                    "    ADD CONSTRAINT unique_atividade_alternativa_codigo UNIQUE (atividadealternativa, atividade_codigo)");
        } catch (Exception e) {}
    }

    @Processo(autor = "Alisson Melo",
              data = "26/05/2024",
              descricao = "Criar colunas novas na tabela flexibilidade",
              motivacao = "TW-133: Customização avaliação física criação novos tipos de flexibilidade",
              ignorarErro = true,
              permitirMultiplasExecucoes = true)
    public void migracaoVersao126(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE flexibilidade ADD COLUMN mobilidadeOmbroEsquerdo INT DEFAULT null;");
        } catch (Exception e) { }
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE flexibilidade ADD COLUMN mobilidadeOmbroDireito INT DEFAULT null;");
        } catch (Exception e) { }
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE flexibilidade ADD COLUMN mobilidadeQuadril INT DEFAULT null;");
        } catch (Exception e) { }
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE flexibilidade ADD COLUMN mobilidadeJoelhoEsquerdo INT DEFAULT null;");
        } catch (Exception e) { }
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE flexibilidade ADD COLUMN mobilidadeJoelhoDireito INT DEFAULT null;");
        } catch (Exception e) { }
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE flexibilidade ADD COLUMN mobilidadeTornozeloEsquerdo INT DEFAULT null;");
        } catch (Exception e) { }
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE flexibilidade ADD COLUMN mobilidadeTornozeloDireito INT DEFAULT null;");
        } catch (Exception e) { }

    }

    @Processo(autor = "Anna Carolina",
            data = "23/05/2024",
            descricao = "Permissao para visualizar WODS de outras unidades",
            motivacao = "feature TW-23")
    public void migracaoVersao127(final String ctx) throws Exception {
        Perfil perfilProfessor = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_PROFESSOR);
        Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_COORDENADOR);

        Permissao permissao1 = new Permissao(RecursoEnum.VISUALIZAR_WOD_OUTRAS_UNIDADES, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor);
        permissaoDao.insert(ctx, permissao1);
        Permissao permissao2 = new Permissao(RecursoEnum.VISUALIZAR_WOD_OUTRAS_UNIDADES, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador);
        permissaoDao.insert(ctx, permissao2);
    }

    @Processo(autor = "Denis Silva",
            data = "29/05/2024",
            descricao = "Criando tabela de nivelwod ",
            motivacao = "TW-24",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao128(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "CREATE TABLE public.nivelwod \n" +
                    "(\n" +
                    "    codigo serial4 NOT NULL,\n" +
                    "    nome varchar(255) NOT NULL,\n" +
                    "    nomepadrao varchar(255) NULL,\n" +
                    "    categoria int4 NOT NULL,\n" +
                    "    CONSTRAINT nivelwod_pk PRIMARY KEY (codigo),\n" +
                    "    CONSTRAINT nivelwod_nome_key UNIQUE (nome)\n" +
                    ");\n");


        } catch (Exception e) {}

        try {
            configuracaoDao.executeNativeSQL(ctx,"INSERT INTO public.nivelwod (categoria, nome, nomepadrao) VALUES(0, 'Iniciante', 'Iniciante')");
        } catch (Exception e) {}

        try {
            configuracaoDao.executeNativeSQL(ctx,"INSERT INTO public.nivelwod (categoria, nome, nomepadrao) VALUES(0, 'Scaled', 'Scaled')");
        } catch (Exception e) {}


        try {
            configuracaoDao.executeNativeSQL(ctx,"INSERT INTO public.nivelwod (categoria, nome, nomepadrao) VALUES(0, 'Junior', 'Junior')");
        } catch (Exception e) {}

        try {
            configuracaoDao.executeNativeSQL(ctx,"INSERT INTO public.nivelwod (categoria, nome, nomepadrao) VALUES(0, 'Intermediário', 'Intermediário')");
        } catch (Exception e) {}

        try {
            configuracaoDao.executeNativeSQL(ctx,"INSERT INTO public.nivelwod (categoria, nome, nomepadrao) VALUES(0, 'RX', 'RX')");
        } catch (Exception e) {}
    }

    @Processo(autor = "Denis Silva",
            data = "27/05/2024",
            descricao = "Criando tabela de atividadevideo e criando coluna professor na tabela atividadeanimacao",
            motivacao = "TW-185",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao129(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "CREATE TABLE public.atividadevideo\n" +
                    "(\n" +
                    "    codigo serial NOT NULL,\n" +
                    "    atividade_codigo int4 NOT NULL,\n" +
                    "    linkvideo varchar NOT NULL,\n" +
                    "    professor boolean NULL DEFAULT false,\n" +
                    "    CONSTRAINT atividadevideo_pk PRIMARY KEY (codigo),\n" +
                    "    CONSTRAINT atividadevideo_fk FOREIGN KEY (codigo) REFERENCES public.atividade(codigo)\n" +
                    ");\n");

            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE public.atividadeanimacao ADD professor boolean NULL DEFAULT false;" );
        } catch (Exception e) {}
    }

    @Processo(autor = "Alisson Melo",
              data = "26/06/2024",
              descricao = "Alterar coluna configuracao de configuracaosistema para string",
              motivacao = "Rodar processo novamente pois o 124 não vai rodar na zona 5 devido conflito ao mergear com a master",
              ignorarErro = true,
              permitirMultiplasExecucoes = true)
    public void migracaoVersao130(final String ctx) throws Exception {
        try {
            boolean naoRodouProcesso124 = false;
            try {
                // Forço consulta comparando configuracao com uma string, se der exceção é porque o tipo de dado ainda é int;
                // Se não der erro mas não localizar nenhum registro o processo 124 pode somente ter alterado o tipo de dado da coluna mas não rodou o update do valor;
                List configuracoes = configuracaoDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.configuracao from configuracaosistema Obj WHERE configuracao = 'PORC_ACIMA_NOTIFICAR'", new HashMap<>(), 0, 0);
                if (UteisValidacao.emptyList(configuracoes)) {
                    naoRodouProcesso124 = true;
                }
            } catch (Exception e) {
                naoRodouProcesso124 = true;
            }

            if (naoRodouProcesso124) {
                configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE configuracaosistema ALTER COLUMN configuracao TYPE varchar(255);");

                List configuracoes = configuracaoDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.configuracao FROM configuracaosistema Obj", new HashMap<>(), 0, 0);
                JSONArray array = new JSONArray(configuracoes);
                for (int i = 0; i < array.length(); i++) {
                    JSONArray objs = (JSONArray) array.get(i);
                    ConfiguracoesEnum config = ConfiguracoesEnum.getFromOrdinal(Integer.parseInt(objs.getString(1)));
                    try {
                        configuracaoDao.executeNativeSQL(ctx, "UPDATE configuracaosistema SET configuracao = '" + config.name() + "' where codigo = " + objs.get(0));
                    } catch (Exception ex) {
                        Uteis.logarDebug(String.format("Erro update configuracao configuracaosistema chave %s and instance: %s",
                                ctx,
                                Aplicacao.instanceName));
                        System.out.println(ex.getMessage());
                        ex.printStackTrace();
                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro update configuracao configuracaosistema chave %s and instance: %s",
                            ctx,
                            Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
    }


    @Processo(autor = "Denis Silva",
            data = "15/07/2024",
            descricao = "Criando processo para pegar link de videos existente e salvar na tabela atividadevideo",
            motivacao = "APPS-1253",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao131(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "insert into atividadevideo (linkvideo, professor, atividade_codigo) \n" +

                    "    select a.linkvideo, false, a.codigo from atividade a\n" +
                    "    left join atividadevideo av on a.codigo = av.atividade_codigo\n" +
                    "    where coalesce(a.linkvideo, '') <> ''\n" +
                    "    and av.codigo is null\n");
        } catch (Exception e) {}
    }

    @Processo(autor = "Alisson Melo",
              data = "07/06/2025",
              descricao = "TW-19",
              motivacao = "Remover Chave Estrangeira (responsavel) Tabela Usuario na LocacaoHorario",
              ignorarErro = true,
              permitirMultiplasExecucoes = true)
    public void migracaoVersao132(final String ctx) throws Exception {
        try {
            if (!br.com.pacto.controller.json.base.SuperControle.independente(ctx)) {
                // Removendo relacionamento com a tabela usuario
                try (Connection conTW = locacaoHorarioDao.getConnection(ctx)) {
                    try {
                        String alterTW = "ALTER TABLE locacaohorario DROP CONSTRAINT fk32987ed2541542fc";
                        try (Statement stm = conTW.createStatement()) {
                            stm.execute(alterTW);
                        }
                    } catch (Exception ex) {
                        Uteis.logarDebug(String.format("Erro remover chave estrangeira locacaohorario chave %s and instance: %s", ctx, Aplicacao.instanceName));
                        System.out.println(ex.getMessage());
                        ex.printStackTrace();
                    }

                    // Povoando colunas com o codigo do usuario do zw, que antes eram chaves estrangerias que guardavam o codigo do usuario do treino para locacaohorario.
                    List listLocacaoHorario = locacaoHorarioDao.findByParamNative(ctx, "SELECT Obj.codigo, Obj.responsavel FROM locacaohorario Obj", new HashMap<>(), 0, 0);
                    JSONArray array2 = new JSONArray(listLocacaoHorario);
                    for (int i = 0; i < array2.length(); i++) {
                        JSONArray objs = (JSONArray) array2.get(i);
                        List codigosUsuario = usuarioDao.findByParamNative(ctx, "SELECT Obj.usuariozw FROM Usuario Obj WHERE Obj.codigo = " + objs.get(1), new HashMap<>(), 0, 0);
                        if (!isNull(codigosUsuario) && !codigosUsuario.isEmpty() && (codigosUsuario.get(0) != null && isNotBlank(codigosUsuario.get(0).toString()))) {
                            try {
                                String updateTW = "update locacaohorario set responsavel = " + codigosUsuario.get(0) + " where codigo = " + objs.get(0);
                                try (Statement stm = conTW.createStatement()) {
                                    stm.execute(updateTW);
                                }
                            } catch (Exception ex) {
                                Uteis.logarDebug(String.format("Erro update responsavel LocacaoHorario chave %s and instance: %s", ctx, Aplicacao.instanceName));
                                System.out.println(ex.getMessage());
                                ex.printStackTrace();
                            }
                        }
                    }
                    validadorVersaoDao.insert(ctx, new ValidadorVersao(true, "migrarCodigosLocacaoHorarioUsuarioTreinoZw", Calendario.hoje()));
                }
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Erro processo ajustar responsavel LocacaoHorario chave %s and instance: %s", ctx, Aplicacao.instanceName));
            System.out.println(ex.getMessage());
            ex.printStackTrace();
        }
    }

    @Processo(autor = "Alisson Melo",
            data = "26/06/2024",
            descricao = "Inicializando configuração sistema HABILITAR_VER_WOD_TODAS_EMPRESAS_APP como true",
            motivacao = "TW-348",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao133(final String ctx) throws Exception {
        try {
            try (ResultSet rs = configuracaoDao.createStatement(ctx, "SELECT EXISTS (SELECT  * FROM configuracaosistema c WHERE configuracao = 'HABILITAR_VER_WOD_TODAS_EMPRESAS_APP')")) {
                if (rs.next()) {
                    if (!rs.getBoolean("exists")) {
                        configuracaoDao.executeNativeSQL(ctx,"INSERT INTO configuracaosistema (configuracao, valor) VALUES ('HABILITAR_VER_WOD_TODAS_EMPRESAS_APP' , 'true');");
                    }
                }
            }
        } catch (Exception e) {}
    }


    @Processo(autor = "Kaio Sanchez",
            data = "08/08/2024",
            descricao = "Criando processo para adicionar nova coluna de id da nova versao do treino gerado por IA",
            motivacao = "TW-617",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao134(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE atividade ADD COLUMN idIA2 INTEGER;");
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE atividade DROP CONSTRAINT atividade_nome_key;");
            configuracaoDao.executeNativeSQL(ctx, "CREATE UNIQUE INDEX atividade_nome_unico_quando_idia2_nulo \n" +
                    "ON atividade(nome) \n" +
                    "WHERE idia2 IS NULL;");
            configuracaoDao.executeNativeSQL(ctx, "CREATE UNIQUE INDEX atividade_nome_idia2_idx\n" +
                    "    ON atividade (nome)\n" +
                    "    WHERE idia2 IS NOT NULL;");
        } catch (Exception e) {}
    }

    @Processo(
            autor = "Anna Carolina",
            data = "21/08/2024",
            descricao = "Criação das colunas mobilidade quadril direito/esquerdo e observações na tabela de flexibilidade",
            motivacao = "TW-573",
            ignorarErro = true,
            permitirMultiplasExecucoes = true
    )
    public void migracaoVersao135(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE flexibilidade ADD COLUMN mobilidadeQuadrilEsquerdo INT DEFAULT NULL;");
        } catch (Exception e) {
            System.err.println("Erro ao adicionar a coluna mobilidadeQuadrilEsquerdo: " + e.getMessage());
        }
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE flexibilidade ADD COLUMN mobilidadeQuadrilDireito INT DEFAULT NULL;");
        } catch (Exception e) {
            System.err.println("Erro ao adicionar a coluna mobilidadeQuadrilDireito: " + e.getMessage());
        }
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE flexibilidade ADD COLUMN observacaoOmbro TEXT DEFAULT NULL;");
        } catch (Exception e) {
            System.err.println("Erro ao adicionar a coluna observacaoOmbro: " + e.getMessage());
        }
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE flexibilidade ADD COLUMN observacaoQuadril TEXT DEFAULT NULL;");
        } catch (Exception e) {
            System.err.println("Erro ao adicionar a coluna observacaoQuadril: " + e.getMessage());
        }
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE flexibilidade ADD COLUMN observacaoJoelho TEXT DEFAULT NULL;");
        } catch (Exception e) {
            System.err.println("Erro ao adicionar a coluna observacaoJoelho: " + e.getMessage());
        }
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE flexibilidade ADD COLUMN observacaoTornozelo TEXT DEFAULT NULL;");
        } catch (Exception e) {
            System.err.println("Erro ao adicionar a coluna observacaoTornozelo: " + e.getMessage());
        }
    }

    @Processo(autor = "Joao Alcides",
            data = "26/07/2024",
            descricao = "Criando notificacaoacessousuario",
            motivacao = "MJ-74",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao136(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "CREATE TABLE NotificacoesAcessoUsuario (\n" +
                    "    codigo serial PRIMARY KEY,\n" +
                    "    usuario INT,\n" +
                    "    semProgramaTreino BOOLEAN,\n" +
                    "    faltosos BOOLEAN,\n" +
                    "    quatrosDiasSemAcesso BOOLEAN,\n" +
                    "    treinoVencido BOOLEAN,\n" +
                    "    treinoVencer BOOLEAN,\n" +
                    "    alunoSemVinculoProfessor BOOLEAN,\n" +
                    "    parcelasAtrasadas BOOLEAN,\n" +
                    "    avaliacaoFisicaAtrasada BOOLEAN,\n" +
                    "    semAssinaturaContrato BOOLEAN,\n" +
                    "    cadastroIncompleto BOOLEAN\n" +
                    ");");

        } catch (Exception e) {}
    }

    @Processo(autor = "Murillo Roseno",
              data = "26/08/2024",
              descricao = "Permissao e funcionalidades novas do Treino",
              motivacao = "feature TW-624")
    public void migracaoVersao137(final String ctx) throws Exception {
        Perfil perfilProfessor = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_PROFESSOR);
        Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_COORDENADOR);

        RecursoEnum[] recursos = {
                RecursoEnum.BI_CROSS,
                RecursoEnum.MONITOR,
                RecursoEnum.ATIVIDADES_GRADUACAO,
                RecursoEnum.FICHA_TECNICA_GRADUACAO,
                RecursoEnum.AVALIACAO_DE_PROGRESSO_GRADUACAO,
                RecursoEnum.BI_AGENDA,
                RecursoEnum.AGENDA_DE_AULAS,
                RecursoEnum.AGENDA_DE_SERVICOS,
                RecursoEnum.RELATORIOS_AGENDA,
                RecursoEnum.BI_AVALIACAO_FISICA,
                RecursoEnum.CADASTROS_AVALIACAO_FISICA,
                RecursoEnum.RELATORIOS_TREINO,
                RecursoEnum.CADASTROS_AGENDA,
                RecursoEnum.UTILIZAR_MODULO_CROSS,
                RecursoEnum.UTILIZAR_MODULO_GRADUACAO,
                RecursoEnum.UTILIZAR_MODULO_AGENDA,
                RecursoEnum.UTILIZAR_MODULO_AVALIACAO_FISICA
        };

        for (RecursoEnum recurso : recursos) {
            Permissao permissaoProfessor = new Permissao(recurso, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor);
            permissaoDao.insert(ctx, permissaoProfessor);

            Permissao permissaoCoordenador = new Permissao(recurso, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador);
            permissaoDao.insert(ctx, permissaoCoordenador);
        }
    }

    @Processo(autor = "Murillo Roseno",
            data = "18/09/2024",
            descricao = "Bugfix para permissao e funcionalidades novas do Treino, para todos os perfis",
            motivacao = "Bugfix TW-624")
    public void migracaoVersao138(final String ctx) throws Exception {
        List<Perfil> todosPerfis = perfilDao.listarPerfis(ctx);

        RecursoEnum[] recursos = {
                RecursoEnum.BI_CROSS,
                RecursoEnum.MONITOR,
                RecursoEnum.ATIVIDADES_GRADUACAO,
                RecursoEnum.FICHA_TECNICA_GRADUACAO,
                RecursoEnum.AVALIACAO_DE_PROGRESSO_GRADUACAO,
                RecursoEnum.BI_AGENDA,
                RecursoEnum.AGENDA_DE_AULAS,
                RecursoEnum.AGENDA_DE_SERVICOS,
                RecursoEnum.RELATORIOS_AGENDA,
                RecursoEnum.BI_AVALIACAO_FISICA,
                RecursoEnum.CADASTROS_AVALIACAO_FISICA,
                RecursoEnum.RELATORIOS_TREINO,
                RecursoEnum.CADASTROS_AGENDA,
                RecursoEnum.UTILIZAR_MODULO_CROSS,
                RecursoEnum.UTILIZAR_MODULO_GRADUACAO,
                RecursoEnum.UTILIZAR_MODULO_AGENDA,
                RecursoEnum.UTILIZAR_MODULO_AVALIACAO_FISICA
        };

        for (Perfil perfil : todosPerfis) {
            for (RecursoEnum recurso : recursos) {
                Permissao permissaoExistente = permissaoDao.obterPermissaoPorPerfilRecurso(ctx, perfil.getCodigo(), recurso);

                if (permissaoExistente == null || permissaoExistente.getCodigo() == null) {
                    Permissao permissao = new Permissao(recurso, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfil);
                    permissaoDao.insert(ctx, permissao);
                }
            }
        }
    }

    @Processo(autor = "Alisson Melo",
              data = "08/10/2024",
              descricao = "Criacao de index",
              motivacao = "Criacao de index",
              ignorarErro = true)
    public void migracaoVersao139(final String ctx) throws Exception {
        try {
            atividadeGrupoMuscularDao.executeNativeSQL(ctx,
                    "CREATE INDEX idx_atividadegrupomuscular_grupomuscular_atividade_codigo \n" +
                    "ON public.atividadegrupomuscular \n" +
                    "USING btree (grupomuscular_codigo, atividade_codigo);");
        } catch (Exception ex) {}

        try {
            usuarioDao.executeNativeSQL(ctx,
                    "CREATE INDEX idx_customrevisionentity_username_processo\n" +
                    "ON customrevisionentity USING btree (username);");
        } catch (Exception ex) {}

        try {
            usuarioDao.executeNativeSQL(ctx,
                    "CREATE INDEX idx_usuario_username\n" +
                    "ON usuario USING btree (username);");
        } catch (Exception ex) {}

        try {
            usuarioDao.executeNativeSQL(ctx, "CREATE EXTENSION IF NOT EXISTS pg_trgm;");
        } catch (Exception ex) {}

        try {
            usuarioDao.executeNativeSQL(ctx,
                    "CREATE INDEX idx_customrevisionentity_processo_trgm\n" +
                    "ON customrevisionentity USING gin (processo gin_trgm_ops);");
        } catch (Exception ex) {}

    }
    @Processo(autor = "Kaio Sanchez",
            data = "04/10/2024",
            descricao = "Corrige ordenação tipo wod",
            motivacao = "APPS-1941")
    public void migracaoVersao140(final String ctx) throws Exception {
        try {
            TipoWod tipoWod = tipoWodService.obterPorNome(ctx, "Amrap + Weight");
            if (tipoWod != null) {
                tipoWod.setOrderBy("ORDER BY rounds  DESC, peso DESC");
                tipoWodService.gravarTipoWod(ctx, tipoWod);

                List<Wod> wods = wodService.obterPorTipoWod(ctx, tipoWod.getCodigo());
                for (Wod wod : wods) {
                    wodService.atualizarRanking(ctx, wod.getCodigo(), wod.getTipoWod().getOrderBy());
                }
            }
        } catch (Exception e) {
        }
    }

    @Processo(autor = "Denis Silva",
              data = "09/09/2024",
              descricao = "Permissao nova do Treino",
              motivacao = "feature TW-558")
    public void migracaoVersao141(final String ctx) throws Exception {
        try {
            Perfil perfilConsultor = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_CONSULTOR);
            Perfil perfilProfessor = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_PROFESSOR);
            Perfil perfilCoordenador = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_COORDENADOR);

            RecursoEnum[] recursos = {
                    RecursoEnum.ENVIAR_TREINO_EM_MASSA
            };

            for (RecursoEnum recurso : recursos) {
                Permissao permissaoCoordenador = new Permissao(recurso, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilCoordenador);
                permissaoDao.insert(ctx, permissaoCoordenador);

                Permissao permissaoProfessor = new Permissao(recurso, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor);
                permissaoDao.insert(ctx, permissaoProfessor);

                Permissao permissaoConsultor = new Permissao(recurso, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilConsultor);
                permissaoDao.insert(ctx, permissaoConsultor);
            }
        } catch (Exception ex) {}
    }

    @Processo(
            autor = "Anna Carolina",
            data = "14/10/2024",
            descricao = "Migração da disponibilidade",
            motivacao = "TW-573",
            ignorarErro = true,
            permitirMultiplasExecucoes = true
    )
    public void migracaoVersao142(final String ctx) throws Exception {
        try {
            disponibilidadeService.migrarDadosAgendamentoModeloNovoDisponibilidade();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Processo(autor = "Anna Carolina",
            data = "18/09/2024",
            descricao = "Permissao e nova configuração de funcionalidade do Treino - Aulas",
            motivacao = "feature TW-777")
    public void migracaoVersao143(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "update configuracaosistema set valor = '0' where configuracao = " + ConfiguracoesEnum.NR_VALIDAR_VEZES_MODALIDADE.ordinal());

        } catch (Exception e) {
            //ignore
        }

    }

    @Processo(autor = "Alisson Melo",
            data = "04/09/2024",
            descricao = "Criar colunas para gerenciar cancelamento de agendamento de locação",
            motivacao = "TW-601",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao144(final String ctx) throws Exception {
        try {
            agendamentoLocacaoDao.executeNativeSQL(ctx, "ALTER TABLE agendamentolocacao ADD COLUMN isCancelado BOOLEAN DEFAULT FALSE;");
            agendamentoLocacaoDao.executeNativeSQL(ctx, "ALTER TABLE agendamentolocacao ADD COLUMN isFinalizado BOOLEAN DEFAULT FALSE;");
            agendamentoLocacaoDao.executeNativeSQL(ctx, "ALTER TABLE agendamentolocacao ADD COLUMN justificativa TEXT DEFAULT NULL;");
            agendamentoLocacaoDao.executeNativeSQL(ctx, "ALTER TABLE agendamentolocacao ADD COLUMN dataCancelamento TIMESTAMP WITHOUT TIME ZONE;");
            agendamentoLocacaoDao.executeNativeSQL(ctx, "ALTER TABLE agendamentolocacao ADD COLUMN usuarioCancelou INT DEFAULT NULL;");
        } catch (Exception e) {}
    }

    @Processo(autor = "Waller Maciel",
            data = "16/10/2024",
            descricao = "Criar índices para otimização de queries em clientesintetico e respostaclienteparq",
            motivacao = "M1-1225",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao145(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_clientesintetico_empresa_matricula ON clientesintetico(empresa, matricula);");
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_clientesintetico_nome ON clientesintetico(nome);");
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_respostaclienteparq_urlassinatura_dataresposta ON respostaclienteparq(urlassinatura, dataresposta);");
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_respostaclienteparq_cliente_codigo ON respostaclienteparq(cliente_codigo);");
        } catch (Exception e) {}
    }

    @Processo(autor = "Alisson Melo",
              data = "31/10/2024",
              descricao = "Criar índices para otimização de consultas listagem parq",
              motivacao = "TW-1068",
              ignorarErro = true,
              permitirMultiplasExecucoes = true)
    public void migracaoVersao146(final String ctx) throws Exception {
        try {
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_clientesintetico_empresa_codigo_nome ON clientesintetico (empresa, codigo, nome);");
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
        try {
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_rspstclienteparq_clientecod_dtrspst_urlassin_nonnull ON respostaclienteparq (cliente_codigo, dataresposta) WHERE urlassinatura IS NOT NULL AND urlassinatura <> '';");
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
        try {
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_rspstclienteparq_clientecod_dtrspst_urlassin_null ON respostaclienteparq (cliente_codigo, dataresposta) WHERE urlassinatura IS NULL OR TRIM(urlassinatura) = '';");
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
        try {
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_clientesintetico_empresa_codigo ON clientesintetico (empresa, codigo);");
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
        try {
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_respostaclienteparq_codigo ON respostaclienteparq (codigo);");
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
        try {
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_respostacliente_respostaclienteparq_codigo_resposta ON respostacliente (respostaclienteparq_codigo, resposta);");
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
        try {
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_respostacliente_cliente_codigo ON respostacliente (cliente_codigo);");
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
        try {
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_respostacliente_resposta ON respostacliente (resposta);");
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
        try {
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_itemavaliacaofisica_cliente_codigo_datalancamento ON itemavaliacaofisica (cliente_codigo, datalancamento);");
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
        try {
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_respostacliente_clienteparq_resposta ON respostacliente (cliente_codigo, respostaclienteparq_codigo, resposta) WHERE resposta = 'SIM';");
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
        try {
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_itemavaliacaofisica_cliente_datalancamento_result ON itemavaliacaofisica (cliente_codigo, datalancamento) WHERE \"result\" = 'PAR-Q';");
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
        try {
            usuarioDao.executeNativeSQL(ctx, "CREATE INDEX idx_respostacliente_itemavaliacao_codigo ON respostacliente (itemavaliacao_codigo);");
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
    }

    @Processo(autor = "Murillo Roseno",
            data = "28/10/2024",
            descricao = "Criar permissões de funcionalidades novas do Treino, para todos os perfis",
            motivacao = "TW-1048")
    public void migracaoVersao147(final String ctx) throws Exception {
        try {
            List<Perfil> todosPerfis = perfilDao.listarPerfis(ctx);

            RecursoEnum[] recursos = {
                    RecursoEnum.TREINO_EM_CASA,
                    RecursoEnum.PRESCRICAO_DE_TREINO,
                    RecursoEnum.CADASTROS_TREINO
            };

            for (Perfil perfil : todosPerfis) {
                for (RecursoEnum recurso : recursos) {
                    Permissao permissaoExistente = permissaoDao.obterPermissaoPorPerfilRecurso(ctx, perfil.getCodigo(), recurso);

                    if (permissaoExistente == null || permissaoExistente.getCodigo() == null) {
                        Permissao permissao = new Permissao(recurso, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfil);
                        permissaoDao.insert(ctx, permissao);
                    }
                }
            }
        } catch (Exception e) {}
    }

    @Processo(autor = "Denis Silva",
            data = "09/09/2024",
            descricao = "Permissao nova do Treino",
            motivacao = "feature TW-558")
    public void migracaoVersao148(final String ctx) throws Exception {
        try {
            Perfil perfilProfessor = perfilDao.insertOrGetObjectForName(ctx, Perfil.NOME_PERFIL_PROFESSOR);

            RecursoEnum[] recursos = {
                    RecursoEnum.ENVIAR_TREINO_EM_MASSA
            };

            for (RecursoEnum recurso : recursos) {
                Permissao permissaoProfessor = new Permissao(recurso, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfilProfessor);
                permissaoDao.insert(ctx, permissaoProfessor);
            }
        } catch (Exception e) {}
    }

    @Processo(autor = "Kaio Sanchez",
            data = "26/09/2024",
            descricao = "Criando tabela perfildisc",
            motivacao = "APPS-1622",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao149(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "CREATE TABLE perfildisc (\n" +
                    "    codigo serial PRIMARY KEY,\n" +
                    "    dominancia DOUBLE PRECISION DEFAULT NULL,\n" +
                    "    influencia DOUBLE PRECISION DEFAULT NULL,\n" +
                    "    estabilidade DOUBLE PRECISION DEFAULT NULL,\n" +
                    "    conformidade DOUBLE PRECISION DEFAULT NULL,\n" +
                    "    cliente int NOT NULL REFERENCES clientesintetico(codigo))" +
                    "    datacadastro TIMESTAMP NOT NULL ");
        } catch (Exception e) {
        }
    }

    @Processo(
            autor = "Anna Carolina",
            data = "03/09/2024",
            descricao = "Criação da coluna datavencimento na tabela respostaclienteparq",
            motivacao = "TW-580",
            ignorarErro = true,
            permitirMultiplasExecucoes = true
    )
    public void migracaoVersao150(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE respostaclienteparq ADD COLUMN dataVencimento DATE;");
        } catch (Exception e) {
        }
    }

    @Processo(autor = "Alisson Melo",
            data = "18/10/2024",
            descricao = "Criando tabela lesao",
            motivacao = "TW-162",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao151(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "CREATE TABLE lesao (\n" +
                    "    codigo SERIAL PRIMARY KEY, \n" +
                    "    cliente_codigo INT, \n" +
                    "    gravidade VARCHAR(255), \n" +
                    "    observacao TEXT, \n" +
                    "    regiaoLesao VARCHAR(255), \n" +
                    "    status VARCHAR(255), \n" +
                    "    dataLesao TIMESTAMP, \n" +
                    "    dataRegistro TIMESTAMP, \n" +
                    "    usuarioLancou_codigo INT, \n" +
                    "    \n" +
                    "    CONSTRAINT fk_cliente_codigo FOREIGN KEY (cliente_codigo) REFERENCES clientesintetico(codigo)\n" +
                    ");");
        } catch (Exception e) {
        }
    }

    @Processo(autor = "Alisson Melo",
              data = "05/11/2024",
              descricao = "Executar migracaoVersao146 que não rodou por completo devido tamanho do nome dos index",
              motivacao = "TW-1068",
              ignorarErro = true,
              permitirMultiplasExecucoes = true)
    public void migracaoVersao152(final String ctx) throws Exception {
        // remover index criado com nome truncado
        try {
            usuarioDao.executeNativeSQL(ctx, "DROP INDEX idx_respostaclienteparq_cliente_codigo_dataresposta_urlassinatu;");
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
        // Executar processo 146 novamente com os devidos tratamentos
        try {
            migracaoVersao146(ctx);
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
    }

    @Processo(autor = "Kaio Sanchez",
            data = "29/10/2024",
            descricao = "Cria tabela de notificação agendada",
            motivacao = "APPS-2119",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao153(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "CREATE TABLE NotificacaoAulaAgendada\n" +
                    "(\n" +
                    "    codigo      SERIAL PRIMARY KEY,\n" +
                    "    cliente INT       NOT NULL references clientesintetico,\n" +
                    "    refpush VARCHAR   NOT NULL,\n" +
                    "    horario INT NOT NULL,\n" +
                    "    dia     TIMESTAMP NOT NULL,\n" +
                    "    cancelada BOOLEAN DEFAULT false\n" +
                    ");");
        } catch (Exception e) {}
    }

    @Processo(autor = "Anderson Rosa",
            data = "29/10/2024",
            descricao = "Cria tabela guardar config gogood",
            motivacao = "GC-699",
            ignorarErro = true,
            permitirMultiplasExecucoes = true)
    public void migracaoVersao154(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "CREATE TABLE configgogood\n" +
                    "(\n" +
                    "    codigo       SERIAL PRIMARY KEY,\n" +
                    "    dataLancamento TIMESTAMP NOT NULL DEFAULT NOW(),\n" +
                    "    usuarioLancou_codigo  INTEGER NOT NULL,\n" +
                    "    empresa_codigo  INTEGER REFERENCES empresa (codigo),\n" +
                    "    ativo  BOOLEAN DEFAULT FALSE,\n" +
                    "    nome  TEXT DEFAULT NULL,\n" +
                    "    tokenAcademyGoGood  TEXT DEFAULT NULL\n" +
                    ");");
        } catch (Exception e) {}
    }

    @Processo(autor = "Murillo Roseno",
              data = "03/12/2024",
              descricao = "Criar permissões para PRESCRICAO_DE_TREINO_POR_IA, para todos os perfis",
              motivacao = "TW-1235")
    public void migracaoVersao155(final String ctx) throws Exception {
        try {
            List<Perfil> todosPerfis = perfilDao.listarPerfis(ctx);

            RecursoEnum recurso = RecursoEnum.PRESCRICAO_DE_TREINO_POR_IA;

            for (Perfil perfil : todosPerfis) {
                Permissao permissaoExistente = permissaoDao.obterPermissaoPorPerfilRecurso(ctx, perfil.getCodigo(), recurso);

                if (permissaoExistente == null || permissaoExistente.getCodigo() == null) {
                    Permissao permissao = new Permissao(recurso, new TipoPermissaoEnum[]{TipoPermissaoEnum.TOTAL}, perfil);
                    permissaoDao.insert(ctx, permissao);
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, MigracaoDadosJPAService.class);
        }
    }

    @Processo(
            autor = "Anderson Rosa",
            data = "06/02/2025",
            descricao = "Controlar preenchimento de campo",
            motivacao = "GC-1190",
            ignorarErro = true,
            permitirMultiplasExecucoes = true
    )
    public void migracaoVersao156(final String ctx) throws Exception {
        try {
            configuracaoDao.executeNativeSQL(ctx, "create table SelfLoopsConfiguracoes ( codigo serial primary key,\n" +
                    "codeSelfloops text default null,\n" +
                    "empresaSelfloops text default null,\n" +
                    "refreshToken text default null,\n" +
                    "empresa_codigo integer references empresa (codigo));");
            configuracaoDao.executeNativeSQL(ctx, "ALTER TABLE aparelho ADD COLUMN sensorSelfloops text default null;");
        } catch (Exception e) {
        }
    }

}
