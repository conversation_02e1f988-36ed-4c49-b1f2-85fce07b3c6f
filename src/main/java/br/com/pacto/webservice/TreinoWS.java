/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.webservice;

import br.com.pacto.bean.cliente.ClientePesquisa;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.gestaopersonal.CreditoPersonal;
import br.com.pacto.bean.gestaopersonal.TipoOperacaoPersonalEnum;
import br.com.pacto.bean.notificacao.Notificacao;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.usuarioEmail.UsuarioEmail;
import br.com.pacto.dao.intf.perfil.PerfilDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClientePesquisaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.creditopersonal.CreditoPersonalService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.pessoa.StatusPessoaService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.sintetico.SinteticoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.usuarioEmail.UsuarioEmailService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import servicos.integracao.zw.beans.ClienteZW;
import servicos.integracao.zw.beans.UsuarioZW;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import javax.servlet.ServletContext;
import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 *
 * <AUTHOR>
 */
@WebService(serviceName = "TreinoWS")
public class TreinoWS {

    @Autowired
    private ServletContext context;

    @WebMethod(operationName = "sincronizarUsuario")
    public String sincronizarUsuario(@WebParam(name = "key") String key,
            @WebParam(name = "usuario") UsuarioZW usuarioZW) {
        try {
            if (usuarioZW.getCodigoExterno() != null && !usuarioZW.getCodigoExterno().isEmpty()
                    && Integer.valueOf(usuarioZW.getCodigoExterno()) > 0) {
                UsuarioService usuarioService = (UsuarioService) UtilContext.getBean(
                        UsuarioService.class);
                EmpresaService empresaService = (EmpresaService) UtilContext.getBean(EmpresaService.class);
                empresaService.persistirEmpresasZW(key, usuarioZW.getEmpresaZW(),
                        usuarioZW.getUsuarioZW());

                TipoUsuarioEnum tipo = usuarioZW.getTipo();
                UsuarioEmail usuarioEmail = null;
                ClienteSintetico cli = null;
                ProfessorSintetico profLocal = null;

                if (tipo.equals(TipoUsuarioEnum.ALUNO)) {
                    sincronizarAluno(key, usuarioZW);
                    ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                            ClienteSinteticoService.class);
                    String s = "select obj from ClienteSintetico obj where codigoPessoa = :id";
                    Map<String, Object> p = new HashMap<String, Object>();
                    p.put("id", usuarioZW.getCliente().getCodigoPessoa());
                    cli = clienteService.obterObjetoPorParam(key, s, p);
                    if (cli != null) {
                        clienteService.refresh(key, cli);
                        usuarioZW.setCliente(ClienteZW.fromClienteSintetico(cli));
                        usuarioZW.getCliente().setCodigo(cli.getCodigo());
                    }
                } else {
                    ProfessorSinteticoService professorService = (ProfessorSinteticoService) UtilContext.getBean(
                            ProfessorSinteticoService.class);
                    professorService.sincronizarProfessor(key, usuarioZW.getProfessor(), usuarioZW.getEmpresaZW(), usuarioZW.getFotoKey());

                    String s = "select obj from ProfessorSintetico obj where codigoColaborador = :id";
                    Map<String, Object> p = new HashMap<String, Object>();
                    p.put("id", usuarioZW.getProfessor().getCodigoColaborador());
                    profLocal = professorService.obterObjetoPorParam(key, s, p);
                    if (profLocal != null) {
                        usuarioZW.setProfessor(profLocal);
                    }
                }

                final String where = tipo.equals(TipoUsuarioEnum.ALUNO) ? " where cliente.codigo = :chave " : " where professor.codigo = :chave";
                String s = "select u from Usuario u " + where;
                HashMap<String, Object> p = new HashMap<String, Object>();
                p.put("chave", tipo.equals(TipoUsuarioEnum.ALUNO) ? usuarioZW.getCliente().getCodigo() : usuarioZW.getProfessor().getCodigo());
                Usuario uLocal = usuarioService.obterObjetoPorParam(key, s, p);

                UsuarioEmailService usuarioEmailService = UtilContext.getBean(UsuarioEmailService.class);
                if (!tipo.equals(TipoUsuarioEnum.ALUNO) && uLocal != null) {
                    if (uLocal.getUsuarioEmail() != null) {
                        usuarioEmail = usuarioEmailService.obterUsuarioEmailParam(key, "codigo", uLocal.getUsuarioEmail().getCodigo());
                        if (usuarioEmail == null) {
                            String email = "";
                            if (!StringUtils.isEmpty(uLocal.getUsuarioEmail().getEmail())) {
                                email = uLocal.getUsuarioEmail().getEmail();
                            } else {
                                email = usuarioZW.getEmail();
                            }
                            usuarioEmail = usuarioEmailService.obterUsuarioEmailParam(key, "email", email);
                        }
                    } else if (!StringUtils.isEmpty(usuarioZW.getEmail())) {
                        usuarioEmail = usuarioEmailService.obterUsuarioEmailParam(key, "email", usuarioZW.getEmail());
                    }
                }

                if (usuarioEmail == null) {
                    usuarioEmail = new UsuarioEmail();
                }
                if (isNotBlank(usuarioZW.getEmail())) {
                    usuarioEmail.setEmail(usuarioZW.getEmail());
                    usuarioEmail.setVerificado(usuarioZW.getEmailVerificado());
                } else {
                    usuarioEmail.setEmail(usuarioZW.getUserName());
                    usuarioEmail.setVerificado(false);
                }
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("usuarioEmail", usuarioEmail.getEmail());
                Usuario usuarioDoEmail = usuarioService.obterObjetoPorParam(key, "SELECT u FROM Usuario u WHERE u.usuarioEmail.email = :usuarioEmail", params);
                if (!tipo.equals(TipoUsuarioEnum.ALUNO) && usuarioDoEmail != null && usuarioDoEmail.getUsuarioZW() != null && !usuarioDoEmail.getUsuarioZW().equals(usuarioZW.getUsuarioZW())) {
                    // Só lançar a excessão se o email na tabela usuario for igual ao email na tabela usuarioemail vinculado ao usuario
                    if (usuarioDoEmail.getEmail() != null && usuarioDoEmail.getEmail().equals(usuarioEmail.getEmail())
                            && usuarioDoEmail.getUsuarioEmail() != null && usuarioDoEmail.getUsuarioEmail().getEmail() != null
                            && usuarioDoEmail.getEmail().equals(usuarioDoEmail.getUsuarioEmail().getEmail())) {
                        return montarMensagemErro("O e-mail está vinculado ao usuário " + usuarioDoEmail.getNome());
                    }else{
                        //Vincular o email ao usuário correto
                        usuarioEmail.setUsuario(null);
                    }
                }

                if (uLocal == null) {
                    usuarioZW.setCodigo(null);
                    uLocal = UsuarioZW.toUsuarioTreino(usuarioZW);
                    uLocal.setCliente(cli);
                    uLocal.setTipo(tipo);
                    uLocal.setProfessor(profLocal);
                    if (!tipo.equals(TipoUsuarioEnum.ALUNO)) {
                        if (isNotBlank(usuarioZW.getEmail())) {
                            UsuarioEmail usEmail = usuarioEmailService.obterUsuarioEmailParam(key, "email", usuarioZW.getEmail());
                            if (usEmail != null && UteisValidacao.notEmptyNumber(usEmail.getCodigo())) {
                                usuarioEmail = usEmail;
                            }
                        }
                        usuarioEmail.setUsuario(uLocal);
                        uLocal.setUsuarioEmail(usuarioEmail);
                    }
                    tratarPerfilUsuario(key, usuarioZW, uLocal);
                    usuarioService.inserir(key, uLocal);
                } else {
                    usuarioZW.setCodigo(uLocal.getCodigo());
                    if (!tipo.equals(TipoUsuarioEnum.ALUNO)
                            && !UteisValidacao.emptyNumber(usuarioZW.getUsuarioZW())){ // usuario é apenas colaborador
                        try {
                            if (usuarioEmail.getCodigo() != null) {
                                if (usuarioEmail.getUsuario() == null) {
                                    usuarioEmail.setUsuario(uLocal);
                                }
                                usuarioEmail = usuarioEmailService.alterar(key, usuarioEmail);
                            } else {
                                usuarioEmail.setUsuario(uLocal);
                                usuarioEmail = usuarioEmailService.inserir(key, usuarioEmail);
                            }
                            uLocal.setEmail(usuarioEmail.getEmail());
                        } catch (Exception ex) {
                            System.out.println("Não foi possível alterar e-mail: " + ex.getMessage());
                        }
                        uLocal.setUsuarioEmail(usuarioEmail);
                    }
                    if (!UteisValidacao.emptyNumber(usuarioZW.getPerfilTw())) {
                        tratarPerfilUsuario(key, usuarioZW, uLocal);
                    }
                    uLocal = usuarioService.alterar(key, uLocal, usuarioZW);
                    usuarioService.adicionarUsuarioServicoDescobrir(key, uLocal.getUserName());
                }

                return "ok";
            } else {
                throw new ServiceException("Usuário inválido!");
            }
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return montarMensagemErro(ex.getMessage());
        }
    }

    private String montarMensagemErro(String msgErro) {
        return "ERRO: " + msgErro;
    }

    @WebMethod(operationName = "adicionarPersonal")
    public String adicionarPersonal(@WebParam(name = "key") String key,
            @WebParam(name = "professor") ProfessorSintetico professor,
            @WebParam(name = "empresa") Integer empresa) {
        try {
            ProfessorSinteticoService professorService = (ProfessorSinteticoService) UtilContext.getBean(
                    ProfessorSinteticoService.class);
            professorService.sincronizarProfessor(key, professor, empresa);
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    private String sincronizarAluno(String key, UsuarioZW usuarioZW) throws Exception {
        if (usuarioZW.getCliente().getCodigoCliente() > 0) {
            ProfessorSintetico professorAtual = null;
            if (usuarioZW.getCliente().getCodigoColaboradorProfessor() != null) {
                ProfessorSinteticoService professorService = (ProfessorSinteticoService) UtilContext.getBean(
                        ProfessorSinteticoService.class);
                professorAtual = professorService.consultarPorCodigoColaborador(key, usuarioZW.getCliente().getCodigoColaboradorProfessor());
                usuarioZW.getCliente().setProfessorSintetico(professorAtual);
            }

            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
            String s = "select obj from ClienteSintetico obj where codigoCliente = :id";
            Map<String, Object> p = new HashMap<String, Object>();
            p.put("id", usuarioZW.getCliente().getCodigoCliente());
            ClienteSintetico clienteSinteticoAtual = clienteService.obterObjetoPorParam(key, s, p);

            if (clienteSinteticoAtual == null) {
                usuarioZW.getCliente().setCodigo(null);
                clienteService.inserir(key, ClienteZW.toClienteSintetico(usuarioZW.getCliente()), usuarioZW.getFotoKey());
            } else {
                clienteService.refresh(key, clienteSinteticoAtual);
                ClienteSintetico novoClienteSintetico = ClienteZW.toClienteSintetico(usuarioZW.getCliente());
                novoClienteSintetico.setCodigo(clienteSinteticoAtual.getCodigo());
                clienteSinteticoAtual.setProfessorSintetico(professorAtual);
                try {
                    if (!UteisValidacao.emptyString(usuarioZW.getFotoKey())
                            && !usuarioZW.getFotoKey().equals(clienteSinteticoAtual.getPessoa().getFotoKey())) {
                        clienteSinteticoAtual.getPessoa().setFotoKey(usuarioZW.getFotoKey());
                    }
                } catch (Exception e) {
                    Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, e);
                }
                clienteService.alterar(key, novoClienteSintetico, clienteSinteticoAtual);
            }
            ConfiguracaoSistemaService configuracaoService = (ConfiguracaoSistemaService) UtilContext.getBean(
                    ConfiguracaoSistemaService.class);
            configuracaoService.atualizarDataUltimaAtualizacao(key);
            return "ok";
        } else {
            throw new ServiceException("Aluno inválido!");
        }

    }

    private void tratarPerfilUsuario(final String key, UsuarioZW usuarioZW, Usuario usuarioTreino) throws Exception {
        SinteticoService sinteticoService = (SinteticoService) UtilContext.getBean(SinteticoService.class);
        sinteticoService.tratarPerfilUsuario(key, usuarioZW, usuarioTreino);
    }

    @WebMethod(operationName = "inserirCreditos")
    public String inserirCreditos(@WebParam(name = "key") String key,
            @WebParam(name = "colaborador") Integer colaborador,
            @WebParam(name = "creditos") Integer unidadesCreditos,
            @WebParam(name = "recibozw") Integer recibozw,
            @WebParam(name = "dataExpiracao") String dataExpiracao) {
        try {
            Date expiracao = dataExpiracao == null || dataExpiracao.isEmpty() ? null : Uteis.getDate(dataExpiracao);
            CreditoPersonalService creditoService = (CreditoPersonalService) UtilContext.getBean(CreditoPersonalService.class);
            CreditoPersonal inserido = creditoService.inserirCreditos(key, colaborador, unidadesCreditos,
                    recibozw, null, TipoOperacaoPersonalEnum.COMPRA_CREDITOS, expiracao);
            return inserido.getCodigo().toString();
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "estornarCreditos")
    public String estornarCreditos(@WebParam(name = "key") String key,
            @WebParam(name = "recibozw") Integer recibozw) {
        try {
            CreditoPersonalService creditoService = (CreditoPersonalService) UtilContext.getBean(CreditoPersonalService.class);
            creditoService.estornarCreditos(key, recibozw, null);
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "estornarFechamentoCreditos")
    public String estornarFechamentoCreditos(@WebParam(name = "key") String key,
            @WebParam(name = "codigoVenda") Integer codigoVenda) {
        try {
            CreditoPersonalService creditoService = (CreditoPersonalService) UtilContext.getBean(CreditoPersonalService.class);
            creditoService.estornarCreditos(key, null, codigoVenda);
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "atualizarStatusAluno")
    public String atualizarStatusAluno(@WebParam(name = "key") String key,
            @WebParam(name = "codigo") Integer codigo,
            @WebParam(name = "dataAcesso") String dataAcesso,
            @WebParam(name = "empresaAcesso") Integer empresaAcesso) {
        try {
            Date data = Uteis.getDateTime(dataAcesso, null);
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(ClienteSinteticoService.class);
            ClienteSintetico cs = clienteService.obterPorCodigoCliente(key, codigo);
            cs.setDataUltimoacesso(data);
            clienteService.alterarAlgunsCampos(key, cs,  new String[]{"dataUltimoacesso"}, new Object[]{data});

            EmpresaService empresaService = (EmpresaService) UtilContext.getBean(EmpresaService.class);
            Empresa empresa = empresaService.obterPorIdZW(key, empresaAcesso);

            StatusPessoaService statusService = (StatusPessoaService) UtilContext.getBean(StatusPessoaService.class);
            statusService.atualizarStatusAluno(key, cs, false, data, empresa);
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "excluirAluno")
    public String excluirAluno(@WebParam(name = "key") String key,
                               @WebParam(name = "codigoCliente") Integer codigoCliente) {

        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
            clienteService.excluirAluno(key, codigoCliente);
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }
    @WebMethod(operationName = "excluirAlunoMatricula")
    public String excluirAlunoMatricula(@WebParam(name = "key") String key,
                               @WebParam(name = "matricula") Integer matricula) {

        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
            clienteService.excluirAluno(key, matricula);
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "sincronizarProfessor")
    public String sincronizarProfessor(@WebParam(name = "key") String key,
            @WebParam(name = "professor") ProfessorSintetico professor,
            @WebParam(name = "empresa") Integer empresa) {
        try {
            ProfessorSinteticoService professorService = (ProfessorSinteticoService) UtilContext.getBean(
                    ProfessorSinteticoService.class);
            professorService.sincronizarProfessor(key, professor, empresa);
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "alterarProfessor")
    public String alterarProfessor(@WebParam(name = "key") String key,
                                       @WebParam(name = "professor") ProfessorSintetico professor,
                                       @WebParam(name = "empresa") Integer empresa) {
        try {
            ProfessorSinteticoService professorService = (ProfessorSinteticoService) UtilContext.getBean(
                    ProfessorSinteticoService.class);
            professorService.alterarProfessor(key, professor, empresa);
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "enviarNotificacoes")
    public String enviarNotificacoes(@WebParam(name = "key") String key,
            @WebParam(name = "titulo") String titulo,
            @WebParam(name = "mensagem") String mensagem,
            @WebParam(name = "opcoes") String opcoes,
            @WebParam(name = "clientes") String clientes) {
        try {
            NotificacaoService notfService = (NotificacaoService) UtilContext.getBean(
                    NotificacaoService.class);
            String[] clis = clientes.split(";");
            StringBuilder msg = new StringBuilder();
            for(String cliente : clis){
                try {
                    Integer idCliente = Integer.valueOf(cliente);
                    Notificacao notf = notfService.gerarNotificacao(key, idCliente, 
                            Calendario.hoje(),titulo,mensagem, 
                            TipoNotificacaoEnum.CONTATO_CRM, opcoes, false);
                    msg.append(idCliente).append(",").append(notf.getCodigo()).append(";");
                } catch (Exception e) {
                    Integer idCliente = Integer.valueOf(cliente);
                    msg.append(idCliente).append(",ERRO:").append(e.getMessage()).append(";");
                }
            }
            return msg.toString();
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }
    
       @WebMethod(operationName = "sincronizarParq")
    public String sincronizarParq(@WebParam(name = "key") String key,
            @WebParam(name = "codigo") Integer codigo,
            @WebParam(name = "parq") String parq) {
        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
            ClienteSintetico cs = clienteService.obterPorCodigoCliente(key, codigo);
            cs.getPessoa().setParq(Boolean.valueOf(parq));
            clienteService.alterar(key, cs);
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }
    
         @WebMethod(operationName = "sincronizarRiscoCliente")
    public String sincronizarRiscoCliente(@WebParam(name = "key") String key,
            @WebParam(name = "codigo") Integer codigo,
            @WebParam(name = "risco") Integer risco) {
        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
            ClienteSintetico cs = clienteService.obterPorCodigoCliente(key, codigo);
            cs.setPesoRisco(risco);
            clienteService.alterar(key, cs);
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

         
         @WebMethod(operationName = "sincronizarCreditosAluno")
    public String sincronizarCreditosAluno(@WebParam(name = "key") String key,
            @WebParam(name = "codigo") Integer codigoCliente,
            @WebParam(name = "saldo") Integer saldo,
            @WebParam(name = "nrComprados") Integer nrComprados) {
        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
            ClienteSintetico cs = clienteService.obterPorCodigoCliente(key, codigoCliente);
            if(cs != null && !UteisValidacao.emptyNumber(cs.getCodigo())){
                cs.setSaldoCreditoTreino(saldo);
                if(nrComprados != null){
                    cs.setTotalCreditoTreino(nrComprados);
                }
                clienteService.alterar(key, cs);
            }
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }
    
         @WebMethod(operationName = "obterDataUltimaAtualizacao")
    public String obterDataUltimaAtualizacao(@WebParam(name = "key") String key) {
        try {
            ConfiguracaoSistemaService configuracaoService = (ConfiguracaoSistemaService) UtilContext.getBean(
                    ConfiguracaoSistemaService.class);
            Date dataUltimaAtualizacao = configuracaoService.obterDataUltimaAtualizacao(key);
            return Uteis.getData(dataUltimaAtualizacao, "yyyy-MM-dd HH:mm");
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

         @WebMethod(operationName = "obterTodosAlunos")
    public String obterTodosAlunos(@WebParam(name = "key") String key) {
        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
            JSONArray todosAlunos = clienteService.todosAlunos(key);
            return todosAlunos.toString();
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "obterTodosProgramasAlunos")
    public String obterTodosProgramasAlunos(@WebParam(name = "key") String key) {
        try {
            ProgramaTreinoService programaTreinoService = (ProgramaTreinoService) UtilContext.getBean(
                    ProgramaTreinoService.class);
            JSONArray todosProgramasAlunos = programaTreinoService.obterProgramasPorClienteJson(key);
            return todosProgramasAlunos.toString();
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }
    
    @WebMethod(operationName = "incrementarVersaoCliente")
    public String incrementarVersaoCliente(@WebParam(name = "key") String key, @WebParam(name = "usuario") UsuarioZW usuarioZW) {
        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
           clienteService.incrementarVersao(key,usuarioZW.getCliente().getCodigoCliente());
            return "OK";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "atualizarCrossfit")
    public String atualizarCrossfit(@WebParam(name = "key") String key,
                                    @WebParam(name = "matricula") Integer matricula,
                                    @WebParam(name = "crossfit") Boolean crossfit) {
        try {
            ClienteSinteticoService clienteService = (ClienteSinteticoService) UtilContext.getBean(
                    ClienteSinteticoService.class);
            ClienteSintetico clienteSintetico = clienteService.consultarPorMatricula(key, matricula.toString());
            clienteSintetico.setCrossfit(crossfit);
            clienteService.alterar(key, clienteSintetico);
            return "OK";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }
    
    @WebMethod(operationName = "atualizarFrequenciaSemanal")
    public String atualizarFrequenciaSemanal(@WebParam(name = "key") String key,
                                             @WebParam(name = "codigoCliente") Integer codigoCliente,
                                             @WebParam(name = "frequenciaSemanal") Integer frequenciaSemanal){
        try {
            ClienteSinteticoService clienteSinteticoService = (ClienteSinteticoService) UtilContext.getBean( ClienteSinteticoService.class);
            ClienteSintetico cliente = clienteSinteticoService.obterPorCodigoCliente(key,codigoCliente);
            if (cliente != null){
                cliente.setFrequenciaSemanal(frequenciaSemanal);
                clienteSinteticoService.alterarAlgunsCampos(
                        key, cliente,
                        new String[]{"frequenciaSemanal"},
                        new Object[]{ Integer.valueOf(frequenciaSemanal)});
            }
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }
    
    @WebMethod(operationName = "realizouTreinoNovo")
    public String realizouTreinoNovo(@WebParam(name = "key") String key,
                                 @WebParam(name = "codigosClientes") String codigosClientes,
                                 @WebParam(name = "data") String data){
        try {
            Date dataPesquisa = Uteis.getDate(data, "dd/MM/yyyy");
            ProgramaTreinoService programaTreinoService = (ProgramaTreinoService) UtilContext.getBean(ProgramaTreinoService.class);
            
            return programaTreinoService.pesquisarTreinoRelaizadoPorClientes(key, codigosClientes, dataPesquisa);
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();

        }
    }

    @WebMethod(operationName = "obterConfiguracaoSistema")
    public String obterConfiguracaoSistema(@WebParam(name = "key") String key,
                                           @WebParam(name = "configuracao") String configuracao){
        try {
            ConfiguracaoSistemaService css = (ConfiguracaoSistemaService) UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfgPesquisada = css.consultarPorTipo(key, ConfiguracoesEnum.valueOf(configuracao));
            if(cfgPesquisada != null && cfgPesquisada.getCodigo() > 0){
                return cfgPesquisada.getValor();
            }
            return "ERRO: " + "configuração não encontrada";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();

        }
    }

    @WebMethod(operationName = "excluirAlunosOrfaos")
    public String excluirAlunosOrfaos(@WebParam(name = "key") String key,
                                @WebParam(name = "codigosPessoa") String codigosPessoa) {

        try {
            JSONObject jsonCodigos = new JSONObject(codigosPessoa);
            String codigos = String.valueOf(jsonCodigos.get("clientes")).replace("[","").replace("]","");
            ClienteSinteticoService clienteService = UtilContext.getBean(ClienteSinteticoService.class);
            String clienteSintetico = clienteService.consultarOrfaos(key, codigos);
            if (clienteSintetico.equals("")){
                throw new Exception("Nenhum aluno duplicado encontrado.");
            }
            for (String clinteExcluir : clienteSintetico.split(",")) {
                clienteService.excluirAluno(key, Integer.parseInt(clinteExcluir));
            }
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "addAlunoPesquisaTW")
    public String addAlunoPesquisaTW(@WebParam(name = "key") String key,
                                           @WebParam(name = "aluno") String json){
        try {
            JSONObject jsonCliente =  new JSONObject(json);
            ClientePesquisaService clientePesquisaService = (ClientePesquisaService) UtilContext.getBean( ClientePesquisaService.class);
            clientePesquisaService.inserir(key, jsonCliente);
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return montarMensagemErro(ex.getMessage());
        }
    }

    @WebMethod(operationName = "deleteAlunoPesquisaTW")
    public String deleteAlunoPesquisaTW(@WebParam(name = "key") String key,
                      @WebParam(name = "matricula") Integer matricula){
        try {
            ClientePesquisaService clientePesquisaService = (ClientePesquisaService) UtilContext.getBean( ClientePesquisaService.class);
            clientePesquisaService.excluirPorMatricula(key, matricula);
            return "ok";
        } catch (Exception ex) {
            Logger.getLogger(TreinoWS.class.getName()).log(Level.SEVERE, null, ex);
            return montarMensagemErro(ex.getMessage());
        }
    }


}
