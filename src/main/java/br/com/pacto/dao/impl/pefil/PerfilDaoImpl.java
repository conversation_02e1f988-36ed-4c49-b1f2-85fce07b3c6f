/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.pefil;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.perfil.FiltroPerfilJSON;
import br.com.pacto.bean.perfil.Perfil;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.perfil.PerfilDao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.PerfilExcecoes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Repository
public class PerfilDaoImpl extends DaoGenericoImpl<Perfil, Integer> implements
        PerfilDao {

    private static final int MAXIMO_PERFIL_CONSULTAR = 50;

    @Override
    public List<Perfil> consultarPerfil(String ctx, FiltroPerfilJSON filtro, PaginadorDTO paginadorDTO) throws ServiceException {
        getCurrentSession(ctx).clear();
        int maxResults = MAXIMO_PERFIL_CONSULTAR;
        int indeceInicial = 0;
        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_PERFIL_CONSULTAR : paginadorDTO.getSize().intValue();
            indeceInicial = paginadorDTO.getPage() == null || paginadorDTO.getSize() == null ? indeceInicial : paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
        }
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> param = new HashMap<String, Object>();

        hql.append("SELECT obj FROM Perfil obj ");
        if (filtro.getNome() && !StringUtils.isBlank(filtro.getParametro())) {
            where.append("where upper(obj.nome) like CONCAT('%',:nome,'%') ");
            param.put("nome", filtro.getParametro().toUpperCase());
            hql.append(where.toString());
        }
        hql.append(paginadorDTO.getSQLOrderByUse());
        List<Perfil> perfis = null;
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            perfis = findByParam(ctx, hql.toString(), param, maxResults, indeceInicial);
        } catch (Exception ex) {
            throw new ServiceException(PerfilExcecoes.ERRO_AO_BUSCAR_PERFIS, ex);
        }

        paginadorDTO.setSize((long) maxResults);
        paginadorDTO.setPage((long) indeceInicial);
        return perfis;
    }

    @Override
    public List<Perfil> listarPerfis(String ctx) throws ServiceException {
        getCurrentSession(ctx).clear();

        String hql = "SELECT obj FROM Perfil obj";

        List<Perfil> perfis = null;
        try {
            perfis = findByParam(ctx, hql, new HashMap<>(), 0, 0);
        } catch (Exception ex) {
            throw new ServiceException(PerfilExcecoes.ERRO_AO_BUSCAR_PERFIS, ex);
        }

        return perfis;
    }
}