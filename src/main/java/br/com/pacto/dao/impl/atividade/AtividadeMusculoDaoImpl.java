/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.dao.impl.atividade;

import br.com.pacto.bean.atividade.AtividadeMusculo;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.atividade.AtividadeMusculoDao;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Repository
public class AtividadeMusculoDaoImpl extends DaoGenericoImpl<AtividadeMusculo, Integer> implements
        AtividadeMusculoDao {

    @Override
    public List<AtividadeMusculo> obterPorMusculos(String ctx, List<Integer> musculosIds) throws Exception {
        StringBuilder hql = new StringBuilder();
        String selecionados = "";
        for (Integer musculoId : musculosIds) {
            selecionados += "," + musculoId;
        }
        selecionados = selecionados.replaceFirst(",", "");
        hql.append("WHERE obj.musculo.codigo in (").append(selecionados).append(") ");

        return findByParam(ctx, hql, new HashMap<>());
    }
}