package br.com.pacto.dao.intf.parceiro;

import br.com.pacto.bean.parceiro.Parceiro;
import br.com.pacto.dao.intf.base.DaoGenerico;
import br.com.pacto.service.exception.ServiceException;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 28/09/2018.
 */
public interface ParceiroDao extends DaoGenerico<Pa<PERSON><PERSON>, Integer> {

    void removerNotIn(String ctx, List<Parceiro> parceiros) throws Exception;
}
