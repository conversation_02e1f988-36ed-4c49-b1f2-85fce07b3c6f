package br.com.pacto.threads;

import br.com.pacto.notificacao.AbstractNotificadorRecursoSistema;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.notificador.NotificadorRecursoEmpresaServiceControle;
import br.com.pacto.util.bean.RecursoEmpresaTO;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ThreadRecursoEmpresa extends Thread {

    private static final Integer SLEEP_MINUTOS = 5;

    public void run() {
        try {
            Uteis.logar(null, "Iniciei ThreadRecursoEmpresa...");
            while (true) {
                processar();
                Thread.sleep(SLEEP_MINUTOS * 60 * 1000);
                Uteis.logar(null, "Finalizei ThreadRecursoEmpresa...");
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ThreadRecursoEmpresa.class);
        }
    }


    public void processar() {
        try {
            List<RecursoEmpresaTO> notificacoes = new ArrayList<>(NotificadorRecursoEmpresaServiceControle.notificacoes);
            for (RecursoEmpresaTO notf : notificacoes) {
                Uteis.logar(null, String.format("Iniciando Sicroniza��o no OAMD do Recurso Empresa -> (%s)  ....", notf.getRecurso()));
                String response = AbstractNotificadorRecursoSistema.enviarNotificacaoOAMD(notf);

                if (new JSONObject(response).has("sucesso")) {
                    NotificadorRecursoEmpresaServiceControle.removerItem(notf);
                }
            }
            Uteis.logar(null, "Rodada de sincroniza��o dos recursos de empresa com OAMD finalizada ....");
        } catch (Exception e) {
            Uteis.logar(e, ThreadRecursoEmpresa.class);
        }
    }
}
