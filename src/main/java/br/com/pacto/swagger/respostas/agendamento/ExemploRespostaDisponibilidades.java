package br.com.pacto.swagger.respostas.agendamento;

import br.com.pacto.controller.json.agendamento.AgendaDisponibilidadeDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint disponibilidades
 * Representa a estrutura EnvelopeRespostaDTO retornada com lista paginada de disponibilidades de agenda
 */
@ApiModel(description = "Resposta da consulta de disponibilidades de agenda com paginação")
public class ExemploRespostaDisponibilidades extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de disponibilidades de agenda no período e tipo especificados, incluindo informações " +
                             "sobre professor responsá<PERSON>, horá<PERSON><PERSON> disponíveis, ambiente, tipos de atividades e configurações")
    private List<AgendaDisponibilidadeDTO> content;

    public List<AgendaDisponibilidadeDTO> getContent() {
        return content;
    }

    public void setContent(List<AgendaDisponibilidadeDTO> content) {
        this.content = content;
    }
}
