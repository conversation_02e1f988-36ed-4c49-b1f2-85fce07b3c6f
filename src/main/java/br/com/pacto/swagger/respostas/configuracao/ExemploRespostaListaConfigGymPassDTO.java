package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.gympass.ConfigGymPassDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de lista de configurações GymPass")
public class ExemploRespostaListaConfigGymPassDTO {

    @ApiModelProperty(value = "Lista de configurações GymPass")
    private List<ConfigGymPassDTO> content;

    public List<ConfigGymPassDTO> getContent() {
        return content;
    }

    public void setContent(List<ConfigGymPassDTO> content) {
        this.content = content;
    }
}
