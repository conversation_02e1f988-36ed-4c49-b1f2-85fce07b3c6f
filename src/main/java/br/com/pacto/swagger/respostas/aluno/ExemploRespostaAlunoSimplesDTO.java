package br.com.pacto.swagger.respostas.aluno;


import br.com.pacto.controller.json.aluno.AlunoSimplesDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaAlunoSimplesDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private AlunoSimplesDTO content;

    public AlunoSimplesDTO getContent() {
        return content;
    }

    public void setContent(AlunoSimplesDTO content) {
        this.content = content;
    }
}
