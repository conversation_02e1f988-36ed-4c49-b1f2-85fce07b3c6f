package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para cadastro de aluno simplificado")
public class ExemploRespostaCadastroAlunoSimplificado {

    @ApiModelProperty(value = "Dados do aluno cadastrado com sucesso")
    private AlunoResponseTO RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema no cadastro",
                     example = "Erro ao cadastrar aluno: Email já está em uso.")
    private String STATUS_ERRO;

    public AlunoResponseTO getRETURN() {
        return RETURN;
    }

    public void setRETURN(AlunoResponseTO RETURN) {
        this.RETURN = RETURN;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
