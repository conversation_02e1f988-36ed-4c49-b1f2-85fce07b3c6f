package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para verificação de nova avaliação física")
public class ExemploRespostaNovaAvaliacao {

    @ApiModelProperty(value = "Indica se o aluno possui uma nova avaliação física disponível ou pendente. " +
                             "true = possui nova avaliação, false = não possui nova avaliação",
                      example = "true")
    private Boolean nova;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso")
    private Boolean sucesso;

    @ApiModelProperty(value = "Código de status da operação")
    private String STATUS_ERRO;
}
