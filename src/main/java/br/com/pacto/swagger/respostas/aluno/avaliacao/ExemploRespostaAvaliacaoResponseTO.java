package br.com.pacto.swagger.respostas.aluno.avaliacao;


import br.com.pacto.controller.json.aluno.AvaliacaoAlunoResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaAvaliacaoResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private AvaliacaoAlunoResponseDTO content;

    public AvaliacaoAlunoResponseDTO getContent() {
        return content;
    }

    public void setContent(AvaliacaoAlunoResponseDTO content) {
        this.content = content;
    }
}
