package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para geração de PDF da avaliação física")
public class ExemploRespostaGerarPdfAvaliacao {

    @ApiModelProperty(value = "URL para download do PDF da avaliação física gerado. " +
                             "O link é temporário e possui validade limitada para garantir a segurança dos dados.",
                      example = "https://sistema.academia.com/pdfs/avaliacao_1001_20250125.pdf?token=abc123xyz")
    private String urlpdf;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso")
    private Boolean sucesso;

    @ApiModelProperty(value = "Código de status da operação")
    private String STATUS_ERRO;
}
