package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para geração de link de evolução física")
public class ExemploRespostaLinkEvolucao {

    @ApiModelProperty(value = "URL criptografada para acesso à página de evolução física do aluno. " +
                             "O link contém parâmetros seguros que identificam o contexto, empresa, aluno e permissões de acesso.",
                      example = "/evolucaoaluno/avtwXyZ123AbC456DeF789GhI012JkL345MnO678PqR901StU234VwX567YzA890BcD123EfG456HiJ789")
    private String url;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso")
    private Boolean sucesso;

    @ApiModelProperty(value = "Código de status da operação")
    private String STATUS_ERRO;
}
