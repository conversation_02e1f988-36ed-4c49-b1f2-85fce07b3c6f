package br.com.pacto.swagger.respostas.eventoUsuario;

import br.com.pacto.bean.eventoUsuario.EventoUsuario;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;
import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de todos os eventos de usuário")
public class ExemploRespostaListEventoUsuario {

    @ApiModelProperty(value = "Lista de eventos de usuário cadastrados no sistema")
    private List<EventoUsuario> content;

    public ExemploRespostaListEventoUsuario() {
        this.content = Arrays.asList(
            new EventoUsuario("EVT001", "Evento de Treinamento Especial"),
            new EventoUsuario("EVT002", "Workshop de Nutrição"),
            new EventoUsuario("EVT003", "Palestra sobre Exercícios Funcionais")
        );
    }

    public List<EventoUsuario> getContent() {
        return content;
    }

    public void setContent(List<EventoUsuario> content) {
        this.content = content;
    }
}
