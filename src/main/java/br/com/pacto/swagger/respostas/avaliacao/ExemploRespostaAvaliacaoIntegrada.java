package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para consulta de avaliações físicas integradas")
public class ExemploRespostaAvaliacaoIntegrada {

    @ApiModelProperty(value = "Lista de avaliações físicas integradas do aluno")
    private List<AvaliacaoIntegradaJSONExemplo> avaliacoes;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso")
    private Boolean sucesso;

    @ApiModelProperty(value = "Código de status da operação")
    private String STATUS_ERRO;

    public static class AvaliacaoIntegradaJSONExemplo {
        
        @ApiModelProperty(value = "Código único identificador da avaliação integrada", example = "1001")
        private Integer codigo;

        @ApiModelProperty(value = "Data em que a avaliação integrada foi realizada", example = "2025-01-15")
        private String dataAvaliacao;

        @ApiModelProperty(value = "Tipo de avaliação integrada", example = "BIOIMPEDANCIA")
        private String tipoAvaliacao;

        @ApiModelProperty(value = "Nome do equipamento utilizado", example = "InBody 970")
        private String equipamento;

        @ApiModelProperty(value = "Altura do aluno em centímetros", example = "175.5")
        private Double altura;

        @ApiModelProperty(value = "Peso corporal do aluno em quilogramas", example = "70.2")
        private Double peso;

        @ApiModelProperty(value = "Índice de Massa Corporal calculado", example = "22.8")
        private Double imc;

        @ApiModelProperty(value = "Percentual de gordura corporal", example = "15.3")
        private Double percentualGordura;

        @ApiModelProperty(value = "Massa magra em quilogramas", example = "59.5")
        private Double massaMagra;

        @ApiModelProperty(value = "Massa gorda em quilogramas", example = "10.7")
        private Double massaGorda;

        @ApiModelProperty(value = "Água corporal total em litros", example = "43.6")
        private Double aguaCorporalTotal;

        @ApiModelProperty(value = "Taxa metabólica basal em kcal/dia", example = "1650")
        private Integer taxaMetabolicaBasal;

        @ApiModelProperty(value = "Massa muscular esquelética em quilogramas", example = "33.8")
        private Double massaMuscularEsqueletica;

        @ApiModelProperty(value = "Densidade mineral óssea", example = "1.15")
        private Double densidadeMineralOssea;

        @ApiModelProperty(value = "Gordura visceral em nível", example = "8")
        private Integer gorduraVisceral;

        @ApiModelProperty(value = "Nome do profissional responsável pela avaliação", example = "Dr. Carlos Silva")
        private String responsavelAvaliacao;

        @ApiModelProperty(value = "Status da avaliação integrada", example = "PROCESSADA")
        private String status;

        @ApiModelProperty(value = "Observações sobre a avaliação integrada", example = "Dados coletados via bioimpedância com alta precisão")
        private String observacoes;

        @ApiModelProperty(value = "Dados brutos do equipamento em formato JSON", example = "{\"frequencia\":\"50kHz\",\"impedancia\":\"485ohm\"}")
        private String dadosBrutos;

        @ApiModelProperty(value = "Indica se a avaliação foi sincronizada com outros sistemas", example = "true")
        private Boolean sincronizada;
    }
}
