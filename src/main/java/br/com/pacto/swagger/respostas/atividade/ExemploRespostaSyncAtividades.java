package br.com.pacto.swagger.respostas.atividade;

import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para sincronização de atividades por data")
public class ExemploRespostaSyncAtividades {

    @ApiModelProperty(value = "Lista de atividades modificadas desde a data especificada")
    private List<AtividadeJSON> RETURN;

    @ApiModelProperty(value = "Indica sucesso da operação", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Mensagem de erro (quando aplicável)", example = "null")
    private String statusErro;
}
