package br.com.pacto.swagger.respostas.professor;

import br.com.pacto.controller.json.professor.ConfiguracaoRankingDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para configuração de ranking de professores encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaConfiguracaoRanking {

    @ApiModelProperty(value = "Dados da configuração de ranking incluída ou atualizada")
    private ConfiguracaoRankingDTO content;

    public ConfiguracaoRankingDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracaoRankingDTO content) {
        this.content = content;
    }
}
