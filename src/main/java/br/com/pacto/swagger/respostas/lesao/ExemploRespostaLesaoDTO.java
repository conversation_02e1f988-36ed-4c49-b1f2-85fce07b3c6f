package br.com.pacto.swagger.respostas.lesao;

import br.com.pacto.bean.lesao.LesaoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representacao da resposta de status 200 para operacoes com lesao")
public class ExemploRespostaLesaoDTO {

    @ApiModelProperty(value = "Conteudo da resposta contendo os dados da lesao")
    private LesaoDTO content;

    public LesaoDTO getContent() {
        return content;
    }

    public void setContent(LesaoDTO content) {
        this.content = content;
    }
}
