package br.com.pacto.swagger.respostas.aparelho;

import br.com.pacto.controller.json.programa.AparelhoResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de aparelhos encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaListAparelhoResponseTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista paginada contendo dados dos aparelhos encontrados conforme os filtros especificados")
    private List<AparelhoResponseTO> content;

    public List<AparelhoResponseTO> getContent() {
        return content;
    }

    public void setContent(List<AparelhoResponseTO> content) {
        this.content = content;
    }
}
