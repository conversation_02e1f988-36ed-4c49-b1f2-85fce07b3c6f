package br.com.pacto.swagger.respostas.atividade;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaDesativarAtividadesIA {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas", example = "2 atividades com idia e/ou idia2 preenchidos foram desativadas com sucesso.")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
