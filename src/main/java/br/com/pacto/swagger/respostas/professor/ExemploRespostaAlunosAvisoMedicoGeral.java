package br.com.pacto.swagger.respostas.professor;

import br.com.pacto.controller.json.professor.ProfessoresAlunosAvisoMedicoResponseDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para alunos com aviso médico - visão geral encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaAlunosAvisoMedicoGeral extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista contendo informações gerais sobre professores com alunos que possuem aviso médico")
    private List<ProfessoresAlunosAvisoMedicoResponseDTO> content;

    public List<ProfessoresAlunosAvisoMedicoResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<ProfessoresAlunosAvisoMedicoResponseDTO> content) {
        this.content = content;
    }
}
