package br.com.pacto.swagger.respostas.cliente.mensagem;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaClienteMensagem {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas", example = "Cartão Vencido")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
