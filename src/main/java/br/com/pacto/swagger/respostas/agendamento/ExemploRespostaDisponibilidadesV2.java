package br.com.pacto.swagger.respostas.agendamento;

import br.com.pacto.controller.json.agendamento.AgendaDisponibilidadeDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint disponibilidadesV2
 * Representa a estrutura EnvelopeRespostaDTO retornada com lista paginada de disponibilidades de agenda (versão 2)
 */
@ApiModel(description = "Resposta da consulta de disponibilidades de agenda (versão 2) com paginação")
public class ExemploRespostaDisponibilidadesV2 extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de disponibilidades de agenda no período e tipo especificados utilizando algoritmo aprimorado, " +
                             "incluindo informações sobre professor respons<PERSON>, hor<PERSON><PERSON><PERSON> disponí<PERSON>, ambiente, tipos de atividades " +
                             "e configurações com melhor tratamento de sobreposições e conflitos")
    private List<AgendaDisponibilidadeDTO> content;

    public List<AgendaDisponibilidadeDTO> getContent() {
        return content;
    }

    public void setContent(List<AgendaDisponibilidadeDTO> content) {
        this.content = content;
    }
}
