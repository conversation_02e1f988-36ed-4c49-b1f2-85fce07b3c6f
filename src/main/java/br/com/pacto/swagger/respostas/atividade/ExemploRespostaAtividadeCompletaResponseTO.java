package br.com.pacto.swagger.respostas.atividade;


import br.com.pacto.bean.atividade.AtividadeCompletaResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaAtividadeCompletaResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas", example = "Email enviado!")
    private AtividadeCompletaResponseTO content;

    public AtividadeCompletaResponseTO getContent() {
        return content;
    }

    public void setContent(AtividadeCompletaResponseTO content) {
        this.content = content;
    }
}
