package br.com.pacto.swagger.respostas.crossfit;

import br.com.pacto.controller.json.gestao.BICrossfitDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de dados de Business Intelligence do Crossfit")
public class ExemploRespostaBICrossfitDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo os dados consolidados de BI do Crossfit")
    private BICrossfitDTO content;

    public BICrossfitDTO getContent() {
        return content;
    }

    public void setContent(BICrossfitDTO content) {
        this.content = content;
    }
}
