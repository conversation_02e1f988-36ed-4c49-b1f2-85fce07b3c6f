package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para recarregamento de configurações de rede")
public class ExemploRespostaReloadRede {

    @ApiModelProperty(value = "Mensagem de confirmação do recarregamento das configurações de rede", 
                     example = "Configurações de rede recarregadas com sucesso! Instance: treino-prod-01")
    private String RETURN;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }
}
