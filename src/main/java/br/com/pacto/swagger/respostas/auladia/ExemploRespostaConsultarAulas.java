package br.com.pacto.swagger.respostas.auladia;

import br.com.pacto.controller.json.aulaDia.AulaDiaJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint consultarAulas
 * Representa a estrutura do ModelMap retornado pelo endpoint
 */
@ApiModel(description = "Resposta da consulta de aulas do dia")
public class ExemploRespostaConsultarAulas {

    @ApiModelProperty(value = "Lista de aulas disponíveis no dia consultado com informações detalhadas de hor<PERSON>, professores, modalidades e disponibilidade de vagas")
    private List<AulaDiaJSON> aulas;

    public List<AulaDiaJSON> getAulas() {
        return aulas;
    }

    public void setAulas(List<AulaDiaJSON> aulas) {
        this.aulas = aulas;
    }
}
