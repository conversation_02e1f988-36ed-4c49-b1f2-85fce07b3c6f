package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.controller.json.agendamento.PerfilDISCVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para busca do perfil DISC")
public class ExemploRespostaBuscarPerfilDISC {

    @ApiModelProperty(value = "Lista de perfis DISC do cliente")
    private List<PerfilDISCVO> content;

    public List<PerfilDISCVO> getContent() {
        return content;
    }

    public void setContent(List<PerfilDISCVO> content) {
        this.content = content;
    }
}
