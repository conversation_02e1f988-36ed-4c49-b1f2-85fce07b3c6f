package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para consulta de todas as avaliações físicas por período")
public class ExemploRespostaTodasAvaliacoesPorPeriodo {

    @ApiModelProperty(value = "Lista de avaliações físicas realizadas no período especificado")
    private List<AvaliacaoFisicaDTOExemplo> avaliacoes;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso")
    private Boolean sucesso;

    @ApiModelProperty(value = "Código de status da operação")
    private String STATUS_ERRO;

    public static class AvaliacaoFisicaDTOExemplo {
        
        @ApiModelProperty(value = "Código único identificador da avaliação física", example = "1001")
        private Integer codigo;

        @ApiModelProperty(value = "Data em que a avaliação física foi realizada", example = "2025-01-15")
        private String dataAvaliacao;

        @ApiModelProperty(value = "Nome completo do aluno avaliado", example = "João Silva Santos")
        private String nomeAluno;

        @ApiModelProperty(value = "Matrícula do aluno no sistema", example = "2024001")
        private String matriculaAluno;

        @ApiModelProperty(value = "Altura do aluno em centímetros", example = "175.5")
        private Double altura;

        @ApiModelProperty(value = "Peso corporal do aluno em quilogramas", example = "70.2")
        private Double peso;

        @ApiModelProperty(value = "Índice de Massa Corporal calculado", example = "22.8")
        private Double imc;

        @ApiModelProperty(value = "Percentual de gordura corporal", example = "15.3")
        private Double percentualGordura;

        @ApiModelProperty(value = "Massa magra em quilogramas", example = "59.5")
        private Double massaMagra;

        @ApiModelProperty(value = "Massa gorda em quilogramas", example = "10.7")
        private Double massaGorda;

        @ApiModelProperty(value = "Nome do profissional responsável pela avaliação", example = "Dr. Carlos Silva")
        private String responsavelAvaliacao;

        @ApiModelProperty(value = "Código do profissional responsável", example = "501")
        private Integer codigoResponsavel;

        @ApiModelProperty(value = "Status da avaliação física", example = "FINALIZADA")
        private String status;

        @ApiModelProperty(value = "Tipo de avaliação realizada", example = "COMPLETA")
        private String tipoAvaliacao;

        @ApiModelProperty(value = "Observações gerais sobre a avaliação física", example = "Aluno apresentou boa evolução nos indicadores")
        private String observacoes;

        @ApiModelProperty(value = "Código da empresa onde foi realizada a avaliação", example = "1")
        private Integer empresaCodigo;

        @ApiModelProperty(value = "Nome da empresa onde foi realizada a avaliação", example = "Academia Fitness Center")
        private String empresaNome;
    }
}
