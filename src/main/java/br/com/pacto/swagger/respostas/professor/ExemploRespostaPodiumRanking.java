package br.com.pacto.swagger.respostas.professor;

import br.com.pacto.controller.json.professor.RankingProfessoresResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para pódium do ranking de professores encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaPodiumRanking {

    @ApiModelProperty(value = "Lista contendo os professores com melhor desempenho no ranking (pódium)")
    private List<RankingProfessoresResponseDTO> content;

    public List<RankingProfessoresResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<RankingProfessoresResponseDTO> content) {
        this.content = content;
    }
}
