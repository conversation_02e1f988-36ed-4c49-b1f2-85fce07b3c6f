package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para processamento das respostas do questionário PAR-Q")
public class ExemploRespostaResponderParQ {

    @ApiModelProperty(value = "Mensagem de confirmação do processamento das respostas do PAR-Q", 
                      example = "sucesso")
    private String RETURN;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso")
    private Boolean sucesso;

    @ApiModelProperty(value = "Código de status da operação")
    private String STATUS_ERRO;
}
