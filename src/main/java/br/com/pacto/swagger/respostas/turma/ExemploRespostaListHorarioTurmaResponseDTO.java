package br.com.pacto.swagger.respostas.turma;

import br.com.pacto.controller.json.turma.HorarioTurmaResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para lista de horários de turma")
public class ExemploRespostaListHorarioTurmaResponseDTO {

    @ApiModelProperty(value = "Lista de horários da turma")
    private List<HorarioTurmaResponseDTO> content;

    public List<HorarioTurmaResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<HorarioTurmaResponseDTO> content) {
        this.content = content;
    }
}
