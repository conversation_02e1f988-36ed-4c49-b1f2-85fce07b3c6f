package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para consulta da evolução física do aluno")
public class ExemploRespostaEvolucaoFisica {

    @ApiModelProperty(value = "Dados da evolução física do aluno")
    private EvolucaoFisicaExemplo RETURN;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso")
    private Boolean sucesso;

    @ApiModelProperty(value = "Código de status da operação")
    private String STATUS_ERRO;

    public static class EvolucaoFisicaExemplo {
        
        @ApiModelProperty(value = "Nome completo do aluno", example = "João Silva Santos")
        private String nomeAluno;

        @ApiModelProperty(value = "Matrícula do aluno no sistema", example = "2024001")
        private String matricula;

        @ApiModelProperty(value = "Data da primeira avaliação física", example = "2024-01-15")
        private String dataPrimeiraAvaliacao;

        @ApiModelProperty(value = "Data da última avaliação física", example = "2025-01-15")
        private String dataUltimaAvaliacao;

        @ApiModelProperty(value = "Número total de avaliações realizadas", example = "5")
        private Integer totalAvaliacoes;

        @ApiModelProperty(value = "Evolução do peso corporal ao longo do tempo")
        private List<PontoEvolucaoExemplo> evolucaoPeso;

        @ApiModelProperty(value = "Evolução do percentual de gordura ao longo do tempo")
        private List<PontoEvolucaoExemplo> evolucaoPercentualGordura;

        @ApiModelProperty(value = "Evolução da massa magra ao longo do tempo")
        private List<PontoEvolucaoExemplo> evolucaoMassaMagra;

        @ApiModelProperty(value = "Evolução do IMC ao longo do tempo")
        private List<PontoEvolucaoExemplo> evolucaoIMC;

        @ApiModelProperty(value = "Evolução das circunferências corporais")
        private List<PontoEvolucaoExemplo> evolucaoCircunferencias;

        @ApiModelProperty(value = "Análise comparativa entre primeira e última avaliação")
        private ComparativoEvolucaoExemplo comparativo;

        @ApiModelProperty(value = "Metas estabelecidas para o aluno")
        private List<MetaExemplo> metas;

        @ApiModelProperty(value = "Observações gerais sobre a evolução", example = "Aluno apresentou excelente progresso na redução do percentual de gordura e ganho de massa magra")
        private String observacoesGerais;
    }

    public static class PontoEvolucaoExemplo {
        
        @ApiModelProperty(value = "Data da medição", example = "2025-01-15")
        private String data;

        @ApiModelProperty(value = "Valor medido", example = "70.2")
        private Double valor;

        @ApiModelProperty(value = "Unidade de medida", example = "kg")
        private String unidade;
    }

    public static class ComparativoEvolucaoExemplo {
        
        @ApiModelProperty(value = "Variação do peso em quilogramas", example = "-3.5")
        private Double variacaoPeso;

        @ApiModelProperty(value = "Variação do percentual de gordura", example = "-4.2")
        private Double variacaoPercentualGordura;

        @ApiModelProperty(value = "Variação da massa magra em quilogramas", example = "+2.1")
        private Double variacaoMassaMagra;

        @ApiModelProperty(value = "Variação do IMC", example = "-1.8")
        private Double variacaoIMC;

        @ApiModelProperty(value = "Período de evolução em dias", example = "365")
        private Integer diasEvolucao;
    }

    public static class MetaExemplo {
        
        @ApiModelProperty(value = "Descrição da meta", example = "Reduzir percentual de gordura para 12%")
        private String descricao;

        @ApiModelProperty(value = "Valor atual", example = "15.3")
        private Double valorAtual;

        @ApiModelProperty(value = "Valor meta", example = "12.0")
        private Double valorMeta;

        @ApiModelProperty(value = "Percentual de progresso da meta", example = "68.5")
        private Double percentualProgresso;

        @ApiModelProperty(value = "Status da meta", example = "EM_PROGRESSO")
        private String status;
    }
}
