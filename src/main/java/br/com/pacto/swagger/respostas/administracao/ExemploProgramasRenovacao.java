package br.com.pacto.swagger.respostas.administracao;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint de programas para renovação
 */
public class ExemploProgramasRenovacao {
    
    private String sucesso;
    private List<ProgramaTreinoExemplo> programas;
    
    public ExemploProgramasRenovacao() {
        this.sucesso = "sucesso";
        this.programas = new ArrayList<>();
        
        ProgramaTreinoExemplo programa1 = new ProgramaTreinoExemplo();
        programa1.setCod(101);
        programa1.setNome("Programa Hipertrofia - <PERSON>");
        programa1.setObjetivos("Ganho de massa muscular e força");
        programa1.setRestricoes("Evitar exercícios de impacto");
        programa1.setNomeMetodo("Método Tradicional");
        programa1.setDescricaoMetodo("Treino com foco em hipertrofia muscular");
        programa1.setVersao("1.0");
        programa1.setProfessorCarteira("Prof. <PERSON>");
        programa1.setProfessorMontou("Prof. <PERSON>");
        programa1.setUserName("joao.silva");
        programa1.setCref("CREF 123456-G/SP");
        programa1.setCrefProfessorMontou("CREF 789012-G/SP");
        programa1.setRevisado("Não");
        programa1.setMensagemAviso("Programa próximo ao vencimento - agendar renovação");
        programa1.setEmRevisaoProfessor(false);
        programa1.setGeradoPorIA(false);
        
        ProgramaTreinoExemplo programa2 = new ProgramaTreinoExemplo();
        programa2.setCod(102);
        programa2.setNome("Programa Emagrecimento - Ana Costa");
        programa2.setObjetivos("Perda de peso e condicionamento físico");
        programa2.setRestricoes("Limitações no joelho direito");
        programa2.setNomeMetodo("Método Funcional");
        programa2.setDescricaoMetodo("Treino funcional para emagrecimento");
        programa2.setVersao("2.1");
        programa2.setProfessorCarteira("Prof. Ana Costa");
        programa2.setProfessorMontou("Prof. Pedro Oliveira");
        programa2.setUserName("ana.costa");
        programa2.setCref("CREF 345678-G/SP");
        programa2.setCrefProfessorMontou("CREF 901234-G/SP");
        programa2.setRevisado("Sim");
        programa2.setMensagemAviso("Programa necessita renovação em 7 dias");
        programa2.setEmRevisaoProfessor(true);
        programa2.setGeradoPorIA(true);
        
        this.programas.add(programa1);
        this.programas.add(programa2);
    }
    
    public String getSucesso() {
        return sucesso;
    }
    
    public void setSucesso(String sucesso) {
        this.sucesso = sucesso;
    }
    
    public List<ProgramaTreinoExemplo> getProgramas() {
        return programas;
    }
    
    public void setProgramas(List<ProgramaTreinoExemplo> programas) {
        this.programas = programas;
    }
    
    public static class ProgramaTreinoExemplo {
        private Integer cod;
        private String nome;
        private String objetivos;
        private String restricoes;
        private String nomeMetodo;
        private String descricaoMetodo;
        private String versao;
        private String professorCarteira;
        private String professorMontou;
        private String userName;
        private String cref;
        private String crefProfessorMontou;
        private String revisado;
        private String mensagemAviso;
        private Boolean emRevisaoProfessor;
        private Boolean geradoPorIA;
        
        // Getters e Setters
        public Integer getCod() { return cod; }
        public void setCod(Integer cod) { this.cod = cod; }
        
        public String getNome() { return nome; }
        public void setNome(String nome) { this.nome = nome; }
        
        public String getObjetivos() { return objetivos; }
        public void setObjetivos(String objetivos) { this.objetivos = objetivos; }
        
        public String getRestricoes() { return restricoes; }
        public void setRestricoes(String restricoes) { this.restricoes = restricoes; }
        
        public String getNomeMetodo() { return nomeMetodo; }
        public void setNomeMetodo(String nomeMetodo) { this.nomeMetodo = nomeMetodo; }
        
        public String getDescricaoMetodo() { return descricaoMetodo; }
        public void setDescricaoMetodo(String descricaoMetodo) { this.descricaoMetodo = descricaoMetodo; }
        
        public String getVersao() { return versao; }
        public void setVersao(String versao) { this.versao = versao; }
        
        public String getProfessorCarteira() { return professorCarteira; }
        public void setProfessorCarteira(String professorCarteira) { this.professorCarteira = professorCarteira; }
        
        public String getProfessorMontou() { return professorMontou; }
        public void setProfessorMontou(String professorMontou) { this.professorMontou = professorMontou; }
        
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        
        public String getCref() { return cref; }
        public void setCref(String cref) { this.cref = cref; }
        
        public String getCrefProfessorMontou() { return crefProfessorMontou; }
        public void setCrefProfessorMontou(String crefProfessorMontou) { this.crefProfessorMontou = crefProfessorMontou; }
        
        public String getRevisado() { return revisado; }
        public void setRevisado(String revisado) { this.revisado = revisado; }
        
        public String getMensagemAviso() { return mensagemAviso; }
        public void setMensagemAviso(String mensagemAviso) { this.mensagemAviso = mensagemAviso; }
        
        public Boolean getEmRevisaoProfessor() { return emRevisaoProfessor; }
        public void setEmRevisaoProfessor(Boolean emRevisaoProfessor) { this.emRevisaoProfessor = emRevisaoProfessor; }
        
        public Boolean getGeradoPorIA() { return geradoPorIA; }
        public void setGeradoPorIA(Boolean geradoPorIA) { this.geradoPorIA = geradoPorIA; }
    }
}
