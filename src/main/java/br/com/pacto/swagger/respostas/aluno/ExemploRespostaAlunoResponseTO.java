package br.com.pacto.swagger.respostas.aluno;


import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaAlunoResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private AlunoResponseTO content;

    public AlunoResponseTO getContent() {
        return content;
    }

    public void setContent(AlunoResponseTO content) {
        this.content = content;
    }
}
