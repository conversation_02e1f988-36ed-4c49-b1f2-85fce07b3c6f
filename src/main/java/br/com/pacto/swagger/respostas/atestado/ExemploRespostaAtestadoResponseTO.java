package br.com.pacto.swagger.respostas.atestado;


import br.com.pacto.controller.json.aluno.AtestadoResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaAtestadoResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private AtestadoResponseTO content;

    public AtestadoResponseTO getContent() {
        return content;
    }

    public void setContent(AtestadoResponseTO content) {
        this.content = content;
    }
}
