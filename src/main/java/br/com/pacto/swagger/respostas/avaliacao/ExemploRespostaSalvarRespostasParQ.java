package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint salvarRespostasParQ
 * Representa a estrutura ModelMap retornada após salvar as respostas do questionário PAR-Q
 */
@ApiModel(description = "Resposta do salvamento das respostas do questionário PAR-Q")
public class ExemploRespostaSalvarRespostasParQ {

    @ApiModelProperty(value = "Resultado do processamento das respostas do PAR-Q. " +
                             "Contém um JSON string com informações sobre o sucesso da operação e se o PAR-Q foi considerado positivo ou negativo. " +
                             "Um PAR-Q positivo indica que o aluno precisa de avaliação médica antes de iniciar atividades físicas.",
                      example = "{\"retorno\":\"sucesso\",\"parqpositivo\":false}")
    private String RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema durante o processamento das respostas do PAR-Q. " +
                             "Este campo só estará presente em caso de erro.",
                      example = "Aluno não encontrado para a matrícula informada")
    private String STATUS_ERRO;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
