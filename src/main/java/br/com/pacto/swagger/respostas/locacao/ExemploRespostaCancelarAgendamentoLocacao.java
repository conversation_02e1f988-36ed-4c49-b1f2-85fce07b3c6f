package br.com.pacto.swagger.respostas.locacao;

import br.com.pacto.controller.json.locacao.ConfigAgendamentoLocacaoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para cancelamento de agendamento de locação encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaCancelarAgendamentoLocacao {

    @ApiModelProperty(value = "Configuração do agendamento de locação após cancelamento")
    private ConfigAgendamentoLocacaoDTO content;


}
