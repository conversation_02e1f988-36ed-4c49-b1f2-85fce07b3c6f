package br.com.pacto.swagger.respostas.locacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Map;

@ApiModel(description = "Representação da resposta de status 200 para adicionar aluno ao horário play encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaAdicionarAlunoHorarioPlay {

    @ApiModelProperty(value = "Mapa contendo os dados do aluno adicionado ao horário play e o código do agendamento de locação criado",
            example = "{\"alunoLocacaoHorarioPlay\": {\"codigo\": 15, \"responsavelLancamento\": 10, \"empresa\": 1, \"cliente\": 123, \"locacaoHorario\": 5, \"ambiente\": 2, \"dia\": \"2024-01-15\", \"checkin\": false}, \"agendamentoLocacaoCodigo\": 45}")
    private Map<String, Object> content;

    public Map<String, Object> getContent() {
        return content;
    }

    public void setContent(Map<String, Object> content) {
        this.content = content;
    }
}
