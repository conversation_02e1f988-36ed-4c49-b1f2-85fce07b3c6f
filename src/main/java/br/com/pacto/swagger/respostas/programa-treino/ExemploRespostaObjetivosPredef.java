package br.com.pacto.swagger.respostas.programatreino;

import br.com.pacto.bean.programa.ObjetivoPredefinido;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint objPredef
 * Representa a estrutura ModelMap retornada com lista de objetivos predefinidos
 */
@ApiModel(description = "Resposta da consulta de objetivos predefinidos")
public class ExemploRespostaObjetivosPredef {

    @ApiModelProperty(value = "Lista completa de objetivos predefinidos disponíveis no sistema para criação de programas de treino")
    private List<ObjetivoPredefinido> RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso a consulta não seja possível", 
                     example = "Erro ao consultar objetivos predefinidos")
    private String statusErro;

    public List<ObjetivoPredefinido> getRETURN() {
        return RETURN;
    }

    public void setRETURN(List<ObjetivoPredefinido> RETURN) {
        this.RETURN = RETURN;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
