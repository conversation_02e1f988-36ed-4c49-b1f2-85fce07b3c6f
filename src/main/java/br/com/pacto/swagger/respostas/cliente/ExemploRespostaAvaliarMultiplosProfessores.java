package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.controller.json.aluno.AvaliacaoProfessorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para avaliação de múltiplos professores")
public class ExemploRespostaAvaliarMultiplosProfessores {

    @ApiModelProperty(value = "Lista de avaliações de professores processadas")
    private List<AvaliacaoProfessorDTO> content;

    public List<AvaliacaoProfessorDTO> getContent() {
        return content;
    }

    public void setContent(List<AvaliacaoProfessorDTO> content) {
        this.content = (List<AvaliacaoProfessorDTO>) content;
    }
}
