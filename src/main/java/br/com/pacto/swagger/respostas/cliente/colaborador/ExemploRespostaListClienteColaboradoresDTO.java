package br.com.pacto.swagger.respostas.cliente.colaborador;


import br.com.pacto.controller.json.gestao.ClienteColaboradoresDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaListClienteColaboradoresDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas", example = "Cartão Vencido")
    private List<ClienteColaboradoresDTO> content;

    public List<ClienteColaboradoresDTO> getContent() {
        return content;
    }

    public void setContent(List<ClienteColaboradoresDTO> content) {
        this.content = content;
    }
}
