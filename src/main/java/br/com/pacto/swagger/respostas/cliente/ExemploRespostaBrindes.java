package br.com.pacto.swagger.respostas.cliente;

import br.com.pacto.service.intf.cliente.BrindesVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de brindes disponíveis")
public class ExemploRespostaBrindes {

    @ApiModelProperty(value = "Lista de brindes disponíveis no programa de fidelidade")
    private List<BrindesVO> content;

    public List<BrindesVO> getContent() {
        return content;
    }

    public void setContent(List<BrindesVO> content) {
        this.content = content;
    }
}
