package br.com.pacto.swagger.respostas.agendamento;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Resposta contendo horários disponíveis para reagendamento em um dia específico")
public class ExemploRespostaHorariosDisponiveis {

    @ApiModelProperty(value = "Lista de horários disponíveis no formato 'HH:mm - HH:mm'", 
                      example = "[\"14:00 - 15:00\", \"15:00 - 16:00\", \"16:00 - 17:00\"]")
    private List<String> horarios;

    @ApiModelProperty(value = "Mensagem de erro, presente apenas quando ocorre algum problema", example = "")
    private String erro;
}
