package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para consulta de avaliações físicas do aluno")
public class ExemploRespostaAvaliacoesFisicas {

    @ApiModelProperty(value = "Lista de avaliações físicas realizadas pelo aluno, ordenadas por data de avaliação")
    private List<AvaliacaoFisicaExemplo> RETURN;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso")
    private Boolean sucesso;

    @ApiModelProperty(value = "Código de status da operação")
    private String STATUS_ERRO;

    public static class AvaliacaoFisicaExemplo {
        
        @ApiModelProperty(value = "Código único identificador da avaliação física", example = "1001")
        private Integer codigo;

        @ApiModelProperty(value = "Data em que a avaliação física foi realizada", example = "2025-01-15")
        private String dataAvaliacao;

        @ApiModelProperty(value = "Altura do aluno em centímetros", example = "175.5")
        private Double altura;

        @ApiModelProperty(value = "Peso corporal do aluno em quilogramas", example = "70.2")
        private Double peso;

        @ApiModelProperty(value = "Índice de Massa Corporal calculado", example = "22.8")
        private Double imc;

        @ApiModelProperty(value = "Percentual de gordura corporal", example = "15.3")
        private Double percentualGordura;

        @ApiModelProperty(value = "Massa magra em quilogramas", example = "59.5")
        private Double massaMagra;

        @ApiModelProperty(value = "Massa gorda em quilogramas", example = "10.7")
        private Double massaGorda;

        @ApiModelProperty(value = "Circunferência da cintura em centímetros", example = "82.0")
        private Double circunferenciaCintura;

        @ApiModelProperty(value = "Circunferência do quadril em centímetros", example = "95.5")
        private Double circunferenciaQuadril;

        @ApiModelProperty(value = "Relação cintura-quadril calculada", example = "0.86")
        private Double relacaoCinturaQuadril;

        @ApiModelProperty(value = "Dobra cutânea tricipital em milímetros", example = "12.5")
        private Double dobraTricipital;

        @ApiModelProperty(value = "Dobra cutânea bicipital em milímetros", example = "8.2")
        private Double dobraBicipital;

        @ApiModelProperty(value = "Dobra cutânea subescapular em milímetros", example = "15.8")
        private Double dobraSubescapular;

        @ApiModelProperty(value = "Dobra cutânea suprailíaca em milímetros", example = "18.3")
        private Double dobraSuprailiaca;

        @ApiModelProperty(value = "Nome do profissional responsável pela avaliação", example = "Dr. Carlos Silva")
        private String responsavelAvaliacao;

        @ApiModelProperty(value = "Observações gerais sobre a avaliação física", example = "Aluno apresentou boa evolução nos indicadores de composição corporal")
        private String observacoes;

        @ApiModelProperty(value = "Status da avaliação física", example = "FINALIZADA")
        private String status;
    }
}
