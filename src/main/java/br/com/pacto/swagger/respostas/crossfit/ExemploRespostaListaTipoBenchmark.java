package br.com.pacto.swagger.respostas.crossfit;

import br.com.pacto.bean.benchmark.TipoBenchmarkResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de todos os tipos de benchmark")
public class ExemploRespostaListaTipoBenchmark {

    @ApiModelProperty(value = "Lista de tipos de benchmark disponíveis no sistema")
    private List<TipoBenchmarkResponseTO> content;

    public List<TipoBenchmarkResponseTO> getContent() {
        return content;
    }

    public void setContent(List<TipoBenchmarkResponseTO> content) {
        this.content = content;
    }
}
