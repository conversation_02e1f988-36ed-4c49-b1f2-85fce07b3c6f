package br.com.pacto.swagger.respostas.professor;

import br.com.pacto.controller.json.colaborador.ProfessorSubstituidoDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para professores substituídos encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaProfessoresSubstituidos extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista contendo informações sobre professores que foram substituídos em aulas")
    private List<ProfessorSubstituidoDTO> content;

    public List<ProfessorSubstituidoDTO> getContent() {
        return content;
    }

    public void setContent(List<ProfessorSubstituidoDTO> content) {
        this.content = content;
    }
}
