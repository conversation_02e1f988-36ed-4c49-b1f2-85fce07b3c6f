package br.com.pacto.swagger.respostas.configuracao;

import br.com.pacto.controller.json.base.ConfiguracoesGeraisDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de configurações gerais")
public class ExemploRespostaConfiguracoesGeraisDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as configurações gerais do sistema")
    private ConfiguracoesGeraisDTO content;

    public ConfiguracoesGeraisDTO getContent() {
        return content;
    }

    public void setContent(ConfiguracoesGeraisDTO content) {
        this.content = content;
    }
}
