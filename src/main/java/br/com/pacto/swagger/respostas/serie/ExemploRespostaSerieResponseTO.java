package br.com.pacto.swagger.respostas.serie;

import br.com.pacto.bean.ficha.SerieResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta contendo os detalhes de uma série")
public class ExemploRespostaSerieResponseTO {

    @ApiModelProperty(value = "Dados da série retornada")
    private SerieResponseTO content;

    public SerieResponseTO getContent() {
        return content;
    }

    public void setContent(SerieResponseTO content) {
        this.content = content;
    }
}
