package br.com.pacto.swagger.respostas.avaliacao;

import br.com.pacto.bean.avaliacao.AvaliacaoFisicaDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de lista de avaliações físicas")
public class ExemploRespostaListAvaliacaoFisicaDTO {

    @ApiModelProperty(value = "Lista contendo todas as avaliações físicas do aluno")
    private List<AvaliacaoFisicaDTO> content;

    public List<AvaliacaoFisicaDTO> getContent() {
        return content;
    }

    public void setContent(List<AvaliacaoFisicaDTO> content) {
        this.content = content;
    }
}
