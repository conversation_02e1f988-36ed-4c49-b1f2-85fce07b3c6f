package br.com.pacto.swagger.respostas.programa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para grupos musculares da ficha")
public class ExemploRespostaGruposMusculares {

    @ApiModelProperty(value = "Lista dos grupos musculares trabalhados na ficha",
            example = "[\"peitorais\", \"deltóide\", \"tríceps\", \"costas\", \"bíceps\", \"quadríceps\", \"glúteos\", \"panturrilha\", \"abdominais\", \"antebraços\"]")
    private List<String> content;

    public List<String> getContent() {
        return content;
    }

    public void setContent(List<String> content) {
        this.content = content;
    }
}
