package br.com.pacto.swagger.respostas.programa;

import br.com.pacto.bean.programa.ProgramaTreinoResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de lista de programas de treino")
public class ExemploRespostaListProgramaTreinoResponseTO {

    @ApiModelProperty(value = "Lista de programas de treino encontrados")
    private List<ProgramaTreinoResponseTO> content;

    public List<ProgramaTreinoResponseTO> getContent() {
        return content;
    }

    public void setContent(List<ProgramaTreinoResponseTO> content) {
        this.content = content;
    }
}
