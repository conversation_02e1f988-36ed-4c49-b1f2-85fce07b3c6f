package br.com.pacto.swagger.respostas.agendamento;

import br.com.pacto.controller.json.agendamento.AlunoLocacaoPlayDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint locacaoPlayDetalhada
 * Representa a estrutura EnvelopeRespostaDTO retornada com lista paginada de alunos da locação play
 */
@ApiModel(description = "Resposta da consulta de alunos de uma locação play detalhada com paginação")
public class ExemploRespostaLocacaoPlayDetalhada extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de alunos participantes da locação play específica, incluindo informações sobre " +
                             "situação do aluno, dados pessoais, unidade de matrícula e status de check-in")
    private List<AlunoLocacaoPlayDTO> content;

    public List<AlunoLocacaoPlayDTO> getContent() {
        return content;
    }

    public void setContent(List<AlunoLocacaoPlayDTO> content) {
        this.content = content;
    }
}
