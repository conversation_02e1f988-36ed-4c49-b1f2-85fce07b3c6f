package br.com.pacto.swagger.respostas.aluno.perfil;

import br.com.pacto.service.impl.cliente.perfil.PerfilAlunoAvaliacaoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaPerfilAlunoAvaliacaoDTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private PerfilAlunoAvaliacaoDTO content;

    public PerfilAlunoAvaliacaoDTO getContent() {
        return content;
    }

    public void setContent(PerfilAlunoAvaliacaoDTO content) {
        this.content = content;
    }
}
