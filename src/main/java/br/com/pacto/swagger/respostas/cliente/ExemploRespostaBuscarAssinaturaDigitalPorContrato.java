package br.com.pacto.swagger.respostas.cliente;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta de assinatura digital por código do contrato")
public class ExemploRespostaBuscarAssinaturaDigitalPorContrato {

    @ApiModelProperty(value = "Dados da assinatura digital do contrato específico em formato JSON. Contém informações detalhadas sobre o contrato consultado, incluindo código do contrato, data de assinatura, status de validação e dados da assinatura digital. Retorna array vazio '[]' se o contrato não possuir assinatura digital ou se a funcionalidade estiver desabilitada.",
                     example = "[{\"codigoContrato\":\"CONT2024001\",\"dataAssinatura\":\"2024-01-15T10:30:00Z\",\"statusValidacao\":\"VALIDA\",\"tipoContrato\":\"MATRICULA\",\"assinaturaBase64\":\"iVBORw0KGgoAAAANSUhEUgAA...\",\"nomeAluno\":\"João Silva Santos\",\"cpfAluno\":\"123.456.789-00\",\"matriculaAluno\":\"12345\",\"valorContrato\":150.00,\"dataVencimento\":\"2024-12-31\"}]")
    private String RETURN;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema na consulta da assinatura digital do contrato",
                     example = "Ocorreu um erro ao buscar contratos do contrato: CONT123")
    private String STATUS_ERRO;

    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }

    public String getSTATUS_ERRO() {
        return STATUS_ERRO;
    }

    public void setSTATUS_ERRO(String STATUS_ERRO) {
        this.STATUS_ERRO = STATUS_ERRO;
    }
}
