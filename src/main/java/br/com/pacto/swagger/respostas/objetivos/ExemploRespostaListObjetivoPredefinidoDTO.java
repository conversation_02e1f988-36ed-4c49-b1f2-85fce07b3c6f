package br.com.pacto.swagger.respostas.objetivos;

import br.com.pacto.bean.programa.ObjetivoPredefinidoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta de todos os objetivos predefinidos")
public class ExemploRespostaListObjetivoPredefinidoDTO {

    @ApiModelProperty(value = "Lista completa de objetivos predefinidos disponíveis")
    private List<ObjetivoPredefinidoDTO> content;

    public List<ObjetivoPredefinidoDTO> getContent() {
        return content;
    }

    public void setContent(List<ObjetivoPredefinidoDTO> content) {
        this.content = content;
    }
}
