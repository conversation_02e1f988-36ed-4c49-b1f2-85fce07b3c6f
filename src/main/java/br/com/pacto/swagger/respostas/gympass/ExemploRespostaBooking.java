package br.com.pacto.swagger.respostas.gympass;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para processamento de booking GymPass")
public class ExemploRespostaBooking {

    @ApiModelProperty(value = "Resultado do processamento do booking", example = "Booking processado com sucesso - ID: BK123456")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
