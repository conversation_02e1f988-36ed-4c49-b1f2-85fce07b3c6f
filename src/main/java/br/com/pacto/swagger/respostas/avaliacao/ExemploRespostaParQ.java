package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para obtenção do questionário PAR-Q")
public class ExemploRespostaParQ {

    @ApiModelProperty(value = "Dados do questionário PAR-Q configurado para a empresa")
    private QuestionarioJSONExemplo parq;

    @ApiModelProperty(value = "Indica se deve apresentar informações sobre a lei PAR-Q do estado", example = "true")
    private Boolean apresentarLeiParq;

    @ApiModelProperty(value = "Sigla do estado para exibição da lei PAR-Q específica", example = "SP")
    private String siglaEstadoLeiParq;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso")
    private Boolean sucesso;

    @ApiModelProperty(value = "Código de status da operação")
    private String STATUS_ERRO;

    public static class QuestionarioJSONExemplo {
        
        @ApiModelProperty(value = "Código único identificador do questionário PAR-Q", example = "1")
        private Integer codigo;

        @ApiModelProperty(value = "Tipo do questionário", example = "parq")
        private String tipoQuestionario;

        @ApiModelProperty(value = "Descrição do questionário PAR-Q", example = "Questionário de Prontidão para Atividade Física")
        private String descricao;

        @ApiModelProperty(value = "Lista de perguntas do questionário PAR-Q")
        private List<PerguntaJSONExemplo> perguntas;
    }

    public static class PerguntaJSONExemplo {
        
        @ApiModelProperty(value = "Código único identificador da pergunta", example = "1")
        private Integer codigo;

        @ApiModelProperty(value = "Tipo da pergunta", example = "MULTIPLA_ESCOLHA")
        private String tipoPergunta;

        @ApiModelProperty(value = "Texto da pergunta do PAR-Q", 
                         example = "Algum médico já disse que você possui um problema do coração e que só deveria realizar atividade física supervisionada por profissionais de saúde?")
        private String descricao;

        @ApiModelProperty(value = "Lista de opções de resposta para a pergunta")
        private List<RespostaPerguntaJSONExemplo> respostas;

        @ApiModelProperty(value = "Indica se a pergunta é obrigatória", example = "true")
        private Boolean obrigatoria;

        @ApiModelProperty(value = "Ordem de exibição da pergunta", example = "1")
        private Integer ordem;
    }

    public static class RespostaPerguntaJSONExemplo {
        
        @ApiModelProperty(value = "Código único identificador da opção de resposta", example = "1")
        private Integer codigo;

        @ApiModelProperty(value = "Texto da opção de resposta", example = "Sim")
        private String descricao;

        @ApiModelProperty(value = "Valor da resposta para processamento", example = "1")
        private String valor;

        @ApiModelProperty(value = "Indica se esta opção representa uma resposta positiva (risco)", example = "true")
        private Boolean respostaPositiva;

        @ApiModelProperty(value = "Ordem de exibição da opção", example = "1")
        private Integer ordem;
    }
}
