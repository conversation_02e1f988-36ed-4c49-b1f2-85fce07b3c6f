package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para URL de relatório PDF de avaliação física")
public class ExemploRespostaUrlPdfAvaliacaoFisica {

    @ApiModelProperty(value = "URL completa do relatório PDF de avaliação física gerado. O arquivo contém dados antropométricos, composição corporal, flexibilidade, capacidade cardiorrespiratória, análise postural e recomendações personalizadas para o aluno.", 
                     example = "https://treino.pacto.com.br/relatorios/avaliacao-fisica/AF_2024_789_Maria_Silva_20240620.pdf")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
