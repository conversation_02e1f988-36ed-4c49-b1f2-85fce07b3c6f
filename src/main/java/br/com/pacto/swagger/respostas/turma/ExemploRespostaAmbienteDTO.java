package br.com.pacto.swagger.respostas.turma;

import br.com.pacto.controller.json.turma.AmbienteDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para ambiente")
public class ExemploRespostaAmbienteDTO {

    @ApiModelProperty(value = "Dados do ambiente cadastrado")
    private AmbienteDTO content;

    public AmbienteDTO getContent() {
        return content;
    }

    public void setContent(AmbienteDTO content) {
        this.content = content;
    }
}
