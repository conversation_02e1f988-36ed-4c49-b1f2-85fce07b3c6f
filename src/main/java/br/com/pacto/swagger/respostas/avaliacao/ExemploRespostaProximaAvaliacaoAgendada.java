package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para consulta da próxima avaliação física agendada")
public class ExemploRespostaProximaAvaliacaoAgendada {

    @ApiModelProperty(value = "Dados da próxima avaliação física agendada")
    private AgendamentoJSONExemplo RETURN;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso")
    private Boolean sucesso;

    @ApiModelProperty(value = "Código de status da operação")
    private String STATUS_ERRO;

    public static class AgendamentoJSONExemplo {
        
        @ApiModelProperty(value = "Código único identificador do agendamento", example = "2001")
        private Integer codigo;

        @ApiModelProperty(value = "Data do agendamento no formato dd/MM/yyyy HH:mm", example = "25/07/2025 09:00")
        private String dataHora;

        @ApiModelProperty(value = "Horário de início da avaliação no formato HH:mm", example = "09:00")
        private String horaInicio;

        @ApiModelProperty(value = "Horário de fim da avaliação no formato HH:mm", example = "10:00")
        private String horaFim;

        @ApiModelProperty(value = "Tipo do evento agendado", example = "Avaliação Física Completa")
        private String tipoEvento;

        @ApiModelProperty(value = "Status atual do agendamento", example = "Confirmado")
        private String status;

        @ApiModelProperty(value = "Nome do profissional responsável pela avaliação", example = "Dr. Carlos Silva")
        private String nomeProfessor;

        @ApiModelProperty(value = "Código do profissional responsável", example = "501")
        private Integer codProfessor;

        @ApiModelProperty(value = "Cor associada ao status do agendamento (hexadecimal)", example = "#28a745")
        private String statusCor;

        @ApiModelProperty(value = "Cor associada ao tipo de evento (hexadecimal)", example = "#007bff")
        private String cor;

        @ApiModelProperty(value = "Código numérico do status do agendamento", example = "1")
        private Integer statusCod;

        @ApiModelProperty(value = "URL da foto do profissional responsável", example = "https://exemplo.com/fotos/professor501.jpg")
        private String fotoProfessor;

        @ApiModelProperty(value = "Timestamp da data/hora do agendamento", example = "1721894400000")
        private Long timestamp;

        @ApiModelProperty(value = "Observações sobre o agendamento", example = "Avaliação de reavaliação semestral")
        private String observacoes;

        @ApiModelProperty(value = "Local onde será realizada a avaliação", example = "Sala de Avaliação Física - Unidade Centro")
        private String local;

        @ApiModelProperty(value = "Duração estimada da avaliação em minutos", example = "60")
        private Integer duracaoMinutos;
    }
}
