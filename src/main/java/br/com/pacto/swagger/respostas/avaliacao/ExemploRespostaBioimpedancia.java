package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para gravação de dados de bioimpedância")
public class ExemploRespostaBioimpedancia {

    @ApiModelProperty(value = "Mensagem de confirmação da gravação dos dados de bioimpedância", 
                      example = "Avaliação gravada com sucesso.")
    private String STATUS_SUCESSO;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso")
    private Boolean sucesso;

    @ApiModelProperty(value = "Código de status da operação")
    private String STATUS_ERRO;
}
