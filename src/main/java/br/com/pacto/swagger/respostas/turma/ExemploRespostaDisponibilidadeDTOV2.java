package br.com.pacto.swagger.respostas.turma;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaDisponibilidadeDTOV2 {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO content;

    public br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO getContent() {
        return content;
    }

    public void setContent(br.com.pacto.controller.json.disponibilidade.DisponibilidadeDTO content) {
        this.content = content;
    }
}
