package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Map;

@ApiModel(description = "Representação da resposta de status 200 para leitura de opções globais")
public class ExemploRespostaReadGlobalOptions {

    @ApiModelProperty(value = "Mapa contendo as opções globais de configuração do sistema")
    private Map<String, Object> RETURN;

    public Map<String, Object> getRETURN() {
        return RETURN;
    }

    public void setRETURN(Map<String, Object> RETURN) {
        this.RETURN = RETURN;
    }
}
