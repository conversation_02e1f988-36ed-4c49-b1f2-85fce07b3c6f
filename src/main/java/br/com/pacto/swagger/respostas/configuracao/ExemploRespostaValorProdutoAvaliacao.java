package br.com.pacto.swagger.respostas.configuracao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para consulta do valor do produto de avaliação")
public class ExemploRespostaValorProdutoAvaliacao {

    @ApiModelProperty(value = "Valor monetário do produto de avaliação física em reais. " +
                             "Retorna 0.0 quando não está configurado para lançar produto de avaliação.", 
                     example = "85.50")
    private Double RETURN;

    public Double getRETURN() {
        return RETURN;
    }

    public void setRETURN(Double RETURN) {
        this.RETURN = RETURN;
    }
}
