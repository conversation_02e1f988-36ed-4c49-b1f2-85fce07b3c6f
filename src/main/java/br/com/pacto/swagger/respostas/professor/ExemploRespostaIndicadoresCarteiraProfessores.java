package br.com.pacto.swagger.respostas.professor;

import br.com.pacto.controller.json.professor.ProfessorIndicadorResponseDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para indicadores da carteira de professores")
public class ExemploRespostaIndicadoresCarteiraProfessores extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista contendo os indicadores da carteira de professores")
    private List<ProfessorIndicadorResponseDTO> content;

    public List<ProfessorIndicadorResponseDTO> getContent() {
        return content;
    }

    public void setContent(List<ProfessorIndicadorResponseDTO> content) {
        this.content = content;
    }
}
