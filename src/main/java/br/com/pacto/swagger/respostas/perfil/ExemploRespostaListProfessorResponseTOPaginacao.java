package br.com.pacto.swagger.respostas.perfil;

import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de professores por perfil")
public class ExemploRespostaListProfessorResponseTOPaginacao extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista contendo os professores que possuem o perfil especificado")
    private List<ProfessorResponseTO> content;

    public List<ProfessorResponseTO> getContent() {
        return content;
    }

    public void setContent(List<ProfessorResponseTO> content) {
        this.content = content;
    }
}
