package br.com.pacto.swagger.respostas.agendamento;

import br.com.pacto.controller.json.agendamento.AlunoTurmaDTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint aulaDetalhada
 * Representa a estrutura EnvelopeRespostaDTO retornada com lista paginada de alunos da aula
 */
@ApiModel(description = "Resposta da consulta de alunos de uma aula detalhada com paginação")
public class ExemploRespostaAulaDetalhada extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista de alunos matriculados na aula específica, incluindo informações completas sobre " +
                             "situação, presença, dados pessoais, equipamentos reservados e métricas de treino quando disponíveis")
    private List<AlunoTurmaDTO> content;

    public List<AlunoTurmaDTO> getContent() {
        return content;
    }

    public void setContent(List<AlunoTurmaDTO> content) {
        this.content = content;
    }
}
