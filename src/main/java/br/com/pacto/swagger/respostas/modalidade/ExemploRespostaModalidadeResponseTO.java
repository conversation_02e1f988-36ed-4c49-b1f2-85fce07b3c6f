package br.com.pacto.swagger.respostas.modalidade;

import br.com.pacto.controller.json.modalidade.ModalidadeResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para modalidade")
public class ExemploRespostaModalidadeResponseTO {

    @ApiModelProperty(value = "Dados da modalidade")
    private ModalidadeResponseTO content;

    public ModalidadeResponseTO getContent() {
        return content;
    }

    public void setContent(ModalidadeResponseTO content) {
        this.content = content;
    }
}
