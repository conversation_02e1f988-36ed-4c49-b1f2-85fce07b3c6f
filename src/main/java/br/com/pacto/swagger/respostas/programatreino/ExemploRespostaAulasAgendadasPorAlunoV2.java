package br.com.pacto.swagger.respostas.programatreino;

import br.com.pacto.controller.json.aulaDia.AulaAlunoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Classe de exemplo para resposta do endpoint consultarAulasAgendadasPorAlunoV2
 * Representa a estrutura EnvelopeRespostaDTO retornada com lista de aulas agendadas (formato V2)
 */
@ApiModel(description = "Resposta da consulta de aulas agendadas por aluno (versão 2 - formato de data dd/MM/yyyy)")
public class ExemploRespostaAulasAgendadasPorAlunoV2 {

    @ApiModelProperty(value = "Lista de aulas agendadas para o aluno no período especificado com data no formato dd/MM/yyyy")
    private List<AulaAlunoDTO> RETURN;

    @ApiModelProperty(value = "Indica se a operação foi executada com sucesso", example = "true")
    private Boolean sucesso;

    @ApiModelProperty(value = "Status da operação", example = "OK")
    private String statusErro;
}
