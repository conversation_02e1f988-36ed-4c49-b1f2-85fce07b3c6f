package br.com.pacto.swagger.respostas.turma;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para validação de horário")
public class ExemploRespostaValidacaoHorario {

    @ApiModelProperty(value = "Resultado da validação se existem alunos no horário", 
                     example = "Existem 5 alunos matriculados neste horário")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
