package br.com.pacto.swagger.respostas.modalidade;

import br.com.pacto.controller.json.modalidade.CoresResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Exemplo de resposta para lista de cores disponíveis")
public class ExemploRespostaListCoresResponseTO {

    @ApiModelProperty(value = "Lista de cores disponíveis no sistema")
    private List<CoresResponseTO> content;

    public List<CoresResponseTO> getContent() {
        return content;
    }

    public void setContent(List<CoresResponseTO> content) {
        this.content = content;
    }
}
