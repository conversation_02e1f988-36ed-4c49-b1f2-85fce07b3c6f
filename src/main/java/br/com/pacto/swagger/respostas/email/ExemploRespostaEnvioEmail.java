package br.com.pacto.swagger.respostas.email;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para envio de email de relatório")
public class ExemploRespostaEnvioEmail {

    @ApiModelProperty(value = "Conteúdo da mensagem de email que foi enviada com sucesso",
            example = "Relatório de alunos inativos gerado em 15/01/2024")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
