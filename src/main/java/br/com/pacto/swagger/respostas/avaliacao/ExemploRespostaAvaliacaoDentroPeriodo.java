package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para verificação de avaliação física no período")
public class ExemploRespostaAvaliacaoDentroPeriodo {

    @ApiModelProperty(value = "Indica se o aluno possui pelo menos uma avaliação física no período especificado. " +
                             "true = possui avaliação no período, false = não possui avaliação no período",
                      example = "true")
    private Boolean RETURN;

    @ApiModelProperty(value = "Indica se a operação foi realizada com sucesso")
    private Boolean sucesso;

    @ApiModelProperty(value = "Código de status da operação")
    private String STATUS_ERRO;
}
