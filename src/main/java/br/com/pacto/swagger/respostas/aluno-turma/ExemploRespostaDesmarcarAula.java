package br.com.pacto.swagger.respostas.alunoturma;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Resposta da operação de desmarcar aula")
public class ExemploRespostaDesmarcarAula {

    @ApiModelProperty(value = "Mensagem de sucesso da operação", example = "Aula desmarcada com sucesso")
    private String statusSucesso;

    @ApiModelProperty(value = "Mensagem de erro caso a operação falhe", example = "Erro: Não foi possível desmarcar a aula")
    private String statusErro;
}
