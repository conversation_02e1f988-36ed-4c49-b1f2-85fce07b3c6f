package br.com.pacto.swagger.respostas.usuario;

import br.com.pacto.service.login.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para operações com token de verificação encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaTokenDTO {

    @ApiModelProperty(value = "Token de verificação gerado para o usuário")
    private TokenDTO content;

    public TokenDTO getContent() {
        return content;
    }

    public void setContent(TokenDTO content) {
        this.content = content;
    }
}
