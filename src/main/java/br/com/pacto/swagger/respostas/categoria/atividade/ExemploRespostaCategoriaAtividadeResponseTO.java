package br.com.pacto.swagger.respostas.categoria.atividade;


import br.com.pacto.bean.atividade.CategoriaAtividadeResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200")
public class ExemploRespostaCategoriaAtividadeResponseTO {

    @ApiModelProperty(value = "Conteúdo da resposta contendo as informações solicitadas")
    private CategoriaAtividadeResponseTO content;

    public CategoriaAtividadeResponseTO getContent() {
        return content;
    }

    public void setContent(CategoriaAtividadeResponseTO content) {
        this.content = content;
    }
}
