package br.com.pacto.swagger.respostas.agendamento;

import br.com.pacto.controller.json.agendamento.TurmaResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta do endpoint confirmarTodasPresencas
 * Representa a estrutura EnvelopeRespostaDTO retornada com dados da turma após confirmação de todas as presenças
 */
@ApiModel(description = "Resposta da confirmação de presença de todos os alunos de uma turma")
public class ExemploRespostaConfirmarTodasPresencas {

    @ApiModelProperty(value = "Dados completos da turma após a confirmação de todas as presenças, incluindo " +
                              "informações dos alunos, professor, modalidade, ambiente e status atualizado das presenças")
    private TurmaResponseDTO content;

    public TurmaResponseDTO getContent() {
        return content;
    }

    public void setContent(TurmaResponseDTO content) {
        this.content = content;
    }
}
