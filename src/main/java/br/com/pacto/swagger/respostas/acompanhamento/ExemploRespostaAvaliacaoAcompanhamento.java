package br.com.pacto.swagger.respostas.acompanhamento;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Exemplo de resposta para avaliação de acompanhamento de treino")
public class ExemploRespostaAvaliacaoAcompanhamento {

    @ApiModelProperty(value = "Mensagem de sucesso da operação", example = "Sua avaliação foi registrada com sucesso!")
    private String sucesso;

    @ApiModelProperty(value = "Status de erro (presente apenas em caso de erro)", example = "null")
    private String statusErro;

    public String getSucesso() {
        return sucesso;
    }

    public void setSucesso(String sucesso) {
        this.sucesso = sucesso;
    }

    public String getStatusErro() {
        return statusErro;
    }

    public void setStatusErro(String statusErro) {
        this.statusErro = statusErro;
    }
}
