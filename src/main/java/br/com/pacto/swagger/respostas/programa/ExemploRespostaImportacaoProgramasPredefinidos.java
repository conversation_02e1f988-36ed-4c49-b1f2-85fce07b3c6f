package br.com.pacto.swagger.respostas.programa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe de exemplo para resposta de importação de programas predefinidos
 */
@ApiModel(description = "Representação da resposta de status 200 para importação de programas predefinidos encapsulada em EnvelopeRespostaDTO")
public class ExemploRespostaImportacaoProgramasPredefinidos {

    @ApiModelProperty(value = "Relatório detalhado do processo de importação de programas predefinidos, incluindo quantidades de programas encontrados, existentes, importados com sucesso e com erro", 
                     example = "Existem 15 programas predefinidos na chave de origem. Na chave destino já existiam 3 com o mesmo nome e não foram importados. 10 programas foram importados com sucesso, e 2 tiveram erro durante a importação")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
