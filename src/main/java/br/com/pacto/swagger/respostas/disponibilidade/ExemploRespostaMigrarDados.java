package br.com.pacto.swagger.respostas.disponibilidade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para migração de dados de agendamento")
public class ExemploRespostaMigrarDados {

    @ApiModelProperty(value = "Resultado da operação de migração de dados. Retorna 'OK' quando a migração é concluída com sucesso.", 
                     example = "OK")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
