/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.json;

import br.com.pacto.controller.json.agendamento.EventoAulaDTO;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.agenda.HistoricoProfessorTurmaVO;
import br.com.pacto.util.AgendadoJSON;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.DiasSemana;
import br.com.pacto.util.json.TipoToleranciaAulaEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(description = "Informações completas de uma agenda de aula")
public class AgendaTotalJSON extends SuperJSON{
    @ApiModelProperty(value = "Código da empresa/academia", example = "1")
    private Integer empresa;

    @ApiModelProperty(value = "Nome da empresa/academia", example = "Academia Fitness Pro")
    private String nomeEmpresa;

    @ApiModelProperty(value = "Chave da logo da empresa", example = "logo_academia_123")
    private String logoEmpresa;

    @ApiModelProperty(value = "Horário de início da aula no formato dd/MM/yyyy HH:mm", example = "15/03/2024 08:00")
    private String inicio;

    @ApiModelProperty(value = "Horário de fim da aula no formato dd/MM/yyyy HH:mm", example = "15/03/2024 09:00")
    private String fim;

    @ApiModelProperty(value = "Título/nome da aula", example = "Musculação Avançada")
    private String titulo;

    @ApiModelProperty(value = "Identificador único da aula", example = "1001")
    private String id;

    @ApiModelProperty(value = "Nome do professor responsável pela aula", example = "João Silva")
    private String responsavel;

    @ApiModelProperty(value = "Código do professor responsável", example = "123")
    private Integer codigoResponsavel;

    @ApiModelProperty(value = "Código da pessoa do professor responsável", example = "456")
    private Integer codigoPessoaResponsavel;

    @ApiModelProperty(value = "Nível da aula", example = "Intermediário")
    private String nivel;

    @ApiModelProperty(value = "Código do nível da aula", example = "2")
    private Integer codigoNivel;

    @ApiModelProperty(value = "Data de início da vigência da aula")
    private Date inicioVigencia;

    @ApiModelProperty(value = "Data de fim da vigência da aula")
    private Date fimVigencia;

    @ApiModelProperty(value = "Dia da semana da aula", example = "Segunda-feira")
    private String diaSemana;

    @ApiModelProperty(value = "Tipo/modalidade da aula", example = "Musculação")
    private String tipo;

    @ApiModelProperty(value = "Identificador da modalidade", example = "10")
    private Integer identificadorModalidade;

    @ApiModelProperty(value = "Código do tipo de aula", example = "5")
    private Integer codigoTipo;

    @ApiModelProperty(value = "Local/ambiente onde a aula acontece", example = "Sala de Musculação A")
    private String local;

    @ApiModelProperty(value = "Código do local/ambiente", example = "3")
    private Integer codigoLocal;

    @ApiModelProperty(value = "Número total de vagas disponíveis na aula", example = "20")
    private Integer nrVagas;

    @ApiModelProperty(value = "Número de vagas já preenchidas", example = "15")
    private Integer nrVagasPreenchidas;

    @ApiModelProperty(value = "Número de vagas preenchidas por alunos experimentais", example = "2")
    private Integer nrVagasPreenchidasExperimental;

    @ApiModelProperty(value = "Indica se a aula está com lotação completa", example = "false")
    private Boolean aulaCheia;

    @ApiModelProperty(value = "Indica se permite aula experimental", example = "true")
    private Boolean permitirAulaExperimental;

    @ApiModelProperty(value = "Tolerância em minutos para entrada na aula", example = "10")
    private Integer tolerancia;

    @ApiModelProperty(value = "Tipo de tolerância aplicada", example = "1")
    private Integer tipoTolerancia;

    @ApiModelProperty(value = "Mensagem adicional sobre a aula", example = "Traga toalha e garrafa de água")
    private String mensagem;

    @ApiModelProperty(value = "Bonificação em pontos para a aula", example = "5.0")
    private Double bonificacao;

    @ApiModelProperty(value = "Pontos bônus da aula", example = "10")
    private Integer pontosBonus;

    @ApiModelProperty(value = "Meta de participantes para a aula", example = "18.0")
    private Double meta;

    @ApiModelProperty(value = "Código do contrato relacionado", example = "789")
    private Integer codigoContrato;

    @ApiModelProperty(value = "Lista de alunos agendados para a aula")
    private List<AgendadoJSON> alunos;

    @ApiModelProperty(value = "Código da aula de origem para reposição", example = "555")
    private Integer codigoAulaOrigemReposicao;

    @ApiModelProperty(value = "Cor da modalidade em hexadecimal", example = "#FF5722")
    private String cor;

    @ApiModelProperty(value = "Cor do texto da modalidade", example = "#FFFFFF")
    private String textoCor;

    //aqui é só pros casos do retorno do aula cheia ao adicionar aluno
    @ApiModelProperty(value = "Número de aulas experimentais", example = "3")
    private Integer aulasExperimentais;

    @ApiModelProperty(value = "Matrícula do aluno", example = "2024001")
    private Integer matricula;

    @ApiModelProperty(value = "Ocupação atual da aula", example = "15")
    private Integer ocupacao;

    @ApiModelProperty(value = "Nome do aluno", example = "Maria Santos")
    private String nomeAluno;

    @ApiModelProperty(value = "Indica se o aluno já marcou 'Eu Quero' para a aula", example = "false")
    private boolean jaMarcouEuQuero;

    @ApiModelProperty(value = "Foto do professor", example = "foto_professor_123.jpg")
    private String fotoProfessor;

    @ApiModelProperty(value = "Foto da modalidade", example = "foto_musculacao.jpg")
    private String fotoModalidade;

    @ApiModelProperty(value = "Indica se deve validar restrições de marcação", example = "true")
    private boolean validarRestricoesMarcacao;

    @ApiModelProperty(value = "Indica se não deve validar modalidade do contrato", example = "false")
    private boolean naoValidarModalidadeContrato;

    @ApiModelProperty(value = "Tolerância para apresentar no app em minutos", example = "15")
    private Integer toleranciaApresentarApp;

    @ApiModelProperty(value = "Indica se é uma aula experimental", example = "false")
    private boolean aulaExperimental = false;

    @ApiModelProperty(value = "Indica se permite aluno de outra empresa", example = "false")
    private boolean permiteAlunoOutraEmpresa = false;

    @ApiModelProperty(value = "Código da turma", example = "101")
    private Integer codigoTurma;

    @ApiModelProperty(value = "Situação da aula. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- AT (ATIVO)\n" +
            "- IN (INATIVO)", example = "AT")
    private String situacao;

    @ApiModelProperty(value = "Data que o aluno entrou na turma")
    private Date dataEntrouTurma;

    @ApiModelProperty(value = "Data que o aluno saiu da turma")
    private Date dataSaiuTurma;

    @ApiModelProperty(value = "Data de lançamento da aula")
    private Date datalancamento;

    @ApiModelProperty(value = "Indica se o aluno está na aula", example = "false")
    private boolean alunoEstaNaAula = false;

    @ApiModelProperty(value = "Indica se existe outra aula marcada no mesmo horário e ambiente", example = "false")
    private boolean existeOutraAulaMarcadaMesmoHorarioEAmbiente;

    @ApiModelProperty(value = "Valor do produto relacionado à aula", example = "50.00")
    private Double valorProduto;

    @ApiModelProperty(value = "Código do produto relacionado", example = "25")
    private Integer codigoProduto;

    @ApiModelProperty(value = "URL do vídeo no YouTube", example = "https://www.youtube.com/watch?v=abc123")
    private String urlVideoYoutube;

    @ApiModelProperty(value = "URL da imagem da aula", example = "https://exemplo.com/imagem_aula.jpg")
    private String imageUrl;

    @ApiModelProperty(value = "Níveis permitidos para a aula", example = "Iniciante,Intermediário")
    private String niveis;

    @ApiModelProperty(value = "Idade máxima permitida em anos", example = "65")
    private Integer idadeMaxima;

    @ApiModelProperty(value = "Idade mínima permitida em anos", example = "16")
    private Integer idadeMinima;

    @ApiModelProperty(value = "Idade máxima permitida em meses", example = "780")
    private Integer idadeMaximaMeses;

    @ApiModelProperty(value = "Idade mínima permitida em meses", example = "192")
    private Integer idadeMinimaMeses;

    @ApiModelProperty(value = "Quantidade máxima de alunos experimentais", example = "5")
    private Integer qtdeMaximaAlunoExperimental;

    @ApiModelProperty(value = "Lista de vídeos vinculados à turma")
    private List<TurmaVideoDTO> linkVideos;

    @ApiModelProperty(value = "Indica se deve bloquear matrículas acima do limite", example = "false")
    private boolean bloquearMatriculasAcimaLimite;

    @ApiModelProperty(value = "Indica se deve visualizar produtos Gympass", example = "true")
    private Boolean visualizarProdutosGympass;;

    @ApiModelProperty(value = "Indica se deve visualizar produtos TotalPass", example = "false")
    private Boolean visualizarProdutosTotalPass;

    @ApiModelProperty(value = "Mapa de equipamentos da aula", example = "Esteira:2,Bicicleta:3")
    private String mapaEquipamentos;

    @ApiModelProperty(value = "Tipo de reserva de equipamento", example = "OBRIGATORIA")
    private String tipoReservaEquipamento;

    public AgendaTotalJSON() {
    }

    public AgendaTotalJSON(Date data, AgendaTotalJSON agenda) {
        this(data, agenda, null);
    }

    public AgendaTotalJSON(EventoAulaDTO eventoAulaDTO,
                           Integer nrVagasPreenchidas) {
        this.inicio = Uteis.getData(Uteis.dataHoraZeradaUTC(eventoAulaDTO.getDia())) + " " + eventoAulaDTO.getHorarioInicio();
        this.fim = Uteis.getData(Uteis.dataHoraZeradaUTC(eventoAulaDTO.getDia())) + " " + eventoAulaDTO.getHorarioFim();
        this.titulo = eventoAulaDTO.getAula().getNome();
        this.id = eventoAulaDTO.getId().toString();
        this.responsavel = eventoAulaDTO.getAula().getProfessor().getNome();
        this.codigoResponsavel = eventoAulaDTO.getAula().getProfessor().getId();
        this.nivel = "";
        this.codigoNivel = 0;
        this.inicioVigencia = Uteis.dataHoraZeradaUTC(eventoAulaDTO.getAula().getDataInicio());
        this.fimVigencia = Uteis.dataHoraZeradaUTC(eventoAulaDTO.getAula().getDataInicio());
        this.diaSemana = DiasSemana.getDiaSemanaNumeral(Calendario.getInstance(Uteis.dataHoraZeradaUTC(eventoAulaDTO.getDia())).get(Calendar.DAY_OF_WEEK)).getChave();
        this.tipo = eventoAulaDTO.getAula().getModalidade().getNome();
        this.identificadorModalidade = eventoAulaDTO.getAula().getModalidade().getId();
        this.codigoTipo = eventoAulaDTO.getAula().getModalidade().getId();
        this.local = eventoAulaDTO.getAula().getAmbiente().getNome();
        this.codigoLocal = eventoAulaDTO.getAula().getAmbiente().getId();
        this.nrVagas = eventoAulaDTO.getAula().getCapacidade();
        this.nrVagasPreenchidas = nrVagasPreenchidas;
        this.nrVagasPreenchidasExperimental = nrVagasPreenchidasExperimental;
        this.aulaCheia = true;
        this.permitirAulaExperimental = true;
        this.tolerancia = eventoAulaDTO.getAula().getToleranciaMin();
        this.mensagem = eventoAulaDTO.getAula().getMensagem();
        this.bonificacao = eventoAulaDTO.getAula().getBonificacao() == null ? 0.0 : eventoAulaDTO.getAula().getBonificacao().doubleValue();
        this.pontosBonus = eventoAulaDTO.getAula().getPontuacaoBonus();
        this.meta = eventoAulaDTO.getAula().getMeta() == null ? 0.0 : eventoAulaDTO.getAula().getMeta().doubleValue();
        this.cor = eventoAulaDTO.getAula().getModalidade().getCor().getValor();
        this.aulasExperimentais = aulasExperimentais;
        this.matricula = null;
        this.ocupacao = 0;
        this.nomeAluno = "";
        this.jaMarcouEuQuero = false;
        this.fotoProfessor = eventoAulaDTO.getAula().getProfessor().getImageUri();
        this.fotoModalidade = "";
        this.validarRestricoesMarcacao = false;
        this.naoValidarModalidadeContrato = false;
        this.aulaExperimental = false;
    }

    public AgendaTotalJSON(Date data, AgendaTotalJSON agenda, HistoricoProfessorTurmaVO historicoProfessorTurmaVO) {
        this.empresa = agenda.empresa;
        this.inicio = Uteis.getData(data) + " "+agenda.inicio;
        this.fim = Uteis.getData(data) + " "+agenda.fim;
        this.titulo = agenda.titulo;
        this.responsavel = historicoProfessorTurmaVO == null ? agenda.responsavel : historicoProfessorTurmaVO.getProfessor().getPessoa().getNome();
        this.codigoResponsavel = historicoProfessorTurmaVO == null ? agenda.codigoResponsavel : historicoProfessorTurmaVO.getProfessor().getCodigo();
        this.tipo = agenda.tipo;
        this.identificadorModalidade = agenda.identificadorModalidade;
        this.codigoTipo = agenda.codigoTipo;
        this.local = agenda.local;
        this.codigoLocal = agenda.codigoLocal;
        this.nivel = agenda.nivel;
        this.codigoNivel = agenda.codigoNivel;
        this.nrVagas = agenda.nrVagas;
        this.inicioVigencia= agenda.inicioVigencia;
        this.fimVigencia= agenda.fimVigencia;
        this.nrVagasPreenchidas = agenda.nrVagasPreenchidas;
        this.cor = agenda.cor;
        this.id = agenda.id;
        this.aulaCheia = agenda.aulaCheia;
        this.permitirAulaExperimental = agenda.permitirAulaExperimental;
        this.qtdeMaximaAlunoExperimental = agenda.qtdeMaximaAlunoExperimental;
        this.nrVagasPreenchidasExperimental = agenda.nrVagasPreenchidasExperimental;
        this.tolerancia = agenda.tolerancia;
        this.tipoTolerancia = agenda.tipoTolerancia;
        this.mensagem = agenda.mensagem;
        this.bonificacao = agenda.bonificacao;
        this.pontosBonus = agenda.pontosBonus;
        this.meta = agenda.meta;
        this.jaMarcouEuQuero = agenda.jaMarcouEuQuero;
        this.diaSemana = agenda.diaSemana;
        this.ocupacao = agenda.ocupacao;
        this.fotoProfessor = historicoProfessorTurmaVO == null ? agenda.fotoProfessor : historicoProfessorTurmaVO.getUrlFoto();
        this.fotoModalidade = agenda.fotoModalidade;
        this.validarRestricoesMarcacao = agenda.validarRestricoesMarcacao;
        this.naoValidarModalidadeContrato = agenda.naoValidarModalidadeContrato;
        this.integracaoSpivi = agenda.integracaoSpivi;
        this.aulaExperimental = agenda.isAulaExperimental();
        this.setCodigoContrato(agenda.getCodigoContrato());
        this.permiteAlunoOutraEmpresa = agenda.isPermiteAlunoOutraEmpresa();
        this.codigoTurma = agenda.getCodigoTurma();
        this.alunoEstaNaAula = agenda.isAlunoEstaNaAula();
        this.urlVideoYoutube = agenda.getUrlVideoYoutube();
        this.imageUrl = agenda.getImageUrl();
        this.linkVideos = agenda.getLinkVideos();
        if(agenda.getVisualizarProdutosGympass() != null){
            this.visualizarProdutosGympass = agenda.getVisualizarProdutosGympass();
        }
        if(agenda.getVisualizarProdutosTotalPass() != null){
            this.visualizarProdutosTotalPass = agenda.getVisualizarProdutosTotalPass();
        }
        if(agenda.getMapaEquipamentos() != null){
            this.mapaEquipamentos = agenda.getMapaEquipamentos();
        }
        if(agenda.getTipoReservaEquipamento() != null){
            this.tipoReservaEquipamento = agenda.getTipoReservaEquipamento();
        }
        try{
            this.situacao = agenda.getSituacao();
            this.dataEntrouTurma = agenda.getDataEntrouTurma();
            this.dataSaiuTurma = agenda.getDataSaiuTurma();
        }catch(Exception ignore){}
        this.bloquearMatriculasAcimaLimite = agenda.isBloquearMatriculasAcimaLimite();
        try{
            this.idadeMinima = agenda.getIdadeMinima();
            this.idadeMaxima = agenda.getIdadeMaxima();
            this.idadeMinimaMeses = agenda.getIdadeMinimaMeses();
            this.idadeMaximaMeses = agenda.getIdadeMaximaMeses();
        }catch (Exception ignored){}

    }

    private boolean integracaoSpivi;

    public boolean isIntegracaoSpivi() {
        return integracaoSpivi;
    }

    public void setIntegracaoSpivi(final boolean integracaoSpivi) {
        this.integracaoSpivi = integracaoSpivi;
    }

    public String getInicio() {
        return inicio;
    }
    
    public Date getDia() throws Exception{
        return Uteis.getDate(inicio, "dd/MM/yyyy HH:mm");
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public Integer getCodigoResponsavel() {
        return codigoResponsavel;
    }

    public void setCodigoResponsavel(Integer codigoResponsavel) {
        this.codigoResponsavel = codigoResponsavel;
    }

    public Integer getCodigoPessoaResponsavel() {
        return codigoPessoaResponsavel;
    }

    public void setCodigoPessoaResponsavel(Integer codigoPessoaResponsavel) {
        this.codigoPessoaResponsavel = codigoPessoaResponsavel;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    public Integer getCodigoLocal() {
        return codigoLocal;
    }

    public void setCodigoLocal(Integer codigoLocal) {
        this.codigoLocal = codigoLocal;
    }

    public Integer getNrVagas() {
        return nrVagas;
    }

    public void setNrVagas(Integer nrVagas) {
        this.nrVagas = nrVagas;
    }

    public Integer getNrVagasPreenchidas() {
        return nrVagasPreenchidas;
    }

    public void setNrVagasPreenchidas(Integer nrVagasPreenchidas) {
        this.nrVagasPreenchidas = nrVagasPreenchidas;
    }

    public Integer getNrVagasPreenchidasExperimental() {
        return nrVagasPreenchidasExperimental;
    }

    public void setNrVagasPreenchidasExperimental(Integer nrVagasPreenchidasExperimental) {
        this.nrVagasPreenchidasExperimental = nrVagasPreenchidasExperimental;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getIdentificadorModalidade() {
        return identificadorModalidade;
    }

    public void setIdentificadorModalidade(Integer identificadorModalidade) {
        this.identificadorModalidade = identificadorModalidade;
    }

    public Integer getCodigoTipo() {
        return codigoTipo;
    }

    public void setCodigoTipo(Integer codigoTipo) {
        this.codigoTipo = codigoTipo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getNomeEmpresa() { return nomeEmpresa; }

    public void setNomeEmpresa(String nomeEmpresa) { this.nomeEmpresa = nomeEmpresa; }

    public String getLogoEmpresa() { return logoEmpresa; }

    public void setLogoEmpresa(String logoEmpresa) { this.logoEmpresa = logoEmpresa; }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public Integer getCodigoNivel() {
        return codigoNivel;
    }

    public void setCodigoNivel(Integer codigoNivel) {
        this.codigoNivel = codigoNivel;
    }

    public Date getInicioVigencia() {
        return inicioVigencia;
    }

    public void setInicioVigencia(Date inicioVigencia) {
        this.inicioVigencia = inicioVigencia;
    }

    public Date getFimVigencia() {
        return fimVigencia;
    }

    public void setFimVigencia(Date fimVigencia) {
        this.fimVigencia = fimVigencia;
    }

    public Boolean getAulaCheia() {
        return aulaCheia;
    }

    public void setAulaCheia(Boolean aulaCheia) {
        this.aulaCheia = aulaCheia;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public Integer getTolerancia() {
        return tolerancia;
    }

    public void setTolerancia(Integer tolerancia) {
        this.tolerancia = tolerancia;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Double getBonificacao() {
        return bonificacao;
    }

    public void setBonificacao(Double bonificacao) {
        this.bonificacao = bonificacao;
    }

    public Integer getPontosBonus() {
        return pontosBonus;
    }

    public void setPontosBonus(Integer pontosBonus) {
        this.pontosBonus = pontosBonus;
    }

    public Double getMeta() {
        return meta;
    }

    public void setMeta(Double meta) {
        this.meta = meta;
    }

    public Integer getAulasExperimentais() {
        return aulasExperimentais;
    }

    public void setAulasExperimentais(Integer aulasExperimentais) {
        this.aulasExperimentais = aulasExperimentais;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public boolean isJaMarcouEuQuero() {
        return jaMarcouEuQuero;
    }

    public void setJaMarcouEuQuero(boolean jaMarcouEuQuero) {
        this.jaMarcouEuQuero = jaMarcouEuQuero;
    }

    public boolean getJaMarcouEuQuero() {
        return this.jaMarcouEuQuero;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Boolean getPermitirAulaExperimental() {
        return permitirAulaExperimental;
    }

    public void setPermitirAulaExperimental(Boolean permitirAulaExperimental) {
        this.permitirAulaExperimental = permitirAulaExperimental;
    }

    public String getFotoProfessor() {
        return fotoProfessor;
    }

    public void setFotoProfessor(String fotoProfessor) {
        this.fotoProfessor = fotoProfessor;
    }

    public String getFotoModalidade() {
        return fotoModalidade;
    }

    public void setFotoModalidade(String fotoModalidade) {
        this.fotoModalidade = fotoModalidade;
    }

    @JsonIgnore
    public String getCodDia() throws Exception{
        return getId() + "_" + Uteis.getData(Uteis.getDate(inicio, "dd/MM/yyyy HH:mm"));
    }

    public boolean isValidarRestricoesMarcacao() {
        return validarRestricoesMarcacao;
    }

    public void setValidarRestricoesMarcacao(boolean validarRestricoesMarcacao) {
        this.validarRestricoesMarcacao = validarRestricoesMarcacao;
    }

    public String getPrimeiroNomeComLetraSobrenome() {
        if (responsavel != null) {
            return Uteis.obterPrimeiroNomeConcatenadoSobreNome(responsavel, false);
        }
        return "";
    }

    public Integer getToleranciaApresentarApp() {
        return toleranciaApresentarApp;
    }

    public void setToleranciaApresentarApp(Integer toleranciaApresentarApp) {
        this.toleranciaApresentarApp = toleranciaApresentarApp;
    }

    public List<AgendadoJSON> getAlunos() {
        return alunos;
    }

    public void setAlunos(List<AgendadoJSON> alunos) {
        this.alunos = alunos;
    }

    public Integer getCodigoAulaOrigemReposicao() {
        return codigoAulaOrigemReposicao;
    }

    public void setCodigoAulaOrigemReposicao(Integer codigoAulaOrigemReposicao) {
        this.codigoAulaOrigemReposicao = codigoAulaOrigemReposicao;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getTextoCor() {
        return textoCor;
    }

    public void setTextoCor(String textoCor) {
        this.textoCor = textoCor;
    }

    public boolean isAulaExperimental() {
        return aulaExperimental;
    }

    public void setAulaExperimental(boolean aulaExperimental) {
        this.aulaExperimental = aulaExperimental;
    }

    public boolean isPermiteAlunoOutraEmpresa() {
        return permiteAlunoOutraEmpresa;
    }

    public void setPermiteAlunoOutraEmpresa(boolean permiteAlunoOutraEmpresa) {
        this.permiteAlunoOutraEmpresa = permiteAlunoOutraEmpresa;
    }

    public Integer getCodigoTurma() {
        return codigoTurma;
    }

    public void setCodigoTurma(Integer codigoTurma) {
        this.codigoTurma = codigoTurma;
    }

    public boolean isNaoValidarModalidadeContrato() {
        return naoValidarModalidadeContrato;
    }

    public void setNaoValidarModalidadeContrato(boolean naoValidarModalidadeContrato) {
        this.naoValidarModalidadeContrato = naoValidarModalidadeContrato;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Date getDataEntrouTurma() {
        return dataEntrouTurma;
    }

    public void setDataEntrouTurmaDt(Date dataEntrouTurma) throws Exception {
        this.dataEntrouTurma = dataEntrouTurma;
    }
    public void setDataEntrouTurma(String dataEntrouTurma) throws Exception {
        try {
            if (!UteisValidacao.emptyString(dataEntrouTurma)) {
                this.dataEntrouTurma = Uteis.getDate(dataEntrouTurma, "yyyy-MM-dd HH:mm:ss");
            } else {
                this.dataEntrouTurma = null;
            }
        }catch (Exception e){
            long timestamp = Long.valueOf(dataEntrouTurma);
            Date dataEntrou = new Timestamp(timestamp);
            this.dataEntrouTurma = Uteis.getDate(dataEntrou.toString(), "yyyy-MM-dd HH:mm:ss");
        }
    }

    public Date getDataSaiuTurma() {
        return dataSaiuTurma;
    }

    public void setDataSaiuTurmaDt(Date dataSaiuTurma) throws Exception {
        this.dataSaiuTurma = dataSaiuTurma;
    }
    public void setDataSaiuTurma(String dataSaiuTurma) throws Exception {
        try {
            if (!UteisValidacao.emptyString(dataSaiuTurma)) {
                this.dataSaiuTurma = Uteis.getDate(dataSaiuTurma, "yyyy-MM-dd HH:mm:ss");
            } else {
                this.dataSaiuTurma = null;
            }
        }catch (Exception e){
            long timestamp = Long.parseLong(dataSaiuTurma);
            Date dataSaiu = new Timestamp(timestamp);
            this.dataSaiuTurma = Uteis.getDate(dataSaiu.toString(), "yyyy-MM-dd HH:mm:ss");
        }
    }

    public Date getDatalancamento() { return datalancamento; }

    public void setDatalancamento(String datalancamento) throws Exception {
        try {
            if (!UteisValidacao.emptyString(datalancamento)) {
                this.datalancamento = Uteis.getDate(datalancamento, "yyyy-MM-dd HH:mm:ss");
            } else {
                this.datalancamento = null;
            }
        }catch (Exception e){
            long timestamp = Long.valueOf(datalancamento);
            Date dataLancou = new Timestamp(timestamp);
            this.datalancamento = Uteis.getDate(dataLancou.toString(), "yyyy-MM-dd HH:mm:ss");
        }
    }

    public Integer getTipoTolerancia() {
        if(tipoTolerancia == null){
            tipoTolerancia = TipoToleranciaAulaEnum.APOS_INICIO.getCodigo();
        }
        return tipoTolerancia;
    }

    public void setTipoTolerancia(Integer tipoTolerancia) {
        this.tipoTolerancia = tipoTolerancia;
    }

    public boolean isAlunoEstaNaAula() {
        return alunoEstaNaAula;
    }

    public void setAlunoEstaNaAula(boolean alunoEstaNaAula) {
        this.alunoEstaNaAula = alunoEstaNaAula;
    }

    public Double getValorProduto() {
        return valorProduto;
    }

    public void setValorProduto(Double valorProduto) {
        this.valorProduto = valorProduto;
    }

    public Integer getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(Integer codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public boolean isExisteOutraAulaMarcadaMesmoHorarioEAmbiente() {
        return existeOutraAulaMarcadaMesmoHorarioEAmbiente;
    }

    public void setExisteOutraAulaMarcadaMesmoHorarioEAmbiente(boolean existeOutraAulaMarcadaMesmoHorarioEAmbiente) {
        this.existeOutraAulaMarcadaMesmoHorarioEAmbiente = existeOutraAulaMarcadaMesmoHorarioEAmbiente;
    }

    public String getUrlVideoYoutube() {
        return urlVideoYoutube;
    }

    public void setUrlVideoYoutube(String urlVideoYoutube) {
        this.urlVideoYoutube = urlVideoYoutube;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getNiveis() {
        return niveis;
    }

    public void setNiveis(String niveis) {
        this.niveis = niveis;
    }

    public Integer getIdadeMaxima() {
        return idadeMaxima;
    }

    public void setIdadeMaxima(Integer idadeMaxima) {
        this.idadeMaxima = idadeMaxima;
    }

    public Integer getIdadeMinima() {
        return idadeMinima;
    }

    public void setIdadeMinima(Integer idadeMinima) {
        this.idadeMinima = idadeMinima;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public Integer getQtdeMaximaAlunoExperimental() {
        return qtdeMaximaAlunoExperimental;
    }

    public void setQtdeMaximaAlunoExperimental(Integer qtdeMaximaAlunoExperimental) {
        this.qtdeMaximaAlunoExperimental = qtdeMaximaAlunoExperimental;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public List<TurmaVideoDTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<TurmaVideoDTO> linkVideos) {
        this.linkVideos = linkVideos;
    }

    public boolean isBloquearMatriculasAcimaLimite() {
        return bloquearMatriculasAcimaLimite;
    }

    public void setBloquearMatriculasAcimaLimite(boolean bloquearMatriculasAcimaLimite) {
        this.bloquearMatriculasAcimaLimite = bloquearMatriculasAcimaLimite;
    }

    public Boolean getVisualizarProdutosGympass() {
        return visualizarProdutosGympass;
    }

    public void setVisualizarProdutosGympass(Boolean visualizarProdutosGympass) {
        this.visualizarProdutosGympass = visualizarProdutosGympass;
    }

    public Boolean getVisualizarProdutosTotalPass() {
        return visualizarProdutosTotalPass;
    }

    public void setVisualizarProdutosTotalPass(Boolean visualizarProdutosTotalPass) {
        this.visualizarProdutosTotalPass = visualizarProdutosTotalPass;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }

    public String getTipoReservaEquipamento() {
        return tipoReservaEquipamento;
    }

    public void setTipoReservaEquipamento(String tipoReservaEquipamento) {
        this.tipoReservaEquipamento = tipoReservaEquipamento;
    }

}
