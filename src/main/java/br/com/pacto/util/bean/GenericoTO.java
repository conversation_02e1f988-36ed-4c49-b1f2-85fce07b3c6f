/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;

import br.com.pacto.util.json.TipoLinhaEnum;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class GenericoTO implements Serializable {

    private Integer codigo = null;
    private String codigoStr = null;
    private boolean escolhido = false;
    private String label = "";
    private String css = "";
    private Integer ordem = 0;
    private String detalhe = "";
    private TipoLinhaEnum tipoLinha;

    public GenericoTO(Integer codigo, String label, String css) {
        this.codigo = codigo;
        this.label = label;
        this.css = css;
    }

    public GenericoTO(Integer codigo, String label) {
        this.codigo = codigo;
        this.label = label;
    }
    public GenericoTO(String codigoStr, String label) {
        this.codigoStr = codigoStr;
        this.label = label;
    }
    
    public GenericoTO(Integer codigo, String label, Integer ordem) {
        this.codigo = codigo;
        this.label = label;
        this.ordem = ordem;
    }

    public GenericoTO(String codigoStr, String label, boolean escolhido) {
        this.codigoStr = codigoStr;
        this.label = label;
        this.escolhido = escolhido;
    }

    public GenericoTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public boolean getEscolhido() {
        return escolhido;
    }

    public void setEscolhido(boolean escolhido) {
        this.escolhido = escolhido;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getCss() {
        return css;
    }

    public void setCss(String css) {
        this.css = css;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getDetalhe() {
        return detalhe;
    }

    public void setDetalhe(String detalhe) {
        this.detalhe = detalhe;
    }

    public String getCodigoStr() {
        return codigoStr;
    }

    public void setCodigoStr(String codigoStr) {
        this.codigoStr = codigoStr;
    }

    public TipoLinhaEnum getTipoLinha() {
        return tipoLinha;
    }

    public void setTipoLinha(TipoLinhaEnum tipo) {
        this.tipoLinha = tipo;
    }
    
    public String getLabelAbreviado() {
        return label == null || label.length() < 20 ? label : (label.substring(0, 17)+"...");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        GenericoTO that = (GenericoTO) o;

        return getCodigo().equals(that.getCodigo());
    }

    @Override
    public int hashCode() {
        return getCodigo().hashCode();
    }
}
