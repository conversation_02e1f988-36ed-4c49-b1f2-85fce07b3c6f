/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util.bean;


public class AgendaJustfitDTO {

    private String turmaNome;

    private String identificador;
    private String diaSemana;
    private String dia;
    private Integer idHorario;

    private String horaInicio;
    private String horaFim;

    private String local;
    private Integer codigoLocal;

    private Integer nrVagas;
    private Integer nrOcupadas;
    private Integer nrDisponiveis;

    private boolean alunoMarcou = false;

    public AgendaJustfitDTO() {
    }

    public AgendaJustfitDTO(AgendaTotalTO agendaTotalTO) {
        this.turmaNome = agendaTotalTO.getTitulo();
        this.idHorario = Integer.parseInt(agendaTotalTO.getId());
        this.identificador = agendaTotalTO.getIdentificador();
        this.diaSemana = agendaTotalTO.getDiaSemana();
        this.dia = agendaTotalTO.getDia();
        this.horaInicio = agendaTotalTO.getInicio();
        this.horaFim = agendaTotalTO.getFim();
        this.local = agendaTotalTO.getLocal();
        this.codigoLocal = agendaTotalTO.getCodigoLocal();
        this.nrVagas = agendaTotalTO.getNrVagas();
        this.nrOcupadas = agendaTotalTO.getNrVagasPreenchidas();
        this.nrDisponiveis = (this.nrVagas - this.nrOcupadas);
    }

    public String getTurmaNome() {
        return turmaNome;
    }

    public void setTurmaNome(String turmaNome) {
        this.turmaNome = turmaNome;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    public Integer getCodigoLocal() {
        return codigoLocal;
    }

    public void setCodigoLocal(Integer codigoLocal) {
        this.codigoLocal = codigoLocal;
    }

    public Integer getNrVagas() {
        return nrVagas;
    }

    public void setNrVagas(Integer nrVagas) {
        this.nrVagas = nrVagas;
    }

    public Integer getNrOcupadas() {
        return nrOcupadas;
    }

    public void setNrOcupadas(Integer nrOcupadas) {
        this.nrOcupadas = nrOcupadas;
    }

    public Integer getNrDisponiveis() {
        return nrDisponiveis;
    }

    public void setNrDisponiveis(Integer nrDisponiveis) {
        this.nrDisponiveis = nrDisponiveis;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(String horaInicio) {
        this.horaInicio = horaInicio;
    }

    public String getHoraFim() {
        return horaFim;
    }

    public void setHoraFim(String horaFim) {
        this.horaFim = horaFim;
    }

    public boolean isAlunoMarcou() {
        return alunoMarcou;
    }

    public void setAlunoMarcou(boolean alunoMarcou) {
        this.alunoMarcou = alunoMarcou;
    }

    public Integer getIdHorario() {
        return idHorario;
    }

    public void setIdHorario(Integer idHorario) {
        this.idHorario = idHorario;
    }
}
