/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.validacao.impl.ficha;

import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.util.UtilContext;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.impl.ValidacaoImpl;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.validacao.intf.ficha.FichaValidacaoService;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class FichaValidacaoServiceImpl implements FichaValidacaoService {

    @Autowired
    private Validacao validacao;

    @Override
    public void validarNomeFichaPreDefinida(final String key, Integer codigo, String nome) throws ValidacaoException, ServiceException {
        FichaService fs = (FichaService) UtilContext.getBean(FichaService.class);
        List<Ficha> fichas = fs.obterPorNome(key, true, nome, codigo);
        if (!fichas.isEmpty()) {
            throw new ValidacaoException("validacao.fichaPredefinida.nomeunico");
        }
    }

    @Override
    public void validarDadosBasicos(Ficha ficha) throws ValidacaoException {
        List<String> msgs = new ArrayList<String>();
        if (validacao.emptyString(ficha.getNome())) {
            msgs.add("obrigatorio.nome");
        }        
        if (msgs.isEmpty()) {
            return;
        }
        throw new ValidacaoException(msgs);
    }
}
