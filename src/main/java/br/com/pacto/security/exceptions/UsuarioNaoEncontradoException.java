package br.com.pacto.security.exceptions;

import br.com.pacto.service.exception.ServiceException;

/**
 * Exceção para usuários não encontrados
 *
 * <AUTHOR>
 * @since 18/07/2018
 */
public class UsuarioNaoEncontradoException extends ServiceException {

    private static final String CHAVE_EXCECAO = "usuario_nao_encontrado";
    private static final String MENSAGEM_EXCECAO = "O usuário não foi encontrado";

    public UsuarioNaoEncontradoException() {
        super(CHAVE_EXCECAO, MENSAGEM_EXCECAO);
    }

    public UsuarioNaoEncontradoException(Throwable causa) {
        super(CHAVE_EXCECAO, MENSAGEM_EXCECAO, causa);
    }

}
