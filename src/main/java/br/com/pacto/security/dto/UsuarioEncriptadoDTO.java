package br.com.pacto.security.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados do usuário encriptado para geração de token ZW")
public class UsuarioEncriptadoDTO {

    @ApiModelProperty(value = "Chave de identificação da empresa no sistema", example = "ACADEMIA123", required = true)
    private String chave;

    @ApiModelProperty(value = "Código do usuário encriptado para validação", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9", required = true)
    private String codigo;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }
}
