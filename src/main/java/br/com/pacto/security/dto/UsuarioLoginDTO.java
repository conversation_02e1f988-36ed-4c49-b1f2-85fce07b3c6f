package br.com.pacto.security.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * DTO de Login no sistema
 *
 * <AUTHOR>
 * @since 19/07/2018
 */
@ApiModel(description = "Dados necessários para autenticação do usuário no sistema")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UsuarioLoginDTO {

    @ApiModelProperty(value = "Chave de identificação da empresa no sistema", example = "ACADEMIA123", required = true)
    private String chave;

    @ApiModelProperty(value = "Nome de usuário ou email para autenticação", example = "<EMAIL>", required = true)
    private String username;

    @ApiModelProperty(value = "Senha do usuário para autenticação", example = "minhasenha123", required = true)
    private String senha;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }
}
