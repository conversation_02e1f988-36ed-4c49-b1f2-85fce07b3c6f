package br.com.pacto.security.service;

import br.com.pacto.security.dto.UsuarioEncriptadoDTO;
import br.com.pacto.security.dto.UsuarioLoginV2DTO;
import br.com.pacto.service.exception.ServiceException;

/**
 * Contrato para os serviços de Login
 *
 * <AUTHOR>
 * @since 18/07/2018
 */
public interface LoginService {

    /**
     * @param chave   Chave de LOGIN do usuário
     * @param username Nome ou email de usuário
     * @param senha   Senha do usuário
     * @return O TOKEN do usuário loggado no sistema
     */
    String login(String chave, String username, String senha, Boolean isEncript) throws ServiceException;

    String validarUsuarioMovel(String chave, String username) throws ServiceException;

    String gerarTokenZw(UsuarioEncriptadoDTO dados) throws ServiceException;

    UsuarioAutenticadoDTO login(String chave, String username, String senha) throws ServiceException;

    UsuarioAutenticadoDTO usuarioZw(String chave, Integer usuarioZw) throws ServiceException;

    void addUsuarioSessao(String chave, UsuarioAutenticadoDTO usuario) throws ServiceException;

    String app(String chave, String username) throws ServiceException;
    String app(String chave, Integer codUsuario) throws ServiceException;

    UsuarioAutenticadoDTO loginV2(final UsuarioLoginV2DTO dto) throws ServiceException;

}
