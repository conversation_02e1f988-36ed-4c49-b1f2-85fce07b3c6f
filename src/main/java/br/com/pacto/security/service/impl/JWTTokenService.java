package br.com.pacto.security.service.impl;

import br.com.pacto.bean.perfil.permissao.Permissao;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.exceptions.SecretException;
import br.com.pacto.security.exceptions.TokenExpiradoException;
import br.com.pacto.security.exceptions.TokenInvalidoException;
import br.com.pacto.security.service.TokenService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.apache.commons.io.FileUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.adm.client.EmpresaWS;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR> Karlus
 * @since 19/07/2018
 */
@Service
public class JWTTokenService implements TokenService {

    @Autowired
    private UsuarioService usuarioService;
    private static final String EMITENTE = "aut_pacto";
    private final String CLAIM_CONTENT = "content";
    private static final String CLAIM_CHAVE = "chave";
    private static final String CLAIM_USERNAME = "username";
    private static final String PROP_COD_ZW = "cz";
    private static final String PROP_COD_TR = "ct";
    private static final String CLAIM_EMPRESAS = "empresas";
    private static final String CLAIM_COLABORADORID = "colaboradorid";
    private static final String CLAIM_PROVIDER = "provider";
    private String PROP_PERSONA_PERMISSOES = "p";
    private String PROP_PERSONA_DESCRICAO = "d";
    private Algorithm algoritimo;
    private JWTVerifier verificador;
    private final Long tempoExpiracao;
    private static String authSecret = null;

    private Map<String, UsuarioSimplesDTO> usuarios;

    public void reload() {
        authSecret = null;
        algoritimo = Algorithm.HMAC256(authSecret());
        verificador = JWT.require(algoritimo).withIssuer(EMITENTE).build();
        authSecret();
    }

    public static String authSecret() {
        if (UteisValidacao.emptyString(authSecret)) {
            String path_id_persona = Aplicacao.getProp(Aplicacao.AUTH_SECRET_PATH);
            if (!UteisValidacao.emptyString(path_id_persona) && !path_id_persona.contains("@")) {
                try {
                    authSecret = FileUtils.readFileToString(new File(path_id_persona));
                    authSecret = authSecret.replace("\n", "").replace("\r", "");
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        if (UteisValidacao.emptyString(authSecret)) {
            Uteis.logarDebug("auth secret vazia");
        }
        return authSecret;
    }

    public JWTTokenService() {
        this.algoritimo = Algorithm.HMAC256(authSecret());
        if(Aplicacao.isAmbienteTeste()){
            this.tempoExpiracao = 2880000000000000000L;
        }else{
            this.tempoExpiracao = 28800000L;
        }
        this.verificador = JWT.require(algoritimo).withIssuer(EMITENTE).build();
        this.usuarios = new HashMap<String, UsuarioSimplesDTO>();
    }

    public String empresas(Usuario usuario, String chave) {
        try {
            if (!SuperControle.independente(chave)) {
                String empresas = "";
                if (usuario.getEmpresasZW() != null && !usuario.getEmpresasZW().isEmpty()) {
                    for (EmpresaWS empresa : usuario.getEmpresasZW()) {
                        empresas += "," + empresa.getCodigo();
                    }
                    return empresas.replaceFirst(",", "");
                }
                return usuario.getEmpresaZW().toString();
            }
        } catch (Exception e) {
            Uteis.logar(e, JWTTokenService.class);
        }
        return "";
    }

    public String gerarTokenSimples(String chave, Integer empresa, Usuario usuario) {
        UsuarioSimplesDTO tokenDTO = new UsuarioSimplesDTO();
        tokenDTO.setChave(chave);
        tokenDTO.setUsername("");
        if (usuario != null) {
            tokenDTO.setId(usuario.getCodigo());
            tokenDTO.setUsername(usuario.getUserName());

        }
        JSONObject jsonObject = new JSONObject(tokenDTO);
        jsonObject.put("idEmpresa", empresa == null ? 0 : empresa);
        return Uteis.encriptar(jsonObject.toString(), authSecret());
    }

    @Override
    public String gerarToken(Usuario usuario, String chave) throws ServiceException {
        try {
            Date dataExpiracao = new Date(new Date().getTime() + tempoExpiracao);
            UsuarioSimplesDTO usuarioSimples = new UsuarioSimplesDTO();
            usuarioSimples.setId(usuario.getCodigo());
            usuarioSimples.setChave(chave);
            usuarioSimples.setUsername(usuario.getUserName());
            usuarioSimples.setRecursos(getRecursos(usuario));
            if(usuario.getProfessor() != null){
                usuarioSimples.setColaboradorId(usuario.getProfessor().getCodigoColaborador() == null ?
                        usuario.getProfessor().getCodigo() : usuario.getProfessor().getCodigoColaborador());
            }
            usuarioSimples.setProvider(!(usuario.getModulos() != null && usuario.getModulos().toUpperCase().contains("ZW")) ? "tr" : "zw");

            String token = JWT.create()
                    .withIssuer(EMITENTE)
                    .withExpiresAt(dataExpiracao)
                    .withClaim(CLAIM_CHAVE, chave)
                    .withClaim(CLAIM_EMPRESAS, empresas(usuario, chave))
                    .withClaim(CLAIM_USERNAME, usuario.getUserName())
                    .withClaim(PROP_COD_ZW, usuario.getUsuarioZW() != null ? usuario.getUsuarioZW() : 0)
                    .withClaim(PROP_COD_TR, usuario.getCodigo())
                    .withClaim(CLAIM_COLABORADORID, usuario.getProfessor() == null ? 0 : usuario.getProfessor().getCodigoColaborador() == null ?
                            usuario.getProfessor().getCodigo() : usuario.getProfessor().getCodigoColaborador())
                    .withClaim(CLAIM_PROVIDER, !(usuario.getModulos() != null && usuario.getModulos().toUpperCase().contains("ZW")) ? "tr" : "zw")
                    .sign(algoritimo);
            usuarioSimples.setToken(token);
            usuarios.put(token, usuarioSimples);
            return token;

        } catch (Exception e) {
            Uteis.logar(e, JWTTokenService.class);
            throw new ServiceException("jwt_token_nao_gerado", "O TOKEN JWT não pôde ser gerado.", e);
        }
    }

    @Override
    public UsuarioSimplesDTO validarRecuperandoUsuario(String token,
                                                       boolean guardarMapa) throws TokenInvalidoException, TokenExpiradoException, SecretException{
        return validarRecuperandoUsuario(token, null, null, guardarMapa);
    }

    @Override
    public UsuarioSimplesDTO validarRecuperandoUsuario(String token,
                                                       String chave, String empresaId,
                                                       boolean guardarMapa) throws TokenInvalidoException, TokenExpiradoException, SecretException{
        DecodedJWT decodedJWT = validarToken(token);
        UsuarioSimplesDTO usuario = usuarios == null ? null : usuarios.get(token);
        if (usuario == null) {
            Uteis.logar(null, "Token não encontrado no map: " + token);
            try {
                if(guardarMapa){
                    JSONObject tokenDecode = getClaim(decodedJWT);
                    usuario = povoarUsuario(tokenDecode, token, chave, empresaId, guardarMapa);
                    usuarios.put(token, usuario);
                } else {
                    JSONObject claim = null;
                    try {
                        claim = new JSONObject(getClaim(String.class, decodedJWT, CLAIM_CONTENT));
                        usuario = povoarUsuario(claim, token, chave, empresaId, guardarMapa);
                    } catch (Exception e) {
                        Uteis.logar(e, JWTTokenService.class);
                    }
                    if (claim == null){
                        claim = getClaim(decodedJWT);
                        usuario = povoarUsuario(claim, token, chave, empresaId, true);
                    }

                }

            } catch (Exception ex) {
                ex.printStackTrace();
                Uteis.logarDebug("problema ao obter dados do token: "  + token);
                throw new TokenInvalidoException(ex);
            }
        }
        usuario.setToken(token);
        return usuario;
    }

    @Override
    public String chaveFromToken(String token) throws Exception{
        DecodedJWT decodedJWT = validarToken(token);
        JSONObject tokenDecode = getClaim(decodedJWT);
        if (tokenDecode.opt("content") != null) {
            JSONObject contentJson = new JSONObject(tokenDecode.getString("content"));
            return contentJson.getString("k");
        }
        return (String) tokenDecode.get("chave");
    }


    public <T> T getClaim(Class<T> tipo, DecodedJWT decodedJWT, String claimCode){
        Claim claim = decodedJWT.getClaims().get(claimCode);
        return claim == null ? null : claim.as(tipo);
    }

    public JSONObject getClaim(DecodedJWT decodedJWT) throws JSONException {
        Map<String, Claim> claims = decodedJWT.getClaims();
        JSONObject result = new JSONObject();
        for (Map.Entry claim : claims.entrySet()) {
            result.put(claim.getKey().toString(), claims.get(claim.getKey()).as(String.class));
        }
        return result;
    }

    public DecodedJWT validarToken(String token) throws TokenInvalidoException, TokenExpiradoException, SecretException {
        try {
            return verificador.verify(token);
        } catch (TokenExpiredException ex) {
            throw new TokenExpiradoException(ex);
        } catch (JWTVerificationException ex) {
            throw new  TokenInvalidoException(ex);
        }
    }

    private UsuarioSimplesDTO povoarUsuario(JSONObject tokenDecode,
                                            String tokenIncode,
                                            String chaveRequest, String empresaId,
                                            boolean guardarMapa) throws JSONException, ServiceException {
        UsuarioSimplesDTO usuarioSimples = new UsuarioSimplesDTO();
        if ( guardarMapa ) {
            JSONObject contentJson = tokenDecode.opt("content") == null ?
                    null :
                    new JSONObject(tokenDecode.getString("content"));
            String chave = "";
            String username = "";
            Usuario usuario = null;
            if (contentJson == null) {
                chave = (String) tokenDecode.get("chave");
                username = tokenDecode.getString("username");
                usuario = usuarioService.validarUsuarioEmail(chave, username, true);
            } else if(contentJson.has("k")){
                chave = contentJson.getString("k");
                username = contentJson.getString("u");
                usuario = usuarioService.validarUsuarioEmail(chave, username, true);
            } else if(contentJson.has("p")){
                try {
                    chave = chaveRequest;
                    usuario = usuarioService.userIA(chaveRequest,
                            Integer.valueOf(empresaId),
                            contentJson);
                }catch (Exception e){
                    Uteis.logar(null, "PACTO_IA_ERRO_TOKEN");
                    Uteis.logar(e, JWTTokenService.class);
                    throw new ServiceException(e);
                }
            }

            if (usuario != null) {
                usuarioSimples = new UsuarioSimplesDTO();
                usuarioSimples.setChave(chave);
                usuarioSimples.setId(usuario.getCodigo());
                usuarioSimples.setToken(tokenIncode);
                usuarioSimples.setUsername(usuario.getUserName());
                usuarioSimples.setRecursos(getRecursos(usuario));
            }
        }else{
            usuarioSimples.setUsername(tokenDecode.optString(PROP_PERSONA_DESCRICAO));
            usuarioSimples.setPermissoes(tokenDecode.optString(PROP_PERSONA_PERMISSOES));
        }

        return usuarioSimples;
    }



    private Set<RecursoEnum> getRecursos(Usuario usuario) {
        Set<RecursoEnum> recursos = new HashSet<RecursoEnum>();
        if (usuario.getPerfil() != null) {
            for (Permissao permissao : usuario.getPerfil().getPermissoes()) {
                recursos.add(permissao.getRecurso());
            }
        }
        return recursos;
    }


    @Override
    public void addUsuarioComToken(String token,
                                   Integer codigo,
                                   String username,
                                   Integer colaboradorId,
                                   String chave,
                                   Set<RecursoEnum> recursos) throws ServiceException {
        try {
            UsuarioSimplesDTO usuarioSimples = new UsuarioSimplesDTO();
            usuarioSimples.setId(codigo);
            usuarioSimples.setChave(chave);
            usuarioSimples.setUsername(username);
            usuarioSimples.setRecursos(recursos);
            if(!UteisValidacao.emptyNumber(colaboradorId)) {
                usuarioSimples.setColaboradorId(colaboradorId);
            }
            usuarios.put(token, usuarioSimples);
        } catch (Exception e) {
            Uteis.logar(e, JWTTokenService.class);
            throw new ServiceException("jwt_token_nao_gerado", "O TOKEN JWT não pôde ser gerado.", e);
        }
    }
}
