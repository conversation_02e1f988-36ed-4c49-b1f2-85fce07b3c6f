<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
                           http://www.springframework.org/schema/context
                           http://www.springframework.org/schema/context/spring-context-3.0.xsd
                           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.2.xsd">

    <tx:annotation-driven/>
    <tx:jta-transaction-manager/>

    <bean id="utilContext" class="br.com.pacto.util.UtilContext"/>
    <bean class="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping"
          depends-on="utilContext"/>

    <context:component-scan base-package="br.com.pacto.*"/>
    <context:component-scan base-package="servicos.*"/>

    <bean id="jsonConverter" class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
        <property name="prefixJson" value="false"/>
        <property name="supportedMediaTypes" value="application/json"/>
    </bean>

    <!--<bean class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter">-->
    <bean class="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter">
        <property name="messageConverters">
            <list>
                <ref bean="jsonConverter"/>
            </list>
        </property>
    </bean>

    <bean id="sessionFactory"
          class="org.springframework.orm.hibernate3.LocalSessionFactoryBean">
        <property name="dataSource"
                  ref="dataSource"/>

        <property name="hibernateProperties">
            <props>
                <prop key="hibernate.hbm2ddl.auto">
                    update
                </prop>
                <prop key="hibernate.dialect">
                    org.hibernate.dialect.PostgreSQLDialect
                </prop>
            </props>
        </property>
    </bean>

    <bean id="dataSource"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="org.postgresql.Driver" />
        <property name="url" value="***************************************" />
        <property name="username" value="postgres" />
        <property name="password" value="pactodb" />
    </bean>


    <bean id="transactionManager" class="org.springframework.orm.hibernate3.HibernateTransactionManager">
        <property name="sessionFactory" ref="sessionFactory"/>
    </bean>

    <!-- ASPECTJ -->
    <bean id="permissaoAspect" class="br.com.pacto.security.aspecto.PermissaoAspect"/>

    <!-- ARQUIVOS DE CONFIGURAÇÃO -->
    <bean class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath:configuracoes.properties</value>
            </list>
        </property>
        <property name="ignoreUnresolvablePlaceholders" value="true"/>
    </bean>

    <bean class="br.com.pacto.controller.json.global.GlobalErrorHandling"/>

</beans>
