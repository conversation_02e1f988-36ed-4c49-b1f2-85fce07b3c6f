# To change this template, choose Too<PERSON> | Templates
# and open the template in the editor.
CALCULAR_NR_AULAS_PREVISTAS_AUTOMATICO=Calcular n\u00FAmero de aulas previstas/t\u00E9rmino do programa
PERMITIR_REAGENDAMENTO_POR_ALUNO=Permitir que o aluno fa\u00E7a reagendamento
alunosacessaram=Acessos de alunos de turma
principal.lingua=L\u00EDngua
imprimir=Imprimir
visualizar_impressao=Visualizar Impress\u00E3o
enviar=Enviar
APPS_ATIVOS=Aplicativos ativos
ALUNOS_APP_INSTALADO=Alunos inativos que utilizam o App
ALUNOS_APP_INSTALADO_ATIVOS=Alunos ativos que utilizam o App
ALUNOS_APP_NAO_INSTALADO=Alunos ativos que n\u00E3o utilizam o App
app.instalado=Alunos utilizam atualmente o aplicativo
PRODUTO_INSCRICAO_GAME=Produto de inscri\u00E7\u00E3o Game Crossfit
principal.cadastros = Cadastros
principal.gestao = Gest\u00E3o
principal.alunos = Alunos
homem=Homem
mulher=Mulher
observacao=Observa\u00E7\u00E3o
muito.fraca=Muito Fraca
condicao.atletica=Condi\u00E7\u00E3o Atl\u00E9tica
faixa.recomendavel=Faixa Recomend\u00E1vel
baixa.aptidao=Baixa Aptid\u00E3o
condicao.de.risco=Condi\u00E7\u00E3o de Risco
FRACA=Fraca
MEDIA=M\u00E9dia
REGULAR=Regular
tabela=Tabela
lateral.esquerda=Lateral esquerda
lateral.direita=Lateral direita
posterior=Posterior
anterior=Anterior
visao.lateral=Vis\u00E3o lateral
concluir.avaliacao.fisica=Concluir toda a avalia\u00E7\u00E3o f\u00EDsica
visao.lateral.esquerda=Vis\u00E3o lateral esquerda
visao.lateral.direita=Vis\u00E3o lateral direita
visao.posterior=Vis\u00E3o posterior
visao.anterior=Vis\u00E3o anterior
ANTERVERSAO_QUADRIL=Antervers\u00E3o de quadril
RETROVERSAO_QUADRIL=Retrovers\u00E3o de quadril
ROTACAO_INTERNA_OMBROS=Rota\u00E7\u00E3o interna dos ombros
RETIFICACAO_CERVICAL=Retifica\u00E7\u00E3o cervical
RETIFICACAO_LOMBAR=Retifica\u00E7\u00E3o lombar
PROTUSAO_ABDOMINAL=Protus\u00E3o abdominal
HIPERLORDOSE_CERVICAL=Hiperlordose cervical
HIPERLORDOSE_LOMBAR=Hiperlordose lombar
HIPERCIFOSE_TORACICA=Hipercifose tor\u00E1cica
PROTACAO_ESCPULAR=Prota\u00E7\u00E3o escapular
PE_PLANO=P\u00E9 plano
PE_CAVO=P\u00E9 cavo
PE_CALCANEO=P\u00E9 calc\u00E2neo
PE_EQUINO=P\u00E9 equino
GENU_FLEXO=Joelho flexo
GENU_RECURVADO=Joelho recurvado
ESCOLIOSE_CERVICAL=Escoliose cervical
ESCOLIOSE_TORACICA=Escoliose tor\u00E1cica
ESCOLIOSE_LOMBAR=Escoliose lombar
PROTACAO_ESCAPULAR=Prota\u00E7\u00E3o escapular
RETRACAO_ESCAPULAR=Retra\u00E7\u00E3o escapular
DEPRESSAO_ESCAPULAR=Depress\u00E3o escapular
PE_VAGO=P\u00E9 valgo
PE_VARO=P\u00E9 varo
ENCURTAMENTO_TRAPEZIO=Encurtamento de trap\u00E9zio
GENU_VALGO=Joelho valgo
GENU_VARO=Joelho varo
PE_ADUTO=P\u00E9 aduto
PE_ABDUTO=P\u00E9 abduto
NENHUMA_ELEVACAO=Nenhuma eleva\u00E7\u00E3o
ELEVECAO_DIREITO=Eleva\u00E7\u00E3o ombro direito
ELEVECAO_ESQUERDO=Eleva\u00E7\u00E3o ombro esquerdo
ELEVECAO_PELVE_DIREITA=Eleva\u00E7\u00E3o pelve direita
ELEVECAO_PELVE_ESQUERDA=Eleva\u00E7\u00E3o pelve esquerda 
ASSIMETRIA_QUADRIL=Assimetria de quadril
OMBROS_ASSIMETRICOS=Ombros assim\u00E9tricos
assimetrias=Assimetrias
ESQUERDA=ESQUERDA
DIREITA=DIREITA
USAR_INTEGRACAO_BIOIMPEDANCIA=Usar integra\u00E7\u00E3o com balan\u00E7a de bioimped\u00E2ncia
perimetros=Per\u00EDmetros
protocolos.campo=Protocolos de campo
teste=Teste
vo2max=Vo2Max
VO_CAMINHADA_3_KM=Caminhada de 3 Km
VO_CAMINHADA_1600_M=Caminhada de 1600 m
teste.esteira.bruce=Teste de esteira (protocolo de Bruce)
diametros.osseos=Di\u00E2metros \u00F3sseos 
diametros.punho=Di\u00E2metro do punho (biestil\u00F3ide)
diametros.cotovelo=Di\u00E2metro do cotovelo (biepicondiliano umeral)
diametros.femur=Di\u00E2metro do joelho (bicondiliano femural)
diametros.tornozelo=Di\u00E2metro do tornozelo (bimaleolar)
avaliacao.flexibilidade.wells=Banco de Wells & Dilon
avaliacao.flexibilidade.alcance=Alcance m\u00E1ximo obtido (cm)
classificacao=Classifica\u00E7\u00E3o
anamnese.respondida=Respondida em 
avaliacao.indice=\u00CDndice de massa corporal (IMC)
avaliacao.objatual=Objetivo Atual
avaliacao.adicionar=Nova avalia\u00E7\u00E3o
avaliacao.evolucao=Evolu\u00E7\u00E3o f\u00EDsica
avaliacao.alterar=Alterar atual
avaliacao.risco=Risco cardiovascular
hint.avaliacao.risco=Classifica\u00E7\u00E3o do risco cardiovascular de acordo com a circunfer\u00EAncia abdominal. <br/>Informe a medida da circunfer\u00EAncia abdominal na perimetria e verifique o resultado abaixo. 
avaliacao.circunferencia.risco=Circunfer\u00EAncia abdominal e risco cardiovascular
avaliacao.circunferencia.riscocomplicacoes=Risco de complica\u00E7\u00F5es
avaliacao.circunferencia.aumentado.substancialmente=Risco aumentado substancialmente
avaliacao.circunferencia.aumentado=Risco aumentado
avaliacao.circunferencia.normal=Normal
avaliacao.composicao=Composi\u00E7\u00E3o corporal
avaliacao.composicaosolo=Composi\u00E7\u00E3o
dica.avaliacao.enviar=Voc\u00EA pode combinar avalia\u00E7\u00F5es para envio por e-mail. <br/>Basta selecionar as avalia\u00E7\u00F5es desejadas e clicar em qualquer bot\u00E3o de envio.
dica.avaliacao.perimetria=PERIMETRIA: medidas em cent\u00EDmetros | DOBRAS CUT\u00C2NEAS: medidas em mil\u00EDmetros
dica.avaliacao.cintura=Medida realizada no plano transverso, na metade da dist\u00E2ncia entre o \u00FAltimo arco costal e a crista il\u00EDaca,<br/> com o avaliado em p\u00E9, em posi\u00E7\u00E3o ortost\u00E1tica. Geralmente localiza-se a cerca de 2 cm da cicatriz umbilical.
dica.avaliacao.circunferencia=Medida realizada no plano transverso. Estando o avaliado em p\u00E9,<br/>em posi\u00E7\u00E3o ortost\u00E1tica, posicionar a fita m\u00E9trica sobre a cicatriz umbilical.
dica.avaliacao.impressa=Voc\u00EA pode combinar avalia\u00E7\u00F5es para impress\u00E3o. <br/>Basta selecionar as avalia\u00E7\u00F5es desejadas e clicar no bot\u00E3o para imprimir a compara\u00E7\u00E3o.
dica.avaliacao.edite=Edite essa avalia\u00E7\u00E3o. 
dica.avaliacao.edite.sem=Voc\u00EA precisa da permiss\u00E3o 'Alterar Avali\u00E7\u00E3o F\u00EDsica' para editar essa avalia\u00E7\u00E3o. 
dica.avaliacao.dataretroativa=\u00C9 poss\u00EDvel alterar a data de lan\u00E7amento da avalia\u00E7\u00E3o f\u00EDsica com a permiss\u00E3o 'Alterar Avali\u00E7\u00E3o F\u00EDsica' habilitada!
principal.agenda = Agenda
principal.notificacoes = Notifica\u00E7\u00F5es
principal.aplicativo = Aplicativo
principal.pesquisa = Pesquisa
cadastros.musculos = M\u00FAsculos
cadastros.gruposmusculares = Grupos Musculares
cadastros.aparelhos = Aparelhos
cadastros.atividades = Atividades
cadastros.programas = Programas de Treino
cadastros.todosalunos = Alunos
cadastros.agendamentos = Agendamentos
cadastros.compromissos = Compromissos
cadastros.objetivos = Objetivos predefinidos
cadastros.fichas = Fichas
cadastros.salvar=Salvar
cadastros.clonar=Clonar
cadastros.salvar.atividade=Salvar atividade
cadastros.addNovaAtividade=Adicionar nova atividade
cadastros.addNovoGrupo=Adicionar novo grupo
cadastros.musculosCadastrados=M\u00FAsculos cadastrados
cadastros.adicionarMusculo=Adicionar m\u00FAsculo
cadastros.filtrar=Filtrar
cadastros.relacoes=rela\u00E7\u00F5es
cadastros.musculo.nome=Nome do m\u00FAsculo
cadastros.excluir=Excluir
cadastros.questiona.excluir=Tem certeza que deseja remover?
cadastros.gruposmuscularescadastrados=Grupos Musculares Cadastrados
cadastros.addNovoMusculo=Adicionar novo m\u00FAsculo
cadastros.grupoMuscular.nome=Nome do grupo
cadastros.adicionarGrupoMuscular=Adicionar Grupo Muscular
cadastros.aparelho.nome=Nome do Aparelho
cadastros.adicionarAparelho=Adicionar Aparelho
cadastros.aparelhoscadastrados=Aparelhos Cadastrados
cadastros.aparelho.tipo=Tipo
cadastros.atividade.nome=Nome da atividade
cadastros.atividade.descricao=Descri\u00E7\u00E3o
cadastros.atividade.linkVideo=Link Video YouTube
cadastros.atividade.ativo=Ativa
cadastros.atividade.categoria=Categoria
cadastros.atividade.unidadeMedida=Unidade de Medida
cadastros.atividade.crossfit=Crossfit
cadastros.atividade.seriesApenasDuracao=S\u00E9ries apenas com Dura\u00E7\u00E3o
cadastros.atividade.tipo=Tipo
cadastros.atividade.copyDescToSerie=Copia desc. para s\u00E9rie
cadastros.atividade.identificador=Identificador
cadastros.aparelho.identificador=Identificador
cadastros.atividade.addEmpresa=Adicionar empresa
cadastros.aparelho.addEmpresa=Adicionar empresa
cadastros.adicionarAtividade=Adicionar atividade
cadastros.addNovoAparelho=Adicionar novo aparelho
cadastros.addCategoria=Adicionar nova categoria
cadastros.categoriaAtividades=Cat. de Atividades
cadastros.atividadescadastradas=Atividades cadastradas
cadastros.nome=Nome
cadastros.descricao=Descri\u00E7\u00E3o
cadastros.sim=Sim
cadastros.nao=N\u00E3o
cadastros.categoriaatividadeadicionar=Adicionar categoria
cadastros.categoriaatividade.nome=Nome da categoria
cadastros.categoriaatividade=Categorias de Atividades
cadastros.categoriaatividadecadastradas=Categorias de atividades cadastradas
cadastros.categoriaficha=Categorias de Fichas
cadastros.categoriafichacadastradas=Categorias de fichas cadastradas
cadastros.addFichas=Adicionar nova ficha
cadastros.nivel=N\u00EDveis
cadastros.adicionarNivel=Adicionar n\u00EDvel
cadastros.nivel.nome=Nome do N\u00EDvel
cadastros.nivel.ordem=Ordem
cadastros.adicionarobjetivos=Adicionar Objetivo
cadastros.objetivos.nome=Nome do objetivo
cadastros.objetivoscadastrados=Objetivos predefinidos cadastrados
cadastros.niveiscadastrados=N\u00EDveis cadastrados
cadastros.imagens=Imagens
cadastros.imagensadicionar=Adicionar imagem
#cadastros.imagem.nome=Nome da imagem
cadastros.imagem.endereco=Endere\u00E7o da imagem
cadastros.imagemcadastradas=Imagens cadastradas
cadastros.addImagem=Adicionar nova imagem
cadastros.todos=Todos
domingo=Domingo
segunda=Segunda
terca=Ter\u00E7a
quarta=Quarta
quinta=Quinta
sexta=Sexta
sabado=S\u00E1bado
cadastros.ficha=Ficha
cadastros.programa.nome=Nome do Programa
cadastros.programa.professorCarteira=Na carteira de
cadastros.detalhes=Editar Detalhes
cadastros.datainicio=Data In\u00EDcio
cadastros.datafim=Data Fim
cadastros.diassemana=Dias por Semana
cadastros.programa.aulasprevistas=Aulas previstas
cadastros.programa.proximarevisao=Pr\u00F3xima revis\u00E3o
cadastros.programa.termino=T\u00E9rmino (Previs\u00E3o)
cadastros.ficha.nova=Nova ficha
cadastros.fichas.predefinidas=Fichas predefinidas
cadastros.objetivos.predefinidos=Objetivos predefinidos
cadastros.atividades.selecionadas=Atividades da ficha
cadastros.fichas.adicionadas=Fichas Adicionadas
cadastros.ficha.editar=Editar ficha
cadastros.ficha.tornarpre=Adicionar \u00E1s predefinidas
cadastros.fichas.nome=Nome da nova ficha
cadastros.ficha.tipoexecucao=Tipo de execu\u00E7\u00E3o
cadastros.mensagem=Mensagem
cadastros.ajuste=Ajuste
cadastros.valor=Valor
cadastros.adicionarAjuste=Adicionar ajuste
cadastros.serie=S\u00E9rie
cadastros.quantidade=Sets
cadastros.carga=Carga
cadastros.repeticoes=Repeti\u00E7\u00F5es
cadastros.duracao=Dura\u00E7\u00E3o
cadastros.distancia=Dist\u00E2ncia
cadastros.velocidade=Velocidade
cadastros.descanso=Descanso
cadastros.repeticoes.abrev=Rep
cadastros.carga.abrev=C
cadastros.duracao.abrev=Dur
cadastros.distancia.abrev=Dist
cadastros.velocidade.abrev=Vel
cadastros.descanso.abrev=Desc
cadastros.complemento=Complemento
cadastros.remover=Remover
cadastros.serie.adicionar=Adicionar s\u00E9rie
cadastros.serie.remover=Excluir S\u00E9rie
cadastros.ficha.concluir=Salvar Ficha
cadastros.programa.novo=Novo programa
cadastros.musculo=M\u00FAsculo
cadastros.grupomuscular=Grupo muscular
cadastros.aparelho=Aparelho
cadastros.atividade=Atividade
cadastros.categoriaatividadesing=Categoria de atividade
cadastros.categoriafichasing=Categoria de ficha
cadastros.categoriafichaplur=Categoria das Fichas
cadastros.nivelsing=N\u00EDvel
cadastros.objetivo=Objetivo predefinido
cadastros.imagem=Imagem
cadastros.programa=Programa de treino
cadastros.serie.adicionada=S\u00E9rie adicionada com sucesso!
cadastros.aluno.maisinfo=Mais informa\u00E7\u00F5es
cadastros.aluno.objetivos=Objetivos do Aluno
cadastros.aluno.observacoes=Observa\u00E7\u00F5es do Aluno
cadastros.aluno.avaliacao=Dados da Avalia\u00E7\u00E3o
cadastros.aluno.drobras_cutaneas=DOBRAS CUT\u00C2NEAS
cadastros.aluno.abdominal=Abdominal
cadastros.aluno.peitoral=Peitoral
cadastros.aluno.pescoco=Pesco\u00E7o
cadastros.aluno.ombro=Ombro
cadastros.aluno.quadril=Quadril
cadastros.aluno.coxa_medial=Coxa média
cadastros.aluno.coxa_distal=Coxa distal
cadastros.aluno.coxa_proximal=Coxa proximal
cadastros.aluno.suprailiaca=Supra-il\u00EDaca
cadastros.aluno.biceps=B\u00EDceps
cadastros.aluno.subescapular=Subescapular
cadastros.aluno.axilarMedia=Axilar m\u00E9dia
cadastros.aluno.triceps=Tr\u00EDceps
cadastros.aluno.total_dobras=Total Dobras
cadastros.aluno.peso=Peso
cadastros.aluno.altura=Altura
metrosExt=metros
cadastros.aluno.alturaEmMetros=Altura em metros
cadastros.aluno.perimetria=PERIMETRIA
cadastros.aluno.anteBraco=Antebra\u00E7o
cadastros.aluno.bracoRelaxado=Bra\u00E7o relaxado
cadastros.aluno.bracoContraido=Bra\u00E7o contra\u00EDdo
cadastros.aluno.coxaMedia=Coxa m\u00E9dia
cadastros.aluno.panturrilha=Panturrilha
cadastros.aluno.torax_bustoRelaxado=T\u00F3rax / Busto relaxado
cadastros.aluno.cintura=Cintura
cadastros.aluno.gluteo=Gl\u00FAteo
cadastros.aluno.flexibilidade=Flexibilidade
cadastros.aluno.circunferencia=Circunfer\u00EAncia abdominal
cadastros.aluno.total_perimetria=Total Perimetria
cadastros.aluno.resultado=RESULTADO
cadastros.aluno.percentualGordura=% Gordura
cadastros.aluno.percentualGorduraStr=Percentual de Gordura
cadastros.aluno.percentualMassaMagraStr=Percentual de Massa Magra
cadastros.aluno.categoria_percentualGordura=C. % Gordura
cadastros.aluno.massa_magra=Massa magra
cadastros.aluno.massa_gorda=Massa gorda
cadastros.aluno.imc=IMC
cadastros.aluno.pesoAtual=Peso atual
cadastros.aluno.pesoGordura=Gordura
cadastros.aluno.pesoGorduraStr=Peso de gordura
cadastros.aluno.pesoResidualStr=Peso residual
cadastros.aluno.pesoMuscularStr=Peso muscular
cadastros.aluno.pesoOsseoStr=Peso \u00F3sseo
#cadastros.aluno=ALUNO
cadastros.aluno.categoria_imc=C. IMC
cadastros.parqplus=Aluno Parq+?
cadastros.benchmark=Benchmark
cadastros.benchmark.tipo=Tipo
cadastros.benchmark.exercicios=Exerc\u00EDcios
cadastros.benchmark.obs=Observa\u00E7\u00E3o
cadastros.benchmark.midia=M\u00EDdia
cadastros.tiposBenchmark=Tipos Benchmark
cadastros.tipoBenchmark=Tipo Benchmark
cadastros.benchmarks=Benchmarks
cadastros.benchmark.nome=Nome
cadastros.benchmark.observacao=Observa\u00E7\u00E3o
cadastros.benchmark.tipoBenchmark=Tipo Benchmark
cadastros.benchmark.adicionarTipoBenchmark=Adicionar tipo Benchmark
cadastros.benchmark.videoIncorporar=URL video incorporar
cadastros.wod=WOD
cadastros.wods=WODs
cadastros.wod.midia=Imagem
cadastros.wod.importar.exercicio=Importar Exerc\u00EDcio
cadastros.wod.tipowod=Tipo WOD
cadastros.wod.configuracaohabilitada.objetivo=Objetivo
cadastros.wod.configuracaohabilitada.objetivos=Objetivos
cadastros.wod.configuracaohabilitada.periodizacao=Periodiza\u00E7\u00E3o
cancelar=Cancelar
cadastros.atividade.remover=Atividade
cadastros.atividade.salvar=Atividade
disponibilidade=Disponibilidade
contatoInterpessoal=Contato Interpessoal
prescricaoTreino=Prescri\u00E7\u00E3o Treino
revisaoTreino=Revis\u00E3o Treino
renovarTreino=Renovar Treino
agenda.tipo=Tipo
agenda.inicio=In\u00EDcio
agenda.fim=Fim
agenda.diaTodo=Dia todo
agenda.novo=Novo agendamento
agenda.aluno=Aluno
professor=Professor
professores=Professores
categorias=Categorias
categoria=Categoria
calendario=Calend\u00E1rio
tiposEvento=Tipos de Evento
editarDisponibilidade=Editar disponibilidade
start=Iniciar
finishTreino=Abandonar treino
finishSerie=Finalizar s\u00E9rie
cadastros.ajustes=Ajustes
realizada=Realizada
alterarSerie=Alterar s\u00E9rie
status=Status
historicoExecucoes=Hist\u00F3rico de execu\u00E7\u00F5es
series=S\u00E9ries
quilos=Kg
metros=m
minutos=min
semNotificacao=Sem notifica\u00E7\u00E3o
diminuiuCarga=Diminuiu carga
aumentouCarga=Aumentou carga
buscar=Buscar
concluirCadastro=Concluir cadastro
selecioneProfessor=Selecione um professor
naotemprofessor=N\u00E3o tem professor
voltar=Voltar
emaillogincontato=E-mail para login e contato
senhapodedeixar=Senha (Pode deixar em branco)
nomecompletoparte=Nome (Completo ou Parte)
CPF=CPF
matriculaaluno=Matr\u00EDcula do aluno
badge=Badge
badges=Badges
valorBadge=Valor badge
ativo=Ativo
cadastros.metodo=M\u00E9todo de treinamento
horarioComercial=Hr. comercial
repetir=Repetir
diaSemana=Dias da semana
repetirAte=Data limite
remover=Remover
disponibilidades=Disponibilidades
concluirEdicao=Concluir edi\u00E7\u00E3o
dia=Dia
veragenda=Ver agenda
acompanhar=Acompanhar
agendar=Agendar
configuracoesGlobais=Configura\u00E7\u00F5es globais
configuracoesUsuario=Configura\u00E7\u00F5es do usu\u00E1rio
configuracoesPerfis=Configura\u00E7\u00F5es de perfis
configuracoesAtendimento=Configura\u00E7\u00F5es de Atendimento
novoperfil=Novo perfil
nomeperfil=T\u00EDtulo do Perfil
cadastros.tipoEvento=Tipos de Eventos
cadastros.adicionarTipoEvento=Adicionar tipo
cadastros.tiposcadastrados=Tipos de evento cadastrados
cadastros.tiposEvento=Tipos de evento
cadastros.tiponome=Nome do tipo
cor=Cor
comportamento=Comportamento
permiteAluno=Permite aluno marcar pelo app.
cortipoevento=Cor do tipo do evento
disponibilidadepara=Disponibilidade para
todosTipos=Todos os tipos
agruparPor=Agrupar disponibilidades por
editar=Editar
fechar=Fechar
aplicarNSU=Confirmar altera\u00E7\u00F5es
somentemesmotipo=Eventos do mesmo tipo
agendaBack=Replicar Altera\u00E7\u00E3o para...
NENHUM=N\u00E3o agrupar
PROFESSOR=Professor
TIPOEVENTO=Tipo evento
CADASTROS_AUXILIARES=Cadastros Auxiliares
TREINO_INDEPENDENTE_PERMISSAO=Treino Independente
AGENDA=Agenda
ALUNO=Aluno
GERAL=Geral
entidade=Entidades
funcionalidade=Funcionalidades
permitir=Permitir
Janeiro=Janeiro
Fevereiro=Fevereiro
Marco=Mar\u00E7o
Abril=Abril
Maio=Maio
Junho=Junho
Julho=Julho
Agosto=Agosto
Setembro=Setembro
Outubro=Outubro
Novembro=Novembro
Dezembro=Dezembro
nrAgendamentos=N\u00FAmero de agendamentos
cada=a cada
dias=dias
agrupar=Agrupar
apenasAlunosCarteira=S\u00F3 alunos na carteira do prof.
configuracoesUsuarios=Usu\u00E1rios
usuario=Usu\u00E1rio
usuario.codColabZW=Colaborador ZW
senha=Nova senha
perfil=Perfil
DM=D
SG=S
TR=T
QA=Q
QI=Q
SX=S
SB=S
editarUsuario=Editar usu\u00E1rios
editarPerfil=Editar perfis
somenteEste=Somente este evento
todosEventosRepetidos=Todos os eventos repetidos
acompanhadoPor=Acompanhado por
vencimentoContrato=Vencimento do contrato
nascimento=Nascimento
PORC_ACIMA_NOTIFICAR=Porcentagem m\u00EDnima acima da carga para notificar o professor
PORC_ABAIXO_NOTIFICAR=Porcentagem m\u00EDnima abaixo da carga para notificar o professor
voltarAgenda=Voltar
gestao.indicadores=Indicadores
gestao.agendados=Agendados
gestao.executados=Executados
gestao.inidicadores=Indicadores
itens=itens
total=Total
SUGERIR_TIPO_ALTERNADO=Sugerir tipo de execu\u00E7\u00E3o alternado na ficha
INTEGRACAO_SISTEMA_OLYMPIA=Habilita Integra\u00E7\u00E3o Sistema Olympia
URL_SISTEMA_OLYMPIA=URL
TOKEN_SISTEMA_OLYMPIA=TOKEN
USUARIO_SISTEMA_OLYMPIA=Usu\u00E1rio
SENHA_SISTEMA_OLYMPIA=Senha
qrCodePlayStore=Obter aplicativo na Play Store
qrCodeAppStore=Obter aplicativo na App Store
qrCodeChaveEmpresa=Capturar QrCode Chave da Empresa
verAgendados=Ver Agendados
porcExecucao=Execu\u00E7\u00E3o do Programa de Treino em rela\u00E7\u00E3o ao Previsto
enfileirarImpressao=Enviar para impressora
gestao.cancelados=Cancelados
gestao.faltas=Faltas
gestao.indicadoresAgenda=Indicadores da Agenda
DURACAO_LIVRE=Dura\u00E7\u00E3o livre
DURACAO_PREDEFINIDA=Dura\u00E7\u00E3o predefinida
INTERVALO_DE_TEMPO=Intervalo de dura\u00E7\u00E3o
duracao_min=Dura\u00E7\u00E3o m\u00EDnima (hr:min)
duracao_max=Dura\u00E7\u00E3o m\u00E1xima
duracao_evento=Dura\u00E7\u00E3o (hr:min)
pesquisaGeralAluno=Pesquisar Aluno (Ctrl+Shift+L)
fichaDeHoje=Ficha de Hoje
previstoRealizado=Previsto / Realizado
digiteAlgoAguarde=Digite algo e pressione <ENTER>...
SUGERIR_DESCANSO=Sugerir descanso padr\u00E3o nas s\u00E9ries
intervalo_minimo=Intervalo m\u00EDnimo em caso de falta
legenda=Legenda
carregando=Carregando
leve=Leve
grave=Grave
criadoPor=Criado por
AGENDA_NOVA_ABA=Abrir Agenda sempre em uma nova aba
irParaAluno=Ir para Aluno
atividadesNaFicha=Atividades na ficha
dadosFicha=Dados da ficha
adicionandoAtividadesFicha=Adicionando atividades na ficha
verdadosficha=Editar dados da ficha
cadastros.serie.gerar=S\u00E9ries
cadastros.serie.gerar.hint=Gerar s\u00E9ries em todas as atividades da ficha
addAluno.usarAplicativo=Vai usar o aplicativo agora?
gestao.carteira=Carteira
gestaoAgenda=Gest\u00E3o: Agenda
gestaoProfessores=Gest\u00E3o: Carteiras dos professores
indicadoresAgenda=Indicadores da agenda
indicadoresProfessores=Indicadores da carteira dos professores
gestao.semCarteira=Sem treino
intervaloPesquisa=Intervalo da pesquisa
filtros=Filtros
data=Data
carteiras=Carteiras dos professores
atividadesProfessores=Atividades dos professores
indicadoresAtividadesProfessores=Indicadores das atividades dos professores
gestaoAtividades=Gest\u00E3o: Atividades
gestao.treinoNovo=Novos
renovar=Renovar
gestao.treinoRenovado=Renovados
gestao.treinoRevisado=Revisados
gestao.treinoAcompanhados=Acompanhados
notificacao.push=Push enviado
notificacao.sms=SMS enviado
DIAS_ANTES_VENCIMENTO=Quantidade de dias para o t\u00E9rmino
DIAS_DEPOIS_VENCIMENTO=Dias depois vencimento
MODULO_AULAS=Habilitar m\u00F3dulo Aulas
MODULO_AULAS_ABA_SALDO=Saldo
MODULO_AULAS_ABA_TURMAS=Turmas
MODULO_AULAS_ABA_AULAS_COLETIVAS=Aulas Coletivas
MODULO_TREINAR=Habilitar m\u00F3dulo Treinar
VALIDAR_HORARIO_CONTRATO=Validar hor\u00E1rio do contrato
gestao.treinoProxVencimento=Pr\u00F3x.Vencimento
gestao.vencidos=Vencidos
gestao.avaliacao=Avalia\u00E7\u00E3o
gestao.data.avaliacao=DATA DA AVALIA\u00C7\u00C3O:
gestao.duasEstrelas=2 Estrelas
GRAVE=Grave
LEVE=Leve
horaInicio=Hora in\u00EDcio
horaFim=Hora fim
gestao.indicadoresTreinos=Indicadores de treinos
ATIVO=Ativo
VENCIDO=Vencido
TRANCADO=Trancado
DESISTENTE=Desistente
CANCELADO=Cancelado
MATRICULA=Matr\u00EDcula
REMATRICULA=Rematr\u00EDcula
RENOVACAO=Renova\u00E7\u00E3o
VISITANTE=Visitante
ATESTADO=Atestado
CARENCIA=Car\u00EAncia
cliente.enviarEmailAtivacao=Enviar/Reenviar email de ativa\u00E7\u00E3o ao aluno
revisar=Revisado
arevisar=Revisar
OUTROS=Outros

descricaoSituacao=Situa\u00E7\u00E3o de contrato vigente na data de hoje do aluno. Independente da data base da consulta, esta situa\u00E7\u00E3o \u00E9 a atual.
situacoesContrato=Situa\u00E7\u00F5es de contrato
situacoesPrograma=Situa\u00E7\u00F5es do programa
gravidade=Gravidade
gestaoNotificacoes=Gest\u00E3o de notifica\u00E7\u00F5es
notificacoes=notifica\u00E7\u00F5es
dataReferenciaCarteira=Data refer\u00EAncia carteira
cadastros.programa.comoAlterarProfCarteira=Para alterar o professor da carteira, deve-se executar "Adicionar Aluno ao Pacto Treino" novamente.
tipSemTreino=N\u00FAmero de alunos que est\u00E3o no treino mas que nunca tiveram um programa de treino associado.
tipTotalAlunos=Este indicador leva em considera\u00E7\u00E3o o n\u00FAmero de alunos na carteira do professor.
tipTreinoNovo=Numero de treinos novos, ou seja o primeiro treino do aluno ap\u00F3s a matricula, o professor identificou deste treino como um treino novo. O professor escolheu o bot\u00E3o "Treino Novo"
tipProgramasVencidos=N\u00FAmero de alunos que possuem programa de treino que est\u00E1 vencido e n\u00E3o tem nenhum ativo na data base.
tipProximoVencidos=Alunos com programa pr\u00F3ximo de vencer de acordo com configura\u00E7\u00E3o do sistema.
tipIndiceAvaliacao=M\u00E9dia das notas dos treinos recebidas pelos professores  durante dois meses (m\u00EAs da data base e o anterior). O valor do rodap\u00E9 \u00E9 a soma de todas as m\u00E9dias, dividida pelo n\u00FAmero de professores que receberam alguma nota, e n\u00E3o pelo total de professores.
tipDuasEstrelas=N\u00FAmero de notas menor ou igual a 2 recebidas pelos professores  durante dois meses (m\u00EAs da data base e o anterior).
tipTreinosRenovados=A partir de um programa de treino o professor pode apertar o bot\u00E3o \u201CRenovar\u201D e o sistema cria um novo treino a partir do treino anterior e coloca o treino anterior como renovado e este como sendo uma renova\u00E7\u00E3o.
tipTreinosRevisados=Quando professor entra do programa de treino e clica no bot\u00E3o revisar, o sistema registra uma revis\u00E3o.
tipTreinosAcompanhados=Quando o professor escolhe a op\u00E7\u00E3o de acompanhar no cadastro do aluno o sistema registra a data e hora. A a\u00E7\u00E3o representa que o professor est\u00E1 junto com o alunos na execu\u00E7\u00E3o de todas atividades de dia de treino do aluno.
cadastros.atividade.abrirGaleriaImagens=Abrir galeria de Imagens
cadastros.atividade.selecaoImagens=Selecionar imagem para atividade
cadastros.programa.datarevisao=Data revis\u00E3o
cadastros.programa.justificativaRevisao=Justificativa
cadastros.programa.historicoRevisoes=Hist\u00F3rico Revis\u00F5es
cadastros.confirmar=Confirmar
cadastros.empresa= Empresa
cadastros.config.empresa = Config. Empresas
marcarTodos=Marcar Todos
desmarcarTodos=Desmarcar Todos
PERMITIR_APENAS_ALUNOS_ATIVOS=Adicionar apenas alunos ativos no ZillyonWeb
auditoria=Log
alteracoes=Altera\u00E7\u00F5es
personal=Personal
acompanhar_personal=Acompanhamento de Personal
fazerCheckIn=Check In
iniciarAtendimento=Iniciar atendimento
historicoAulas=Hist\u00F3rico de aulas
operacao=Opera\u00E7\u00E3o
saldo=Saldo
fazerCheckOut=Check Out
finalizarAtendimento=Finalizar atendimento
selecionarAluno=Selecionar aluno
iniciar=Iniciar
personaisCadastrados=Personais cadastrados
PAGAMENTO=Pagamento
CHECKIN=Check-In
CHECKOUT=Check-Out
CONS_PRE_PAGO=Cons. Pr\u00E9 Pago
CONS_POS_PAGO=Cons. P\u00F3s Pago
PERSONAL=Gest\u00E3o de personal
autorizar=Autorizar
PERSONAL_SEM_CREDITO=O personal usa cr\u00E9ditos pr\u00E9-pagos e o saldo est\u00E1 zerado.
PARCELA_EM_ABERTO=O personal possui parcela em aberto a mais dias do que o permitido.
LIBERACAO_CHECK_IN=Check-In for\u00E7ado
COMPRA_CREDITOS=Comprou cr\u00E9ditos
GASTO_CREDITOS=Consumiu cr\u00E9ditos
FECHAMENTO_CREDITOS=Fechamento de cr\u00E9ditos
gestaopersonal=Gest\u00E3o de Personal
personais=Personais
CROSSFIT=Crossfit
lanc=Data
cadastros.gestaocreditos=Gest\u00E3o Cr\u00E9ditos
colaboradores=Colaboradores
colaborador=Colaborador
novo=Novo
addColaborador=Add Colaborador
importarColaborador=Importar Colaborador
importaroutro=Importar outro
dataLancamento=Data de lan\u00E7amento
cadastro.aulas=Aulas
cadastro.aula.modalidade=Modalidade
cadastro.aula.modalidades=Modalidades
ALTA_FREQUENCIA=Alta frequ\u00EAncia
BAIXA_FREQUENCIA=Baixa frequ\u00EAncia
salaCheia=Aula Cheia
SALA_CHEIA=Aula Cheia
NR_AULA_EXPERIMENTAL_ALUNO=Aulas experimentais por aluno
MINUTOS_AGENDAR_COM_ANTECEDENCIA=Tempo para agendar com anteced\u00EAncia
MINUTOS_AGENDAR_COM_ANTECEDENCIA_PERFIL=Tempo para usu\u00E1rio agendar com anteced\u00EAncia
PERMITE_ENVIO_RELATORIOS=Permitir envio de relat\u00F3rio para aluno
semana=Semana
mes=M\u00EAs
DIAS_MOSTRAR_TOTEM=Tempo m\u00E1ximo para mostrar no totem
PEDIDOS_NEGADOS=Pedidos negados
PEDIDOS_NEGADOS_MULTIPLOS=Pedidos m\u00FAltiplos negados
AULA_EXPERIMENTAL=Aulas experimentais
AULA_EXPERIMENTAL_MULTIPLOS=Aulas experimentais m\u00FAltiplas
clonar=Clonar
deletarAulas=Excluir aulas geradas
AULAS_VERDES=Atingiram a meta m\u00E1xima
AULAS_VERMELHAS=N\u00E3o atingiram a meta m\u00EDnima
EXCECAO=BI de Ocupa\u00E7\u00E3o e Disponibilidade
DESEMPENHO=BI de Meta de ocupa\u00E7\u00E3o
RANKING_WOD=Ranking
ATIVIDADES_WOD=Atividades
APARELHO_WOD=Aparelhos
RANKING_WOD_ITEM=Um treino
posicao.ranking=Posi\u00E7\u00E3o
RANKING_WOD_ITEMS=treinos
nenhum.wod=Nenhum WOD econtrado
RANKING_VERDE=BI de Professores com maior taxa de ocupa\u00E7\u00E3o
RANKING_VERMELHO=BI de Professores com menor taxa de ocupa\u00E7\u00E3o
DEMANDA=BI de Demanda por Hor\u00E1rio Turma
OCUPACAO=Ocupa\u00E7\u00E3o
AULAS=Aula Cheia
BONIFICACAO=Bonifica\u00E7\u00E3o
VALOR_VERMELHO_BAIXA_FREQUENCIA=Meta m\u00EDnima para aulas de baixa frequ\u00EAncia
VALOR_VERMELHO_ALTA_FREQUENCIA=Meta m\u00EDnima para aulas de alta frequ\u00EAncia
VALOR_VERDE_BAIXA_FREQUENCIA=Meta sugerida para aulas de baixa frequ\u00EAncia
VALOR_VERDE_ALTA_FREQUENCIA=Meta sugerida para aulas de alta frequ\u00EAncia
VALIDAR_MODALIDADE=Validar modalidade para marcar aula
SOBRESSALENCIA=Sobressal\u00EAncia
AULASMARCADAS=Aulas marcadas
SUBSTITUICAO=Professores substitu\u00EDdos
EMITIR_FICHA_APOS_VENCIMENTO_TREINO=Emitir ficha ap\u00F3s vencimento do treino
MOBILE_SEMPRE_ATUALIZAR_CARGA_FICHA=Mobile sempre atualizar a carga da ficha
historicoBV=Visualizar hist\u00F3rico de BV
observacoesAluno=Objetivos do aluno
observacoesAvaliacao=Dados da avalia\u00E7\u00E3o
observacoes=Observa\u00E7\u00F5es
nenhumaObservacaoAluno=Nenhuma observa\u00E7\u00E3o foi feita sobre este aluno.
nenhumaObservacaoAvaliacaoFisica=Nenhuma observa\u00E7\u00E3o foi feita sobre avalia\u00E7\u00F5es f\u00EDsicas deste aluno.
ultimaEdicaoPor=\u00DAltima edi\u00E7\u00E3o por
as=\u00E0s 
historico=Hist\u00F3rico
historicoObsAluno=Hist\u00F3rico de observa\u00E7\u00F5es do aluno
histObsAvaliacao=Hist\u00F3rico de observa\u00E7\u00F5es da avalia\u00E7\u00E3o f\u00EDsica
editadoPor=Editado por
horario=Hor\u00E1rio
nenhumAgendamento=Nenhum agendamento no per\u00EDodo.
AGENDA_ABRIR_LISTA=Abrir agenda sempre na vis\u00E3o de lista
TREINOU=Treinou
FALTOU_AGENDAMENTO=Faltou agendamento
AGENDAMENTO=Agendamento
CONFIRMOU_AGENDAMENTO=Confirmou agendamento
COMPARECEU_AGENDAMENTO=Compareceu agendamento
REVISOU_TREINO=Revisou programa
RENOVOU_TREINO=Renovou programa
ENTROU_NO_TREINO=Entrou no treino
NOTIFICACAO=Notifica\u00E7\u00E3o
MONTOU_TREINO=Montou programa
MUDOU_DE_NIVEL=Mudou de n\u00EDvel
AULA_PERSONAL=Aula com personal
AULA_CHEIA=Participou de aula
GANHOU_BADGE=Conquistou badge
REALIZOU_AVALICAO=Realizou avalia\u00E7\u00E3o fis\u00EDca
atividades.aluno=Atividades do aluno
atualizar=Atualizar
ACABOU_TREINO=Terminou programa
carregar.mais=Carregar mais
mais.dados.sobre=Mais dados abaixo
TREINO=Treino
CARTEIRA=Carteira
AVALIACAO_FISICA=Avalia\u00E7\u00E3o F\u00EDsica
alunos.com.treino=Alunos ativos com treino
treinos.renovar=Treinos a renovar nos pr\u00F3ximos
meses=meses
total.alunos=Total de alunos
ativos=Ativos
inativos=Inativos
sem.avaliacao.fisica=Sem agendamento de avalia\u00E7\u00E3o f\u00EDsica nos \u00FAltimos
dias.acesso=M\u00E9dia de execu\u00E7\u00F5es de treino nos \u00FAltimos
manha=Manh\u00E3
tarde=Tarde
noite=Noite
acesso=Acesso
BI_RENOVACAO=Renovados nos \u00FAltimos
BI_A_VENCER=A vencer em
BI_TREINOS_A_RENOVAR=Treinos a renovar em
BI_NOVOS_NA_CARTEIRA=Novos na carteira
BI_TROCARAM_CARTEIRA=Trocaram de carteira
BI_TEMPO_CARTEIRA=Tempo m\u00E9dio de perman\u00EAncia na carteira
BI_TEMPO_PROGRAMA=Tempo m\u00E9dio de perman\u00EAncia no programa
BI_ATIVOS_FORA_TREINO=Alunos ativos fora do Treino
BI_TREINOS_EM_DIA=% de treinos em dia
BI_AVALIACAO_TREINO=Avalia\u00E7\u00E3o do treino dos alunos
BI_SEM_TREINO=Alunos ativos sem treino
#dia=Dia
BI_ALUNOS_FORA_TREINO=Alunos ativos fora do Treino
ultima.atualizacao=\u00DAltima atualiza\u00E7\u00E3o em
organizador=Ir para Organizador de Carteiras
com.avaliacao=Com avalia\u00E7\u00E3o
em.dia=Em dia
treinos.avencer=Alunos com treinos a renovar
avaliacoes.alunos=Avalia\u00E7\u00F5es dos alunos
ESTRELAS_5=Alunos que deram 5 estrelas
ESTRELAS_4=Alunos que deram 4 estrelas
ESTRELAS_3=Alunos que deram 3 estrelas
ESTRELAS_2=Alunos que deram 2 estrelas
ESTRELAS_1=Alunos que deram 1 estrela
renovaram=Alunos que renovaram contrato
nao.renovaram=Alunos que n\u00E3o renovaram contrato
alunos.a.vencer=Alunos a vencer
novos.carteira=Alunos novos na carteira
trocaram.carteira=Alunos que trocaram de carteira
qrcodeprofessor=QR Code Professor
expiraEm=Expira em
senha=Senha
importarAlunos=Importar Alunos
importarAlunosTreino=Importar alunos p/ TreinoWeb
PERIODO_USADO_BI=Per\u00EDodo abrangido no BI (em dias)
voltarplista=Voltar p/ lista
cadastro.tipoevento=Tipo de evento
definir.padrao.serie=Gerar padr\u00E3o de s\u00E9rie
definir.padrao=Gerar padr\u00E3o
USAR_COMPLEMENTO_REPETICAO_CARGA=Usar complementos para repeti\u00E7\u00E3o e carga
complemento.serie=Comp. s\u00E9rie
complemento.repeticao=Comp. repeti\u00E7\u00E3o
complemento.repeticao.title=Complemento para repeti\u00E7\u00E3o, aceita alfanum\u00E9ricos
complemento.serie.title=Complemento para carga, aceita alfanum\u00E9ricos
cadencia=Cad\u00EAncia
TODOS_APARELHOS=Todos aparelhos
TODAS_ATIVIDADES=Todas atividades
SOMENTE_ATIVIDADES_ATIVAS=Ativas
SOMENTE_ATIVIDADES_INATIVAS=Inativas
add.imagens=Add imagem
voltarNomeOriginal=Resetar nome
cadastros.imagem.nome=T\u00EDtulo da imagem
clique.editar.nome=Clique para editar o nome da imagem
cadastros.salvar.alteracoes=Salvar altera\u00E7\u00F5es
ver.historico.bv=Hist\u00F3rico de BV
clique.editar.nome=Clique para editar o nome da imagem
DURACAO_ALUNO_NA_ACADEMIA=Tempo em que o aluno permanece no filtro "Na academia" (Em minutos)
cadastros.aluno.nome=Nome do aluno
cadastros.novo.aluno=Novo aluno
masculino=Masculino
feminino=Feminino
sexo=Sexo
email=E-mail
telefone=Telefone
nome.usuario=Nome de usu\u00E1rio
addProfessor.criarUsuario=Criar usu\u00E1rio
perfil.usuario=Perfil de usu\u00E1rio
tipo.usuario=Tipo de usu\u00E1rio
LOGIN=Login
SENHA=Senha
REMETENTE=Remetente
EMAIL_PADRAO=E-mail padr\u00E3o
MAIL_SERVER=Mail Server
CONEXAO_SEGURA=Conex\u00E3o segura
INICIAR_TLS=Iniciar TLS
email.teste=Email para Teste
testar.envio=Testar envio
cadastros.empresas=Empresas
cadastros.empresasCadastrados=Empresas cadastradas
cadastros.empresa.nome=Nome da empresa
cadastros.empresa.tokenSms=Token SMS
cadastros.empresa.tokenSmsShortcode=Token SMS Shortcode
cadastros.empresa.codigoFinanceiro=Cod. Empresa Financeiro
cadastros.empresa.urlSite=URL Site
cadastros.empresa.timeZone=TimeZone
cadastros.empresa.usaMobile=Utiliza o aplicativo Pacto Treino
cadastros.empresa.email=E-mail contato
JANEIRO=Janeiro
FEVEREIRO=Fevereiro
MARCO=Mar\u00E7o
ABRIL=Abril
MAIO=Maio
JUNHO=Junho
JULHO=Julho
AGOSTO=Agosto
SETEMBRO=Setembro
OUTUBRO=Outubro
NOVEMBRO=Novembro
DEZEMBRO=Dezembro
nao.existe.historico.objetivos=N\u00E3o existe hist\u00F3rico de altera\u00E7\u00F5es de objetivos para esse aluno.
historico.atestado=Hist\u00F3rico de Atestados
nao.existe.historico.avaliacao=N\u00E3o existe hist\u00F3rico de altera\u00E7\u00F5es de dados de avalia\u00E7\u00E3o para esse aluno.
professor.padrao=Professor padr\u00E3o
importar.alunos.usuario=Os alunos que n\u00E3o tem v\u00EDnculo com professor do TreinoWeb ser\u00E3o vinculados ao professor padr\u00E3o e o nome de usu\u00E1rio do aluno ser\u00E1 seu e-mail ou a matr\u00EDcula, caso n\u00E3o v\u00E1 usar o aplicativo.
alunos.selecionados=alunos selecionados
usuarios.cadastrados=Usu\u00E1rios cadastrados
username=Username
menor=Menor
maior=Maior
complemento.carga.title=Comp. carga
todosEventosRepetidosPosteriores=Eventos posteriores repetidos
gestao.comtreino=Com treino
VENCIMENTO_APENAS_ATIVOS=Considerar apenas alunos ativos no indicador Vencidos
frequencia=Frequ\u00EAncia
CREDITOS_EXPIRADOS=Cr\u00E9ditos expirados
contato=Contato
alterar.informacoes.login=Alterar informa\u00E7\u00F5es de login
adicionar.outro.email=Adicionar outro e-mail
adicionar.outro.telefone=Adicionar outro telefone
colaborador.novo=NOVO COLABORADOR
cadastrar.usuario.treino=Cadastrar usu\u00E1rio no Treino?
colaboradores.cadastrados=Colaboradores cadastrados
replicar.valores.serie=Aplicar valores \u00E0s demais s\u00E9ries
plano=Plano
INATIVOS_A_X_DIAS=Inativos a X dias ( Deixar '0' para mostrar todos)
SOMENTE_ALUNO_CONTRATO_DESISTENTE=Considerar apenas alunos com situa\u00E7\u00E3o "Inativo Desistente", no contador de "Alunos Inativos" do B.I. Gest\u00E3o Carteira
HORARIO_DEFAULT_AC=Hor\u00E1rio inicial da Agenda
HORARIO_DEFAULT_TW=Hor\u00E1rio inicial do treino
NUMERO_IMPRESSAO_FICHA=N\u00FAmero m\u00E1ximo de impress\u00F5es de fichas por dia
USUARIO_SERVICE=Usu\u00E1rio Service
SENHA_SERVICE=Senha Service
EMAIL_SERVICE=E-mail Service
TELEFONE_SERVICE=Telefone
arenovar=A renovar
importante=Importante
hint_importante=Caso a op\u00E7\u00E3o esteja marcada, o sistema abrir\u00E1 o cadastro do aluno sempre na aba 'Observa\u00E7\u00F5es'.
dos.alunos.ativos=dos alunos ativos
cadastros.tirarFoto=Capturar Foto
COMPARECERAM=Executaram
CANCELARAM=Cancelaram
disponibilidade.agendamentos=Disponibilidade X Agendamentos em
professores.agendamentos=Professores X Agendamentos em
horas.disponibilidade=Hrs de disponibilidade
horas.atendimento=Hrs executadas
disponibilidade.ocupacao=Ocupa\u00E7\u00E3o
novos.treinos=Novos treinos
treinos.renovados=Treinos renovados
treinos.revisados=Treinos revisados
avaliacoes.fisicas=Avalia\u00E7\u00F5es f\u00EDsicas
FALTARAM=Faltaram
acessos.execucoes=Acessos X Execu\u00E7\u00F5es nos \u00FAltimos

ACESSOS=Acessos
ACESSOS_TREINO=Acesso alunos do Treino
EXECUCOES_TREINO=Execu\u00E7\u00F5es de treino
SMARTPHONE=Smartphone
AGENDAMENTOS=Agendamentos
AG_CONFIRMACAO=Aguardando confirma\u00E7\u00E3o
CONFIRMARAM=Confirmaram
DISPONIBILIDADES=Disponibilidades
nome.usuario.email=Nome de usu\u00E1rio (E-mail)

replicarAgendamentos=Replicar nos agendamentos
replicarAgendamentosMesmoTipo=Agendamentos de mesmo tipo
salvarReplicar=Salvar e Replicar
todosEventosRepetidos=Eventos do mesmo tipo


turmas.nomegeral=Turmas
turmas.nome=Turma
turmas.dia=Dia
turmas.responsavel=Professor
turmas.tipo=Modalidade
turmas.inicio=In\u00EDcio
turmas.fim=Fim
turmas.desmarcados=Desmarcados
turmas.reposicoes=Reposi\u00E7\u00F5es
turmas.experimentais=Experimentais
turmas.marcacoes=Marca\u00E7\u00F5es
turmas.nrvagas=Capacidade
turmas.agendados=Alunos
turmas.inserir=Adicionar aluno
turmas.local=Ambiente
turmas.confirmados=Confrmados
turmas.ambientes=Ambientes
turmas.visitantes=Visitantes
turmas.nivel=N\u00EDvel
turmas.selecioneResponsavel=Todos os professores
turmas.LOCAL=Ambiente
turmas.TIPO=Modalidade
turmas.RESPONSAVEL=Professor
turmas.permite.experimental=Esta turma permite aula experimental.
turmas.permite.nao.experimental=Esta turma n\u00E3o permite aula experimental.
turmas.desafio=Alunos desafio
aula.coletiva=Aula coletiva
tvgestor=TV Gestor
agora=Agora
linksgerados=Links gerados
geradopor=Gerado por
datageracao=Data gera\u00E7\u00E3o
codigo=C\u00F3digo
BLOQUEAR_IMPRESSAO_FICHA_APOS_TODAS_EXECUCOES=Bloquear impress\u00E3o ap\u00F3s o aluno atingir a quantidade de execu\u00E7\u00F5es previstas
andamentoprogramas=Andamentos dos programas
gestaoAndamento=Gest\u00E3o de andamento dos programas
alunosFavoritos=Alunos favoritos
horas=horas
favoritosacessaram=Favoritos que acessaram no dia
alunosesperados=Alunos esperados
criacaorapida=CRIA\u00C7\u00C3O R\u00C1PIDA DE PROGRAMA DE TREINO
simsomentemesmotipo=Somente do mesmo tipo
adicionar=Adicionar
informacoes=Informa\u00E7\u00F5es
atividades.selecionadas=Atividades selecionadas
adicioneatividadecomecar=Adicione uma atividade para come\u00E7ar
alteracoes.salvas.automaticamente=Altera\u00E7\u00F5es salvas automaticamente.
continue.navegar=Continue a navegar ap\u00F3s concluir a cria\u00E7\u00E3o do Treino
graficos=Gr\u00E1ficos
grafico=Gr\u00E1fico
IND_TOTAL_ALUNOS=Total de alunos
IND_ATIVOS=Alunos Ativos
IND_INATIVOS=Alunos Inativos
IND_ATIVOS_TREINO=Alunos ativos com treino
IND_EM_DIA=Treinos em dia
IND_VENCIDOS=Treinos vencidos
IND_RENOVAR=Treinos a renovar
IND_AGENDAMENTOS=Agendamentos
IND_AGENDAMENTOS_EXECUTARAM=Agendamentos executados
IND_AGENDAMENTOS_FALTARAM=Faltaram ao agendamento
IND_AGENDAMENTOS_CANCELARAM=Cancelaram o agendamento
IND_RENOVADOS=Alunos renovados
IND_NAO_RENOVADOS=Alunos que n\u00E3o renovaram
IND_A_VENCER=Alunos a vencer
IND_NOVOS_CARTEIRA=Entraram na carteira
IND_TROCARAM_CARTEIRA=Sa\u00EDram da carteira
IND_TEMPO_MEDIO_CARTEIRA=Tempo m\u00E9dio na carteira
IND_SEM_TREINO=Alunos sem treino
IND_PERC_TREINO_EM_DIA=% Treinos em dia
IND_PERC_TREINO_VENCIDOS=% Treinos vencidos
IND_TEMPO_MEDIO_PROGRAMA=Tempo m\u00E9dio dos programas
IND_NR_AVALIACOES=Nr. de avalia\u00E7\u00F5es
IND_MEDIA_AVALIACOES=Nota m\u00E9dia
IND_ESTRELA_1=1 estrela
IND_ESTRELA_2=2 estrelas
IND_ESTRELA_3=3 estrelas
IND_ESTRELA_4=4 estrelas
IND_ESTRELA_5=5 estrelas
IND_COM_AVALIACAO=Com avalia\u00E7\u00E3o f\u00EDsica
IND_SEM_AVALIACAO=Sem avalia\u00E7\u00E3o f\u00EDsica
IND_AGENDAMENTO_PROFESSORES=Professores da agenda
IND_HORAS_DISPONIBILIDADE=Horas de disponibilidade
IND_HORAS_EXECUTADAS=Horas executadas
IND_PERC_OCUPACAO=Percentual de ocupa\u00E7\u00E3o
IND_AGENDAMENTO_NOVOS_TREINOS=Agenda: Novos treinos
IND_AGENDAMENTO_TREINO_RENOVADOS=Agenda: Renova\u00E7\u00E3o de treino
IND_AGENDAMENTO_TREINO_REVISADOS=Agenda: Revis\u00E3o de treino
IND_AGENDAMENTO_AVALIACOES_FISICAS=Agenda: Avalia\u00E7\u00F5es f\u00EDsicas
IND_PERCENTUAL_RENOVACAO=% renova\u00E7\u00F5es de contrato
cadastros.fichaativo=Ficha ativa
tipos.agendamento=Tipos de agendamento
totalizador.avaliacao=Total das avalia\u00E7\u00F5es de treino
percentuais=Percentuais
RANKING_TOTAL_ALUNOS=Total de Alunos
RANKING_ATIVOS=Alunos Ativos
RANKING_INATIVOS=Alunos Inativos
RANKING_ALUNOS_CANCELADOS=Alunos cancelados
RANKING_ATIVOS_COM_TREINO=Com treino
RANKING_COM_AVALIACAO_FISICA=Com Avalia\u00E7\u00E3o F\u00EDsica
RANKING_SEM_AVALIACAO=Sem Avalia\u00E7\u00E3o F\u00EDsica
RANKING_ATIVOS_SEM_TREINO=Sem treino
RANKING_EM_DIA=Programa em dia
RANKING_VENCIDOS=Contratos vencidos
RANKING_TREINOS_A_VENCER=Treinos vencendo
RANKING_AVALIACOES=Avalia\u00E7\u00F5es pelo aplicativo
RANKING_ESTRELAS_5=Avalia\u00E7\u00F5es com 5 estrelas
RANKING_ESTRELAS_4=Avalia\u00E7\u00F5es com 4 estrelas
RANKING_ESTRELAS_3=Avalia\u00E7\u00F5es com 3 estrelas
RANKING_ESTRELAS_2=Avalia\u00E7\u00F5es com 2 estrelas
RANKING_ESTRELAS_1=Avalia\u00E7\u00F5es com 1 estrela
RANKING_RENOVARAM=Renovaram contrato
RANKING_NAO_RENOVARAM=Alunos que n\u00E3o renovaram contrato
RANKING_ALUNOS_A_VENCER=Contratos a vencer
RANKING_TROCARAM_CARTEIRA=Sa\u00EDram da carteira
RANKING_NOVOS_CARTEIRA=Entraram na carteira
RANKING_BI_TEMPO_CARTEIRA=M\u00E9dia na carteira (meses)
RANKING_BI_TEMPO_PROGRAMA=M\u00E9dia do programa de treino (meses)
RANKING_ACESSOS=Nr. de alunos do treino que acessaram
RANKING_ACESSOS_TREINO=Nr. de acessos de alunos no treino
RANKING_EXECUCOES_TREINO=Nr. de execu\u00E7\u00F5es de Treino
RANKING_SMARTPHONE=Nr. de execu\u00E7\u00F5es de Treino pelo aplicativo
RANKING_AGENDAMENTOS_DISPONIBILIDADE=Disponibilidade
RANKING_AGENDAMENTOS_PROFESSORES=Professores
RANKING_PROFESSORES=Professores
RANKING_HRS_DISPONIBILIDADE=Horas disponibilidade na agenda
RANKING_HRS_ATENDIMENTO=Horas atendimento na agenda
RANKING_OCUPACAO=Agenda: Ocupa\u00E7\u00E3o
RANKING_NOVOS_TREINOS=Agenda: Novos treinos
RANKING_TREINOS_RENOVADOS=Agenda: Treinos que foram renovados
RANKING_TREINOS_REVISADOS=Agenda: Treinos que foram revisados
RANKING_AVALIACOES_FISICAS=Agenda: Av. f\u00EDsicas que foram realizadas
RANKING_FALTARAM=Agenda: Alunos que faltaram
RANKING_COMPARECERAM=Agenda: Compareceram
RANKING_CANCELARAM=Agenda: Alunos que cancelaram
RANKING_AG_CONFIRMACAO=Agenda: Aguardando confirma\u00E7\u00E3o
RANKING_CONFIRMARAM=Agenda: Alunos que confirmaram presen\u00E7a
RANKING_DISPONIBILIDADES=Disponibilidades
RANKING_AGENDAMENTOS=Agendamentos
RANKING_NOVOS_CARTEIRA_NOVOS=Novos alunos na carteira
RANKING_NOVOS_CARTEIRA_TROCARAM=Trocaram de carteira
RANKING_PERCENTUAL_CRESCIMENTO_CARTEIRA=% crescimento carteira
RANKING_PERCENTUAL_RENOVACAO_CARTEIRA=% renova\u00E7\u00E3o de contrato
RANKING_PERC_TREINO_VENCIDOS=% treinos vencidos
RANKING_PERC_TREINO_EM_DIA=% treinos em dia
RANKING_REAGENDARAM=Reagendados
RANKING_VISITANTES=Visitantes
RANKING_IND_MEDIA_AVALIACOES=M\u00E9dia das avalia\u00E7\u00F5es pelo aplicativo
RANKING_ALUNOS_APP_INSTALADO=Alunos inativos que utilizam o App
RANKING_ALUNOS_APP_INSTALADO_ATIVOS=Alunos ativos que utilizam o App
RANKING_ALUNOS_APP_NAO_INSTALADO=Alunos ativos que n\u00E3o utilizam o App
configurar=Configurar
ZW=ZillyonWeb
AC=Aula Cheia
TODAS=Todas
ALUNOS_ESPERADOS=Alunos esperados
ULTIMOS_ACESSOS=\u00DAltimos acessos
verAnexo=Ver anexo
adicionarObservacao= + Adicionar novo

title_ACESSOS_TREINO=Este indicador apresentar\u00E1 a quantidade de alunos deste professor, que estejam cadastrados no treino, que realizaram acesso na academia.
title_ACESSOS=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que realizaram acesso na academia.
title_AG_CONFIRMACAO=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que precisam confirmar presen\u00E7a em algum agendamento marcado, atrav\u00E9s da AGENDA.
title_AGENDAMENTOS_DISPONIBILIDADE=Este indicador apresentar\u00E1 o n\u00FAmero de horas dispon\u00EDveis, na agenda, que este professor possui.
title_AGENDAMENTOS_PROFESSORES=Este indicador apresenta o n\u00FAmero de professores que tiveram agendamentos programados
title_AGENDAMENTOS=Este indicador apresentar\u00E1 a quantidade de agendamentos de aulas que foram realizados pelo professor.
title_ALUNOS_A_VENCER=Este indicador apresentar\u00E1 a quantidade de contratos, vinculados ao nome deste professor, que est\u00E3o pr\u00F3ximos a sua data de vencimento.
title_ATIVOS_COM_TREINO=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que j\u00E1 possuem um programa de treinos.
title_ATIVOS_SEM_TREINO=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que n\u00E3o possuem programa de treino.
title_ATIVOS=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que est\u00E3o ativos.
title_AVALIACOES_FISICAS=Este indicador apresentar\u00E1 o n\u00FAmero de avalia\u00E7\u00F5es f\u00EDsicas que foram realizadas pelo professor, atrav\u00E9s da AGENDA.
title_AVALIACOES=Este indicador apresentar\u00E1 a quantidade de avalia\u00E7\u00F5es que o professor recebeu atrav\u00E9s do aplicativo.
title_BI_TEMPO_CARTEIRA=Este indicador apresentar\u00E1 a quantidade de meses que os alunos permaneceram na carteira deste professor.
title_BI_TEMPO_PROGRAMA=Este indicador apresentar\u00E1 a quantidade de meses que os alunos, deste professor, permaneceram em um mesmo programa de treino.
title_CANCELARAM=Este indicador apresentar\u00E1 o n\u00FAmero de alunos, de determinado professor, que cancelaram em algum agendamento realizado atrav\u00E9s da AGENDA.
title_COM_AVALIACAO_FISICA=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que j\u00E1 realizaram a avalia\u00E7\u00E3o f\u00EDsica.
title_COMPARECERAM=Este indicador apresentar\u00E1 o n\u00FAmero de alunos, deste professor, que compareceram em algum agendamento realizado atrav\u00E9s da AGENDA.
title_CONFIRMARAM=Este indicador apresentar\u00E1 o n\u00FAmero de alunos, deste professor, que confirmaram presen\u00E7a em algum agendamento realizado atrav\u00E9s da AGENDA.
title_DISPONIBILIDADES=Este indicador apresentar\u00E1 o n\u00FAmero de horas dispon\u00EDveis, na agenda, que este professor possui.
title_EM_DIA=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que est\u00E3o com seus programas de treino em dias.
title_ESTRELAS_1=Este indicador apresentar\u00E1 a quantidade de avalia\u00E7\u00F5es, feitas atrav\u00E9s aplicativo, em que o professor recebeu 1 estrela.
title_ESTRELAS_2=Este indicador apresentar\u00E1 a quantidade de avalia\u00E7\u00F5es, feitas atrav\u00E9s aplicativo, em que o professor recebeu 2 estrelas.
title_ESTRELAS_3=Este indicador apresentar\u00E1 a quantidade de avalia\u00E7\u00F5es, feitas atrav\u00E9s aplicativo, em que o professor recebeu 3 estrelas.
title_ESTRELAS_4=Este indicador apresentar\u00E1 a quantidade de avalia\u00E7\u00F5es, feitas atrav\u00E9s aplicativo, em que o professor recebeu 4 estrelas.
title_ESTRELAS_5=Este indicador apresentar\u00E1 a quantidade de avalia\u00E7\u00F5es, feitas atrav\u00E9s aplicativo, em que o professor recebeu 5 estrelas.
title_EXECUCOES_TREINO=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que executam o treino.
title_FALTARAM=Este indicador apresentar\u00E1 o n\u00FAmero de alunos, de determinado professor, que faltaram a algum agendamento realizado atrav\u00E9s da AGENDA.
title_HRS_ATENDIMENTO=Este indicador apresentar\u00E1 a quantidade de horas de atendimento, na agenda, que foram realizadas pelo professor.
title_HRS_DISPONIBILIDADE=Este indicador apresentar\u00E1 o n\u00FAmero de hora dispon\u00EDveis, na agenda, que este professor possui.
title_INATIVOS=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que est\u00E3o inativos.
title_ALUNOS_CANCELADOS=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que est\u00E3o cancelados.
title_IND_MEDIA_AVALIACOES=Este indicador mostra a m\u00E9dia das avalia\u00E7\u00F5es feitas ao professor recebidas pelo Smartphone
title_NAO_RENOVARAM=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que n\u00E3o realizaram a renova\u00E7\u00E3o dos seus contratos.
title_NOVOS_CARTEIRA_NOVOS=Este indicador apresentar\u00E1 a quantidade de novos alunos que entraram na carteira deste professor.
title_NOVOS_CARTEIRA_TROCARAM=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que sofreram remanejamento de carteira.
title_NOVOS_CARTEIRA=Este indicador apresentar\u00E1 o n\u00FAmero de alunos que entraram na carteira deste professor.
title_NOVOS_TREINOS=Este indicador apresentar\u00E1 o n\u00FAmero de novos treinos que foram criados pelo professor, atrav\u00E9s da AGENDA.
title_OCUPACAO=Este indicador apresentar\u00E1 quantas ocupa\u00E7\u00F5es est\u00E3o marcadas para este professor, em dias que a academia tiver algum evento marcado (exemplo: dias que ser\u00E3o realizadas as avalia\u00E7\u00F5es f\u00EDsicas).
title_PERC_TREINO_EM_DIA=Este indicador apresentar\u00E1, em forma de porcentagem, o n\u00FAmero de treinos, que foram feitos pelo professor, que est\u00E3o em dia.
title_PERC_TREINO_VENCIDOS=Este indicador apresentar\u00E1, em forma de porcentagem, a quantidade de treinos, feitos pelo professor, que j\u00E1 venceram.
title_PERCENTUAL_CRESCIMENTO_CARTEIRA=Este indicador apresentar\u00E1, em forma de porcentagem, o crescimento da carteira do professor.
title_PERCENTUAL_RENOVACAO_CARTEIRA=Este indicador apresentar\u00E1, em forma de porcentagem, o n\u00FAmero de contratos, deste professor, que foram renovados.
title_PROFESSORES=Este indicador apresenta o n\u00FAmero de professores que tiveram agendamentos programados
title_RENOVARAM=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que efetuaram a renova\u00E7\u00E3o dos seus contratos.
title_SEM_AVALIACAO=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que n\u00E3o realizaram a avalia\u00E7\u00E3o f\u00EDsica.
title_SMARTPHONE=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que executaram o treino atrav\u00E9s do aplicativo.
title_TOTAL_ALUNOS=Este indicador apresentar\u00E1 a quantidade de alunos que est\u00E3o vinculados ao nome deste professor.
title_TREINOS_A_VENCER=Este indicador apresentar\u00E1 a quantidade de alunos, deste professor, que est\u00E3o com o programa de treino pr\u00F3ximo a sua data de vencimento.
title_TREINOS_RENOVADOS=Este indicador apresentar\u00E1 a quantidade de treinos que est\u00E3o vinculados ao nome de um determinado professor, e que foram renovados atrav\u00E9s da AGENDA.
title_TREINOS_REVISADOS=Este indicador apresentar\u00E1 a quantidade de treinos que foram revisados pelo professor atrav\u00E9s da AGENDA.
title_TROCARAM_CARTEIRA=Este indicador apresentar\u00E1 a quantidade de alunos que sa\u00EDram da carteira deste professor.
title_VENCIDOS=Este indicador apresentar\u00E1 a quantidade de contratos, vinculados ao nome deste professor, que est\u00E3o j\u00E1 passaram da sua data de vencimento.
avaliacao.fisica.historico=HIST\u00D3RICO AVALIA\u00C7\u00C3O F\u00CDSICA
adicionarbiset=Adicionar atividade para BI-Set
adicionartriset=Adicionar atividade para TRI-Set
cancelarbiset=Cancelar a escolha da atividade BI-Set
cancelartriset=Cancelar a escolha da atividade TRI-Set
removerbiset=Remover atividade BI-Set
removertriset=Remover atividades TRI-Set
cadastros.aluno.adicionarAlunoUsuarioMatricula=Para enviar o e-mail de ativa\u00E7\u00E3o \u00E9 necess\u00E1rio adicionar o aluno com usu\u00E1rio no formato e-mail. Ao confirmar voc\u00EA ser\u00E1 redirecionado para a tela de adicionar aluno.
bonificacao.atingiram.meta=Atingiram a meta m\u00E1xima
bonificacao.nao.atingiram.meta=N\u00E3o atingiram a meta m\u00EDnima
bonificacao.atingiram.meta.minima=Atingiram a meta m\u00EDnima
AULAS_AMARELAS=Atingiram a meta m\u00EDnima
ALUNO_MARCAR_PROPRIA_AULA=Permitir o pr\u00F3prio aluno marcar sua aula (aulas coletivas)
cadastro.tipoBenchmark.origem\=Origem=Origem
cadastros.benchmark.dia=Dia
cadastros.benchmark.tipoExercicio=Tipo de exerc\u00EDcio
cadastros.predefinidos=Pr\u00E9-definido
programa.verifiquehistoricoexecucoes=Com d\u00FAvidas a respeito desse n\u00FAmero? Consulte o hist\u00F3rico de execu\u00E7\u00F5es
programa.execucoes=Execu\u00E7\u00F5es de treino
programa.treinosPrevistos=Treinos previstos
gestao.comentario=Coment\u00E1rio
NUMERO_DIAS_NOTIFICAR_TREINO_VENCIDO=N\u00FAmero de dias para notificar o treino vencido
MENU_COLABORADOR=Exibir colaboradores
AUMENTAR_DISPOSICAO=Aumentar Disposi\u00E7\u00E3o
COND_FISICO=Cond. F\u00EDsico
DEFINICAO=Defini\u00E7\u00E3o
EMAGRECIMENTO=Emagrecimento
ESTETICA=Est\u00E9tica
FLEXIBILIDADE=Flexibilidade
HIPERTROFIA=Hipertrofia
RECOMENDACAO_MEDICA=Recomenda\u00E7\u00E3o M\u00E9dica
REDUCAO_GORDURA=Redu\u00E7\u00E3o da Gordura Corporal
REDUCAO_STRESS=Redu\u00E7\u00E3o do Stress
EXCELENTE=Excelente
superior=Superior
ACIMA_MEDIA=Acima da m\u00E9dia
NA_MEDIA=M\u00E9dia
ABAIXO_MEDIA=Abaixo da m\u00E9dia
FRACO=Fraco
lblALTURA=Altura (m)
lblPESO=Peso (kg)
lblPRESSAO_ARTERIAL=Press\u00E3o arterial (mmHg)
lblFREQUENCIA_CARDIACA=Frequ\u00EAncia card\u00EDaca em repouso (bpm)
lblPRESSAO_ARTERIALtit=Press\u00E3o arterial
lblPRESSAO_ARTERIALtitSistolica=Press\u00E3o arterial sist\u00F3lica
lblPRESSAO_ARTERIALtitDiastolica=Press\u00E3o arterial diast\u00F3lica
lblFREQUENCIA_CARDIACAtit=Frequ\u00EAncia card\u00EDaca em repouso
ALTURA=Altura
PESO=Peso
PRESSAO_ARTERIAL=Press\u00E3o arterial / Frequ\u00EAncia card\u00EDaca em repouso
avaliacao=Avalia\u00E7\u00E3o
MULTIPLA_ESCOLHA=Multipla Escolha
SIMPLES_ESCOLHA=Simples  Escolha
TEXTUAL=Textual
SIM_NAO=Sim/N\u00E3o
lblOBJETIVOS=Objetivos do aluno
OBJETIVOS=Objetivos do aluno
PARQ=Par-Q
lblPARQ=Par-Q positivo?
lblANAMNESE=Anamnese
lblRESISTENCIA_MUSCULAR_ABDOMEN=RML de abd\u00F4men
lblRESISTENCIA_MUSCULAR_BRACO=RML de bra\u00E7os
AGRUPAMENTO_SERIES_SET=Habilitar agrupamento de atividades para o m\u00E9todo BI-Set e Tri-Set
cadastros.anamnese=Anamnese
cadastros.anamneseobj=Anamnese
cadastros.anamneses=Anamneses
cadastros.anamnesescadastradas=Anamneses cadastradas
cadastros.perguntas=Perguntas
MULTIPLA_ESCOLHA=M\u00FAltipla Escolha
SIMPLES_ESCOLHA=Simples escolha
TEXTUAL=Textual
SIM_NAO=Sim ou n\u00E3o
tipo.pergunta=Tipo de pergunta
criar.nova.pergunta=Criar nova pergunta
nova.resposta=Nova resposta
nova.anamnese=Nova anamnese
respostas=Respostas
addResposta=Add resposta
questao=Quest\u00E3o
nova.pergunta=Nova pergunta
nova.pergunta.escolha=Escolha uma pergunta
voce.fuma=Voc\u00EA fuma?
medico.aferiu.pa.alta=Seu m\u00E9dico aferiu uma P.A. muito alta ou muito baixa?
possui.diabeticos=Possui diab\u00E9ticos na fam\u00EDlia
possui.problema.cardiaco=Possui algum problema card\u00EDaco?
nivel.colesterol.alto=Seu n\u00EDvel de colesterol j\u00E1 esteve muito alto?
sobrepeso=Voc\u00EA est\u00E1 com sobrepeso?
lesoes.problemas.ortopedicos=Voc\u00EA tem quaisquer les\u00F5es ou problemas ortop\u00E9dicos?
medicamento.prescrito=Toma algum medicamento prescrito ou suplementos?
medico.recomendou=Seu m\u00E9dico lhe recomendou a pr\u00E1tica de exerc\u00EDcios?
pratica.exercicios=Voc\u00EA pratica alguma atividade f\u00EDsica regularmente?
medico.diagnostico.problema.coracao_pressao.atividade_fisica=Ao realizar atividades f\u00EDsicas, algum m\u00E9dico j\u00E1 diagnosticou algum problema de cora\u00E7\u00E3o ou press\u00E3o arterial, recomendando que voc\u00EA s\u00F3 as realize sob supervis\u00E3o profissional de sa\u00FAde?
sente.dores.peito.atividade_fisica=Voc\u00EA sente dores no peito ao praticar atividade f\u00EDsica?
ultimos.dores.peito.atividade_fisica=No \u00FAltimo m\u00EAs, voc\u00EA sentiu dores no peito ao praticar atividade f\u00EDsica?
desequilibrio.tontura.perda_consciencia=Voc\u00EA apresenta algum desequil\u00EDbrio devido \u00E0 tontura e/ou perda moment\u00E2nea da consci\u00EAncia?
problema.osseo_articular.atividade_fisica=Voc\u00EA possui algum problema \u00F3sseo ou articular que pode ser afetado ou agravado pela atividade f\u00EDsica?
medicacao.continua=Voc\u00EA toma atualmente algum tipo de medica\u00E7\u00E3o de uso cont\u00EDnuo?
tratamento.medico.pressao_coracao=Voc\u00EA realiza algum tipo de tratamento m\u00E9dico para press\u00E3o arterial ou problemas card\u00EDacos?
tratamento.medico.continuo.atividade_fisica=Voc\u00EA realiza algum tratamento m\u00E9dico cont\u00EDnuo que possa ser afetado ou prejudicado pela atividade f\u00EDsica?
cirurgia.afeta_atividade_fisica=Voc\u00EA j\u00E1 se submeteu a algum tipo de cirurgia que possa comprometer sua atividade f\u00EDsica?
outra.razao.comprometer_saude.atividade_fisica=Voc\u00EA sabe de alguma outra raz\u00E3o pela qual a atividade f\u00EDsica possa eventualmente comprometer sua sa\u00FAde?
POLLOCK_3_DOBRAS=Pollock tr\u00EAs dobras
POLLOCK_ADOLESCENTE=Pollock - Adolescente
SOMATOTIPIA=Somatotipia
POLLOCK_7_DOBRAS=Pollock sete dobras
YUHASZ=Yuhasz seis dobras
TG_LOHMAN=T.G. Lohman duas dobras
GUEDES=Guedes
FAULKNER_DOBRAS=Faulkner quatro dobras
BIOIMPEDANCIA=Bioimped\u00E2ncia
protocolo=Protocolo
previsualizarresultado=PR\u00C9-VISUALIZAR RESULTADO
cadastros.aluno.percentualMassaMagra=% M. magra
cadastros.aluno.percentualMassaGorda=% M. gorda
cadastros.aluno.pesoResidual=Res\u00EDduos
cadastros.aluno.pesoMuscular=M\u00FAsculos
cadastros.aluno.pesoOsseo=Ossos
WELTMAN_OBESO=Weltman para obesos
ventilometria=Ventilometria
bioimpedancia.tmb=TMB
bioimpedancia.resistencia=Resist\u00EAncia
bioimpedancia.reatancia=Reat\u00E2ncia
bioimpedancia.perAgua=% \u00C1gua
cadastros.aluno.gorduraIdeal=Gordura ideal
bioimpedancia.necessidade.calorica=Nec. cal\u00F3rica
bioimpedancia.necessidade.fisica=Nec. f\u00EDsica
ventilometria.vo2max=Vo2 Max  
ventilometria.fcmaxima=Frequ\u00EAncia card\u00EDaca m\u00E1xima 
ventilometria.limiar=Limiar ventilat\u00F3rio I
ventilometria.limiar2=Limiar ventilat\u00F3rio II
ventilometria.intensidade=Frequ\u00EAncia card\u00EDaca por intensidade do treino (bpm)
anos=anos
lblVENTILOMETRIA=Ventilometria
addPergunta=Add pergunta
selecione.questionario.desejado=Selecione o question\u00E1rio desejado:
questionario=Question\u00E1rio
importarAlunosErro=Alunos que n\u00E3o foram importados
titulo.importacaoMotivo=Motivo
titulo.importacaoAluno=Aluno
registroNaoEncontrado=Nenhum Registro Encontrado
parq1=Alguma vez seu m\u00E9dico disse que voc\u00EA possui algum problema card\u00EDaco e recomendou que voc\u00EA s\u00F3 praticasse atividade f\u00EDsica sob supervis\u00E3o m\u00E9dica?
parq2=Voc\u00EA sente dor no t\u00F3rax quando pratica uma atividade f\u00EDsica?
parq3=No \u00FAltimo m\u00EAs voc\u00EA sentiu dor tor\u00E1cica quando n\u00E3o estava praticando atividade f\u00EDsica?
parq4=Voc\u00EA perdeu o equil\u00EDbrio em virtude de tonturas ou perdeu a consci\u00EAncia quando estava praticando atividade f\u00EDsica?
parq5=Voc\u00EA tem algum problema \u00F3sseo ou articular que poderia ser agravado com a pr\u00E1tica de atividades f\u00EDsicas?
parq6=Seu m\u00E9dico j\u00E1 recomendou o uso de medicamentos para controle da sua press\u00E3o arterial ou condi\u00E7\u00E3o cardiovascular?
parq7=Voc\u00EA tem conhecimento de alguma outra raz\u00E3o f\u00EDsica que o impe\u00E7a de participar de atividades f\u00EDsicas? 
questionario.parq=Question\u00E1rio PAR-Q
resultado=Resultado
positivo=Positivo
negativo=Negativo
ventilometriavo2=Ventilometria Vo2
vo2max12=VO2 M\u00E1ximo (ml/kg/min)
VO_CAMINHADA_CORRIDA_12_MINUTOS=Caminhada ou corrida de 12 minutos
VO_AEROBICO_DE_BANCO=Aer\u00F3bico de Banco
distancia.percorrida=Dist\u00E2ncia percorrida
tempo.percorrido=Tempo percorrido
dir=dir.
esq=esq.
VO_CAMINHADA_2400_M=Teste de 2400 metros
pesoxpercentual=Peso X percentual de gordura
medidas=Medidas
frequencia.periodo=Frequ\u00EAncia no per\u00EDodo
previstas=previstas
media.semana=M\u00E9dia de %s vezes por semana
ANTEBRACO_ESQ=Antebra\u00E7o esquerdo
ANTEBRACO_DIR=Antebra\u00E7o direito
BRACO_RELAXADO_ESQ=Bra\u00E7o relaxado esquerdo
BRACO_RELAXADO_DIR=Bra\u00E7o relaxado direito
BRACO_CONTRAIDO_ESQ=Bra\u00E7o contra\u00EDdo esquerdo
BRACO_CONTRAIDO_DIR=Bra\u00E7o contra\u00EDdo direito
COXA_DISTAL_DIR=Coxa distal direita
COXA_DISTAL_ESQ=Coxa distal esquerda
COXA_MEDIAL_DIR=Coxa medial direita
COXA_MEDIAL_ESQ=Coxa medial esquerda
COXA_PROXIMAL_DIR=Coxa proximal direita
COXA_PROXIMAL_ESQ=Coxa proximal esquerda
PANTURRILHA_DIR=Panturrilha direita
PANTURRILHA_ESQ=Panturrilha esquerda
PESCOCO=Pesco\u00E7o
OMBRO=Ombro
TORAX=T\u00F3rax / Busto relaxado
QUADRIL=Quadril
CINTURA=Cintura
CIRCUNFERENCIA_ABDOMINAL=Circunfer\u00EAncia abdominal
GLUTEO=Gl\u00FAteo
BRACO_ESQ=Bra\u00E7o esquerdo
BRACO_DIR=Bra\u00E7o direito
grupos.musculares.trabalhados=Grupos musculares trabalhados
grupos.musculares.programa.atual=No programa atual
grupos.musculares.programa.periodo=No per\u00EDodo
hint.grupos.trabalhados.periodo=O c\u00E1lculo \u00E9 feito com base no n\u00FAmero de s\u00E9ries executadas por atividade no per\u00EDodo.<br/> O relacionamento atividade / grupo muscular \u00E9 feito no cadastro de atividades.
hint.grupos.trabalhados.programa=O c\u00E1lculo \u00E9 feito com base no n\u00FAmero de s\u00E9ries cadastradas por atividade no programa de treino atual.<br/> O relacionamento atividade / grupo muscular \u00E9 feito no cadastro de atividades.
historico.imc=Hist\u00F3rico de IMC
add.aluno.experimental=Adicionar aluno experimental
add.aluno.aulafutura=Adicionar aluno aula futura
aluno.experimental=Aluno experimental
aluno=Aluno
matricula=Matr\u00EDcula
produto.free.pass=Produto free pass ou di\u00E1ria
produto.free.pass.existe=Cliente j\u00E1 tem um Free Pass no dia da aula.
marcar.presenca.aula=Confirmar presen\u00E7a do aluno.
desmarcar.aluno.aula=Desmarcar aluno da aula
desmarcar.presenca.aula=A presen\u00E7a do aluno j\u00E1 foi confirmada. Clique novamente para reverter.
atividades.trabalhadas.periodo=Atividades trabalhadas no per\u00EDodo
proxima.avaliacao=Pr\u00F3xima avalia\u00E7\u00E3o
CONTROLAR_POR_FREEPASS=Aulas experimentais por freepass
LANCAR_AGENDAMENTO_PROXIMA_AVALIACAO=Lan\u00E7ar agendamento para pr\u00F3xima avalia\u00E7\u00E3o f\u00EDsica
LANCAR_PRODUTO_AVALIACAO=Lan\u00E7ar produto para pr\u00F3xima avalia\u00E7\u00E3o f\u00EDsica
VALIDAR_PRODUTO_AVALIACAO=Validar produto ao lan\u00E7ar avalia\u00E7\u00E3o f\u00EDsica
LANCAR_PRODUTO_AVALIACAO_DATA_VENCIMENTO=Data de vencimento da parcela do produto avalia\u00E7\u00E3o f\u00EDsica igual a data do agendamento 
agendar.avaliacao.fisica=Agendar pr\u00F3xima avalia\u00E7\u00E3o
evento.sem.disponibilidade=N\u00E3o existe disponibilidade para este evento na data informada.
horarios.disponiveis=Hor\u00E1rios dispon\u00EDveis
horarios.escolha=Escolha um hor\u00E1rio dispon\u00EDvel
horarios.informe=Ou informe o hor\u00E1rio desejado
agendado.horario=Agendada para 
historico.avaliacoes=Hist\u00F3rico de avalia\u00E7\u00F5es f\u00EDsicas
imprimir.selecionadas=Imprimir selecionadas
abrir.pagina.avaliacao=Continue a avalia\u00E7\u00E3o f\u00EDsica em um dispositivo m\u00F3vel:
abrir.pagina.avaliacao.link=Link direto
orientacao.mude=Mude a orienta\u00E7\u00E3o do seu dispositivo para paisagem.
avaliacoes.realizadas=Avalia\u00E7\u00F5es realizadas no per\u00EDodo
novas=Novas
reavaliacoes=Reavalia\u00E7\u00F5es
avaliacoes=Avalia\u00E7\u00F5es
ativos.sem.avaliacao=Ativos sem avalia\u00E7\u00E3o
ativos.avaliacao.atrasada=Ativos com avalia\u00E7\u00E3o atrasada
previsao.avaliacao=Previs\u00E3o de reavalia\u00E7\u00E3o no per\u00EDodo
previstas=Previstas
realizadas=Realizadas
atrasadas=Atrasadas
futuras=Futuras
alunos.geral=Alunos - Total
de=De
ate=at\u00E9
top.alunos.perderam.perc.gordura=Alunos que perderam % de gordura - Total
top.alunos.perderam.peso=Alunos que perderam peso - Total
perda.peso.carteira=Perda de peso por carteira - Total
PRODUTO_AVALIACAO=Produto de Avalia\u00E7\u00E3o F\u00EDsica no ZW
treino.rapido.montar=Montar um treino
alunos.parq.positivo=Alunos Par-Q positivo
top.alunos.ganharam.massa=Alunos que ganharam massa magra - Total
objetivos.alunos=Objetivos dos alunos
objetivos.alunos.total=Objetivos dos alunos - Total
intensidade.esforco=Intensidade do esfor\u00E7o
hint.fc.intensidade.treino=A frequ\u00EAncia card\u00EDaca m\u00E1xima \u00E9 sugerida pelo sistema atrav\u00E9s do m\u00E9todo <b>Astrand</b>, que consiste na seguinte f\u00F3rmula: <br/> Nas mulheres, <b>226 - a idade</b> <br/> Nos homens, <b>220 - a idade</b>.<br/>A tabela de frequ\u00EAncia card\u00EDaca do treino \u00E9 calculada seguindo o m\u00E9todo proposto por <b>Karvonen</b>, cuja f\u00F3rmula \u00E9: FCtreino = FCrepouso + ((FCmax - FCrepouso) x (intensidade/100)). 
hint.dobras=DOBRAS CUT\u00C2NEAS: medidas em mil\u00EDmetros
hint.perimetria=PERIMETRIA: medidas em cent\u00EDmetros
qr.code=QR Code
gerar.qr.code.evolucao=Compartilhar via QR Code um link para que o aluno possa acompanhar sua evolu\u00E7\u00E3o f\u00EDsica.
gerar.email.evolucao=Compartilhar via e-mail um link para que o aluno possa acompanhar sua evolu\u00E7\u00E3o f\u00EDsica.
montar.treino=Montar treino
hint.rml=Resist\u00EAncia muscular localizada
hint.rml.idadeInvalida=N\u00E3o ser\u00E1 calculado RML para este cliente devido a sua idade
produto.validado=Produto validado:
clique.informacoes=Clique para obter mais informa\u00E7\u00F5es
hint.pulso=<b>Local de coleta</b> \u2013 dist\u00E2ncia entre os procesos estiloides doo r\u00E1dio e da ulna.<br/><b>Crit\u00E9rios na mensura\u00E7\u00E3o</b> \u2013 estando o testado sentado com o cotovelo em 90 graus de flex\u00E3o, o punho em flex\u00E3o de 90 graus e o ante-bra\u00E7o pronado.<br/><b>Coleta</b> \u2013 o di\u00E2metro deve ser feito com as astes do Paquimetro nas duas estruturas sem usar muita for\u00E7a. Deve ser sentido a estrutura \u00F3ssea.
hint.femur=<b>Local de coleta</b> \u2013 dist\u00E2ncia entre os c\u00F4ndilos medial e laterla do f\u00EAmur.<br/><b>Crit\u00E9rios na mensura\u00E7\u00E3o</b> \u2013 estando o testado sentado com os p\u00E9s, apoiados no ch\u00E3o, a coxa formando um \u00E2ngulo de 90 graus com o tronco e a perna formando \u00E2ngulo de 90 graus com a coxa.<br/><b>Coleta</b> \u2013 o di\u00E2metro deve ser feito com as astes do Paquimetro nas duas estruturas sem usar muita for\u00E7a. Deve ser sentido a estrutura \u00F3ssea.
hint.menor.igual.que=Menor ou igual que
hint.maior.igual.que=Maior ou igual que
nenhuma.atividade.periodo=Nenhuma atividade trabalhada no per\u00EDodo.
nenhum.grupo.associado=Nenhum grupo associado.
assinatura.aluno=Assinatura do aluno
assinatura.remover=Remover assinatura
certeza.assinatura.remover=Tem certeza que deseja remover a assinatura do aluno?
title.situacao=Situa\u00E7\u00E3o:
SITUACAO=Situa\u00E7\u00E3o
gerar.relatorio=Gerar Relat\u00F3rio
title.analitico=Mostrar Anal\u00EDtico:
title.mostrar.periodo=Mostrar por Per\u00EDodo:
title.brinde=Brindes:
title.situacao.ativo=Ativo
title.situacao.inativo=Inativo
TROCA_NOMENCLATURA_CROSSFIT=Usar nomenclatura para treinamento funcional
cadastros.tipoWod=Tipo Wod
cadastros.tipoWods=Tipos Wods
cadastros.tipoWod.cadastrados=Tipo Wods Cadastrados
cadastros.tipo.ordenamento=Tipo Ordenamento
cadastros.periodizacao=Periodiza\u00E7\u00E3o
cadastros.periodizacaos=Periodiza\u00E7\u00E3os
cadastros.periodizacao.cadastros=Periodiza\u00E7\u00F5es
cadastros.profissional.primeiro=Profissionais em primeiro na ordena\u00E7\u00E3o
cadastros.ordenamentos=Ordenamentos
cadastros.ordenamentos.selecionado=Ordenamentos Selecionados
cadastros.trabalhar.ranking=Vai utilizar ranking ?
anexo.excluir=Excluir Anexo
anexo.inserido=Anexo j\u00E1 inserido
PADRAO=Padr\u00E3o
AVALIACAO_INTEGRADA=Avalia\u00E7\u00E3o integrada
QUALIDADE_VIDA=Avalia\u00E7\u00E3o da qualidade de vida
QUALIDADE_MOVIMENTO=Avalia\u00E7\u00E3o da qualidade do movimento
UTILIZAR_AVALIACAO_INTEGRADA=Utilizar avalia\u00E7\u00E3o integrada
agrupamento=Agrupamento
movimento3d=Avalia\u00E7\u00E3o de movimento 3D
hint_movimento3d=Objetivo em analisar mobilidade, estabilidade e controle do movimento identificando a funcionalidade das articula\u00E7\u00F5es e desequil\u00EDbrios nas cadeias. Foco na preven\u00E7\u00E3o de dores.
placar0=PLACAR 0 - Apresenta dor para executar o movimento, consegue executar com dor ou n\u00E3o consegue executar. Dor em qualquer articula\u00E7\u00E3o da cadeia representa um placar 0. 
placar1=PLACAR 1 - Executa o movimento com dificuldade, n\u00E3o consegue chegar em todo os padr\u00F5es estabelecidos ou apresenta falta de controle do movimento.
placar2=PLACAR 2 - Executa o movimento com boa amplitude e controle.
placar3=PLACAR 3 - Executa o movimento com excel\u00EAncia de controle e passa as amplitudes estabelecidas.
mobilidade=Mobilidade
cadeia_anterior=Cadeia anterior
cadeia_posterior=Cadeia posterior
cadeia_lateral=Cadeia lateral
cadeia_rotacional=Cadeia rotacional
estabilidade_controle=Estabilidade e controle
controle=Controle
cadeia_fechamento=Cadeia de fechamento
cadeia_abertura=Cadeia de abertura
placar_soma=Placar (soma)
placar_media=Placar (m\u00E9dia)
hint_placar_soma=(soma dos n\u00FAmeros acima)
hint_placar_media=(m\u00E9dia dos n\u00FAmeros acima)
dir=dir.
esq=esq.
enviar.email=Enviar por e-mail
vidaFRACA=Fraca (0 - 5 pontos)
vidaMEDIA=M\u00E9dia (6 - 8 pontos)
vidaBOA=Boa (9 - 11 pontos)
vidaMUITO_BOA=Muito boa (12 -13 pontos)
vidaEXCELENTE=Excelente (14 - 15 pontos)
movFRACA=Fraca (0 - 6 pontos)
movMEDIA=M\u00E9dia (6,1 - 10 pontos)
movBOA=Boa (10,1 - 14 pontos)
movMUITO_BOA=Muito boa (14,1 - 16 pontos)
movEXCELENTE=Excelente (16,1 - 18 pontos)
resultado_qualidade_vida=Qualidade de vida
resultado_qualidade_movimento=Qualidade de movimento
BOA=Boa
MUITO_BOA=Muito boa
EXCELENTE=Excelente
gravar.avaliacao.integrada=Gravar
nova.avaliacao=Nova avalia\u00E7\u00E3o
cadastros.aluno=Aluno
respondida.em=Respondida em
anexo.inserido=Anexo inserido com sucesso
em=em
recomendacoes=Recomenda\u00E7\u00F5es
tip.recomendacoes=O avaliador deve usar esse espa\u00E7o para fazer recomenda\u00E7\u00F5es ao aluno e ao professor que ir\u00E1 prescrever o programa de treino
editando.avaliacao=Editando avalia\u00E7\u00E3o f\u00EDsica de 
visualizando.avaliacao=Visualizando avalia\u00E7\u00E3o f\u00EDsica atual
CFG_OBJETIVOS_ANAMNESE=Utilizar objetivos e anamnese
CFG_PESO_ALTURA_PA_FC=Utilizar peso, altura, press\u00E3o arterial e frequ\u00EAncia card\u00EDaca
CFG_PARQ=Utilizar question\u00E1rio PAR-Q
CFG_DOBRAS_CUTANEAS=Utilizar dobras cut\u00E2neas
CFG_PERIMETRIA=Utilizar perimetria
CFG_COMPOSICAO_CORPORAL=Utilizar composi\u00E7\u00E3o corporal
CFG_FLEXIBILIDADE=Utilizar teste de flexibilidade
CFG_POSTURA=Utilizar avalia\u00E7\u00E3o postural
CFG_RML=Utilizar teste de resist\u00EAncia muscular localizada
CFG_VO2MAX=Utilizar testes de VO2 m\u00E1ximo
CFG_RECOMENDACOES=Utilizar recomenda\u00E7\u00F5es do profissional
salvar.continuando=Salvar dados e continuar lan\u00E7ando os dados
IND_AF_TODAS=Avalia\u00E7\u00F5es f\u00EDsicas no per\u00EDodo
IND_AF_NOVAS=Novas avalia\u00E7\u00F5es f\u00EDsicas no per\u00EDodo
IND_AF_REAVALIACOES=Reavalia\u00E7\u00F5es f\u00EDsicas realizadas no per\u00EDodo
IND_AF_PREVISTAS=Reavalia\u00E7\u00F5es f\u00EDsicas previstas para o per\u00EDodo
IND_AF_REALIZADAS=Reavalia\u00E7\u00F5es f\u00EDsicas previstas e realizadas no per\u00EDodo
IND_AF_ATRASADAS=Reavalia\u00E7\u00F5es f\u00EDsicas previstas e atrasadas no per\u00EDodo
IND_AF_FUTURAS=Reavalia\u00E7\u00F5es f\u00EDsicas previstas e que ainda v\u00E3o acontecer
IND_AF_ATIVOS_SEM=Total de alunos ativos e sem avalia\u00E7\u00E3o f\u00EDsica
IND_AF_ATIVOS_AVALIACAO_ATRASADA=Total de alunos ativos com avalia\u00E7\u00E3o f\u00EDsica atrasada
IND_AF_PERDERAM_PERCENTUAL=Total de alunos que perderam percentual de gordura
IND_AF_PERDERAM_PESO=Total de alunos que perderam peso
IND_AF_GANHARAM_MASSA=Total de alunos que ganharam massa muscular
IND_AF_PARQ_POSITIVO=Total de alunos com Par-Q positivo
IND_AF_OBJETIVOS=Objetivos dos alunos
CFG_RECOMENDACOES=Utilizar recomenda\u00E7\u00F5es do profissional
CFG_VENTILOMETRIA=Utilizar informa\u00E7\u00F5es de ventilometria
CFG_SOMATOTIPIA=Utilizar somatotipia
CFG_TESTES_CAMPO=Utilizar testes de VO2 m\u00E1ximo de campo
CFG_IMPORTACAO_BIOSANNY=Habilitar upload avaliação física BioSanny
CFG_TESTE_QUEENS=Utilizar testes de VO2 com protocolo Queens College 
CFG_TESTE_BIKE=Utilizar testes de VO2 m\u00E1ximo na Bike
VALIDAR_AGENDA_AULACHEIA=Validar se o cliente j\u00E1 possui compromisso no mesmo hor\u00E1rio
cfg.ordem.perimetria=Ordem das perimetrias no cadastro da avalia\u00E7\u00E3o f\u00EDsica
cfg.ordem.dobras=Ordem das dobras no cadastro da avalia\u00E7\u00E3o f\u00EDsica
tip.weltman.obeso=Este protocolo n\u00E3o se utiliza de dobras cut\u00E2neas, mas das medidas da circunfer\u00EAncia abdominal(cm), da altura (cm) e da massa (Kg). Preencha a perimetria <b>Circunfer\u00EAncia abdominal</b> com a m\u00E9dia de duas medidas da circunfer\u00EAncia do Abd\u00F4men para obter o percentual de gordura do avaliado. 
teste.bike=Teste subm\u00E1ximo de Astrand em cicloerg\u00F4metro (bicicleta) 
teste.queens=Teste subm\u00E1ximo de Queens College
cadastros.aluno.supraespinhal=Supra-espinhal
SOMATOTIPIA=Somatotipia
endomorfia=Endomorfia
ectomorfia=Ectomorfia
mesomorfia=Mesomorfia
hint.tornozelo=<b>Local de coleta</b> \u2013 dist\u00E2ncia entre os dois mal\u00E9olos (medial e lateral) <br/><b>Coleta</b> \u2013 estando o testado sentado e com os p\u00E9s apoiados no ch\u00E3o.
hint.cotovelo=<b>Local de coleta</b> \u2013 dist\u00E2ncia entre os epic\u00F4ndilos medial e lateral do \u00FAmero<br/><b>Coleta</b> \u2013 com o indiv\u00EDduo em posi\u00E7\u00E3o ortost\u00E1ticam bra\u00E7o flexionado em 90 graus com o tronco e o antebra\u00E7o formando 90 graus com o bra\u00E7o.
teste.bike.fc=Frequ\u00EAncia card\u00EDaca (bpm)
teste.bike.carga=Carga (kg-m/min)
teste.bike.vo2=VO2 estimado (l)
teste.bike.vo2Max=VO2 M\u00E1ximo (ml/kg/min):
SLAUGHTER=Slaugther (adolescentes)
KATCH=Katch & McArdle
clique.informacoes.categoria.percentual=Clique para ver a tabela de refer\u00EAncia para a classifica\u00E7\u00E3o do percentual de gordura.
nivel.idade=N\u00EDvel / Idade
EXCELENTE=Excelente
BOM=Bom
ACIMA_MEDIA=Acima m\u00E9dia
media=M\u00E9dia
ABAIXO_MEDIA=Abaixo m\u00E9dia
RUIM=Ruim
MUITO_RUIM=Muito ruim
tabela.percentual.mulheres=Percentual de gordura (G%) para mulheres
tabela.percentual.homens=Percentual de gordura (G%) para homens
restricoes=Restri\u00E7\u00F5es
METABOLICO=Metab\u00F3lico
ORTOPEDICO=Ortop\u00E9dico
CARDIOLOGICO=Cardiol\u00F3gico
RESPIRATORIO=Respirat\u00F3rio
tipTreinosRevisar=Alunos com programa de treino com data prevista de renova\u00E7\u00E3o para o per\u00EDodo consultado.
RANKING_UNISSEX=Ranking unissex
RANKING_MASCULINO=Apenas sexo masculino
RANKING_SEPARADO_MESMA_TELA=Separado por sexo na mesma tela
RANKING_FEMININO=Apenas sexo feminino
CFG_RANKING_MONITOR=Configure o seu ranking para o monitor
CFG_RANKING_MONITOR_header=Gerar link para o ranking do WOD
CFG_RANKING_MONITOR_GENERO=G\u00EAnero dos participantes
CFG_RANKING_MONITOR_NIVEL=N\u00EDvel dos participantes
CFG_RANKING_MONITOR_NIVEL_TODOS=Todos os n\u00EDveis
CFG_RANKING_MONITOR_NIVEL_UM=Um n\u00EDvel espec\u00EDfico
CFG_RANKING_MONITOR_NIVEL_TODOS_tip=Ser\u00E3o exibidos todos os n\u00EDveis que tiverem resultados e um ranking geral, alternado a cada 2 minutos.
CFG_RANKING_MONITOR_NIVEL_UM_tip=Escolha um n\u00EDvel abaixo para exibir no monitor:
RANKING_SEPARADO_ALTERNANDO=Separado por sexo alternando
RANKING_LINK_MONITOR=Gerar link para o monitor
RANKING_LINK_EXIBICAO=Exibi\u00E7\u00E3o dos resultados:
PROTOCOLO_QUEENS_COLLEGE=Protocolo Queens College
AGENDA_ABRIR_VAZIA=Abrir agenda com nenhum evento selecionado
linha_do_tempo=Linha do tempo
FEZ_AULA=Aula coletiva
HABILITAR_CROSSFIT=Habilitar m\u00F3dulo Crossfit
MODULO_PRINCIPAL_APLICATIVO=M\u00F3dulo Principal App do Aluno
LINK_APP_EMAIL=Link do app no e-mail
MODELO_EVOLUCAO_FISICA=Qual avalia\u00E7\u00E3o f\u00EDsica deseja comparar na evolu\u00E7\u00E3o f\u00EDsica
PROIBIR_MARCAR_AULA_PARCELA_VENCIDA=Proibir o aluno marcar aula coletiva, caso exista mensagem de parcela vencida
NAO_EXIBIR_NUMERO_DE_VEZES_NO_APP=N\u00E3o exibir o n\u00FAmero de vezes da Modalidade no App
#INICIO DASH ALUNOS
minha_carteira=Meus alunos
#FIMDASH ALUNOS
MINUTOS_DESMARCAR_COM_ANTECEDENCIA=Tempo para desmarcar com anteced\u00EAncia
pressao_frequencia_cardiaca=PRESS\u00C3O E FREQU\u00CANCIA CARD\u00CDACA EM REPOUSO
USAR_NOVA_MONTAGEM=Usar nova montagem de fichas
DIAS_SEM_OBSERVACAO_CONSIDERAR_DESACOMPANHADO=N\u00FAmero de dias para considerar um aluno desacompanhado
AULAS_CHEIAS=Tv Aula
ponto.aula.datainicio=Data In\u00EDcio Aula
ponto.aula.datafim=Data Fim Aula
PERMITIR_ALUNO_MARCAR_AULA_POR_TIPO_MODALIDADE=Permitir que o aluno marque aula pelo tipo de modalidade
URL_GOOGLE_PLAY=link do app na loja da Google Play
URL_ITUNES=link do app na loja da Apple Store
NOME_APLICATIVO_PARA_ENVIO_EMAIL=Nome do aplicativo no envio de E-mail
LANCAR_ATESTADO_ZILLYONWEB=Lan\u00E7ar atestado no ZW ao incluir Par-Q
PRODUTO_ATESTADO=Produto de Atestado no ZW
USAR_PRESSAO_SISTOLICA_DIASTOLICA=Usar press\u00E3o arterial sist\u00F3lica e diast\u00F3lica
USAR_ORDEM_DOBRAS_CUTANEAS=Definir ordem das dobras cut\u00E2neas na avalia\u00E7\u00E3o f\u00EDsica
OBRIGAR_CAMPOS_DOBRAS_BIOIMPEDANCIA=Tornar obrigatorio campos de dobras do protocolo bioimpedancia
cadastros.localRetiraFicha=Configura\u00E7\u00F5es Retira Ficha
cadastros.localRetiraFicha.descricao = Descri\u00E7\u00E3o do Local do Retira Ficha
cadastros.localRetiraFicha.codigo= Codigo Coletor Retira Ficha
cadastros.localRetiraFicha.urlTurmas= URL Turmas da Configuracao Retira Ficha
cadastros.localRetiraFicha.pastaFotos= Pasta Fotos 
cadastros.localRetiraFicha.pastaDados= Pasta Dados 
cadastros.localRetiraFicha.monitor= Monitor
cadastros.localRetiraFicha.barraFerramentas= Esconder Barra de Ferramentas
cadastros.localRetiraFicha.modoCompartilhado= Modo Compartilhado
cadastros.localRetiraFicha.chave= Chave Empresa da Configuracao Retira Ficha
cadastros.localRetiraFicha.mostrarFrequenciaPorcentagem= Mostrar Frequ\u00EAncia em Porcentagem 
cadastros.localRetiraFicha.aumentarInformacoesAluno= Aumentar Informacoes do Aluno
cadastros.localRetiraFicha.mostrarNotificacoes= Mostrar Notifica\u00E7\u00F5es
cadastros.localRetiraFicha.naoImprimirProgramasVencidos= N\u00E3o Imprimir Programas Vencidos
cadastros.localRetiraFicha.habilitarModoVisualizacao= Habilitar Modo Visualiza\u00E7\u00E3o
cadastros.localRetiraFicha.tempoAguarde= Tempo de Aguarde
cadastros.localRetiraFicha.componenteVideo= Componente de V\u00EDdeo
cadastros.localRetiraFicha.Impressao = Impress\u00E3o
cadastros.localRetiraFicha.layout = Layout Ficha
cadastros.localRetiraFicha.utilizarImpressoraMatricial = Utilizar Impressora Matricial
cadastros.localRetiraFicha.tipoImpressora = Tipo Impressora
cadastros.localRetiraFicha.porta = Porta
cadastros.localRetiraFicha.fonte = Fonte
cadastros.localRetiraFicha.conexao = Conex\u00E3o
cadastros.localRetiraFicha.destBuscaRrlAutomatica = Desativar Busca URL Automatica
cadastros.localRetiraFicha.ativarDigitalNeokoros = Ativar Digital Neokoros
cadastros.localRetiraFicha.utilizaServidorUnificado = Utiliza Servidor Unificado
cadastros.localRetiraFicha.serial = Serial
cadastros.localRetiraFicha.digital = Digital
cadastros.localRetiraFicha.portaDigital = Porta Digital
cadastros.localRetiraFicha.resolucao = Resolu\u00E7\u00E3o
cadastros.localRetiraFicha.ativarDigitalNitigen = Ativar Digital Nitigen
cadastros.localRetiraFicha.equipamento = Equipamento
cadastros.localRetiraFicha.bancodeDados = Banco de Dados
cadastros.localRetiraFicha.usuarioBDDigital = Usuario BD Digital
cadastros.localRetiraFicha.senhaBDDigital = Senha BD Digital
cadastros.localRetiraFicha.turmas= Turmas
cadastros.localRetiraFicha.layoutFichaTurma = Layout Ficha Turma
cadastros.localRetiraFicha.ativarModuloAulas = Ativar M\u00F3dulo Aulas
cadastros.localRetiraFicha.desativarModuloRetiraFicha = Desativar M\u00F3dulo Retira Fichas
cadastros.localRetiraFicha.fecharTelaAposInclusao= Fechar Tela Ap\u00F3s Inclus\u00E3o
cadastros.localRetiraFicha.permitirImpressaoSegundaVia= Permitir Impressao Segunda Via
cadastros.localRetiraFicha.telaPrincipal= Tela Principal
cadastros.localRetiraFicha.logoCabecalho= Logo Cabecalho
cadastros.localRetiraFicha.naoMostrarLogoCabecalho= N\u00E3o Mostrar Logo Cabecalho
cadastros.localRetiraFicha.logoRodape= Logo Rodap\u00E9
cadastros.localRetiraFicha.naoMostrarRodape= N\u00E3o Mostrar Rodap\u00E9
cadastros.localRetiraFicha.linkFacebook= Link Facebook
cadastros.localRetiraFicha.linkTwitter= Link Twitter
cadastros.localRetiraFicha.aguardarCarregamentoSistema= Aguardar Carregamento Sistema
cadastros.localRetiraFicha.pastaBanner= Pasta Banner
cadastros.localRetiraFicha.pastaBannerCustom= Pasta Banner Custom
cadastros.localRetiraFicha.naoUsarBannerSistema= N\u00E3o Usar Banner Sistema
dica.avaliacao.enviaComparativo=Voc\u00EA pode combinar avalia\u00E7\u00F5es para enviar. <br/>Basta selecionar as avalia\u00E7\u00F5es desejadas e clicar no bot\u00E3o para enviar a compara\u00E7\u00E3o.
enviar.comparativoIntegradaemail=Enviar Avali\u00E7\u00F5es Integradas por e-mail
TRAZER_ULTIMA_AVALIACAO_INTEGRADA = Trazer \u00DAltima Avalia\u00E7\u00E3o Integrada 
imprimir.comparativoIntegrada=Imprimir Avali\u00E7\u00F5es Integradas
CFG_AVALIACAO_INTEGRADA = Usar Avalia\u00E7\u00E3o Integrada na Avalia\u00E7\u00E3o F\u00EDsica 
AVALIACOES_INTEGRADAS = Avalia\u00E7\u00F5es integradas
cadastros.localRetiraFicha.versaoRetiraFicha= Vers\uFFFDo do Retira Ficha
cadastros.ficha.genero=G\u00EAnero
cadastros.categoriaatividade.nivel=N\u00EDvel
DESCRICAO_AVALIACAO_INTEGRADA = Descri\u00E7\u00E3o para avalia\u00E7\u00E3o integrada
REDE_SOCIAL_FACEBOOK = Link do Facebook da empresa
REDE_SOCIAL_INSTAGRAM = Link do Instagram da empresa
categoria.avaliacao.imc.nenhum=Nenhum
categoria.avaliacao.imc.baixo=Abaixo do peso normal
categoria.avaliacao.imc.normal=Peso normal
categoria.avaliacao.imc.sobrepeso=Sobrepeso
categoria.avaliacao.imc.obesidade1=Obesidade grau I
categoria.avaliacao.imc.obesidade2=Obesidade grau II
categoria.avaliacao.imc.obesidade3=Obesidade grau III
categoria.avaliacao.percentualGordura.nenhum=Nenhuma
categoria.avaliacao.percentualGordura.excelente=Excelente
categoria.avaliacao.percentualGordura.bom=Bom
categoria.avaliacao.percentualGordura.acimaMedia=Acima m\u00E9dia
categoria.avaliacao.percentualGordura.media=M\u00E9dia
categoria.avaliacao.percentualGordura.abaixoMedia=Abaixo m\u00E9dia
categoria.avaliacao.percentualGordura.ruim=Ruim
categoria.avaliacao.percentualGordura.muitoRuim=Muito ruim
categoria.avaliacao.percentualGordura.obesidade=Obesidade
categoria.avaliacao.percentualGordura.alto=Alto
avaliacao.resistencia=Resist\u00EAncia
avaliacao.flexibilidade.classificacao.fraca=Fraca
avaliacao.flexibilidade.classificacao.regular=Regular
avaliacao.flexibilidade.classificacao.media=M\u00E9dia
avaliacao.flexibilidade.classificacao.boa=Boa
avaliacao.flexibilidade.classificacao.excelente=Excelente
avaliacao.protocolo.total=Total
avaliacao.postural.naoDetectada=Nenhuma altera\u00E7\u00E3o detectada
ALTERACAO_PROFESSOR_NIVEL=Altera\u00E7\u00E3o no cadastro aluno
NOVO_PROGRAMA=Novo programa
RENOVOU_PROGRAMA=Renovou programa
EXCLUIU_PROGRAMA=Excluiu programa
CRIOU_AVALIACAO=Criou avalia\u00E7\u00E3o
EXCLUIU_AVALIACAO=Excluiu avalia\u00E7\u00E3o
ADICIONAR_OBSERVACAO=Adicionar observa\u00E7\u00E3o
ADICIONAR_ANEXO=Adicionar anexo
EXCLUIR_ANEXO=Excluir anexo
AGENDOU_SERVICO=Agendou um servi\u00E7o
ALTEROU_SERVICO=Alterou um agendamento de servi\u00E7o
EXCLUIU_SERVICO=Excluiu um agendamento de servi\u00E7o
NORMAL=Normal
POSITIVA=Positiva
VALGO_DINAMICO_MEMBRO_DOMINANTE=Valgo din\u00E2mico em membro dominante
VALGO_DINAMICO_MEMBRO_NAO_DOMINANTE=Valgo din\u00E2mico em membro n\u00E3o dominante
VALGO_DINAMICO_BILATERAL=Valgo din\u00E2mico bilateral
ABAIXO_DA_MEDIA=Abaixo da m\u00E9dia
HIPERMOBILIDADE=Hipermobilidade
