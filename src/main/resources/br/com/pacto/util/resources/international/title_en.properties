# To change this template, choose Too<PERSON> | Templates
# and open the template in the editor.
CALCULAR_NR_AULAS_PREVISTAS_AUTOMATICO=Calculate the number of classes scheduled/end of program
alunosacessaram=Acessos de alunos de turma
principal.lingua=Language
APPS_ATIVOS=Aplicativos ativos
ALUNOS_APP_INSTALADO=Alunos com App Instalado
ALUNOS_APP_INSTALADO_ATIVOS=Alunos ativos que instalaram o App
ALUNOS_APP_NAO_INSTALADO=Alunos que n\uFFFDo instalaram o App
app.instalado=Alunos utilizam atualmente o aplicativo
principal.cadastros = Signups
principal.gestao = Management
principal.alunos = Students
principal.agenda = Calendar
principal.notificacoes = Notifications Center
principal.aplicativo = App
principal.pesquisa = Search
cadastros.musculos = Muscles
cadastros.gruposmusculares = Muscles Groups
cadastros.aparelhos = Devices
cadastros.atividades = Activities
cadastros.programas = Training Program
cadastros.todosalunos = View All
cadastros.agendamentos = Schedules
cadastros.compromissos = Commitment
cadastros.objetivos = Predefined goals
cadastros.fichas = Sheets
cadastros.salvar=Save
cadastros.addNovaAtividade=Add new activitie
cadastros.addNovoGrupo=Add new group
cadastros.musculosCadastrados=Registered muscles
cadastros.adicionarMusculo=Add muscle
cadastros.filtrar=Filter
cadastros.relacoes=relations
cadastros.musculo.nome=Muscle name
cadastros.excluir=Delete
cadastros.gruposmuscularescadastrados=Registered Muscles Groups
cadastros.addNovoMusculo=Add new muscle
cadastros.grupoMuscular.nome=Group name
cadastros.adicionarGrupoMuscular=Add Muscle Group
cadastros.aparelho.nome=Device name
cadastros.adicionarAparelho=Add Device
cadastros.aparelhoscadastrados=Registered Devices
cadastros.aparelho.tipo=Type
cadastros.atividade.nome=Activitie name
cadastros.atividade.descricao=Description
cadastros.atividade.ativo=Active
cadastros.atividade.tipo=Type
cadastros.atividade.copyDescToSerie=Copy desc. to serie
cadastros.atividade.identificador=Identifier
cadastros.atividade.addEmpresa=Add company
cadastros.adicionarAtividade=Add activitie
cadastros.addNovoAparelho=Add new device
cadastros.addCategoria=Add new category
cadastros.categoriaAtividades=Activity's Cat.
cadastros.atividadescadastradas=Registered activities
cadastros.nome=Name
cadastros.descricao=Description
cadastros.sim=Yes
cadastros.nao=No
cadastros.categoriaatividadeadicionar=Add category
cadastros.categoriaatividade.nome=Category name
cadastros.categoriaatividade=Categories of activities
cadastros.categoriaatividadecadastradas=Registered categories of activities
cadastros.categoriaficha=Categories of sheets
cadastros.categoriafichacadastradas=Registered categories of sheets
cadastros.addFichas=Add new sheet
cadastros.nivel=Levels
cadastros.adicionarNivel=Add level
cadastros.nivel.nome=Level name
cadastros.nivel.ordem=Order
cadastros.adicionarobjetivos=Add goal
cadastros.objetivos.nome=Goal name
cadastros.objetivoscadastrados=Registered predefined goals
cadastros.niveiscadastrados=Registered levels
cadastros.imagens=Images
cadastros.imagensadicionar=Add image
cadastros.imagem.nome=Image name
cadastros.imagem.endereco=Image path
cadastros.imagemcadastradas=Registered images
cadastros.addImagem=Add new image
cadastros.todos=All
domingo=Sunday
segunda=Monday
terca=Tuesday
quarta=Wednesday
quinta=Thursday
sexta=Friday
sabado=Saturday
DM=S
SG=M
TR=T
QA=W
QI=T
SX=F
SB=S
cadastros.ficha=Sheet
cadastros.programa.nome=Training program name
cadastros.detalhes=Details
cadastros.datainicio=Start date
cadastros.datafim=End date
cadastros.diassemana=Days per week
cadastros.programa.aulasprevistas=Classes planned
cadastros.programa.proximarevisao=Next review
cadastros.programa.termino=Finish
cadastros.ficha.nova=New sheet
cadastros.fichas.predefinidas=Predefined sheet
cadastros.objetivos.predefinidos=Predefined objectives
cadastros.atividades.selecionadas=Sheet activities
cadastros.fichas.adicionadas=Selected sheets
cadastros.ficha.editar=Edit sheet
cadastros.ficha.tornarpre=Making predefined
cadastros.fichas.nome=New sheet name
cadastros.ficha.tipoexecucao=Execution type
cadastros.mensagem=Message
cadastros.ajuste=Adjustment
cadastros.valor=Value
cadastros.adicionarAjuste=Add adjustment
cadastros.serie=Series
cadastros.quantidade=Sets
cadastros.carga=Weight
cadastros.repeticoes=Repetitions
cadastros.duracao=Duration
cadastros.distancia=Distance
cadastros.velocidade=Speed
cadastros.descanso=Rest
cadastros.complemento=Complement
cadastros.remover=Remove
cadastros.serie.adicionar=Add series
cadastros.ficha.concluir=Save sheet
cadastros.programa.novo=New program
cadastros.musculo=Muscle
cadastros.grupomuscular=Muscular group
cadastros.aparelho=Device
cadastros.atividade=Activity
cadastros.categoriaatividadesing=Activity category
cadastros.categoriafichasing=Sheet category
cadastros.nivelsing=Level
cadastros.objetivo=Goal
cadastros.imagem=Image
cadastros.programa=Training program
cadastros.serie.adicionada=Series added successfully!
cadastros.aluno.maisinfo=More info
cadastros.aluno.objetivos=Student goals
cadastros.aluno.avaliacao=Evaluation data
cadastros.parqplus=  Student have parq
cadastros.benchmark=Benchmark
cadastros.tipoBenchmark=Type of Benchmark
cadastros.benchmark.tipo=Type
cadastros.benchmark.nome=Name
cadastros.benchmark.exercicios=Description Exercicies
cadastros.benchmark.observacao=Note
#Traduzido por google.translate, jae
cadastros.benchmark.videoIncorporar=URL video incorporate
cancelar=Cancel
cadastros.atividade.remover=Remove activity
cadastros.atividade.salvar=Save activity
disponibilidade=Availability
contatoInterpessoal=Interpersonal contact
prescricaoTreino=Prescription Training
revisaoTreino=Review of Training
renovarTreino=Renew Training
agenda.tipo=Type
agenda.inicio=Start
agenda.fim=End
agenda.diaTodo=All day
agenda.novo=New schedule
agenda.aluno=Student
professor=Teacher
editarDisponibilidade=Edit availability
start=Start
finishTreino=Finish training
finishSerie=Finish series
cadastros.ajustes=Adjustments
realizada=Held
alterarSerie=Change series
status=Status
historicoExecucoes=History plays
series=Series
quilos=Kg
metros=m
minutos=min
semNotificacao=Without notice
diminuiuCarga=Decreased load
aumentouCarga=Increased load
buscar=Search
concluirCadastro=Complete registration
selecioneProfessor=Select a Teacher
naotemprofessor=Without a teacher
voltar=Back
emaillogincontato=Email to login and contact
senhapodedeixar=Password (Can leave blank)
nomecompletoparte=Name (Full or Part)
CPF=CPF
matriculaaluno=Student enrollment
badge=Badge
badges=Badges
valorBadge=Badge value
ativo=Active
horarioComercial=Business hours
repetir=Repeat
diaSemana=Days of the week
repetirAte=Repeat until
remover=Remove
disponibilidades=Availabilities
voltarAgenda=Back
dia=Day
veragenda=See schedule
acompanhar=Follow
agendar=Schedule
configuracoesGlobais=Global settings
configuracoesUsuario=User settings
configuracoesPerfis=Profile settings
configuracoesAtendimento=Attendance settings
novoperfil=New profile
cadastros.tipoEvento=Event types
cadastros.metodo=Training method
cadastros.adicionarTipoEvento=Add type
cadastros.tiposcadastrados=Registered event types
cadastros.tiposEvento=Event types
cadastros.tiponome=Type name
cor=Color
comportamento=Behavior
cortipoevento=Event type color
disponibilidadepara=Availability for
todosTipos=All types
agruparPor=Group by
editar=Edit
fechar=Close
aplicarNSU=Confirm changes
simsomentemesmotipo=As the same type
NENHUM=Don't group
PROFESSOR=Teacher
TIPOEVENTO=Tipo evento
CADASTROS_AUXILIARES=Auxiliary registers
AGENDA=Agenda
ALUNO=Student
GERAL=General
entidade=Entities
funcionalidade=Features
permitir=Allow
Janeiro=January
Fevereiro=February
Marco=March
Abril=April
Maio=May
Junho=June
Julho=July
Agosto=August
Setembro=September
Outubro=October
Novembro=November
Dezembro=December
nrAgendamentos=Number of schedules
cada=each
dias=days
agrupar=Group
apenasAlunosCarteira=Only students in the teacher's wallet
configuracoesUsuarios=Users
usuario=User
usuario.codColabZW=Colaborador ZW
senha=New password
perfil=Profile
editarUsuario=Edit user
editarPerfil=Edit profile
concluirEdicao=End editing
somenteEste=Only this event
todosEventosRepetidos=All repeating events
acompanhadoPor=Accompanied by
vencimentoContrato=Expiration
nascimento=Birthday
nomeperfil=Profile title
PORC_ACIMA_NOTIFICAR=Percentage above the load to notify the teacher
PORC_ABAIXO_NOTIFICAR=Percentage under load to notify the teacher
gestao.inidicadores=Indicators
gestao.agendados=Scheduled
gestao.executados=Executed
itens=items
total=Amount
professores=Teachers
categorias=Categories
calendario=Calendar
tiposEvento=Events
SUGERIR_TIPO_ALTERNADO=Suggest alternate form of execution on record
qrCodePlayStore=Get application on Play Store
qrCodeAppStore=Get application on App Store
qrCodeChaveEmpresa=Capture QrCode Key from Customers
verAgendados=View Scheduling
porcExecucao=Implementation of the Training Program in relation to combined
enfileirarImpressao=Send to Printer
gestao.indicadores=Indicators
gestao.cancelados=Canceled
gestao.faltas=Absence
gestao.indicadoresAgenda=Scheduling Indicators
DURACAO_LIVRE=Free
DURACAO_PREDEFINIDA=Pre-defined
INTERVALO_DE_TEMPO=Interval
duracao_min=Min.
duracao_max=Max.
duracao_evento=Duration
pesquisaGeralAluno=Search Student (Ctrl+Shift+L)
fichaDeHoje=Today's record
previstoRealizado=Intended / Held
digiteAlgoAguarde=Type something and waint
SUGERIR_DESCANSO=Suggest standard rest in series
intervalo_minimo=Minimum interval in case of missing
legenda=Legend
carregando=Loading
leve=Light
grave=Severe
media=Normal
criadoPor=Created by
AGENDA_NOVA_ABA=Open Schedule always in a new tab
irParaAluno=Go To Student
atividadesNaFicha=Activities on sheet
dadosFicha=Data of record
adicionandoAtividadesFicha=Add activities on record
verdadosficha=Edit data of record
cadastros.serie.remover=Series
cadastros.repeticoes.abrev=Rep
cadastros.duracao.abrev=Dur
cadastros.distancia.abrev=Dist
cadastros.velocidade.abrev=Vel
cadastros.descanso.abrev=Desc
cadastros.carga.abrev=Carga
cadastros.serie.gerar=Gen Series
cadastros.serie.gerar.hint=Generate series in all activities
gestao.carteira=Carteira
gestaoAgenda=Management: Agenda
gestaoProfessores=Management: Teachers
indicadoresAgenda=Indicators of Agenda
indicadoresProfessores=Indicators of Teachers
intervaloPesquisa=Range of research
filtros=Filters
data=Date
gestao.semCarteira=Without program
carteiras=Portfolios of teachers
atividadesProfessores=Activities of teachers
indicadoresAtividadesProfessores=Indicators of the activities of teachers
gestaoAtividades=Management: Activities
gestao.treinoNovo=New training
renovar=Renew
gestao.treinoRenovado=Renew training
gestao.treinoRevisado=Revised training
gestao.treinoAcompanhados=Accompanied trainings
cadastros.atividade.seriesApenasDuracao=Series just with Duration
DIAS_ANTES_VENCIMENTO=Days before expiration
DIAS_DEPOIS_VENCIMENTO=Days after expiration
MODULO_AULAS=Enable Lessos module
MODULO_AULAS_ABA_SALDO=Enable Balance tab (Classes Module) on Client App
MODULO_AULAS_ABA_TURMAS=Enable Class tab (Classes Module) on Client App
MODULO_AULAS_ABA_AULAS_COLETIVAS=Enable Collective Lessons tab (Classes Module) on Client App
MODULO_TREINAR=Enable Train module
VALIDAR_HORARIO_CONTRATO=Validate contract time
gestao.treinoProxVencimento=Prox. expiration
gestao.vencidos=Expired
gestao.avaliacao=Rating
gestao.duasEstrelas=2 stars
GRAVE=Severe
LEVE=Light
MEDIA=Normal
horaInicio=Start hour
horaFim=End hour
gestao.indicadoresTreinos=Indicators of trainings
ATIVO=Active
VENCIDO=Expired
TRANCADO=Locked
DESISTENTE=Quitter
CANCELADO=Canceled
MATRICULA=Matriculation
REMATRICULA=Matriculation
RENOVACAO=Renovation
VISITANTE=Visitor
cliente.enviarEmailAtivacao=Send/Resend activation for email
notificacao.push=Sent PUSH
notificacao.sms=Sent SMS
addAluno.usarAplicativo=Do you going use app?
revisar=Review
OUTROS=Others
descricaoSituacao=Current contract situation as of today's student. Regardless of the base date of the query, this is the current situation.
situacoesContrato=Contract situation
situacoesPrograma=Program situation
gravidade=Gravity
gestaoNotificacoes=Management notifications
notificacoes=notifications
dataReferenciaCarteira=Date reference portfolio
cadastros.programa.comoAlterarProfCarteira=To change the teacher's portfolio must run "Add Student to Treino" again.
tipSemTreino=Number of students who are in training but never had an associated training program.
tipTotalAlunos=This indicator takes into account the number of students with the training program based on the date of consultation, involving the teacher portfolio. Students are considered assets and accrued programs.
tipTreinoNovo=Number of new training, ie the first workout after the student enrolls, the teacher identified this as a new training workout. The teacher chose the "New Training" button
tipProgramasVencidos=Number of students who have training program that has expired and has no assets in the base date.
tipProximoVencidos=You can configure the number of days before and after the expiration of a training program for it to be considered in this indicator. That is, if the settings indicate that five days before and 1 day after the period is to be taken into consideration, and the basis of the query date is 02/06, all programs that expire between 28/05, 03/06 will be presented as a result.
tipIndiceAvaliacao=Average grade of the training received by teachers for two months (month of the base date and the previous). The value of the footer is the sum of all the mean, divided by the number of teachers who received some note, and not by the total teachers.
tipDuasEstrelas=Number less than or equal to 2 notes received by teachers for two months (month of the base date and the previous).
tipTreinosRenovados=From a training program teacher can press the "Reload" button, the system creates a new workout from the previous workout and puts the previous workout as renewed and this as a renewal.
tipTreinosRevisados=When the teacher enters the training program and review click the button, the system records a revision.
tipTreinosAcompanhados=When the teacher chooses to accompany the student join the system records the date and time. The action is that the teacher is with the students to perform all activities of daily training of the student.
cadastros.atividade.abrirGaleriaImagens=Open Image Gallery
cadastros.atividade.selecaoImagens=Select
cadastros.programa.datarevisao=Revision date
cadastros.programa.justificativaRevisao=History
cadastros.programa.historicoRevisoes=Revision History
cadastros.confirmar=Confirm
cadastros.empresa=Company
cadastros.localRetiraFicha=Retira Ficha Configurations
cadastros.localRetiraFicha.codigo= collector Code Retira Ficha
cadastros.config.empresa = Sets. Companies
marcarTodos=Check All
desmarcarTodos=Uncheck All
PERMITIR_APENAS_ALUNOS_ATIVOS=Allow only active students
auditoria=Log
alteracoes=Changes
personal=Personal
acompanhar_personal=Personal tracking
fazerCheckIn=Check In
iniciarAtendimento=Start service
historicoAulas=History lessons
operacao=Operation
saldo=Balance
fazerCheckOut=Check Out
finalizarAtendimento=Finish service
selecionarAluno=Select student
iniciar=Start
personaisCadastrados=Registered Personals
PAGAMENTO=Pagamento pr\u00E9 pago
CHECKIN=Check-In
CHECKOUT=Check-Out
CONS_PRE_PAGO=Cons. Pr\u00E9 Pago
CONS_POS_PAGO=Cons. P\u00F3s Pago
PERSONAL=Personal
autorizar=Autorizar
PERSONAL_SEM_CREDITO=O personal usa cr\u00E9ditos pr\u00E9-pagos e o saldo est\u00E1 zerado.
PARCELA_EM_ABERTO=O personal possui parcela em aberto a mais dias do que o permitido.
LIBERACAO_CHECK_IN=Libera\u00E7\u00E3o de Check-In
COMPRA_CREDITOS=Comprou cr\u00E9ditos
GASTO_CREDITOS=Consumiu cr\u00E9ditos
FECHAMENTO_CREDITOS=Fechamento de cr\u00E9ditos
gestaopersonal=Gest\u00E3o de Personal
crossfit=Crossfit
personais=Personais
lanc=Lanc.
cadastros.gestaocreditos=Gest\u00E3o Cr\u00E9ditos
colaboradores=Colaboradores
colaborador=Colaborador
novo=New
addColaborador=Add Colaborador
importarColaborador=Importar Colaborador
importaroutro=Importar outro
dataLancamento=Registration date
cadastro.aulas=Aulas
cadastro.aula.modalidade=Modalidade
ALTA_FREQUENCIA=Alta frequ\u00EAncia
BAIXA_FREQUENCIA=Baixa frequ\u00EAncia
salaCheia=Sala Cheia
SALA_CHEIA=Sala Cheia
NR_AULA_EXPERIMENTAL_ALUNO=Aulas experimentias por aluno
MINUTOS_AGENDAR_COM_ANTECEDENCIA=Permitir agendar com anteced\u00EAncia
PERMITE_ENVIO_RELATORIOS=Permitir envio de relat\u00F3rio para aluno
semana=Week
mes=Month
DIAS_MOSTRAR_TOTEM=Dias para mostrar no totem
PEDIDOS_NEGADOS=Pedidos negados
PEDIDOS_NEGADOS_MULTIPLOS=Pedidos m\u00FAltiplos negados
AULA_EXPERIMENTAL=Aulas experimentais
AULA_EXPERIMENTAL_MULTIPLOS=Aulas experimentais m\u00FAltiplas
clonar=Clonar
deletarAulas=Excluir aulas geradas
EMITIR_FICHA_APOS_VENCIMENTO_TREINO=Print sheet after training closure
SUBSTITUICAO=Replaced teachers
AULASMARCADAS=Classes marked
SOBRESSALENCIA=Overjet
MOBILE_SEMPRE_ATUALIZAR_CARGA_FICHA=Mobile sempre atualizar a carga da ficha
historicoBV=Visualizar hist\u00F3rico de BV
observacoesAluno=Observa\u00E7\u00E3o do aluno
observacoesAvaliacao=Observa\u00E7\u00F5es da avalia\u00E7\u00E3o f\u00EDsica
observacoes=Observa\u00E7\u00F5es
nenhumaObservacaoAluno=Nenhuma observa\u00E7\u00E3o foi feita sobre este aluno.
nenhumaObservacaoAvaliacaoFisica=Nenhuma observa\u00E7\u00E3o foi feita sobre avalia\u00E7\u00F5es f\u00EDsicas deste aluno.
ultimaEdicaoPor=\u00DAltima edi\u00E7\u00E3o por
as=\u00E0s
historico=Hist\u00F3rico
historicoObsAluno=Hist\u00F3rico da observa\u00E7\u00E3o do aluno
histObsAvaliacao=Hist\u00F3rico da observa\u00E7\u00E3o da avalia\u00E7\u00E3o f\u00EDsica
editadoPor=Editado por
horario=Hor\u00E1rio
nenhumAgendamento=Nenhum agendamento no per\u00EDodo.
AGENDA_ABRIR_LISTA=Abrir agenda sempre na vis\u00E3o de lista
TREINOU=Treinou
FALTOU_AGENDAMENTO=Faltou agendamento
AGENDAMENTO=Agendamento
CONFIRMOU_AGENDAMENTO=Confirmou agendamento
COMPARECEU_AGENDAMENTO=Compareceu agendamento
REVISOU_TREINO=Revisou programa
RENOVOU_TREINO=Renovou programa
ENTROU_NO_TREINO=Entrou no treino
NOTIFICACAO=Notifica\u00E7\u00E3o
MONTOU_TREINO=Montou programa
MUDOU_DE_NIVEL=Mudou de n\u00EDvel
AULA_PERSONAL=Aula com personal
AULA_CHEIA=Participou de aula
GANHOU_BADGE=Conquistou badge
atividades.aluno=Atividades do aluno
atualizar=Atualizar
ACABOU_TREINO=Terminou programa
carregar.mais=Carregar mais
mais.dados.sobre=Mais dados abaixo
TREINO=Treino
CARTEIRA=Carteira
AVALIACAO_FISICA=Avalia\u00E7\u00E3o F\u00EDsica
alunos.com.treino=Alunos ativos com treino
treinos.renovar=Treinos a renovar nos pr\u00F3ximos
meses=meses
total.alunos=Total de alunos
ativos=Ativos
inativos=Inativos
sem.avaliacao.fisica=Sem agendamento de avalia\u00E7\u00E3o f\u00EDsica nos \u00FAltimos
dias.acesso=M\u00E9dia de execu\u00E7\u00F5es de treino nos \u00FAltimos
manha=Manh\u00E3
tarde=Tarde
noite=Noite
acesso=Acesso
BI_RENOVACAO=% de renova\u00E7\u00E3o na carteira
BI_A_VENCER=A vencer em
BI_TREINOS_A_RENOVAR=Treinos a renovar
BI_NOVOS_NA_CARTEIRA=Novos na carteira
BI_TROCARAM_CARTEIRA=Trocaram de carteira
BI_TEMPO_CARTEIRA=Tempo m\u00E9dio de perman\u00EAncia na carteira
BI_TEMPO_PROGRAMA=Tempo m\u00E9dio de perman\u00EAncia no programa
BI_ATIVOS_FORA_TREINO=Alunos ativos fora do Treino
BI_TREINOS_EM_DIA=% de treinos em dia
BI_AVALIACAO_TREINO=Avalia\u00E7\u00E3o do treino dos alunos
BI_SEM_TREINO=Alunos ativos sem treino
BI_ALUNOS_FORA_TREINO=Alunos ativos fora do Treino
ultima.atualizacao=\u00DAltima atualiza\u00E7\u00E3o em
organizador=Ir para Organizador de Carteiras
com.avaliacao=Com avalia\u00E7\u00E3o
em.dia=Em dia
treinos.avencer=Alunos com treinos a renovar
avaliacoes.alunos=Avalia\u00E7\u00F5es dos alunos
ESTRELAS_5=Alunos que deram 5 estrelas
ESTRELAS_4=Alunos que deram 4 estrelas
ESTRELAS_3=Alunos que deram 3 estrelas
ESTRELAS_2=Alunos que deram 2 estrelas
ESTRELAS_1=Alunos que deram 1 estrelas
renovaram=Alunos que renovaram contrato
nao.renovaram=Alunos que n\u00E3o renovaram contrato
alunos.a.vencer=Alunos a vencer
novos.carteira=Alunos novos na carteira
trocaram.carteira=Alunos que trocaram de carteira
qrcodeprofessor=QR Code Professor
expiraEm=Expira em
importarAlunos=Importar Alunos
importarAlunosTreino=Importar alunos p/ TreinoWeb
PERIODO_USADO_BI=Per\u00EDodo abrangido no BI (em dias)
voltarplista=Voltar p/ lista
cadastro.tipoevento=Tipo de evento
definir.padrao.serie=Gerar padr\u00E3o de s\u00E9rie
definir.padrao=Gerar padr\u00E3o
USAR_COMPLEMENTO_REPETICAO_CARGA=Usar complementos para repeti\u00E7\u00E3o e carga
complemento.serie=Comp. s\u00E9rie
complemento.repeticao=Comp. repeti\u00E7\u00E3o
complemento.repeticao.title=Complemento para repeti\u00E7\u00E3o, aceita alfanum\u00E9ricos
complemento.serie.title=Complemento para carga, aceita alfanum\u00E9ricos
cadencia=Cad\u00EAncia
TODAS_ATIVIDADES=Todas atividades
SOMENTE_ATIVIDADES_ATIVAS=Ativas
SOMENTE_ATIVIDADES_INATIVAS=Inativas
add.imagens=Add imagem
voltarNomeOriginal=Resetar nome
cadastros.salvar.alteracoes=Salvar altera\u00E7\u00F5es
ver.historico.bv=Hist\u00F3rico de BV
clique.editar.nome=Clique para editar o nome da imagem
DURACAO_ALUNO_NA_ACADEMIA=Tempo em que o aluno permanece no filtro "Na academia" (Em minutos)
cadastros.aluno.nome=Nome do aluno
cadastros.novo.aluno=Novo aluno
masculino=Masculino
feminino=Feminino
sexo=Sexo
email=E-mail
telefone=Telefone
nome.usuario=Nome de usu\u00E1rio
addProfessor.criarUsuario=Criar usu\u00E1rio
perfil.usuario=Perfil de usu\u00E1rio
tipo.usuario=Tipo de usu\u00E1rio
LOGIN=Login
SENHA=Senha
REMETENTE=Remetente
EMAIL_PADRAO=E-mail padr\u00E3o
MAIL_SERVER=Mail Server
CONEXAO_SEGURA=Conex\u00E3o segura
INICIAR_TLS=Iniciar TLS
email.teste=Email para Teste
testar.envio=Testar envio
cadastros.empresas=Empresas
cadastros.empresasCadastrados=Empresas cadastradas
cadastros.empresa.nome=Nome da empresa
JANEIRO=Janeiro
FEVEREIRO=Fevereiro
MARCO=Mar\u00E7o
ABRIL=Abril
importar.alunos.usuario=Os alunos que n\u00E3o tem v\u00EDnculo com professor do TreinoWeb ser\u00E3o vinculados ao professor padr\u00E3o.<br/>O nome de usu\u00E1rio do aluno ser\u00E1 seu e-mail ou a matr\u00EDcula, caso n\u00E3o v\u00E1 usar o aplicativo.
MAIO=Maio
JUNHO=Junho
JULHO=Julho
AGOSTO=Agosto
SETEMBRO=Setembro
OUTUBRO=Outubro
NOVEMBRO=Novembro
DEZEMBRO=Dezembro
nao.existe.historico.objetivos=N\u00E3o existe hist\u00F3rico de altera\u00E7\u00F5es de objetivos para esse aluno.
nao.existe.historico.avaliacao=N\u00E3o existe hist\u00F3rico de altera\u00E7\u00F5es de dados de avalia\u00E7\u00E3o para esse aluno.
professor.padrao=Professor padr\u00E3o
AULAS_VERDES=They reached the maximum goal
AULAS_VERMELHAS=Did not reach the minimun goal
EXCECAO=BI de Ocupa\u00E7\u00E3o e Disponibilidade
DESEMPENHO=BI de Meta de ocupa\u00E7\u00E3o
RANKING_VERDE=BI de Professores com maior taxa de ocupa\u00E7\u00E3o
RANKING_VERMELHO=BI de Professores com menor taxa de ocupa\u00E7\u00E3o
OCUPACAO=Ocupa\u00E7\u00E3o
AULAS=Aula Cheia
BONIFICACAO=Bonifica\u00E7\u00E3o
VALOR_VERMELHO_BAIXA_FREQUENCIA=Meta m\u00EDnima baixa frequ\u00EAncia
VALOR_VERMELHO_ALTA_FREQUENCIA=Meta m\u00EDnima alta frequ\u00EAncia
VALOR_VERDE_BAIXA_FREQUENCIA=Meta m\u00E1xima baixa frequ\u00EAncia
VALOR_VERDE_ALTA_FREQUENCIA=Meta m\u00E1xima alta frequ\u00EAncia
VALIDAR_MODALIDADE=Validar modalidade para marcar aula
arevisar=Revisar
cadastros.programa.professorCarteira=Na carteira de
cadastros.salvar.atividade=Salvar atividade
alunos.selecionados=alunos selecionados
usuarios.cadastrados=Usu\u00E1rios cadastrados
username=Username
menor=Menor
maior=Maior
complemento.carga.title=Comp. carga
todosEventosRepetidosPosteriores=Todos eventos posteriores repetidos
gestao.comtreino=Com treino
VENCIMENTO_APENAS_ATIVOS=Considerar apenas alunos ativos no indicador Vencidos
cadastros.tirarFoto=Tirar Foto
frequencia=Frequ\u00EAncia
CREDITOS_EXPIRADOS=Cr\u00E9ditos expirados
contato=Contato
alterar.informacoes.login=Alterar informa\u00E7\u00F5es de login
adicionar.outro.email=Adicionar outro email
adicionar.outro.telefone=Adicionar outro telefone
colaborador.novo=NOVO COLABORADOR
cadastrar.usuario.treino=Cadastrar usu\u00E1rio no Treino?
colaboradores.cadastrados=Colaboradores cadastrados
replicar.valores.serie=Aplicar valores \u00E0s demais s\u00E9ries
plano=Plano
INATIVOS_A_X_DIAS=Inativos a X dias ( Deixar '0' para mostrar todos)
arenovar=A renovar
dos.alunos.ativos=dos alunos ativos
COMPARECERAM=Compareceram
CANCELARAM=Cancelaram
disponibilidade.agendamentos=Disponibilidade X Agendamentos
professores.agendamentos=Professores X Agendamentos em
horas.disponibilidade=Hrs de disponibilidade
horas.atendimento=Hrs de atendimento
disponibilidade.ocupacao=Ocupa\u00E7\u00E3o
novos.treinos=Novos treinos
treinos.renovados=Treinos renovados
treinos.revisados=Treinos revisados
avaliacoes.fisicas=Avalia\u00E7\u00F5es f\u00EDsicas
FALTARAM=Faltaram
acessos.execucoes=Acessos X Execu\u00E7\u00F5es
ACESSOS=Acessos
ACESSOS_TREINO=Alunos do Treino
EXECUCOES_TREINO=Execu\u00E7\u00F5es de treino
SMARTPHONE=Smartphone
replicarAgendamentos=Replicar nos agendamentos
replicarAgendamentosMesmoTipo=Replicar nos agendamentos de mesmo tipo
turmas.nomegeral=Turmas
salvarReplicar=Salvar e replicar
turmas.nome=Turma
turmas.dia=Dia
turmas.responsavel=Professor
turmas.tipo=Modalidade
turmas.inicio=In\u00EDcio
turmas.fim=Fim
turmas.desmarcados=Desmarcados
turmas.reposicoes=Reposi\u00E7\u00F5es
turmas.nrvagas=Capacidade
turmas.agendados=ALunos
turmas.inserir=Adicionar aluno
turmas.local=Ambiente
turmas.ambientes=Ambientes
turmas.nivel=N\u00EDvel
turmas.selecioneResponsavel=Todos os professores
turmas.LOCAL=Ambiente
turmas.TIPO=Modalidade
turmas.RESPONSAVEL=Professor
aula.coletiva=Aula coletiva
tvgestor=TV Gestor
agora=Agora
linksgerados=Links gerados
geradopor=Gerado por
datageracao=Data gera\u00E7\u00E3o
codigo=C\u00F3digo
BLOQUEAR_IMPRESSAO_FICHA_APOS_TODAS_EXECUCOES=Block prints after the student reaches the number of forecast executions
andamentoprogramas=Andamentos dos programas
gestaoAndamento=Gest\u00E3o de andamento dos programas
alunosFavoritos=Alunos favoritos
horas=horas
favoritosacessaram=Favoritos que acessaram no dia
alunosesperados=Alunos esperados
cadastros.aparelho.identificador=Identificador
cadastros.aparelho.addEmpresa=Adicionar empresa
cadastros.categoriafichaplur=Categoria das Fichas
somentemesmotipo=Eventos do mesmo tipo
agendaBack=Replicar
TREINO_INDEPENDENTE_PERMISSAO=Treino independente
cadastro.aula.modalidades=Modalidades
TODOS_APARELHOS=Todos aparelhos
historico.atestado=Hist\u00F3rico
AGENDAMENTOS=Agendamentos
AG_CONFIRMACAO=Aguardando
CONFIRMARAM=Confirmaram
DISPONIBILIDADES=Disponibilidades
nome.usuario.email=E-mail
importante=Important
hint_importante=Caso a op\u00E7\u00E3o esteja marcada, o sistema abrir\u00E1 o cadastro do aluno sempre na aba 'Observa\u00E7\u00F5es'.
HORARIO_DEFAULT_AC=Inicio
HORARIO_DEFAULT_TW=Initial time schedule 
NUMERO_IMPRESSAO_FICHA=Maximum number of sheet prints per day
USUARIO_SERVICE=Usuario service
TELEFONE_SERVICE=Telefone
EMAIL_SERVICE=E-mail Service
SENHA_SERVICE=Senha Service
criacaorapida=CRIA\u00C7\u00C3O R\u00C1PIDA DE PROGRAMA DE TREINO
adicionar=Adicionar
informacoes=Informa\u00E7\u00F5es
atividades.selecionadas=Atividades selecionadas
adicioneatividadecomecar=Adicione uma atividade para come\u00E7ar
alteracoes.salvas.automaticamente=...
continue.navegar=...
turmas.marcacoes=Marca\u00E7\u00F5es
verAnexo=See Attachment
adicionarObservacao= + Add new
avaliacao.fisica.historico=Physical evaluation history
adicionarbiset=Adicionar atividade para BI-Set
adicionartriset=Adicionar atividade para TRI-Set
cancelarbiset=Cancelar a escolha da atividade BI-Set
cancelartriset=Cancelar a escolha da atividade TRI-Set
removerbiset=Remover atividade BI-Set
ALUNO_MARCAR_PROPRIA_AULA=Allow the student to mark his class (collective classes)
bonificacao.atingiram.meta=They reached the maximum goal
bonificacao.nao.atingiram.meta=Did not reach the minimun goal
bonificacao.atingiram.meta.minima=Reached the minimum goal
AULAS_AMARELAS=Reached the minimum goal
cadastro.tipoBenchmark.origem\=Origem=Origin
cadastros.benchmark.dia=Day
gestao.comentario=Comment
RANKING_ALUNOS_APP_INSTALADO=Alunos inativos que utilizam o App
RANKING_ALUNOS_APP_INSTALADO_ATIVOS=Alunos ativos que utilizam o App
RANKING_ALUNOS_APP_NAO_INSTALADO=Alunos ativos que n\u00E3o utilizam o App
HABILITAR_CROSSFIT=Enable Crossfit module
MODULO_PRINCIPAL_APLICATIVO=Main module for 
LINK_APP_EMAIL=Link off app on e-mail
produto.free.pass=Product pass free or daily
MINUTOS_DESMARCAR_COM_ANTECEDENCIA=Tempo para desmarcar com anteced\u00EAncia
PROIBIR_MARCAR_AULA_PARCELA_VENCIDA=Forbid customer check-in for class with installment expired
RANKING_ALUNOS_CANCELADOS=Students canceled
title_ALUNOS_CANCELADOS=This indicator shows a number of students, from this teacher, who are canceled.
hint.rml.idadeInvalida=N\u00E3o ser\u00E1 calculado RML para este cliente devido a sua idade
condicao.atletica=Athletic Condition
faixa.recomendavel=Recommended Range
baixa.aptidao=Low Aptitude
condicao.de.risco=Risk Condition
PERMITE_ALUNO_MARCAR_AULA_OUTRA_UNIDADE=Allow the student to mark a class in another unit
