<?xml version="1.0" encoding="ISO-8859-1"?>
<aplicacao>
    <bd>        
        <nomeBDCep>BDCEP</nomeBDCep>
        <nomeBD></nomeBD>
        <servidor>localhost</servidor>
        <username>postgres</username>
        <senha>pactodb</senha>
        <porta>5432</porta>

        <!--Nunca subir para o SVN este arquivo sem aviso prévio!
        URL OAMD Producao:
        ******************************************
        URL OAMD Desenv:
        ******************************************DESENV

        Para desativar o OAMD  (Multibanco) e usar o cfgXML para conexao,
        deve-se apenas deixar a tag url-oamd vazia.        
        -->
        <url-oamd>**************************************</url-oamd>
        <NFE>false</NFE>
        <!-- Se roboApenasComoServico = true, fará com que o robô só seja utilizado
        como serviço e nunca através da aplicação JSF. Valor default = false
        -->
        <roboApenasComoServico>true</roboApenasComoServico>
        

        <!--alguns exemplos de context name, para tomcat vide tambï¿½m context.xml-->
        <JNDI-GlassFish>jdbc/Teste</JNDI-GlassFish>
        <JNDI-Tomcat>java:/comp/env/jdbc/BDTeste</JNDI-Tomcat>

        <!--em uso-->
        <JNDI>java:/comp/env/jdbc/BDZillyon</JNDI>        
        
        <DESV>false</DESV>
    </bd>
</aplicacao>