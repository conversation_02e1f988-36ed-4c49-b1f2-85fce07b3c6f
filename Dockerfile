FROM registry.gitlab.com/plataformazw/docker-pacto/tomcat:8

ENV DISCOVERY_URL=${DISCOVERY_URL:-"http://10.10.1.7:8080"}
ENV SERVIDOR_MEMCACHED=${SERVIDOR_MEMCACHED:-"DISABLED"}
ENV AUTH_SECRET_PATH=${AUTH_SECRET_PATH:-"/keys/auth-secret"}
ENV AUTH_SECRET_PERSONA_PATH=${AUTH_SECRET_PERSONA_PATH:-"/keys/auth-secret-persona"}
ENV URL_ZW_INTEGRACAO=${URL_ZW_INTEGRACAO}
ENV HEALTHCHECK_KEY=${HEALTHCHECK_KEY:-"teste"}
ENV DB_OAMD2_URL=${DB_OAMD2_URL:-"*************************************"}
ENV ENABLE_NEW_LOGIN=${ENABLE_NEW_LOGIN:-"false"}
ENV AMBIENTE_TESTE=${AMBIENTE_TESTE:-"true"}
ENV TOKENS_ACESSO_API_CLIENTE=${TOKENS_ACESSO_API_CLIENTE:-"gP6pV2pS6lC8sY7nH6vG8tN4xT0vR9tU"}
ENV TOKEN_GYMPASS_V3=${TOKEN_GYMPASS_V3}
ENV CONSULTA_USUARIOS_APP_PELO_FIREBASE=${CONSULTA_USUARIOS_APP_PELO_FIREBASE:-"true"}
ENV FOTOS_NUVEM=${FOTOS_NUVEM:-"false"}

COPY target/TreinoWeb /usr/local/tomcat/webapps/TreinoWeb
ENV TZ=America/Sao_Paulo

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
COPY src/main/resources/br/com/pacto/util/resources/OpcoesGlobais.properties /app/OpcoesGlobais.properties
COPY target/TreinoWeb/WEB-INF/classes/br/com/pacto/util/resources/OpcoesGlobais.properties /app/OpcoesGlobais.build.properties
COPY docker/bin/*.sh /bin/
COPY docker/keys/* /keys/
RUN chmod +x /bin/*.sh

#ADD https://ssl-ccp.godaddy.com/repository/gd_bundle-g2-g1.crt $JAVA_HOME/lib/security/gd_bundle-g2-g1.crt
#RUN cd $JAVA_HOME/lib/security/ \
#    && keytool -import -alias gd_bundle-g2-g1 -file gd_bundle-g2-g1.crt -keystore cacerts -trustcacerts -storepass changeit

#HEALTHCHECK --interval=1m --timeout=1m --retries=10 --start-period=2m \
#  CMD curl -f http://localhost:8080/TreinoWeb/prest/health/$HEALTHCHECK_KEY


ENTRYPOINT ["bash", "/bin/entrypoint.sh"]
