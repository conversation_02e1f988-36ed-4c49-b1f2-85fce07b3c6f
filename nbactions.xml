<?xml version="1.0" encoding="UTF-8"?>
<actions>
        <action>
            <actionName>run</actionName>
            <goals>
                <goal>package</goal>
            </goals>
            <properties>
                <netbeans.deploy>true</netbeans.deploy>
                <netbeans.deploy.clientUrlPart>/login</netbeans.deploy.clientUrlPart>
            </properties>
            <activatedProfiles>
                <activatedProfile>tomcat7</activatedProfile>
            </activatedProfiles>
        </action>
        <action>
            <actionName>debug</actionName>
            <goals>
                <goal>package</goal>
            </goals>
            <properties>
                <netbeans.deploy.debugmode>true</netbeans.deploy.debugmode>
                <netbeans.deploy>true</netbeans.deploy>
                <netbeans.deploy.clientUrlPart>/login</netbeans.deploy.clientUrlPart>
            </properties>
            <activatedProfiles>
                <activatedProfile>tomcat7</activatedProfile>
            </activatedProfiles>
        </action>
        <action>
            <actionName>profile</actionName>
            <goals>
                <goal>package</goal>
            </goals>
            <properties>
                <netbeans.deploy>true</netbeans.deploy>
                <netbeans.deploy.profilemode>true</netbeans.deploy.profilemode>
                <netbeans.deploy.clientUrlPart>/login</netbeans.deploy.clientUrlPart>
            </properties>
            <activatedProfiles>
                <activatedProfile>tomcat7</activatedProfile>
            </activatedProfiles>
        </action>
        <action>
            <actionName>build</actionName>
            <goals>
                <goal>validate</goal>
            </goals>
            <activatedProfiles>
                <activatedProfile>tomcat7</activatedProfile>
                <activatedProfile>desenv</activatedProfile>
            </activatedProfiles>
        </action>
        <action>
            <actionName>rebuild</actionName>
            <goals>
                <goal>clean</goal>
                <goal>install</goal>
            </goals>
            <activatedProfiles>
                <activatedProfile>tomcat7</activatedProfile>
            </activatedProfiles>
        </action>
        <action>
            <actionName>build-with-dependencies</actionName>
            <reactor>also-make</reactor>
            <goals>
                <goal>install</goal>
            </goals>
            <activatedProfiles>
                <activatedProfile>tomcat7</activatedProfile>
            </activatedProfiles>
        </action>
        <action>
            <actionName>test</actionName>
            <goals>
                <goal>test</goal>
            </goals>
            <activatedProfiles>
                <activatedProfile>tomcat7</activatedProfile>
            </activatedProfiles>
        </action>
    </actions>
