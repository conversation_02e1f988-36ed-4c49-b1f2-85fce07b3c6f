#!/bin/bash

set -e

echo "=== Building and Running Treino Project ==="

source "$HOME/.sdkman/bin/sdkman-init.sh"

if ! sdk list java | grep -q "8.0.402-tem"; then
  sdk install java 8.0.402-tem
fi
sdk use java 8.0.402-tem

echo "Building project with Maven..."
mvn clean package -P desenv,docker,tomcat7,local

TOMCAT_DIR="$HOME/tomcat8"
WEBAPPS_DIR="$TOMCAT_DIR/webapps"

echo "Cleaning old deployment..."
rm -rf "$WEBAPPS_DIR/TreinoWeb"
rm -f "$WEBAPPS_DIR/TreinoWeb.war"

echo "Deploying WAR file..."
cp target/TreinoWeb.war "$WEBAPPS_DIR/"

echo "Starting Tomcat..."
"$TOMCAT_DIR/bin/startup.sh"

echo "=== Application started! ==="
echo "Access the application at: http://localhost:8080/TreinoWeb"
echo "To stop the application, run: $TOMCAT_DIR/bin/shutdown.sh"

sleep 5
if curl -f http://localhost:8080 > /dev/null 2>&1; then
    echo "✅ Tomcat is running successfully!"
else
    echo "⚠️  Tomcat may still be starting up. Check logs at: $TOMCAT_DIR/logs/catalina.out"
fi
